<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>kbc-common</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-elms</artifactId>
    <name>kbc-elms</name>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>


    <modules>
        <module>kbc-elms-api</module>
        <module>kbc-elms-web</module>
        <module>kbc-elms-service</module>
        <module>kbc-elms-entity</module>
    </modules>

    <properties>
        <!--mvn -N versions:update-child-modules-->
        <java.version>1.8</java.version>
        <core.version>1.0.3-SNAPSHOT</core.version>
        <elasticsearch.version>7.6.2</elasticsearch.version>
        <fastjson.version>1.2.83</fastjson.version>
    </properties>

    <dependencyManagement>
        <!--web启动器依赖-->
        <dependencies>
            <!-- 业务实体-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-elms-entity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-elms-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-elms-flowable</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>common-spring-boot-starter</artifactId>
                <version>${core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>config-spring-boot-starter</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <!--工具类 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>tool-spring-boot-starter</artifactId>
                <version>${core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- 非关系型数据库配置 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>redis-spring-boot-starter</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>feign-spring-boot-starter</artifactId>
                <version>${core.version}</version>
            </dependency>
            <!-- API文档配置 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bsc-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bsc-entity</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ums-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-tps-api-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ufs-web-client</artifactId>
                <version>1.1.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ucs-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.kbao</groupId>
                        <artifactId>kbc-asc-shop-web-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.kbao</groupId>
                        <artifactId>kbc-asc-shop-api-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.kbao</groupId>
                        <artifactId>kbc-asc-shop-entity</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--任务调度-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-job-client-starter</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bpm-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bpm-entity</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-parent</artifactId>
                <version>3.5.10</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://10.176.1.3:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://10.176.1.3:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>


    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://10.176.1.3:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.4.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <target>${java.version}</target>
                        <source>${java.version}</source>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <configuration>
                        <archive>
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.yml</include>
                </includes>
                <!-- profiles 结合该参数在 yml中使用，需要设置 filter 为true -->
                <!--Maven 配置filter true 导致Excel文件损坏的问题 -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.p12</include>
                    <include>**/*.conf</include>
                    <include>**/*.txt</include>
                    <include>**/*.wsdl</include>
                    <include>**/*.xsd</include>
                    <include>**/*.ftl</include>
                    <include>**/*.lua</include>
                    <include>**/*.json</include>
                    <include>processes/*</include>
                    <include>**/template/**/*</include>
                    <include>**/spring.factories</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.config</include>
                    <include>**/pdf/**</include>
                    <include>**/template/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <profiles.active>local</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
        </profile>
        <profile>
            <id>sta</id>
            <properties>
                <profiles.active>sta</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profiles.active>uat</profiles.active>
                <env.version>-uat-</env.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <env.version>-</env.version>
            </properties>

            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <url>http://10.176.1.3:8082/nexus/content/repositories/releases</url>
                </repository>
                <snapshotRepository>
                    <id>snapshots</id>
                    <url>http://10.176.1.3:8082/nexus/content/repositories/snapshots</url>
                </snapshotRepository>
            </distributionManagement>


            <repositories>
                <repository>
                    <id>nexus</id>
                    <name>nexus</name>
                    <url>http://10.176.1.3:8082/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

</project>