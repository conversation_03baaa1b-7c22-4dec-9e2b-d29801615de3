package com.kbao.kbcelms;

import com.kbao.kbcbsc.log.annotation.EnableLogging;
import com.kbao.kbcbsc.log.controller.LogController;
import com.kbao.kbcbsc.log.service.LogService;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableFeignClients(basePackages = "com.kbao")
@Configuration
@EnableLogging
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.kbao")
@EnableTransactionManagement
@EnableScheduling
@EnableEncryptableProperties
@ComponentScan(basePackages = {"com.kbao"}, excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
        classes = {LogService.class, LogController.class})})
public class KbcElmsApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(KbcElmsApiApplication.class, args);
    }

}
