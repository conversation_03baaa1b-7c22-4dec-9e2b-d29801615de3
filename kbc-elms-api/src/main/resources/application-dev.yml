spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: kbc_elms_sta_rw
    password: QA3xcHNE_unFIEQh
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置(通常来说，只需要修改initialSize、minIdle、maxActive
    # 配置获取连接等待超时的时间
    druid:
      max-active: 20
      min-idle: 5
      initial-size: 5
      max-wait: 10000
  #  jpa:
  #    hibernate:
  #      ddl-auto: update
  #    show-sql: true
  #    database-platform: org.hibernate.dialect.MySQL8Dialect

  redis:
    ################### redis 单机版 start ##########################
    host: ***********
    port: 6379
    password: ewOIJ*f7gUT^&63tiu3flk3o
    timeout: 6000
    database: 18
    lettuce:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）,如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)
        max-idle: 8   # 连接池中的最大空闲连接 ，默认值也是8
        max-wait: -1  # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
        min-idle: 2    # 连接池中的最小空闲连接 ，默认值也是0
        time-between-eviction-runs: 10000ms # 这个设置是，每隔多少毫秒，空闲线程驱逐器关闭多余的空闲连接，且保持最少空闲连接可用，这个值最好设置大一点，否者影响性能。同时 genericObjectPoolConfig.setMinIdle(minIdle); 中minldle值要大于0。
        #lettuce连接池属性timeBetweenEvictionRunsMillis如果不设置 默认是 -1，当该属性值为负值时，lettuce连接池要维护的最小空闲连接数的目标minIdle就不会生效 。源码中的解释如下：
      shutdown-timeout: 100ms

  ################### redis 单机版 end ##########################

  ################## mongodb 配置 #################
  data:
    mongodb:
      uri: mongodb://kbc_elms_sta_rw:<EMAIL>:27017/kbc_elms_sta?authsource=kbc_elms_sta

################## eureka 注册中心配置 #################
eureka:
  client:
    enabled: true # 是否开启向注册中心进行注册
    serviceUrl:
      #      defaultZone: http://kbc:<EMAIL>-cs-bsc-sta:8000/eureka/,http://kbc:<EMAIL>-cs-bsc-sta:8000/eureka/,http://kbc:<EMAIL>-cs-bsc-sta:8000/eureka/
      defaultZone: https://kbc:<EMAIL>/eureka/
    registry-fetch-interval-seconds: 5
    instance-info-replication-interval-seconds: 10
    register-with-eureka: false
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 20  # 续约时间30， 主动下线检测心跳， 服务器默认90秒
    status-page-url: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html

ribbon:
  ServerListRefreshInterval: 5000    #刷新服务列表源的间隔时间

##feign参数优化
feign:
  client:
    config:
      default:
        # 配合logging.level=trace debug用于开发调式日志
        loggerLevel: full
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 10000
  httpclient:
    enabled: true
    # 最大连接数
    max-connections: 400
    # 单个路由最大链接数
    max-connections-per-route: 100

server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 300
    max-connections: 1000

logging:
  level:
    com.kbao: debug
    org.hibernate: info
    org.springframework: info
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate.type.descriptor.sql.BasicExtractor: trace
    org.springframework.data.mongodb.core.MongoTemplate: debug
    com.alibaba.nacos.client: off
    org.flowable: debug
    com.kbc.flowable: debug
    org.flowable.common.engine.impl.db: debug


#文件上传系统对接账号、秘钥
file:
  upload:
    userName: kbcmes
    secretKey: a85732ba533544b7a41e8c2c692ac00d
  sts:
    userName: kbcmes
    secretKey: a85732ba533544b7a41e8c2c692ac00d
    network: sts

#定时任务调度系统(本地无法调试)
xxl:
  job:
    admin:
      addresses: http://sta-kbcs-job-rest-jar.kbao-cs-bsc-sta.svc.cluster.local:8180
    access-token: 8cb51079ee4f4951a74d378fa61f45aa
    executor:
      app-name: kbc-elms-sta
    client:
      enable: false  #设置为fals表示关闭定时任务，因为默认为开启，dev环境需要关闭
