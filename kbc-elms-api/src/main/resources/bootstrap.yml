#端口配置
server:
  port: 7002   #固定端口
#  port: ${randomServerPort.value[7000,7005]}  #随机端口

#服务名称
spring:
  main: 
    allow-bean-definition-overriding: true
  application:
    name: kbc-elms-api
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      config:
        # 配置中心地址
        server-addr: https://config-sta-lan.kbao123.com
        # 账号
        username: kbc-elms-r
        # 密码
        password: 7R5oukELtULjALhS6FiXtSkmMfhg5UMW
        # 自动刷新配置
        #refresh-enabled: true
        # 配置文件格式
        file-extension: yml
        # 指定group 默认 DEFAULT_GROUP
        group: group-kbc-elms
        # 指定namespace id 默认public
        namespace: ${spring.profiles.active}
        # 自定义dataId，默认spring.application.name
        prefix: sta-kbcs-app-elms-web-jar
        enabled: false

#禁用自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
#
redis:
  application:
    name: kbc-elms-api

