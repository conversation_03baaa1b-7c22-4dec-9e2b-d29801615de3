-- --------------------------------------------------------
-- 主机:                           mysql-kbcs-test-lan.kbao123.com
-- 服务器版本:                        5.7.24-log - MySQL Community Server (GPL)
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  12.3.0.6589
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 kbc_elms_sta.t_auth 结构
CREATE TABLE IF NOT EXISTS `t_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '功能编号',
  `auth_code` varchar(64) NOT NULL COMMENT '权限编号',
  `auth_name` varchar(32) DEFAULT NULL COMMENT '权限位名称',
  `parent_code` varchar(255) DEFAULT NULL COMMENT '权限路由',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_code` (`auth_code`),
  KEY `index_sort` (`sort`) USING BTREE,
  KEY `index_func_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='功能权限位';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_bas_code 结构
CREATE TABLE IF NOT EXISTS `t_bas_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(12) NOT NULL COMMENT '编码',
  `code_type` varchar(10) DEFAULT NULL COMMENT '状态',
  `name` varchar(20) NOT NULL COMMENT '名称',
  `parent_code` varchar(10) DEFAULT NULL COMMENT '父级编码',
  `pinyin_code` varchar(10) DEFAULT NULL COMMENT 'PINYIN',
  `enable` varchar(10) DEFAULT NULL COMMENT '是否可用',
  `sort` varchar(10) DEFAULT NULL COMMENT '排序',
  `remark` varchar(10) DEFAULT NULL COMMENT '注释',
  `post_code` varchar(10) DEFAULT NULL,
  `company_id` varchar(20) NOT NULL COMMENT '保司ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code_company_id_0` (`code`,`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15399 DEFAULT CHARSET=utf8;

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_data_template 结构
CREATE TABLE IF NOT EXISTS `t_data_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_name` varchar(50) NOT NULL COMMENT '模板名称',
  `biz_code` varchar(50) NOT NULL COMMENT '模板编号',
  `type` varchar(50) DEFAULT NULL COMMENT '分类信息',
  `status` char(1) DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(500) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` varchar(50) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(4) NOT NULL COMMENT '是否删除 0-未删除 1-已删除',
  `tenant_id` varchar(10) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_biz_code` (`biz_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据模板表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity 结构
CREATE TABLE IF NOT EXISTS `t_opportunity` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `biz_code` varchar(50) NOT NULL COMMENT '机会编码',
  `agent_code` varchar(50) NOT NULL COMMENT '顾问工号',
  `agent_name` varchar(50) DEFAULT NULL COMMENT '顾问姓名',
  `opportunity_name` varchar(50) DEFAULT NULL COMMENT '机会名称',
  `company_id` int(11) NOT NULL COMMENT '关联企业ID',
  `opportunity_type` char(2) DEFAULT NULL COMMENT '机会类型: 1-员服，2-综合',
  `industry_id` int(11) NOT NULL COMMENT '关联行业ID',
  `status` char(2) DEFAULT NULL COMMENT '机会状态：0-待提交，1-已提交，2-锁定，3-中止，4-终止',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人 当前用户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(10) DEFAULT NULL COMMENT '租户ID',
  `current_process_id` int(11) DEFAULT NULL COMMENT '当前流程id，对应 opportunity_process 表 id',
  `is_deleted` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_biz_code` (`biz_code`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_industry_id` (`industry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机会表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_detail 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `opportunity_id` int(11) NOT NULL COMMENT '机会ID',
  `insure_num` int(11) DEFAULT NULL COMMENT '投保人员规模',
  `has_history_policy` char(1) DEFAULT NULL COMMENT '是否有历史保单：0-否，1-是',
  `policy_expire_time` date DEFAULT NULL COMMENT '保单到期日期',
  `is_bid` char(1) DEFAULT NULL COMMENT '是否需要投标：0-否，1-是',
  `bid_start_date` date DEFAULT NULL COMMENT '投标开始时间',
  `big_end_date` date DEFAULT NULL COMMENT '投标结束时间',
  `premium_budget` int(11) DEFAULT NULL COMMENT '保费预算',
  `contacter` varchar(50) DEFAULT NULL COMMENT '企业对接人',
  `contacter_post` varchar(50) DEFAULT NULL COMMENT '企业对接人职务',
  `add_health_service` char(1) DEFAULT NULL COMMENT '是否添加健康服务产品',
  `add_rescue_service` char(1) DEFAULT NULL COMMENT '是否添加救援服务产品',
  `general_insurance_name` varchar(100) DEFAULT NULL COMMENT '综合险种名称，多个用逗号分割',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人 当前用户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(10) DEFAULT NULL COMMENT '租户ID',
  `is_deleted` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_opportunity_id` (`opportunity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机会明细表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_order 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `opportunity_id` varchar(16) DEFAULT NULL COMMENT '机会id',
  `tenant_id` varchar(16) DEFAULT NULL COMMENT '租户id',
  `company_code` varchar(16) DEFAULT NULL,
  `order_code` varchar(16) DEFAULT NULL,
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_role_index` (`opportunity_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_process 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_process` (
  `Id` int(11) NOT NULL COMMENT '编号',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户id',
  `process_key` varchar(50) DEFAULT NULL COMMENT '流程定义key',
  `process_name` varchar(50) DEFAULT NULL COMMENT '流程名称',
  `opportunity_id` int(100) DEFAULT NULL COMMENT '机会id',
  `bpm_process_id` varchar(200) DEFAULT NULL COMMENT '业务流程表Id',
  `process_status` varchar(50) DEFAULT NULL COMMENT '流程状态',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人编号 当前用户ID',
  `create_time` date DEFAULT NULL COMMENT '创建日期',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
  `update_time` date DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(11) DEFAULT NULL COMMENT '删除',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 ROW_FORMAT=DYNAMIC COMMENT='流程定义表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_process_log 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_process_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `opportunity_id` varchar(16) DEFAULT NULL COMMENT '机会id',
  `tenant_id` varchar(16) DEFAULT NULL COMMENT '租户id',
  `process_id` varchar(16) DEFAULT NULL COMMENT '流程id',
  `operaor_id` varchar(16) DEFAULT NULL COMMENT '操作人id',
  `operaor_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_role_index` (`opportunity_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_team 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `opportunity_id` int(11) DEFAULT NULL COMMENT '机会id',
  `tenant_id` varchar(50) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `role_des` varchar(4000) DEFAULT NULL COMMENT '角色描述',
  `join_type` int(11) DEFAULT NULL COMMENT '参与状态 0 待确认，1已确认  5 已拒绝',
  `times` int(11) DEFAULT NULL COMMENT '邀请次数',
  `create_id` varchar(4000) NOT NULL COMMENT '创建人编号 当前用户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建人姓名',
  `update_id` varchar(4000) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL COMMENT '创建日期 默认为当前时间',
  `is_deleted` int(11) DEFAULT NULL COMMENT '排序号',
  PRIMARY KEY (`id`),
  KEY `opportunity_id` (`opportunity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='机会项目成员列表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_team_division 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_team_division` (
  `Id` int(11) NOT NULL COMMENT '编号',
  `opportunity_id` int(11) DEFAULT NULL COMMENT '机会id',
  `tenant_id` varchar(50) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `division_id` int(11) DEFAULT NULL COMMENT '项目分工',
  `division_ratio` float DEFAULT NULL COMMENT '分工比例',
  `first_status` int(11) DEFAULT NULL COMMENT '首次参与状态 0 待确认，1已确认  5 已拒绝',
  `division_ratio_confirm` varchar(4000) DEFAULT NULL COMMENT '确认分工比例',
  `create_id` varchar(4000) DEFAULT NULL COMMENT '创建人编号 当前用户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建人姓名',
  `update_id` varchar(4000) DEFAULT NULL COMMENT '更新人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间 0正常 1审核中 2被否决 -1已删除 -2草稿',
  `is_deleted` int(11) DEFAULT NULL COMMENT '删除',
  `last_status` int(11) DEFAULT NULL COMMENT '最后确认状态',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='项目分工比例';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_process_define 结构
CREATE TABLE IF NOT EXISTS `t_process_define` (
  `Id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户id',
  `process_key` varchar(50) DEFAULT NULL COMMENT '流程定义key',
  `process_name` varchar(50) DEFAULT NULL COMMENT '流程名称',
  `process_status` int(5) DEFAULT NULL COMMENT '流程状态 0 草稿  1 启用 2 停用',
  `process_desc` varchar(200) DEFAULT NULL COMMENT '流程描述',
  `process_condition` int(5) DEFAULT NULL COMMENT '流程条件 1 完成企业进阶信息录入 2 完成风险评估报告 3 完成投保信息录入',
  `process_type` int(5) DEFAULT NULL COMMENT '流程类型 1 默认  2自定义',
  `company_addrs` varchar(200) DEFAULT NULL COMMENT '公司所在地 多个地址，号隔开',
  `company_industry` varchar(200) DEFAULT NULL COMMENT '公司行业 多个行业，号隔开',
  `company_type` varchar(100) DEFAULT NULL COMMENT '企业规模 A,B,C,D',
  `agent_legal_code` varchar(100) DEFAULT NULL COMMENT '代理人法人公司',
  `agent_trading_center_code` varchar(100) DEFAULT NULL COMMENT '营业部编码',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人编号 当前用户ID',
  `create_time` date DEFAULT NULL COMMENT '创建日期',
  `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
  `update_time` date DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(11) DEFAULT NULL COMMENT '删除',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1 COMMENT='流程定义表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_role 结构
CREATE TABLE IF NOT EXISTS `t_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称',
  `tenant_id` varchar(16) DEFAULT NULL COMMENT '归属租户Id',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `role_type` int(11) DEFAULT NULL COMMENT '角色类型：1分公司统筹、2总公司统筹、3分公司项目经理、4总公司项目经理、99其他',
  `role_desc` varchar(100) DEFAULT NULL COMMENT '角色说明',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_role_auth 结构
CREATE TABLE IF NOT EXISTS `t_role_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL COMMENT '编号',
  `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称',
  `tenant_id` varchar(16) DEFAULT NULL COMMENT '归属租户Id',
  `auth_code` varchar(16) DEFAULT NULL COMMENT '功能ID',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_role_id` (`role_id`) USING BTREE,
  KEY `index_tenant_id` (`tenant_id`) USING BTREE,
  KEY `index_func_id` (`auth_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色功能权限';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_user 结构
CREATE TABLE IF NOT EXISTS `t_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(16) NOT NULL COMMENT '编号',
  `bsc_use_name` varchar(32) DEFAULT NULL COMMENT '云服登录账户',
  `nick_name` varchar(32) DEFAULT NULL COMMENT '姓名',
  `phone` varchar(11) DEFAULT NULL COMMENT '电话',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `wechat_user_id` varchar(16) DEFAULT NULL COMMENT '企微账号',
  `ehr_user_code` varchar(16) DEFAULT NULL COMMENT 'ehr账户',
  `agent_code` varchar(32) DEFAULT NULL COMMENT '大童销售工号',
  `person_desc` varchar(16) DEFAULT NULL COMMENT '个人简介',
  `insurance_types` varchar(16) DEFAULT NULL COMMENT '擅长险种',
  `industry_types` varchar(16) DEFAULT NULL COMMENT '擅长行业',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `index_user_name` (`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_user_role 结构
CREATE TABLE IF NOT EXISTS `t_user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` varchar(16) DEFAULT NULL COMMENT '用户ID',
  `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_role_index` (`user_id`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_user_tenant 结构
CREATE TABLE IF NOT EXISTS `t_user_tenant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` varchar(16) NOT NULL COMMENT '编号',
  `tenant_id` varchar(16) DEFAULT NULL COMMENT '租户id',
  `relation_type` varchar(10) DEFAULT NULL COMMENT '关联类型： org 机构 dept部门 out 外部',
  `organ_id` varchar(32) DEFAULT NULL,
  `organ_id_path` varchar(255) DEFAULT NULL COMMENT '机构/部门/渠道Id节点路径（集合）',
  `organ_path` varchar(128) DEFAULT NULL COMMENT '节点路径 1/2/3/4',
  `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '0 有效 1 删除',
  `stop_reason` varchar(500) DEFAULT NULL COMMENT '停用原因',
  `status` varchar(2) DEFAULT '1' COMMENT '1 启用 0 停用',
  `wechat_account` varchar(64) DEFAULT NULL COMMENT '企微账号',
  `expert_type` int(11) DEFAULT NULL COMMENT '专家身份  1 团财专员、2 代理人、3 经纪人、4 营业部负责人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_user_tenant` (`user_id`,`tenant_id`) USING BTREE,
  KEY `index_org_id` (`organ_id`) USING BTREE,
  KEY `index_relation` (`relation_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表租户关系表';

-- 数据导出被取消选择。

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
