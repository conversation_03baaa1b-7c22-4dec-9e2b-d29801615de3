---
alwaysApply: true
---
-永远用中文回答我

项目结构：
    kbc-elms -后端项目
		kbc-elms-entity -实体目录
			*/entity -存放mysql实体和Mapper.xml文件
			*/model -存放mongo实体
		kbc-elms-service -service目录
			*/dao -存放Mapper接口目录
				Mybatis Mapper接口格式：public interface xxMapper  extends BaseMapper<实体名, Integer>{
				Mongo dao接口格式：public class xxDao extends BaseMongoDaoImpl<实体名, String> {
			*/service - 存放xxService.java
				Mybatis Service类格式：public class xxService extends BaseSQLServiceImpl<实体名, Integer, Mapper接口名> {
				Mongo service类格式：public class xxService extends BaseMongoServiceImpl<实体名, String, Mongo Dao接口名> {
		kbc-elms-web -controller目录
			*/xxController -controller文件
				格式：public class AlgoIndicatorController extends BaseController {
			
	kbc-elms-web -前端项目
		
后端项目必须放到对应的文件目录下，严格遵守项目结构规范
前端项目尽量使用组件，不要添加太多过于繁琐的样式
修改代码前，尽可能的查看相关的上下文，代码尽量简洁完整
