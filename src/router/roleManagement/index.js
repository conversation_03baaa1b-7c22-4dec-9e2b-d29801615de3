export default [
  {
    path: "/permission",
    name: "permission",
    component: () => import("@/views/roleManagement/permission"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/permissionConfig",
    name: "permissionConfig",
    component: () => import("@/views/roleManagement/permissionConfig"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/roleList",
    name: "roleList",
    component: () => import("@/views/roleManagement/roleList"),
    meta: {
      notCache: true
    }
  },

]
