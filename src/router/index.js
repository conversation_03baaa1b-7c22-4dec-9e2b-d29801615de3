import Vue from "vue";
import VueRouter from "vue-router";
import home from "./home";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import store from "@/store/layoutStore.js";
import { stringToObject} from '@/utils/utils'
import merge from "webpack-merge"
import aggregateRoutes from './aggregate'
import formulaEngine from './formulaEngine'
import questionnaireRoutes from './questionnaire'
import enterpriseRoutes from './enterprise'
import riskMatrixRoutes from './riskMatrix'
import basicConfigRoutes from './basicConfig'
import constantSetting from './constantSetting'
import onlineProductRoutes from './onlineProduct'

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "index",
    redirect: "/home",
    component: () =>
      import(/* webpackChunkName: "layouts" */ "@/views/index.vue"),
    children: [...home]
  },
  {
    path: "*",
    name: "404",
    component: () =>
      import(/* webpackChunkName: "exception" */ "@/views/exception/404")
  },
  ...formulaEngine,
  ...questionnaireRoutes,
  ...enterpriseRoutes,
  ...riskMatrixRoutes,
  ...aggregateRoutes,
  ...basicConfigRoutes,
  ...constantSetting,
  ...onlineProductRoutes // 新增线上产品配置路由
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

router.beforeEach((to, from, next) => {
  //页面从iframe中打开
  let pageFrom = to.query.from;
  let param = to.query.param;
  if(pageFrom=="iframe"&&param){
    param = decodeURIComponent(param);
    let objParam = stringToObject(param);
    let path = objParam.path;
    if(path){
        delete objParam['path']
        next({
          name:path,
          query:objParam
        });
    }else{
        next({
          name:to.name,
          query:merge(objParam)
        });
    }
  }
  //页面的滚动条
  if (to.path !== from.path) {
    NProgress.start();
    let el = document.getElementById("nprogress");
    if (el) {
      el.querySelector(".bar").style.background = store.state.themeObj.color;
      el.querySelector(".spinner-icon").style.borderTopColor = store.state.themeObj.color;
      el.querySelector(".spinner-icon").style.borderLeftColor = store.state.themeObj.color;
    }
  }
  next();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
