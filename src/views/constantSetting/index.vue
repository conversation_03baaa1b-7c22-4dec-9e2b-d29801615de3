<template>
  <div class="basic-parameter-container">

    <!-- 只保留UniversalTable及其相关插槽，删除el-table、el-table-column、原有分页div等多余代码 -->
    <UniversalTable
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-form-config="searchFormConfig"
      :search-params="searchParams"
      add-button-text="新增常数"
      title="常数设置"
      subtitle="管理系统常数配置，支持数学常数、权重系数等，可在公式配置中直接使用"
      title-icon="el-icon-setting"
      :action-column-width="280"
      empty-title="暂无常数数据"
      empty-description="点击上方新增常数按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <template #category="{ row }">
        <el-tag type="info" size="small" effect="light">
          {{ row.categoryName || getCategoryName(row.category) }}
            </el-tag>
          </template>
      <template #dataType="{ row }">
        <el-tag :type="getDataTypeTagType(row.dataType)" size="small" effect="light">
          {{ getDataTypeLabel(row.dataType) }}
            </el-tag>
          </template>
      <template #status="{ row }">
            <el-switch
          v-model="row.status"
              :active-value="1"
              :inactive-value="0"
          @change="handleStatusChange(row)"
              class="status-switch"
            />
          </template>
      <template #createTime="{ row }">
            <div class="time-cell">
              <i class="el-icon-time"></i>
          {{ row.createTime }}
            </div>
          </template>
    </UniversalTable>



    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该常数？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import {
  getConstantList,
  deleteConstant,
  getConstantCategoryList,
  updateConstantStatus
} from '@/api/constantSetting';
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
import {getDicItems} from "@/api/dictionary";
import {getDicItemList} from "@/config/tool";

export default {
  name: 'ConstantSetting',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        name: '',
        code: '',
        category: '',
        dataType: '',
        status: ''
      },

      // 搜索表单参数（用于 SearchForm 组件）
      searchParams: {
        param: {
          name: '',
          code: '',
          category: '',
          dataType: '',
          status: ''
        }
      },

      // 表格数据
      tableData: [],
      loading: false,

      // 分页
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 分类选项
      categoryOptions: [],

      // 当前操作行
      currentRow: null,
      tableColumns: [
        { prop: 'name', label: '常数名称', minWidth: 120, align: 'center' },
        { prop: 'code', label: '常数编码', minWidth: 120, align: 'center' },
        { prop: 'description', label: '常数描述', minWidth: 200, align: 'center' },
        { prop: 'defaultValue', label: '默认值', minWidth: 120, align: 'center' },
        { prop: 'category', label: '常数分类', minWidth: 120, align: 'center' },
        { prop: 'dataType', label: '数据类型', minWidth: 100, align: 'center' },
        { prop: 'usageCount', label: '使用次数', minWidth: 100, align: 'center' },
        { prop: 'status', label: '状态', minWidth: 100, align: 'center' },
        { prop: 'createTime', label: '创建时间', minWidth: 180, align: 'center' }
      ],
      tableActions: [
        { key: 'view', label: '查看', icon: 'el-icon-view', class: 'view-btn', size: 'mini' },
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'edit-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ],
      searchFormConfig: [
        { label: '常数名称', name: 'name', type: 'input', placeholder: '请输入常数名称或编码' },
        { label: '常数分类', name: 'category', type: 'select', placeholder: '请选择常数分类', list: [] },
        { label: '数据类型', name: 'dataType', type: 'select', placeholder: '请选择数据类型', list: [
          { dicItemName: '数值', dicItemCode: 'number' },
          { dicItemName: '变量', dicItemCode: 'variable' },
          { dicItemName: '字符串', dicItemCode: 'string' }
        ] },
        { label: '状态', name: 'status', type: 'select', placeholder: '请选择状态', list: [
          { dicItemName: '启用', dicItemCode: 1 },
          { dicItemName: '禁用', dicItemCode: 0 }
        ] }
      ]
    }
  },

  watch: {
    // searchForm 变化时同步到 searchParams.param
    searchForm: {
      handler(newVal) {
        console.log('searchForm changed:', newVal);
        this.searchParams.param = { ...newVal };
      },
      deep: true,
      immediate: true
    },
    // searchParams.param 变化时同步到 searchForm
    'searchParams.param': {
      handler(newVal) {
        console.log('searchParams.param changed:', newVal);
        // 避免循环更新
        if (JSON.stringify(this.searchForm) !== JSON.stringify(newVal)) {
          this.searchForm = { ...newVal };
        }
      },
      deep: true
    }
  },

  mounted() {
    this.loadCategoryOptions();
    this.loadTableData();
  },

  methods: {
    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await getDicItemList("elsm.constant.cateory");
        console.log(response)
        // 健壮性处理：确保 response.data 是数组
        if (response && Array.isArray(response)) {
          // 严格基于mockCategories结构
          this.categoryOptions = response.map(cat => ({
            dicItemName: cat.dicItemName,
            dicItemCode: parseInt(cat.dicItemCode)
          }));
          // 更新搜索表单配置中的分类选项
          this.searchFormConfig.forEach(item => {
            if (item.name === 'category') {
              item.list = this.categoryOptions;
            }
          });
        } else if (response && response.data && Array.isArray(response.data.data)) {
          this.categoryOptions = response.data.data.map(cat => ({
            dicItemName: cat.name,
            dicItemCode: cat.name
          }));
          this.searchFormConfig.forEach(item => {
            if (item.name === 'category') {
              item.list = this.categoryOptions;
            }
          });
        } else {
          this.categoryOptions = [];
          this.searchFormConfig.forEach(item => {
            if (item.name === 'category') {
              item.list = [];
            }
          });
          this.$message.error('加载分类选项失败，返回数据异常');
          console.error('加载分类选项失败:', response);
        }
      } catch (error) {
        this.categoryOptions = [];
        this.searchFormConfig.forEach(item => {
          if (item.name === 'category') {
            item.list = [];
          }
        });
        this.$message.error('加载分类选项失败');
        console.error('加载分类选项失败:', error);
      }
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true;
      try {
        // 构建分页请求对象
        const pageRequest = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            name: this.searchForm.name || null,
            code: this.searchForm.code || null,
            category: this.searchForm.category || null,
            dataType: this.searchForm.dataType || null,
            status: this.searchForm.status !== '' ? this.searchForm.status : null
          }
        };

        console.log('分页请求参数:', pageRequest);

        const response = await getConstantList(pageRequest);
        if (response) {
          this.tableData =  response.list || [];
          this.pagination.total = response.total || 0;
        }
      } catch (error) {
        console.error('加载常数列表失败:', error);
        this.$message.error('加载常数列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch(searchData) {
      console.log('搜索参数:', searchData);
      // 更新搜索表单数据
      if (searchData && searchData.param) {
        this.searchForm = { ...searchData.param };
        this.searchParams.param = { ...searchData.param };
      }
      this.pagination.pageNum = 1;
      this.loadTableData();
    },

    // 重置搜索
    handleReset() {
      const resetForm = {
        name: '',
        code: '',
        category: '',
        dataType: '',
        status: ''
      };
      this.searchForm = { ...resetForm };
      this.searchParams.param = { ...resetForm };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadTableData();
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadTableData();
    },

    // 新增常数
    handleAdd() {
      this.$router.push({
        name: 'ConstantAdd',
        query: { from: 'list' }
      });
    },



    // 查看常数
    handleView(row) {
      this.$router.push({
        name: 'ConstantView',
        params: { id: row.id },
        query: { mode: 'view', from: 'list' }
      });
    },

    // 编辑常数
    handleEdit(row) {
      this.$router.push({
        name: 'ConstantEdit',
        params: { id: row.id },
        query: { mode: 'edit', from: 'list' }
      });
    },

    // 删除常数
    handleDelete(row) {
      this.currentRow = row;
      this.$refs.confirmDialog.show();
    },
    async confirmDelete() {
      if (this.currentRow) {
        try {
          const response = await deleteConstant(this.currentRow.id);
          if (response) {
            this.$message.success('删除成功');
            this.loadTableData();
            // 关闭确认弹窗
            this.$refs.confirmDialog.hide();
          } else {
            this.$message.error(response.message || '删除失败');
            // 删除失败也要关闭弹窗
            this.$refs.confirmDialog.hide();
          }
        } catch (error) {
          this.$message.error('删除失败');
          // 删除失败也要关闭弹窗
          this.$refs.confirmDialog.hide();
        }
      } else {
        // 没有选中行也要关闭弹窗
        this.$refs.confirmDialog.hide();
      }
    },



    // 状态改变
    async handleStatusChange(row) {
      try {
        const response = await updateConstantStatus(row.id, row.status);
        if (response) {
          this.$message.success(row.status === 1 ? '启用成功' : '禁用成功');
        } else {
          this.$message.error(response.message || '状态更新失败');
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1;
        }
      } catch (error) {
        console.error('状态更新失败:', error);
        this.$message.error('状态更新失败');
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1;
      }
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      const typeMap = {
        '数学常数': 'primary',
        '权重系数': 'success',
        '输入变量': 'warning',
        '业务常数': 'info'
      };
      return typeMap[category] || 'info';
    },

    // 获取数据类型标签类型
    getDataTypeTagType(dataType) {
      const typeMap = {
        'number': 'primary',
        'variable': 'success',
        'string': 'warning'
      };
      return typeMap[dataType] || 'info';
    },

    // 获取数据类型标签
    getDataTypeLabel(dataType) {
      const labelMap = {
        'number': '数值',
        'variable': '变量',
        'string': '字符串'
      };
      return labelMap[dataType] || dataType;
    },

    // 根据分类代码获取分类名称
    getCategoryName(categoryCode) {
      const category = this.categoryOptions.find(cat => cat.dicItemCode === categoryCode);
      return category ? category.dicItemName : categoryCode;
    },

    handleAction({ action, row }) {
      switch (action) {
        case 'view':
          this.handleView(row);
          break;
        case 'edit':
          this.handleEdit(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
        default:
          console.warn('未知的操作类型:', action);
      }
    }
  }
}
</script>
