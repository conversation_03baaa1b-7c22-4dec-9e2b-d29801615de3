<template>
  <div class="constant-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button 
            type="text" 
            @click="goBack" 
            class="back-btn"
          >
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <div class="page-title">
            <h1>{{ pageTitle }}</h1>
            <p class="page-subtitle">{{ pageSubtitle }}</p>
          </div>
        </div>
        <div class="header-actions" v-if="!isView">
          <el-button @click="goBack">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="submitLoading"
            class="save-btn"
          >
            <i class="el-icon-check"></i>
            保存
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-card class="form-card" shadow="never">
        <div class="card-header" slot="header">
          <span class="card-title">
            <i class="el-icon-setting"></i>
            基本信息
          </span>
        </div>
        
        <el-form
          ref="constantForm"
          :model="constantForm"
          :rules="isView ? {} : constantRules"
          label-width="120px"
          class="constant-form"
          :class="{ 'view-mode': isView }"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="常数名称" prop="name" :required="!isView">
                <el-input 
                  v-if="!isView" 
                  v-model="constantForm.name" 
                  placeholder="请输入常数名称"
                  maxlength="50"
                  show-word-limit
                />
                <div v-else class="view-field">
                  <span class="field-value">{{ constantForm.name || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="常数编码" prop="code" :required="!isView">
                <el-input 
                  v-if="!isView" 
                  v-model="constantForm.code" 
                  placeholder="请输入常数编码"
                  maxlength="50"
                  show-word-limit
                />
                <div v-else class="view-field">
                  <span class="field-value code-value">{{ constantForm.code || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="常数描述" prop="description" :required="!isView">
                <el-input 
                  v-if="!isView" 
                  v-model="constantForm.description" 
                  type="textarea" 
                  :rows="3" 
                  placeholder="请输入常数描述"
                  maxlength="200"
                  show-word-limit
                />
                <div v-else class="view-field">
                  <span class="field-value description-value">{{ constantForm.description || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="默认值" prop="defaultValue" :required="!isView">
                <el-input 
                  v-if="!isView" 
                  v-model="constantForm.defaultValue" 
                  placeholder="请输入默认值"
                >
                  <template slot="append" v-if="constantForm.unit">{{ constantForm.unit }}</template>
                </el-input>
                <div v-else class="view-field">
                  <span class="field-value value-display">
                    {{ constantForm.defaultValue || '-' }}
                    <span v-if="constantForm.unit" class="unit-text">{{ constantForm.unit }}</span>
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位" prop="unit">
                <el-input 
                  v-if="!isView" 
                  v-model="constantForm.unit" 
                  placeholder="请输入单位（可选）"
                  maxlength="20"
                />
                <div v-else class="view-field">
                  <span class="field-value">{{ constantForm.unit || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="常数分类" prop="category" :required="!isView">
                <el-select 
                  v-if="!isView" 
                  v-model="constantForm.category" 
                  placeholder="请选择常数分类"
                  class="full-width"
                >
                  <el-option 
                    v-for="cat in categoryOptions" 
                    :key="cat.dicItemCode" 
                    :label="cat.dicItemName" 
                    :value="cat.dicItemCode" 
                  />
                </el-select>
                <div v-else class="view-field">
                  <el-tag :type="getCategoryTagType(constantForm.category)" size="small">
                    {{ constantForm.category || '-' }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据类型" prop="dataType" :required="!isView">
                <el-select 
                  v-if="!isView" 
                  v-model="constantForm.dataType" 
                  placeholder="请选择数据类型"
                  class="full-width"
                >
                  <el-option label="数值" value="number" />
                  <el-option label="变量" value="variable" />
                  <el-option label="字符串" value="string" />
                </el-select>
                <div v-else class="view-field">
                  <el-tag :type="getDataTypeTagType(constantForm.dataType)" size="small">
                    {{ getDataTypeLabel(constantForm.dataType) || '-' }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-switch 
                  v-if="!isView" 
                  v-model="constantForm.status" 
                  :active-value="1" 
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                />
                <div v-else class="view-field">
                  <el-tag :type="constantForm.status === 1 ? 'success' : 'info'" size="small">
                    {{ constantForm.status === 1 ? '启用' : '禁用' }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isView && constantForm.usageCount !== undefined">
              <el-form-item label="使用次数">
                <div class="view-field">
                  <span class="field-value usage-count">{{ constantForm.usageCount || 0 }} 次</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 时间信息 (仅查看模式显示) -->
          <el-row :gutter="24" v-if="isView">
            <el-col :span="12">
              <el-form-item label="创建时间">
                <div class="view-field">
                  <span class="field-value time-value">
                    <i class="el-icon-time"></i>
                    {{ constantForm.createTime || '-' }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间">
                <div class="view-field">
                  <span class="field-value time-value">
                    <i class="el-icon-time"></i>
                    {{ constantForm.updateTime || '-' }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getConstantDetail, updateConstant, getConstantCategoryList } from '@/api/constantSetting';
export default {
  name: 'ConstantDetail',
  data() {
    return {
      constantForm: {
        name: '', 
        code: '', 
        description: '', 
        defaultValue: '', 
        unit: '', 
        category: '', 
        dataType: 'number', 
        status: 1,
        usageCount: 0,
        createTime: '',
        updateTime: ''
      },
      constantRules: {
        name: [
          { required: true, message: '请输入常数名称', trigger: 'blur' },
          { min: 1, max: 50, message: '常数名称长度在 1 到 50 个字符', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/, message: '常数名称只能包含中文、英文、数字、下划线和连字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入常数编码', trigger: 'blur' },
          { min: 1, max: 50, message: '常数编码长度在 1 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '常数编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入常数描述', trigger: 'blur' },
          { min: 1, max: 200, message: '常数描述长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        defaultValue: [
          { required: true, message: '请输入默认值', trigger: 'blur' },
          { validator: this.validateDefaultValue, trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择常数分类', trigger: 'change' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ]
      },
      categoryOptions: [],
      submitLoading: false,
      isView: false,
      loading: false
    }
  },
  computed: {
    id() {
      return this.$route.params.id;
    },
    pageTitle() {
      return this.isView ? '常数详情' : (this.id ? '编辑常数' : '新增常数');
    },
    pageSubtitle() {
      return this.isView ? '查看常数的详细信息' : '配置系统常数参数';
    }
  },
  async created() {
    this.isView = this.$route.name === 'ConstantDetail';
    await this.init();
  },
  methods: {
    async init() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadCategoryOptions(),
          this.id ? this.loadDetail() : Promise.resolve()
        ]);
      } catch (error) {
        console.error('初始化失败:', error);
        this.$message.error('页面初始化失败');
      } finally {
        this.loading = false;
      }
    },
    
    async loadDetail() {
      try {
        const res = await getConstantDetail(this.id);
        if (res && res.code === 200) {
          this.constantForm = { 
            ...this.constantForm,
            ...res.data 
          };
        } else {
          this.$message.error(res?.message || '获取常数详情失败');
          this.goBack();
        }
      } catch (error) {
        console.error('获取常数详情失败:', error);
        this.$message.error('获取常数详情失败');
        this.goBack();
      }
    },
    
    async loadCategoryOptions() {
      try {
        const response = await getConstantCategoryList();
        if (response && Array.isArray(response.data)) {
          this.categoryOptions = response.data.map(cat => ({
            dicItemName: cat.name,
            dicItemCode: cat.name
          }));
        } else {
          this.categoryOptions = [];
          console.warn('分类数据格式异常:', response);
        }
      } catch (error) {
        console.error('加载分类选项失败:', error);
        this.categoryOptions = [];
        this.$message.warning('加载分类选项失败，请刷新重试');
      }
    },
    
    // 自定义验证默认值
    validateDefaultValue(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入默认值'));
        return;
      }
      
      const dataType = this.constantForm.dataType;
      if (dataType === 'number') {
        if (!/^-?\d+(\.\d+)?$/.test(value)) {
          callback(new Error('数值类型的默认值必须是有效的数字'));
          return;
        }
      } else if (dataType === 'variable') {
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
          callback(new Error('变量类型的默认值必须是有效的变量名'));
          return;
        }
      }
      
      callback();
    },
    
    getDataTypeLabel(dataType) {
      const labelMap = { 
        number: '数值', 
        variable: '变量', 
        string: '字符串' 
      };
      return labelMap[dataType] || dataType;
    },
    
    getCategoryTagType(category) {
      const typeMap = {
        '数学常数': 'primary',
        '权重系数': 'success',
        '输入变量': 'warning',
        '业务常数': 'info'
      };
      return typeMap[category] || 'info';
    },
    
    getDataTypeTagType(dataType) {
      const typeMap = {
        'number': 'primary',
        'variable': 'success',
        'string': 'warning'
      };
      return typeMap[dataType] || 'info';
    },
    
    goBack() {
      // 优先返回到常数设置列表页
      if (this.$route.query.from === 'list' || !window.history.length || window.history.length <= 1) {
        this.$router.push({ name: 'constantSetting' });
      } else {
        this.$router.back();
      }
    },
    
    async handleSubmit() {
      if (this.isView) return;
      
      this.$refs.constantForm.validate(async (valid) => {
        if (!valid) {
          this.$message.warning('请检查表单填写是否正确');
          return;
        }
        
        this.submitLoading = true;
        try {
          const res = await updateConstant(this.id, this.constantForm);
          if (res && res.code === 200) {
            this.$message.success('保存成功');
            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
              this.goBack();
            }, 1000);
          } else {
            this.$message.error(res?.message || '保存失败');
          }
        } catch (error) {
          console.error('保存常数失败:', error);
          this.$message.error('保存失败，请重试');
        } finally {
          this.submitLoading = false;
        }
      });
    }
  }
}
</script>

<style scoped>
.constant-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 页面头部 */
.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  margin-right: 16px;
  color: #606266;
  font-size: 14px;
}

.back-btn:hover {
  color: #409eff;
}

.page-title h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 13px;
  color: #909399;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.save-btn {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.save-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
}

/* 表单容器 */
.form-container {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0 24px;
}

.form-card {
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
  margin: -20px -20px 20px -20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409eff;
}

/* 表单样式 */
.constant-form {
  padding: 20px;
}

.constant-form .el-form-item {
  margin-bottom: 24px;
}

.constant-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.full-width {
  width: 100%;
}

/* 查看模式样式 */
.view-mode .el-form-item__label {
  color: #909399;
}

.view-field {
  min-height: 32px;
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.field-value {
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
}

.code-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.description-value {
  line-height: 1.6;
  white-space: pre-wrap;
}

.value-display {
  font-weight: 500;
}

.unit-text {
  color: #909399;
  margin-left: 4px;
  font-size: 12px;
}

.usage-count {
  color: #67c23a;
  font-weight: 500;
}

.time-value {
  color: #909399;
  font-size: 13px;
}

.time-value i {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    margin: 16px;
    padding: 0;
  }
  
  .page-header {
    padding: 12px 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .constant-form {
    padding: 16px;
  }
  
  .el-col {
    margin-bottom: 0;
  }
}
</style>