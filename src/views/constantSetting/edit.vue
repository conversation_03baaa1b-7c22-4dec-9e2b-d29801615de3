<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import {
  getConstantDetail,
  updateConstant,
  createConstant
} from '@/api/constantSetting'
import {getDicItemList} from "@/config/tool";

export default {
  name: 'ConstantSettingEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: '',
        name: '',
        code: '',
        constantValue: '',
        defaultValue: '',
        dataType: 'string',
        category: '',
        description: '',
        status: 1,
        usageCount: 0,
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              {
                prop: 'name',
                label: '常量名称',
                type: 'input',
                placeholder: '请输入常量名称',
                maxlength: 50,
                showWordLimit: true
              },
              {
                prop: 'code',
                label: '常量键名',
                type: 'input',
                placeholder: '请输入常量键名',
                maxlength: 100,
                showWordLimit: true
              },
              {
                prop: 'category',
                label: '常量分类',
                type: 'select',
                placeholder: '请选择常量分类',
                options: [],
                filterable: true
              }
            ],
            [
              {
                prop: 'dataType',
                label: '数据类型',
                type: 'select',
                placeholder: '请选择数据类型',
                options: [
                  { label: '字符串', value: 'string' },
                  { label: '数字', value: 'number' },
                  { label: '布尔值', value: 'boolean' },
                  { label: '日期', value: 'date' },
                  { label: 'JSON', value: 'json' }
                ]
              },
              {
                prop: 'constantValue',
                label: '常量值',
                type: 'input',
                placeholder: '请输入常量值',
                rows: 2,
                maxlength: 500,
                showWordLimit: true
              },
              {
                prop: 'defaultValue',
                label: '默认值',
                type: 'input',
                placeholder: '请输入默认值',
                rows: 2,
                maxlength: 500,
                showWordLimit: true
              }
            ],
            [
              {
                prop: 'status',
                label: '状态',
                type: 'radio',
                options: [
                  { label: '启用', value: 1 },
                  { label: '禁用', value: 0 }
                ]
              },
              {
                prop: 'usageCount',
                label: '使用次数',
                type: 'input',
                disabled: true,
                placeholder: '系统自动统计'
              }
            ],
            [
              {
                prop: 'description',
                label: '描述信息',
                type: 'textarea',
                placeholder: '请输入描述信息',
                maxlength: 200,
                rows: 3,
                showWordLimit: true,
                span: 24
              }
            ]
          ]
        },
        {
          title: '系统信息',
          icon: 'el-icon-time',
          fields: [
            [
              {
                prop: 'createTime',
                label: '创建时间',
                type: 'input',
                disabled: true,
                placeholder: '系统自动生成'
              },
              {
                prop: 'updateTime',
                label: '更新时间',
                type: 'input',
                disabled: true,
                placeholder: '系统自动生成'
              },
              {
                prop: 'createUser',
                label: '创建人',
                type: 'input',
                disabled: true,
                placeholder: '系统自动记录'
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入常量名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入常量键名', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
          { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '键名只能包含大写字母、数字和下划线，且以大写字母或下划线开头', trigger: 'blur' }
        ],
        constantValue: [
          { required: true, message: '请输入常量值', trigger: 'blur' },
          { validator: this.validateConstantValue, trigger: 'blur' }
        ],
        defaultValue: [
          { validator: this.validateDefaultValue, trigger: 'blur' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        category: [
          { required: true, message: '请选择常量分类', trigger: 'change' }
        ]
      },
      categoryOptions: []
    }
  },

  computed: {
    // 页面标题
    pageTitle() {
      return this.isView ? '查看常量设置' : (this.isEdit ? '编辑常量设置' : '新增常量设置')
    },

    // 页面图标
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },

    // 面包屑导航
    breadcrumbItems() {
      return [
        {
          text: '常量设置',
          icon: 'el-icon-setting',
          to: { name: 'constantSetting' }
        },
        {
          text: this.pageTitle,
          icon: this.pageIcon
        }
      ]
    }
  },

  created() {
    this.init()
  },

  methods: {
    async init() {
      const id = this.$route.params.id
      const mode = this.$route.query.mode

      if (id) {
        this.isEdit = mode !== 'view'
        this.isView = mode === 'view'
      }

      // 并行加载分类选项和常量详情
      const promises = [this.loadCategoryOptions()]
      if (id) {
        promises.push(this.loadConstantDetail(id))
      }

      try {
        await Promise.all(promises)
      } catch (error) {
        console.error('初始化失败:', error)
      }
    },

    async loadCategoryOptions() {
      console.log('加载分类选项');
      try {
        const response = await getDicItemList("elsm.constant.cateory");
        console.log('字典数据:', response);

        // 健壮性处理：确保 response 是数组
        if (response && Array.isArray(response)) {
          // 基于字典数据结构
          this.categoryOptions = response.map(cat => ({
            dicItemName: cat.dicItemName,
            dicItemCode: parseInt(cat.dicItemCode)
          }));

          // 更新表单配置中的分类选项
          const categoryOptions = this.categoryOptions.map(item => ({
            label: item.dicItemName,
            value: item.dicItemCode
          }));

          // 使用 $set 确保响应式更新
          this.$set(this.formGroups[0].fields[0][2], 'options', categoryOptions);

          console.log('分类选项已更新:', categoryOptions);
          console.log('表单配置中的分类选项:', this.formGroups[0].fields[0][2].options);
        } else {
          this.categoryOptions = [];
          this.$set(this.formGroups[0].fields[0][2], 'options', []);
          this.$message.error('加载分类选项失败，返回数据异常');
          console.error('加载分类选项失败:', response);
        }
      } catch (error) {
        this.categoryOptions = [];
        this.$set(this.formGroups[0].fields[0][2], 'options', []);
        console.error('加载分类选项失败:', error);
        this.$message.error('加载分类选项失败');
      }
    },

    async loadConstantDetail(id) {
      this.loading = true
      try {
        const response = await getConstantDetail(id)
        if (response) {
          this.form = {
            ...this.form,
            ...response
          }
        } else {
          this.$message.error(response.message || '加载常量详情失败')
        }
      } catch (error) {
        console.error('加载常量详情失败:', error)
        this.$message.error('加载常量详情失败')
      } finally {
        this.loading = false
      }
    },

    // 验证常量值
    validateConstantValue(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入常量值'))
        return
      }

      const dataType = this.form.dataType

      try {
        switch (dataType) {
          case 'number':
            if (isNaN(Number(value))) {
              callback(new Error('常量值必须是有效的数字'))
              return
            }
            break
          case 'boolean':
            if (!['true', 'false', '1', '0'].includes(value.toLowerCase())) {
              callback(new Error('常量值必须是 true/false 或 1/0'))
              return
            }
            break
          case 'json':
            try {
              JSON.parse(value)
            } catch {
              callback(new Error('常量值必须是有效的JSON格式'))
              return
            }
            break
          case 'date':
            if (isNaN(Date.parse(value))) {
              callback(new Error('常量值必须是有效的日期格式'))
              return
            }
            break
        }
        callback()
      } catch (error) {
        callback(new Error('常量值格式验证失败'))
      }
    },

    // 验证默认值
    validateDefaultValue(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      const dataType = this.form.dataType

      try {
        switch (dataType) {
          case 'number':
            if (isNaN(Number(value))) {
              callback(new Error('默认值必须是有效的数字'))
              return
            }
            break
          case 'boolean':
            if (!['true', 'false', '1', '0'].includes(value.toLowerCase())) {
              callback(new Error('默认值必须是 true/false 或 1/0'))
              return
            }
            break
          case 'json':
            try {
              JSON.parse(value)
            } catch {
              callback(new Error('默认值必须是有效的JSON格式'))
              return
            }
            break
          case 'date':
            if (isNaN(Date.parse(value))) {
              callback(new Error('默认值必须是有效的日期格式'))
              return
            }
            break
        }
        callback()
      } catch (error) {
        callback(new Error('默认值格式验证失败'))
      }
    },

    async handleSave() {
      if (this.isView) {
        this.handleBack()
        return
      }

      try {
        await this.$refs.universalForm.validate()
        this.loading = true

        const saveData = { ...this.form }
        let response

        if (this.isEdit) {
          response = await updateConstant(saveData.id, saveData)
        } else {
          response = await createConstant(saveData)
        }

        if (response) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.handleBack()
          }, 1000)
        } else {
          this.$message.error(response.message || (this.isEdit ? '更新失败' : '创建失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败，请检查表单数据')
      } finally {
        this.loading = false
      }
    },

    handleBack() {
      // 优先返回到常量设置列表页
      const from = this.$route.query.from
      if (from === 'list') {
        this.$router.push({ name: 'constantSetting' })
      } else {
        this.$router.go(-1)
      }
    },

    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style>
