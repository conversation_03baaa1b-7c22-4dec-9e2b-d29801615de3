<template>
  <div class="questionnaire-management-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="问卷管理"
      subtitle="管理所有问卷，支持新建、编辑、预览和删除操作"
      title-icon="el-icon-document"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="240"
      :search-label-width="'120px'"
      add-button-text="新建问卷"
      empty-title="暂无问卷数据"
      empty-description="点击上方新建问卷按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 适用企业类型列插槽 -->
      <template #enterpriseTypes="{ row }">
        <span v-if="row.enterpriseTypes && row.enterpriseTypes.length">
          <el-tag
            v-for="type in row.enterpriseTypes"
            :key="type"
            type="info"
            size="small"
            style="margin: 2px;"
          >
            {{ type }}类企业
          </el-tag>
        </span>
        <span v-else style="color:#bbb;">未配置</span>
      </template>
      
      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === 'active' ? 'success' : row.status === 'draft' ? 'warning' : 'info'"
          size="small"
        >
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.updateTime }}
        </div>
      </template>
    </UniversalTable>
    
    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该问卷？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getQuestionnaireList, deleteQuestionnaire, getEnterpriseTypeOptions } from '@/api/questionnaire/management'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'QuestionnaireManagement',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        name: '',
        description: '',
        enterpriseType: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '问卷名称',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 300,
          align: 'center'
        },
        {
          prop: 'enterpriseTypes',
          label: '适用企业类型',
          minWidth: 180,
          align: 'center'
        },
        {
          prop: 'questionCount',
          label: '题目数',
          width: 100,
          align: 'center'
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          align: 'center'
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'preview',
          label: '预览',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '问卷名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入问卷名称'
        },
        {
          label: '描述',
          name: 'description',
          type: 'input',
          placeholder: '请输入描述'
        },
        {
          label: '企业类型',
          name: 'enterpriseType',
          type: 'select',
          placeholder: '请选择企业类型',
          options: [
            { value: 'A', label: 'A类企业' },
            { value: 'B', label: 'B类企业' },
            { value: 'C', label: 'C类企业' },
            { value: 'D', label: 'D类企业' },
            { value: 'E', label: 'E类企业' }
          ]
        }
      ],
      deleteRowData: null
    }
  },
  created() {
    this.loadQuestionnaireList()
  },
  methods: {
    // 加载问卷列表
    async loadQuestionnaireList() {
      try {
        this.loading = true
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }
        
        const response = await getQuestionnaireList(params)
        if (response.code === 200) {
          this.tableData = response.data.list
          this.pagination.total = response.data.total
        }
      } catch (error) {
        console.error('加载问卷列表失败:', error)
        this.$message.error('加载问卷列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch(searchParams) {
      this.searchForm = { ...searchParams.param }
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        description: '',
        enterpriseType: ''
      }
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },
    
    // 新增问卷
    handleAdd() {
      this.$router.push({ name: 'questionnaireEdit' })
    },
    
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadQuestionnaireList()
    },
    
    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadQuestionnaireList()
    },
    
    // 操作按钮点击
    handleAction(eventData) {
      const { action, row } = eventData
      switch (action) {
        case 'edit':
          this.handleEdit(row)
          break
        case 'preview':
          this.handlePreview(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },
    
    // 编辑问卷
    handleEdit(row) {
      this.$router.push({ 
        name: 'questionnaireEdit', 
        params: { 
          id: row.id
        } 
      })
    },
    
    // 预览问卷
    handlePreview(row) {
      this.$router.push({ 
        name: 'questionnaireFill', 
        params: { 
          id: row.id
        } 
      })
    },
    
    // 删除问卷
    handleDelete(row) {
      this.deleteRowData = row
      this.$refs.confirmDialog.show()
    },
    
    // 确认删除
    async confirmDelete() {
      try {
        const response = await deleteQuestionnaire(this.deleteRowData.id)
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadQuestionnaireList()
        }
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '启用',
        'inactive': '禁用',
        'draft': '草稿'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="less" scoped>
.questionnaire-management-container {
  min-height: 100vh;
  background: #fbf6ee;
  
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    
    i {
      color: #D7A256;
      font-size: 14px;
    }
  }
  
  /deep/ .universal-table-container {
    .action-buttons {
      .action-btn {
        margin: 0 2px;
        
        &.edit-btn {
          color: #409EFF;
          border-color: #409EFF;
          
          &:hover {
            background: #409EFF;
            color: white;
          }
        }
        
        &.view-btn {
          color: #67C23A;
          border-color: #67C23A;
          
          &:hover {
            background: #67C23A;
            color: white;
          }
        }
        
        &.delete-btn {
          color: #F56C6C;
          border-color: #F56C6C;
          
          &:hover {
            background: #F56C6C;
            color: white;
          }
        }
      }
    }
  }
}
</style>