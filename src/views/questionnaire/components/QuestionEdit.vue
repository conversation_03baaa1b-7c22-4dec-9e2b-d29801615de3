<template>
  <div>
    <el-form :model="localQuestion" label-width="120px">
      <el-form-item label="题型">
        <el-select v-model="localQuestion.type">
          <el-option label="单选" value="single" />
          <el-option label="多选" value="multi" />
          <el-option label="简答" value="text" />
        </el-select>
      </el-form-item>
      <el-form-item label="题干">
        <el-input v-model="localQuestion.title" placeholder="请输入题目内容" />
      </el-form-item>
      <el-form-item label="关联评分项">
        <el-select v-model="localQuestion.scoreItem" placeholder="请选择关联的风险评分项" style="width: 100%">
          <el-option label="不关联评分项" value=""></el-option>
          <el-option 
            v-for="item in scoreItemOptions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选项" v-if="localQuestion.type !== 'text'">
        <el-table :data="localQuestion.options" style="width: 100%" class="options-table">
          <el-table-column label="内容" min-width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.text" placeholder="请输入选项内容" />
            </template>
          </el-table-column>
          <el-table-column label="分数" width="100" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.score" type="number" min="0" class="score-input" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="removeOption(scope.$index)" class="delete-option-btn">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="primary" icon="el-icon-plus" @click="addOption" size="mini" style="margin-top:8px;">添加选项</el-button>
      </el-form-item>
    </el-form>
    <div style="text-align:right;margin-top:16px;">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionEdit',
  props: {
    question: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localQuestion: JSON.parse(JSON.stringify(this.question)),
      scoreItemOptions: [
        { value: '财务风险评分', label: '财务风险评分' },
        { value: '运营风险评分', label: '运营风险评分' },
        { value: '市场风险评分', label: '市场风险评分' },
        { value: '合规风险评分', label: '合规风险评分' },
        { value: '技术风险评分', label: '技术风险评分' },
        { value: '人员风险评分', label: '人员风险评分' }
      ]
    }
  },
  watch: {
    question: {
      handler(val) {
        this.localQuestion = JSON.parse(JSON.stringify(val))
      },
      deep: true
    }
  },
  methods: {
    addOption() {
      this.localQuestion.options.push({ id: Date.now(), text: '', score: 0 })
    },
    removeOption(idx) {
      this.localQuestion.options.splice(idx, 1)
    },
    handleSave() {
      this.$emit('save', JSON.parse(JSON.stringify(this.localQuestion)))
    }
  }
}
</script>

<style scoped>
/deep/ .el-form-item__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/deep/ .el-table__body-wrapper td {
  padding: 8px 4px;
  font-size: 14px;
}

/deep/ .el-input-number {
  width: 100px !important;
}

/* 去除 input[type=number] 默认小圆点和 spin button */
/deep/ .el-input-number input[type="number"]::-webkit-inner-spin-button,
/deep/ .el-input-number input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/deep/ .el-input-number input[type="number"] {
  -moz-appearance: textfield;
}

.options-table {
  /deep/ .el-table__header-wrapper {
    .el-table__header {
      th {
        background: #fbf6ee;
        font-weight: 600;
        font-size: 14px;
        color: #2c3e50;
        border-bottom: 1px solid #f7ecdd;
      }
    }
  }
  
  /deep/ .el-table__body-wrapper {
    .el-table__row {
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(215, 162, 86, 0.05) !important;
      }
      
      td {
        border-bottom: 1px solid #f7ecdd;
        padding: 12px 8px;
      }
    }
  }
}

.score-input {
  /deep/ .el-input__inner {
    text-align: center;
  }
}

.delete-option-btn {
  color: #f56c6c;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(245, 108, 108, 0.1);
    transform: translateY(-1px);
  }
}
</style> 