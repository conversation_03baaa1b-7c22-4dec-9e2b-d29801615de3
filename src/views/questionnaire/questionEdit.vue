<template>
  <EditPageContainer
    :title="isEdit ? '编辑题目' : '新增题目'"
    icon="el-icon-edit-outline"
    :breadcrumb-items="breadcrumbItems"
    :loading="saving"
    @save="handleSave"
    @back="handleBack"
  >
    <div class="question-edit-container">
      <!-- 题目基本信息 -->
      <div class="form-section">
        <UniversalForm
          ref="questionForm"
          :form-data="form"
          :form-rules="formRules"
          :form-groups="formGroups"
          label-width="120px"
        />
      </div>

      <!-- 选项配置 -->
      <div v-if="form.type !== 'text'" class="options-section">
        <UniversalTable
          title="选项配置"
          subtitle="设置题目选项和对应分值"
          title-icon="el-icon-menu"
          :table-data="form.options"
          :loading="false"
          :columns="optionColumns"
          :actions="optionActions"
          :show-search-form="false"
          :show-pagination="false"
          add-button-text="添加选项"
          empty-title="暂无选项"
          empty-description="点击添加选项按钮开始创建"
          @add="addOption"
          @action-click="handleOptionAction"
        >
          <!-- 选项内容列插槽 -->
          <template #text="{ row, index }">
            <el-input v-model="row.text" placeholder="请输入选项内容" />
          </template>
          
          <!-- 分值列插槽 -->
          <template #score="{ row, index }">
            <el-input-number v-model="row.score" :min="0" :max="100" />
          </template>
        </UniversalTable>
      </div>
    </div>
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import UniversalTable from '@/components/layouts/UniversalTable.vue'

export default {
  name: 'QuestionEdit',
  components: {
    EditPageContainer,
    UniversalForm,
    UniversalTable
  },
  data() {
    return {
      saving: false,
      form: {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        options: []
      },
      scoreItemOptions: [
        { value: '财务风险评分', label: '财务风险评分' },
        { value: '运营风险评分', label: '运营风险评分' },
        { value: '市场风险评分', label: '市场风险评分' },
        { value: '合规风险评分', label: '合规风险评分' },
        { value: '技术风险评分', label: '技术风险评分' },
        { value: '人员风险评分', label: '人员风险评分' }
      ]
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { text: '问卷管理', to: '/questionnaire' },
        { text: '编辑问卷', to: `/questionnaire/edit/${this.$route.params.id}` },
        { text: this.isEdit ? '编辑题目' : '新增题目' }
      ]
    },
    isEdit() {
      return !!this.$route.params.questionId
    },
    formRules() {
      return {
        type: [{ required: true, message: '请选择题型', trigger: 'change' }],
        title: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
      }
    },
    formGroups() {
      return [
        {
          title: '题目信息',
          fields: [[
            {
              prop: 'type',
              label: '题型',
              type: 'select',
              options: [
                { value: 'single', label: '单选题' },
                { value: 'multi', label: '多选题' },
                { value: 'text', label: '简答题' }
              ],
              required: true
            },
            {
              prop: 'scoreItem',
              label: '关联评分项',
              type: 'select',
              options: [{ label: '不关联评分项', value: '' }, ...this.scoreItemOptions],
              clearable: true
            },
            {
              prop: 'title',
              label: '题目内容',
              type: 'textarea',
              placeholder: '请输入题目内容',
              required: true
            }
          ]]
        }
      ]
    },
    optionColumns() {
      return [
        { prop: 'text', label: '选项内容', minWidth: 200, slot: true },
        { prop: 'score', label: '分值', width: 120, slot: true }
      ]
    },
    optionActions() {
      return [
        { key: 'delete', label: '删除', class: 'delete-btn', icon: 'el-icon-delete' }
      ]
    }
  },
  created() {
    this.initQuestion()
  },
  methods: {
    initQuestion() {
      const { questionId } = this.$route.params
      const questionData = this.$route.params.questionData
      
      if (questionData) {
        // 从路由参数获取题目数据
        this.form = JSON.parse(JSON.stringify(questionData))
      } else if (questionId) {
        // 编辑模式，从存储或API获取题目数据
        // 这里可以调用API获取题目详情
        this.loadQuestion(questionId)
      } else {
        // 新增模式，初始化空题目
        this.form = {
          id: Date.now(),
          type: 'single',
          title: '',
          scoreItem: '',
          options: []
        }
      }
    },
    loadQuestion(questionId) {
      // 模拟从API加载题目数据
      // 实际项目中这里应该调用API
      console.log('加载题目:', questionId)
    },
    handleSave() {
      this.$refs.questionForm.validate((valid) => {
        if (!valid) {
          this.$message.warning('请完善题目信息')
          return
        }
        
        if (this.form.type !== 'text' && this.form.options.length === 0) {
          this.$message.warning('请至少添加一个选项')
          return
        }
        
        this.saving = true
        
        // 模拟保存
        setTimeout(() => {
          this.saving = false
          this.$message.success('题目保存成功')
          
          // 返回上一页并传递保存的数据
          this.$router.push({
            path: `/questionnaire/edit/${this.$route.params.id}`,
            params: {
              savedQuestion: this.form,
              isEdit: this.isEdit
            }
          })
        }, 1000)
      })
    },
    handleBack() {
      this.$router.back()
    },
    addOption() {
      this.form.options.push({
        id: Date.now(),
        text: '',
        score: 0
      })
    },
    handleOptionAction(eventData) {
      const { action, row, index } = eventData
      if (action === 'delete') {
        this.removeOption(index)
      }
    },
    removeOption(index) {
      this.form.options.splice(index, 1)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.question-edit-container {
  .form-section {
    margin-bottom: 24px;
  }
  
  .options-section {
    margin-top: 24px;
  }
}
</style>