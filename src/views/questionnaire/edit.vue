<template>
  <EditPageContainer
    title="编辑问卷"
    icon="el-icon-edit"
    :breadcrumb-items="breadcrumbItems"
    :loading="saving"
    @save="handleSave"
    @back="handleBack"
  >
    <!-- 基本信息表单 -->
    <div class="form-content">
      <UniversalForm
        ref="basicForm"
        :form-data="form"
        :form-rules="formRules"
        :form-groups="basicFormGroups"
        label-width="120px"
      />
    
      <!-- 题目列表 -->
      <div class="questions-section">
        <UniversalForm
          ref="questionsForm"
          :form-data="form"
          :form-groups="questionsFormGroups"
          label-width="120px"
        />
      </div>
    

    </div>
    
    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该题目？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDeleteQuestion"
    />
  </EditPageContainer>
</template>

<script>
import { questionnaireMock } from '@/mock'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'QuestionnaireEdit',
  components: {
    EditPageContainer,
    UniversalForm,
    ConfirmDialog
  },
  data() {
    return {
      saving: false,
      form: {
        title: '员工满意度调查',
        description: '请如实填写以下问卷',
        questions: [
          { id: 1, type: 'single', title: '你对公司环境满意吗？', scoreItem: '人员风险评分', options: [
            { id: 1, text: '非常满意', score: 5 },
            { id: 2, text: '一般', score: 3 },
            { id: 3, text: '不满意', score: 0 }
          ] },
          { id: 2, type: 'multi', title: '你喜欢哪些福利？', scoreItem: '运营风险评分', options: [
            { id: 1, text: '五险一金', score: 2 },
            { id: 2, text: '带薪年假', score: 2 },
            { id: 3, text: '免费午餐', score: 1 }
          ] },
          { id: 3, type: 'text', title: '你对公司还有什么建议？', scoreItem: '', options: [] }
        ]
      },

      deleteQuestionIndex: -1,
      scoreItemOptions: [
        { value: '财务风险评分', label: '财务风险评分' },
        { value: '运营风险评分', label: '运营风险评分' },
        { value: '市场风险评分', label: '市场风险评分' },
        { value: '合规风险评分', label: '合规风险评分' },
        { value: '技术风险评分', label: '技术风险评分' },
        { value: '人员风险评分', label: '人员风险评分' }
      ]
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { text: '问卷管理', to: '/questionnaire' },
        { text: '编辑问卷' }
      ]
    },
    formRules() {
      return {
        title: [{ required: true, message: '请输入问卷标题', trigger: 'blur' }],
        description: [{ required: true, message: '请输入问卷描述', trigger: 'blur' }]
      }
    },
    basicFormGroups() {
      return [
        {
          title: '基本信息',
          fields: [[
            {
              prop: 'title',
              label: '问卷标题',
              type: 'input',
              placeholder: '请输入问卷标题',
              required: true
            },
            {
              prop: 'description',
              label: '问卷描述',
              type: 'textarea',
              placeholder: '请输入问卷描述',
              required: true
            }
          ]]
        }
      ]
    },

    questionsFormGroups() {
      return [
        {
          title: '题目列表',
          icon: 'el-icon-edit-outline',
          fields: [[
            {
              prop: 'questions',
              label: '题目列表',
              type: 'list',
              addText: '添加题目',
              emptyText: '暂无题目，点击添加题目按钮开始创建',
              columns: [
                { 
                  prop: 'title', 
                  label: '题目内容', 
                  minWidth: 300,
                  type: 'input',
                  placeholder: '请输入题目内容'
                },
                { 
                  prop: 'type', 
                  label: '题型', 
                  width: 120,
                  type: 'select',
                  placeholder: '选择题型',
                  options: [
                    { value: 'single', label: '单选' },
                    { value: 'multi', label: '多选' },
                    { value: 'text', label: '简答' }
                  ]
                },
                { 
                  prop: 'maxScore', 
                  label: '最高分', 
                  width: 100,
                  type: 'input',
                  placeholder: '最高分'
                },
                { 
                  prop: 'scoreItem', 
                  label: '关联评分项', 
                  minWidth: 150,
                  type: 'select',
                  placeholder: '选择评分项',
                  options: this.scoreItemOptions
                }
              ],
              actions: [
                 {
                   label: '删除',
                   icon: 'el-icon-delete',
                   type: 'danger',
                   onClick: (row, index) => this.removeQuestion(index)
                 }
               ],
              defaultRow: () => ({
                 id: Date.now(),
                 type: 'single',
                 title: '',
                 scoreItem: '',
                 options: [],
                 maxScore: 0
               })
            }
          ]]
        }
      ]
    },

  },
  mounted() {
    // 重写UniversalForm的addListRow方法，让添加题目跳转到编辑页面
    if (this.$refs.questionsForm) {
      const originalAddListRow = this.$refs.questionsForm.addListRow
      this.$refs.questionsForm.addListRow = (field) => {
        if (field.prop === 'questions') {
          this.addQuestion()
        } else {
          originalAddListRow.call(this.$refs.questionsForm, field)
        }
      }
    }
  },
  created() {
    // 处理从题目编辑页面返回的数据
    this.handleReturnFromQuestionEdit()
    
    // 根据路由参数加载问卷
    const { id, enterpriseType, questionnaireData } = this.$route.params
    
    if (questionnaireData) {
      // 使用传递的问卷数据
      this.form = {
        title: questionnaireData.title,
        description: questionnaireData.description,
        questions: questionnaireData.questions.map((q, idx) => ({
          id: idx + 1,
          type: 'single',
          title: q.question,
          scoreItem: q.scoreItem,
          options: q.options.map((opt, oidx) => ({
            id: oidx + 1,
            label: opt.label,
            text: opt.text,
            score: opt.score
          }))
        }))
      }
    } else if (id === 1 || id === '1') {
      // 兼容旧的数据结构
      const questionnaires = questionnaireMock.questionnaires
      const cdeQuestionnaire = questionnaires.CDE
      
      this.form = {
        title: cdeQuestionnaire.title,
        description: cdeQuestionnaire.description,
        questions: cdeQuestionnaire.questions.map((q, idx) => ({
          id: idx + 1,
          type: 'single',
          title: q.question,
          scoreItem: q.scoreItem,
          options: q.options.map((opt, oidx) => ({
            id: oidx + 1,
            label: opt.label,
            text: opt.text,
            score: opt.score
          }))
        }))
      }
    }
  },
  methods: {
    handleSave() {
      this.saving = true
      setTimeout(() => {
        this.saving = false
        this.$message.success('问卷保存成功（mock）')
      }, 1000)
    },
    handleBack() {
      this.$router.back()
    },
    handleReturnFromQuestionEdit() {
      const { savedQuestion, isEdit, questionIndex } = this.$route.params
      
      if (savedQuestion) {
        if (isEdit && questionIndex !== undefined) {
          // 编辑模式：更新现有题目
          this.$set(this.form.questions, questionIndex, savedQuestion)
          this.$message.success('题目更新成功')
        } else {
          // 新增模式：添加新题目
          this.form.questions.push(savedQuestion)
          this.$message.success('题目添加成功')
        }
        
        // 清除路由参数中的题目数据
        this.$router.replace({
          path: this.$route.path,
          params: {
            ...this.$route.params,
            savedQuestion: undefined,
            isEdit: undefined,
            questionIndex: undefined
          }
        })
      }
    },
    addQuestion() {
      // 跳转到题目编辑页面（新增模式）
      this.$router.push({
        name: 'questionEdit',
        params: {
          id: this.$route.params.id || 'new'
        }
      })
    },

    removeQuestion(idx) {
      this.deleteQuestionIndex = idx
      this.$refs.confirmDialog.show()
    },
    confirmDeleteQuestion() {
      if (this.deleteQuestionIndex !== -1) {
        this.form.questions.splice(this.deleteQuestionIndex, 1)
        this.$message.success('删除成功')
        this.deleteQuestionIndex = -1
        this.$refs.confirmDialog.hide()
      }
    },

  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.form-content {
  margin-bottom: 24px;
}

.questions-section {
  margin-bottom: 24px;
}
</style>