<!--  -->
<template>
		<el-container class="wh100">	
			<el-main class="main-container page-component__scroll" @scroll.native="handleScroll">
				<div class="main-bg">
					<keep-alive :include="cacheArr" :exclude="notCacheArr">
						<router-view ref="childTop" :scrollTop="scrollTop" @viewScroll="viewScroll"></router-view>
					</keep-alive>
					<el-backtop target=".page-component__scroll" ref="backtop"></el-backtop>
				</div>
			</el-main>
		</el-container>
</template>

<script>
export default {
  name: "",
  components: { },
 
  props: {},
  data() {    
    return {
      scrollTop: 0
    };
  },
  //监听属性 类似于data概念 ,true是收起，false 是展开
  computed: {
    isCollapse() {
      return this.$store.state["layoutStore"].isCollapse;
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    },
    cacheArrScrollTop(){
      return this.$store.state["layoutStore"].cacheArrScrollTop;
    },
    sideClass() {
      if (this.isCollapse) {
        return "sideClass-hide";
      } else {
        return "sideClass-show";
      }
    },
    sideWidth() {
      if (this.isCollapse) {
        return "69px";
      } else {
        return "210px";
      }
    }
  },
  //监控data中的数据变化
  watch: {
    $route: {
      handler(to, from) {
        if (!to.meta.notCache) {
          this.$store.commit("layoutStore/setCacheArr", {
            status: "add",
            routeObj: this.createRouteObj(to)
          });
          //进入缓存的页面时恢复滚动条的位置
          let scrollTop = this.cacheArrScrollTop[to.name] 
          if(typeof scrollTop == 'number'){
            this.$nextTick(()=>{
              let mainDocument = document.getElementsByClassName("main-container page-component__scroll")[0];
              mainDocument.scrollTop=scrollTop;
            })
          }
        }else{
          //进入非缓存页面滚动条重置顶部
          // if(this.$refs.backtop){
          //   this.$refs.backtop.$el.click();
          // }
        }
        if(from&&!from.meta.notCache){
          //进入页面时，判断上一个页面是否为缓存页面，并保存滚动条位置
          this.$store.commit("layoutStore/setCacheArrScrollTop", {
            name:from.name,
            scrollTop:this.scrollTop
          });
        }
      },
      immediate:true,
      deep: true
    }
  },
  //过滤器
  filters: {},
  //方法集合
  methods: {
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop;
    },
    viewScroll(val, index) {
      this.clickIndex = index;
      this.scrollIt(val, 500, "linear");
    },
    scrollIt(destination = 0, duration = 200, easing = "linear", callback) {
      let that = this;
      // define timing functions -- 过渡动效
      let easings = {
        // no easing, no acceleration
        linear(t) {
          return t;
        },
        // accelerating from zero velocity
        easeInQuad(t) {
          return t * t;
        },
        // decelerating to zero velocity
        easeOutQuad(t) {
          return t * (2 - t);
        },
        // acceleration until halfway, then deceleration
        easeInOutQuad(t) {
          return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        },
        // accelerating from zero velocity
        easeInCubic(t) {
          return t * t * t;
        },
        // decelerating to zero velocity
        easeOutCubic(t) {
          return --t * t * t + 1;
        },
        // acceleration until halfway, then deceleration
        easeInOutCubic(t) {
          return t < 0.5
            ? 4 * t * t * t
            : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        },
        // accelerating from zero velocity
        easeInQuart(t) {
          return t * t * t * t;
        },
        // decelerating to zero velocity
        easeOutQuart(t) {
          return 1 - --t * t * t * t;
        },
        // acceleration until halfway, then deceleration
        easeInOutQuart(t) {
          return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;
        },
        // accelerating from zero velocity
        easeInQuint(t) {
          return t * t * t * t * t;
        },
        // decelerating to zero velocity
        easeOutQuint(t) {
          return 1 + --t * t * t * t * t;
        },
        // acceleration until halfway, then deceleration
        easeInOutQuint(t) {
          return t < 0.5
            ? 16 * t * t * t * t * t
            : 1 + 16 * --t * t * t * t * t;
        }
      };
      // requestAnimationFrame()的兼容性封装：先判断是否原生支持各种带前缀的
      // 不行的话就采用延时的方案
      (function() {
        var lastTime = 0;
        var vendors = ["ms", "moz", "webkit", "o"];
        for (
          var x = 0;
          x < vendors.length && !window.requestAnimationFrame;
          ++x
        ) {
          window.requestAnimationFrame =
            window[vendors[x] + "RequestAnimationFrame"];
          window.cancelAnimationFrame =
            window[vendors[x] + "CancelAnimationFrame"] ||
            window[vendors[x] + "CancelRequestAnimationFrame"];
        }

        if (!window.requestAnimationFrame)
          window.requestAnimationFrame = function(callback, element) {
            var currTime = new Date().getTime();
            var timeToCall = Math.max(0, 16 - (currTime - lastTime));
            var id = window.setTimeout(function() {
              callback(currTime + timeToCall);
            }, timeToCall);
            lastTime = currTime + timeToCall;
            return id;
          };

        if (!window.cancelAnimationFrame)
          window.cancelAnimationFrame = function(id) {
            clearTimeout(id);
          };
      })();
      let mainDocument = document.getElementsByClassName(
        "main-container page-component__scroll"
      )[0];
      let start = this.scrollTop;
      let startTime = Date.now(); // 当前时间

      function scroll() {
        // 滚动的实现
        let now = Date.now();
        let time = Math.min(1, (now - startTime) / duration);
        let timeFunction = easings[easing](time);
        console.log(1);
        mainDocument.scrollTop = timeFunction * (destination - start) + start;
        start = timeFunction * (destination - start) + start;
        if (start === destination) {
          that.$refs.childTop.onScroll(that.clickIndex);
          callback; // 此次执行回调函数
          return;
        }
        scroll();
      }
      scroll();
    },
    createRouteObj(obj) {
      if (this._.isEmpty(obj)) {
        return {};
      }
      return {
        name: obj.name,
        meta: obj.meta,
        fullPath: obj.fullPath
      };
    },
  },
  //生命周期 - 创建完成（this.data, this.message）
  created() {},
  //生命周期 - 挂载完成（[dom]this.el, this.data, this.message）
  mounted() { },
  beforeCreate() { }, //生命周期 - 创建之前(undefined)
  beforeMount() { },  //生命周期 - 挂载之前（[virtual dom]this.el, this.data, this.message）
  updated() { },      //生命周期 - 更新之后
  destroyed() { },    //生命周期 - 销毁完成
  activated() { }     //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less">
//@import url();
.header-container {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.main-container.el-main {
  padding:0px;
  background-color: #f0f2f5;
  .main-bg{
    background-color: #fff;
    min-height: 100%;
    box-sizing: border-box;
  }
}

.sideClass-hide {
  .menu-title-name,
  .el-submenu__icon-arrow {
    opacity: 0 !important;
  }
}

.dt-comList-title {
  padding-right: 20px;
  font-size: 14px;
  font-weight: 700;
  background: #d3dce6;
  min-height: 46px;
  line-height: 46px;
  padding-left: 20px;
}
</style>