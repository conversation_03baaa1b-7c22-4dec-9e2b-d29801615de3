<template>
  <div class="formula-form-container">
    <!-- 基本信息配置 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置公式基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
          :model="formData"
          :rules="formRules"
          ref="basicForm"
          label-width="100px"
          class="basic-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公式名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入公式名称"
                  :disabled="isView"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业类型" prop="enterpriseType">
                <el-select
                  v-model="formData.enterpriseType"
                  placeholder="请选择企业类型"
                  :disabled="isView"
                  class="modern-select"
                  style="width: 100%"
                  multiple
                >
                  <el-option
                    v-for="item in enterpriseTypeOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status" :disabled="isView">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公式描述" prop="description">
                <el-input
                  v-model="formData.description"
                  placeholder="请输入公式描述"
                  :disabled="isView"
                  class="modern-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 变量配置 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-data-line"></i>
          </div>
          <div class="section-title">
            <h4>变量配置</h4>
            <span class="section-subtitle">定义公式中的变量和参数</span>
          </div>
        </div>
        <div class="header-actions" v-if="!isView">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="addVariable"
            class="add-variable-btn"
          >
            添加变量
          </el-button>
        </div>
      </div>
      
      <div class="section-content">
        <div class="variable-table-wrapper">
          <el-table
            :data="formData.variables"
            class="variable-table"
            empty-text="暂无变量"
          >
            <el-table-column prop="name" label="变量名" width="120" align="center">
              <template slot-scope="scope">
                <el-input
                  v-if="!isView"
                  v-model="scope.row.name"
                  placeholder="变量名"
                  size="small"
                  class="variable-input"
                />
                <span v-else class="variable-name">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="type" label="类型" width="120" align="center">
              <template slot-scope="scope">
                <el-select
                  v-if="!isView"
                  v-model="scope.row.type"
                  placeholder="类型"
                  size="small"
                  class="variable-select"
                >
                  <el-option label="数值" value="number" />
                  <el-option label="变量" value="variable" />
                </el-select>
                <el-tag v-else size="small" :type="scope.row.type === 'number' ? 'primary' : 'success'">
                  {{ scope.row.type === 'number' ? '数值' : '变量' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="defaultValue" label="默认值" width="120" align="center">
              <template slot-scope="scope">
                <el-input-number
                  v-if="!isView"
                  v-model="scope.row.defaultValue"
                  placeholder="默认值"
                  size="small"
                  :precision="4"
                  class="variable-number"
                  controls-position="right"
                  style="width: 100%"
                />
                <span v-else class="variable-value">{{ scope.row.defaultValue }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="description" label="描述" min-width="200" align="center">
              <template slot-scope="scope">
                <el-input
                  v-if="!isView"
                  v-model="scope.row.description"
                  placeholder="变量描述"
                  size="small"
                  class="variable-input"
                />
                <span v-else class="variable-desc">{{ scope.row.description }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100" align="center" v-if="!isView">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeVariable(scope.$index)"
                  class="delete-variable-btn"
                  title="删除变量"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 公式编辑器 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-edit"></i>
          </div>
          <div class="section-title">
            <h4>公式编辑器</h4>
            <span class="section-subtitle">可视化编辑数学公式</span>
          </div>
        </div>
        <div class="header-actions" v-if="!isView">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="validateFormula"
            class="validate-btn"
          >
            验证公式
          </el-button>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="editor-section">
        <div class="editor-guide-inline2" @click="toggleGuide" :class="{hover: guideHover}" @mouseenter="guideHover=true" @mouseleave="guideHover=false">
          <i class="el-icon-info"></i>
          <span class="guide-title">公式编辑器使用说明</span>
          <i class="el-icon-arrow-down guide-arrow" :class="{ 'is-expanded': showGuide }"></i>
        </div>
        <transition name="fade">
          <div v-show="showGuide" class="guide-content2">
            <div class="guide-details">
              <h5>函数说明：</h5>
              <ul>
                <li><b>sin(x)</b> - 正弦函数，计算角度的正弦值</li>
                <li><b>cos(x)</b> - 余弦函数，计算角度的余弦值</li>
                <li><b>tan(x)</b> - 正切函数，计算角度的正切值</li>
                <li><b>log(x)</b> - 对数函数，计算以10为底的对数</li>
                <li><b>ln(x)</b> - 自然对数，计算以e为底的对数</li>
                <li><b>sqrt(x)</b> - 平方根，计算数值的平方根</li>
                <li><b>abs(x)</b> - 绝对值，返回数值的绝对值</li>
                <li><b>exp(x)</b> - 指数函数，计算e的幂次方</li>
                <li><b>pow(x, n)</b> - 幂函数，计算x的n次幂</li>
                <li><b>factorial(x)</b> - 阶乘函数，计算数值的阶乘</li>
              </ul>
              <h5>运算符说明：</h5>
              <ul>
                <li><b>+</b> - 加法运算</li>
                <li><b>-</b> - 减法运算</li>
                <li><b>*</b> - 乘法运算</li>
                <li><b>/</b> - 除法运算</li>
                <li><b>^</b> - 幂运算（等同于pow函数）</li>
                <li><b>( )</b> - 括号，用于分组运算</li>
                <li><b>!</b> - 阶乘运算（等同于factorial函数）</li>
              </ul>
              <h5>常数说明：</h5>
              <ul>
                <li>常数由"常数设置"模块统一维护，如PI、E、a、b、c等</li>
                <li>在公式中可直接使用常数名称，如：a * x + b * sqrt(x)</li>
                <li>常数会自动从常数设置模块加载，状态为"启用"的常数才会显示</li>
                <li>点击工具栏"常数"按钮可快速插入常数</li>
                <li>常数A、B、C、D、E是风险评分计算的专用系数，可直接在公式中使用</li>
              </ul>
              <h5>输入变量说明：</h5>
              <ul>
                <li>输入变量需要在下方"变量配置"中定义</li>
                <li>变量类型分为"数值"和"变量"两种</li>
                <li>"数值"类型：固定数值，如权重系数</li>
                <li>"变量"类型：动态输入值，如问卷得分</li>
                <li>点击工具栏"输入变量"按钮可快速插入已定义的变量</li>
                <li>变量名建议使用字母，如x、y、score等</li>
              </ul>
              <h5>配置步骤：</h5>
              <div class="config-steps">
                <ol>
                  <li><b>基本信息配置</b>
                    <ul>
                      <li>填写公式名称和描述</li>
                      <li>选择公式分类</li>
                      <li>设置启用状态</li>
                    </ul>
                  </li>
                  <li><b>变量配置</b>
                    <ul>
                      <li>点击"添加变量"按钮</li>
                      <li>填写变量名、类型、默认值、描述</li>
                      <li>变量类型选择"数值"或"变量"</li>
                      <li>可添加多个变量</li>
                    </ul>
                  </li>
                  <li><b>公式编写</b>
                    <ul>
                      <li>使用工具栏快速插入函数、运算符、常数、变量</li>
                      <li>或直接在文本框中输入公式表达式</li>
                      <li>公式无需写f(x)=，直接写表达式即可</li>
                      <li>注意括号匹配和语法正确性</li>
                    </ul>
                  </li>
                  <li><b>公式验证</b>
                    <ul>
                      <li>点击"验证公式"检查语法</li>
                      <li>查看验证结果和错误提示</li>
                      <li>根据提示修正公式语法</li>
                    </ul>
                  </li>
                  <li><b>保存配置</b>
                    <ul>
                      <li>确认所有信息无误后点击"保存"</li>
                      <li>系统会保存公式配置到数据库</li>
                    </ul>
                  </li>
                </ol>
              </div>
              <h5>配置示例：</h5>
              <div class="example-formula">
                <p><b>示例公式：</b> a * pow(x, 2) + b * sqrt(x) + c * sin(x) + d</p>
                <p><b>变量配置：</b></p>
                <ul>
                  <li>x - 类型：变量，默认值：5，描述：输入参数</li>
                  <li>a - 类型：数值，默认值：0.3，描述：二次项系数</li>
                  <li>b - 类型：数值，默认值：0.2，描述：平方根项系数</li>
                  <li>c - 类型：数值，默认值：0.1，描述：正弦项系数</li>
                  <li>d - 类型：数值，默认值：0.4，描述：常数项</li>
                </ul>
                <p><b>使用步骤：</b></p>
                <ol>
                  <li>在"变量配置"中添加上述变量</li>
                  <li>在"公式内容"中输入：a * pow(x, 2) + b * sqrt(x) + c * sin(x) + d</li>
                  <li>点击"验证公式"检查语法</li>
                  <li>保存公式配置</li>
                </ol>
              </div>
            </div>
          </div>
        </transition>
      </div>
      
      <div class="section-content">
        <div class="formula-editor-block">
          
          <!-- 工具栏 -->
          <div class="editor-section">
            <div class="editor-toolbar" v-if="!isView">
              <div class="toolbar-section">
                <span class="toolbar-label">函数:</span>
                <el-button-group class="function-buttons">
                  <el-button
                    v-for="func in mathFunctions"
                    :key="func.name"
                    size="mini"
                    @click="insertFunction(func)"
                    :title="func.description"
                    class="function-btn"
                  >
                    {{ func.name }}
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="toolbar-section">
                <span class="toolbar-label">运算符:</span>
                <el-button-group class="operator-buttons">
                  <el-button
                    v-for="op in operators"
                    :key="op.symbol"
                    size="mini"
                    @click="insertOperator(op)"
                    :title="op.description"
                    class="operator-btn"
                  >
                    {{ op.symbol }}
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="toolbar-section">
                <span class="toolbar-label">常数:</span>
                <el-button-group class="constant-buttons">
                  <el-button
                    v-for="constant in constants"
                    :key="constant.name"
                    size="mini"
                    @click="insertConstant(constant)"
                    :title="constant.description"
                    class="constant-btn"
                  >
                    {{ constant.name }}
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="toolbar-section">
                <span class="toolbar-label">输入变量:</span>
                <el-button-group class="variable-buttons">
                  <el-button
                    v-for="variable in formData.variables"
                    :key="variable.name"
                    size="mini"
                    @click="insertVariable(variable)"
                    :title="variable.description || variable.name"
                    class="variable-btn"
                    v-if="variable.name"
                  >
                    {{ variable.name }}
                  </el-button>
                  <el-button
                    v-if="!formData.variables || formData.variables.length === 0"
                    size="mini"
                    disabled
                    class="variable-btn"
                  >
                    暂无变量
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>
          <!-- 公式内容 -->
          <div class="editor-section">
            <el-form
              :model="formData"
              :rules="formRules"
              ref="formulaForm"
              class="formula-form"
            >
              <el-form-item label="公式内容" prop="formula">
                <el-input
                  v-model="formData.formula"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入数学公式，例如: a*X^2 + b*sqrt(X) + c"
                  :disabled="isView"
                  class="formula-textarea"
                  ref="formulaInput"
                />
              </el-form-item>
            </el-form>
          </div>
          <!-- 公式预览 -->
          <div class="editor-section" v-if="formData.formula">
            <div class="formula-preview">
              <div class="preview-header">
                <span class="preview-label">公式预览:</span>
                <el-tag
                  :type="validationResult.valid ? 'success' : 'danger'"
                  size="small"
                  class="validation-tag"
                >
                  <i :class="validationResult.valid ? 'el-icon-check' : 'el-icon-close'"></i>
                  {{ validationResult.valid ? '语法正确' : '语法错误' }}
                </el-tag>
              </div>
              <div class="preview-content">
                <div class="formula-display">
                  {{ formData.formula }}
                </div>
              </div>
              
              <!-- 验证错误信息 -->
              <div v-if="validationResult.errors && validationResult.errors.length > 0" class="validation-errors">
                <div class="error-header">
                  <i class="el-icon-warning"></i>
                  语法错误:
                </div>
                <ul class="error-list">
                  <li v-for="error in validationResult.errors" :key="error" class="error-item">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions" v-if="!isView">
      <el-button @click="handleCancel" class="cancel-btn">
        取消
      </el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :loading="saving"
        class="save-btn"
      >
        {{ isEdit ? '更新' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { validateFormula, calculateFormula, getFormulaCategoryList } from '@/api/formulaEngine';
import { getConstantList } from '@/api/constantSetting';
import { getEnterpriseTypeList } from '@/api/enterprise/type';

export default {
  name: 'FormulaForm',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      saving: false,
      categoryOptions: [],
      validationResult: {
        valid: true,
        errors: [],
        suggestions: []
      },
      showGuide: false,
      guideHover: false,
      validateTimer: null,
      // 数学函数
      mathFunctions: [
        { name: 'sin', description: '正弦函数 - 计算角度的正弦值，用于周期性变化' },
        { name: 'cos', description: '余弦函数 - 计算角度的余弦值，用于周期性变化' },
        { name: 'tan', description: '正切函数 - 计算角度的正切值，用于角度计算' },
        { name: 'log', description: '对数函数 - 计算以10为底的对数，用于数据压缩' },
        { name: 'ln', description: '自然对数 - 计算以e为底的对数，用于增长率计算' },
        { name: 'sqrt', description: '平方根 - 计算数值的平方根，用于距离计算' },
        { name: 'abs', description: '绝对值 - 返回数值的绝对值，用于误差计算' },
        { name: 'exp', description: '指数函数 - 计算e的幂次方，用于增长率模型' },
        { name: 'pow', description: '幂函数 - 计算数值的任意次幂，用于多项式计算' },
        { name: 'factorial', description: '阶乘函数 - 计算数值的阶乘，用于排列组合' }
      ],
      // 运算符
      operators: [
        { symbol: '+', description: '加法 - 两个数值相加' },
        { symbol: '-', description: '减法 - 两个数值相减' },
        { symbol: '*', description: '乘法 - 两个数值相乘' },
        { symbol: '/', description: '除法 - 两个数值相除' },
        { symbol: '^', description: '幂运算 - 计算数值的幂次方' },
        { symbol: '(', description: '左括号 - 开始分组运算' },
        { symbol: ')', description: '右括号 - 结束分组运算' },
        { symbol: '!', description: '阶乘 - 计算数值的阶乘' }
      ],
      // 常数（动态加载）
      constants: [],
      // 企业类型选项
      enterpriseTypeOptions: [],
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入公式名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        enterpriseType: [
          { type: 'array', required: true, message: '请选择企业类型', trigger: 'change' }
        ],
        formula: [
          { required: true, message: '请输入公式内容', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    // 初始化表单数据
    this.initializeFormData(this.formData);
    
    // 加载常量数据
    this.loadConstants();
    
    // 加载企业类型数据
    this.loadEnterpriseTypes();
  },
  watch: {
    formData: {
      handler(newVal) {
        // 避免无限循环，只有当数据真正变化时才初始化
        if (newVal && JSON.stringify(newVal) !== this._lastFormDataJson) {
          this._lastFormDataJson = JSON.stringify(newVal);
          this.initializeFormData(newVal);
        }
      },
      deep: true,
      immediate: true
    },
    'formData.formula': {
      handler(newVal) {
        if (newVal) {
          this.debounceValidate();
        }
      }
    }
  },
  mounted() {
    this.loadCategoryOptions();
  },
  methods: {
    // 初始化表单数据
    initializeFormData(data) {
      if (!data) {
        data = {
          name: '',
          description: '',
          formula: '',
          variables: [],
          enterpriseType: [], // 添加企业类型字段
          status: 1
        };
      }
      
      if (!data.variables || !Array.isArray(data.variables)) {
        this.$set(data, 'variables', []);
      }
      
      // 确保企业类型字段存在且为数组
      if (!data.enterpriseType || !Array.isArray(data.enterpriseType)) {
        this.$set(data, 'enterpriseType', []);
      }
      
      if (!data.status && data.status !== 0) {
        this.$set(data, 'status', 1);
      }
      
      if (!data.enterpriseType || data.enterpriseType.length === 0) {
        this.$set(data, 'enterpriseType', []);
      }
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await getFormulaCategoryList();
        if (response.code === 200) {
          this.categoryOptions = response.data;
        }
      } catch (error) {
        console.error('加载分类选项失败:', error);
      }
    },

    // 加载常数
    async loadConstants() {
      try {
        const response = await getConstantList({ status: 1, pageSize: 100 });
        if (response.code === 200) {
          this.constants = response.data.list.map(item => ({
            name: item.name,
            description: item.description
          }));
        }
      } catch (error) {
        console.error('加载常数失败:', error);
      }
    },

    // 加载企业类型数据
    async loadEnterpriseTypes() {
      try {
        const res = await getEnterpriseTypeList();
        if (res.code === 200 && Array.isArray(res.data)) {
          this.enterpriseTypeOptions = res.data;
        }
      } catch (error) {
        console.error('加载企业类型失败:', error);
        this.$message.error('加载企业类型数据失败');
      }
    },

    // 插入函数
    insertFunction(func) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea');
      const cursorPos = textarea.selectionStart;
      const formula = this.formData.formula || '';
      const beforeCursor = formula.substring(0, cursorPos);
      const afterCursor = formula.substring(cursorPos);
      
      this.formData.formula = beforeCursor + func.name + '()' + afterCursor;
      
      // 设置光标位置到括号内
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(cursorPos + func.name.length + 1, cursorPos + func.name.length + 1);
      });
    },

    // 插入运算符
    insertOperator(op) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea');
      const cursorPos = textarea.selectionStart;
      const formula = this.formData.formula || '';
      const beforeCursor = formula.substring(0, cursorPos);
      const afterCursor = formula.substring(cursorPos);
      
      this.formData.formula = beforeCursor + op.symbol + afterCursor;
      
      // 设置光标位置到运算符后
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(cursorPos + op.symbol.length, cursorPos + op.symbol.length);
      });
    },

    // 插入常数
    insertConstant(constant) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea');
      const cursorPos = textarea.selectionStart;
      const formula = this.formData.formula || '';
      const beforeCursor = formula.substring(0, cursorPos);
      const afterCursor = formula.substring(cursorPos);
      
      this.formData.formula = beforeCursor + constant.name + afterCursor;
      
      // 设置光标位置到常数后
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(cursorPos + constant.name.length, cursorPos + constant.name.length);
      });
    },

    // 插入变量
    insertVariable(variable) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea');
      const cursorPos = textarea.selectionStart;
      const formula = this.formData.formula || '';
      const beforeCursor = formula.substring(0, cursorPos);
      const afterCursor = formula.substring(cursorPos);
      
      this.formData.formula = beforeCursor + variable.name + afterCursor;
      
      // 设置光标位置到变量后
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(cursorPos + variable.name.length, cursorPos + variable.name.length);
      });
    },

    // 防抖验证公式
    debounceValidate() {
      if (this.validateTimer) {
        clearTimeout(this.validateTimer);
      }
      this.validateTimer = setTimeout(() => {
        this.validateFormula();
      }, 500);
    },

    // 验证公式
    async validateFormula() {
      if (!this.formData.formula) {
        this.validationResult = {
          valid: false,
          errors: ['公式不能为空'],
          suggestions: []
        };
        return;
      }

      try {
        const response = await validateFormula(this.formData.formula);
        if (response.code === 200) {
          this.validationResult = {
            valid: response.data.isValid,
            errors: response.data.errors || [],
            suggestions: response.data.suggestions || []
          };
          
          // 即使有建议，但没有错误，也认为公式有效
          if (this.validationResult.valid || 
              (this.validationResult.errors.length === 0 && this.validationResult.suggestions.length > 0)) {
            this.validationResult.valid = true;
            this.$message.success('公式验证通过');
          } else {
            this.$message.error('公式验证失败');
          }
        }
      } catch (error) {
        console.error('验证公式失败:', error);
        // 如果验证过程出错，但公式语法可能是正确的，我们给出一个警告而不是错误
        this.validationResult = {
          valid: true, // 假设有效，但给出警告
          errors: [],
          suggestions: ['验证过程中出现错误，但公式可能仍然有效。请在测试时再次验证。']
        };
        this.$message.warning('公式验证过程中出现问题，但公式可能仍然有效');
      }
    },

    // 添加变量
    addVariable() {
      this.formData.variables.push({
        name: '',
        type: 'number',
        defaultValue: 0,
        description: ''
      });
    },

    // 删除变量
    removeVariable(index) {
      this.formData.variables.splice(index, 1);
    },

    // 切换使用说明展开/收起
    toggleGuide() {
      this.showGuide = !this.showGuide;
    },

    // 保存
    async handleSave() {
      try {
        // 验证基本信息
        await this.$refs.basicForm.validate();
        
        // 验证公式表单
        await this.$refs.formulaForm.validate();
        
        // 验证公式语法
        if (this.validationResult && !this.validationResult.valid && this.validationResult.errors.length > 0) {
          this.$message.error('请先修复公式语法错误');
          return;
        }

        this.saving = true;
        this.$emit('save', { ...this.formData });
      } catch (error) {
        console.error('表单验证失败:', error);
      } finally {
        this.saving = false;
      }
    },

    // 取消
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../../assets/style/shared-styles.less';

.formula-form-container {
  .editor-guide-inline2 {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 24px 10px 24px;
    border-bottom: 1px solid #f7ecdd;
    color: #b08a3a;
    font-size: 14px;
    user-select: none;
    transition: background 0.2s;
    .el-icon-info {
      margin-right: 8px;
      font-size: 16px;
    }
    .guide-title {
      flex: 1;
      font-weight: 500;
    }
    .guide-arrow {
      font-size: 14px;
      margin-left: 8px;
      transition: transform 0.3s;
      &.is-expanded {
        transform: rotate(180deg);
      }
    }
    &.hover, &:hover {
      background: #fbf6ee;
      color: #d7a256;
    }
  }
  .guide-content2 {
    padding: 12px 24px 12px 48px;
    color: #6a5c3a;
    font-size: 13px;
    line-height: 1.7;
    border-bottom: 1px solid #f7ecdd;
    background: #fff;
    .guide-details {
      margin-top: 16px;
      h5 {
        color: #d7a256;
        font-size: 14px;
        font-weight: 600;
        margin: 16px 0 8px 0;
        &:first-child {
          margin-top: 0;
        }
      }
      ul {
        margin: 8px 0 16px 0;
        padding-left: 20px;
        li {
          margin-bottom: 4px;
          b {
            color: #d7a256;
            font-weight: 600;
          }
        }
      }
      .config-steps {
        background: #fbf6ee;
        border: 1px solid #f7ecdd;
        border-radius: 6px;
        padding: 16px;
        margin: 16px 0;
        ol {
          margin: 0;
          padding-left: 20px;
          li {
            margin-bottom: 12px;
            font-weight: 500;
            b {
              color: #d7a256;
            }
            ul {
              margin: 8px 0 0 0;
              padding-left: 20px;
              li {
                margin-bottom: 4px;
                font-weight: normal;
              }
            }
          }
        }
      }
      .example-formula {
        background: #fbf6ee;
        border: 1px solid #f7ecdd;
        border-radius: 6px;
        padding: 16px;
        margin-top: 16px;
        p {
          margin: 8px 0;
          b {
            color: #d7a256;
          }
        }
        ul, ol {
          margin: 8px 0 16px 20px;
          li {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
  .config-section {
    margin-bottom: 20px;

    .section-header {
      .header-actions {
        .validate-btn, .add-variable-btn {
          height: 28px;
          padding: 0 12px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }

    .section-content {
      .basic-form {
        /deep/ .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            font-weight: 500;
            color: #2c3e50;
          }

          .modern-input, .modern-select {
            .el-input__inner {
              border-radius: 6px;
              border-color: #dcdfe6;
              transition: all 0.3s ease;

              &:focus {
                border-color: #D7A256;
                box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
              }
            }
          }

          .el-radio-group {
            .el-radio {
              margin-right: 20px;

              .el-radio__label {
                font-weight: 500;
              }
            }
          }
        }
      }

      // 公式编辑器样式
      .formula-editor-block {
        display: flex;
        flex-direction: column; /* Changed to column */
        gap: 20px;

        .editor-section {
          margin-bottom: 20px;
          &:last-child { margin-bottom: 0; }
        }

        .editor-toolbar {
          background: #fbf6ee;
          border: 1px solid #f7ecdd;
          border-radius: 8px;
          padding: 16px;
          width: 100%; /* Changed to 100% */
          flex-shrink: 0;

          .toolbar-section {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .toolbar-label {
              font-weight: 500;
              color: #2c3e50;
              margin-right: 12px;
              min-width: 60px;
            }

            .function-buttons, .operator-buttons, .constant-buttons, .variable-buttons {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;

              .function-btn, .operator-btn, .constant-btn, .variable-btn {
                height: 28px;
                padding: 0 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                transition: all 0.3s ease;

                &:hover {
                  background: #D7A256;
                  color: white;
                  transform: translateY(-1px);
                }
              }
            }
          }
        }

        .formula-input-area {
          margin-bottom: 20px;
          .formula-textarea {
            /deep/ .el-textarea__inner {
              border-radius: 8px;
              border-color: #dcdfe6;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 14px;
              line-height: 1.6;
              transition: all 0.3s ease;

              &:focus {
                border-color: #D7A256;
                box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
              }
            }
          }
        }

        .formula-preview {
          background: #fbf6ee;
          border: 1px solid #f7ecdd;
          border-radius: 8px;
          padding: 16px;
          width: 100%; /* Changed to 100% */
          flex-shrink: 0;

          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .preview-label {
              font-weight: 500;
              color: #2c3e50;
            }

            .validation-tag {
              i {
                margin-right: 4px;
              }
            }
          }

          .preview-content {
            .formula-display {
              background: white;
              border: 1px solid #f7ecdd;
              border-radius: 6px;
              padding: 12px;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 16px;
              line-height: 1.5;
              color: #2c3e50;
              min-height: 40px;
              word-break: break-all;
            }
          }

          .validation-errors {
            margin-top: 12px;
            padding: 12px;
            background: rgba(245, 108, 108, 0.1);
            border: 1px solid rgba(245, 108, 108, 0.2);
            border-radius: 6px;

            .error-header {
              display: flex;
              align-items: center;
              gap: 6px;
              font-weight: 500;
              color: #f56c6c;
              margin-bottom: 8px;
            }

            .error-list {
              margin: 0;
              padding-left: 20px;

              .error-item {
                color: #f56c6c;
                font-size: 13px;
                line-height: 1.5;
                margin-bottom: 4px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }

      // 变量表格样式
      .variable-table-wrapper {
        .variable-table {
          /deep/ .el-table__header-wrapper {
            .el-table__header {
              th {
                background: #fbf6ee;
                font-weight: 600;
                color: #2c3e50;
                border-bottom: 1px solid #f7ecdd;
              }
            }
          }

          /deep/ .el-table__body-wrapper {
            .el-table__row {
              &:hover {
                background: rgba(215, 162, 86, 0.05) !important;
              }

              td {
                border-bottom: 1px solid #f7ecdd;
                padding: 16px 0;

                .variable-input, .variable-select, .variable-number {
                  .el-input__inner {
                    border-radius: 4px;
                    border-color: #dcdfe6;
                    height: 32px;
                    transition: all 0.3s ease;

                    &:focus {
                      border-color: #D7A256;
                      box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
                    }
                  }
                }

                .variable-name, .variable-value, .variable-desc {
                  color: #2c3e50;
                  font-size: 13px;
                }

                .delete-variable-btn {
                  color: #f56c6c;
                  font-size: 12px;

                  &:hover {
                    background: rgba(245, 108, 108, 0.1);
                  }
                }
              }
            }
          }

          // 修复固定列高度不一致问题（如果有固定列的话）
          /deep/ .el-table__fixed-right {
            .el-table__fixed-body-wrapper {
              .el-table__row {
                td {
                  padding: 16px 0;
                  height: auto;
                  line-height: 1.5;
                }
              }
            }
          }

          /deep/ .el-table__fixed-header-wrapper {
            .el-table__header {
              th {
                padding: 16px 0;
                height: auto;
                line-height: 1.5;
              }
            }
          }

          // 确保所有表格行高度一致
          /deep/ .el-table__body-wrapper {
            .el-table__row {
              height: auto;
              min-height: 48px;
            }
          }

          /deep/ .el-table__fixed-body-wrapper {
            .el-table__row {
              height: auto;
              min-height: 48px;
            }
          }
        }
      }
    }
  }

  // 操作按钮
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0;
    border-top: 1px solid #f7ecdd;
    margin-top: 20px;

    .cancel-btn, .save-btn {
      height: 36px;
      padding: 0 20px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }

    .save-btn {
      background: #D7A256;
      border-color: #D7A256;

      &:hover {
        background: #E6B366;
        border-color: #E6B366;
      }
    }
  }
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style> 