<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :form-data="formData"
      :form-rules="formRules"
      :form-groups="formGroups"
      :is-view="isView"
      :loading="loading"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getFormulaDetail, createFormula, updateFormula, getFormulaCategoryList } from '@/api/formulaEngine'
import { getEnterpriseTypeList } from '@/api/enterprise/type'

export default {
  name: 'FormulaEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      loading: false,
      categoryOptions: [],
      enterpriseTypeOptions: [],
      formData: {
        name: '',
        description: '',
        formula: '',
        variables: [],
        category: '',
        enterpriseType: [],
        status: 1,
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入公式名称', trigger: 'blur' },
          { min: 1, max: 100, message: '公式名称长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入公式描述', trigger: 'blur' },
          { min: 1, max: 500, message: '公式描述长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        formula: [
          { required: true, message: '请输入公式内容', trigger: 'blur' },
          { min: 1, max: 1000, message: '公式内容长度在 1 到 1000 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择公式分类', trigger: 'change' }
        ]
      },
      formGroups: [
        {
          title: '基本信息',
          fields: [
            [{
              prop: 'name',
              label: '公式名称',
              type: 'input',
              placeholder: '请输入公式名称',
              required: true
            }],
            [{
              prop: 'description',
              label: '公式描述',
              type: 'textarea',
              placeholder: '请输入公式描述',
              required: true,
              rows: 3
            }],
            [{
              prop: 'formula',
              label: '公式内容',
              type: 'textarea',
              placeholder: '请输入公式内容，如：a * X + b * X^2 + c * exp(X/5)',
              required: true,
              rows: 4
            }],
            [{
              prop: 'category',
              label: '公式分类',
              type: 'select',
              placeholder: '请选择公式分类',
              required: true,
              options: []
            }],
            [{
              prop: 'enterpriseType',
              label: '适用企业类型',
              type: 'select',
              placeholder: '请选择适用的企业类型',
              multiple: true,
              options: []
            }],
            [{
              prop: 'status',
              label: '状态',
              type: 'radio',
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            }]
          ]
        },
        {
          title: '变量配置',
          fields: [
            [{
              prop: 'variables',
              label: '公式变量',
              type: 'list',
              addText: '添加变量',
              emptyText: '暂无变量，点击添加按钮添加变量',
              defaultRow: {
                name: '',
                type: 'number',
                defaultValue: '',
                description: ''
              },
              columns: [
                {
                  prop: 'name',
                  label: '变量名',
                  type: 'input',
                  placeholder: '请输入变量名',
                  required: true,
                  width: '120px'
                },
                {
                  prop: 'type',
                  label: '变量类型',
                  type: 'select',
                  placeholder: '请选择类型',
                  required: true,
                  width: '120px',
                  options: [
                    { label: '数值', value: 'number' },
                    { label: '变量', value: 'variable' },
                    { label: '字符串', value: 'string' }
                  ]
                },
                {
                  prop: 'defaultValue',
                  label: '默认值',
                  type: 'input',
                  placeholder: '请输入默认值',
                  width: '120px'
                },
                {
                  prop: 'description',
                  label: '变量描述',
                  type: 'input',
                  placeholder: '请输入变量描述',
                  width: '200px'
                }
              ],
              actions: [
                {
                  label: '删除',
                  type: 'danger',
                  icon: 'el-icon-delete'
                }
              ]
            }]
          ]
        },
        {
          title: '系统信息',
          fields: [
            [{
              prop: 'createTime',
              label: '创建时间',
              type: 'input',
              readonly: true
            }, {
              prop: 'updateTime',
              label: '更新时间',
              type: 'input',
              readonly: true
            }],
            [{
              prop: 'createUser',
              label: '创建人',
              type: 'input',
              readonly: true
            }, {
              prop: 'updateUser',
              label: '更新人',
              type: 'input',
              readonly: true
            }]
          ]
        }
      ]
    }
  },
  computed: {
    formulaId() {
      return this.$route.params.id
    },
    mode() {
      return this.$route.query.mode || 'add'
    },
    isView() {
      return this.mode === 'view'
    },
    isEdit() {
      return this.mode === 'edit'
    },
    isAdd() {
      return this.mode === 'add'
    },
    pageTitle() {
      if (this.isView) return '查看公式'
      if (this.isEdit) return '编辑公式'
      return '新增公式'
    },
    pageIcon() {
      return 'el-icon-data-analysis'
    },
    breadcrumbItems() {
      return [
        { text: '公式管理', to: { name: 'formulaEngineIndex' } },
        { text: this.pageTitle }
      ]
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      this.loading = true
      try {
        await Promise.all([
          this.loadCategoryOptions(),
          this.loadEnterpriseTypeOptions()
        ])
        
        if (this.formulaId) {
          await this.loadFormulaDetail()
        }
      } catch (error) {
        console.error('初始化失败:', error)
        this.$message.error('页面初始化失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadCategoryOptions() {
      try {
        const response = await getFormulaCategoryList()
        if (response.code === 200) {
          this.categoryOptions = response.data.map(item => ({
            label: item.name,
            value: item.name
          }))
          
          // 更新表单配置中的选项
          const categoryField = this.formGroups[0].fields.flat().find(field => field.prop === 'category')
          if (categoryField) {
            categoryField.options = this.categoryOptions
          }
        }
      } catch (error) {
        console.error('加载分类选项失败:', error)
      }
    },
    
    async loadEnterpriseTypeOptions() {
      try {
        const response = await getEnterpriseTypeList()
        if (response.code === 200) {
          // 处理不同的数据结构
          const data = response.data.list || response.data
          this.enterpriseTypeOptions = data.map(item => ({
            label: item.name,
            value: item.code
          }))
          
          // 更新表单配置中的选项
          const enterpriseTypeField = this.formGroups[0].fields.flat().find(field => field.prop === 'enterpriseType')
          if (enterpriseTypeField) {
            enterpriseTypeField.options = this.enterpriseTypeOptions
          }
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
      }
    },
    
    async loadFormulaDetail() {
      try {
        const response = await getFormulaDetail(this.formulaId)
        if (response.code === 200) {
          this.formData = {
            ...this.formData,
            ...response.data
          }
        } else {
          this.$message.error(response.message || '加载公式详情失败')
          this.handleBack()
        }
      } catch (error) {
        console.error('加载公式详情失败:', error)
        this.$message.error('加载公式详情失败')
        this.handleBack()
      }
    },
    
    async handleSave() {
      try {
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.warning('请检查表单填写是否正确')
          return
        }
        
        this.loading = true
        let response
        
        if (this.isEdit) {
          response = await updateFormula(this.formulaId, this.formData)
        } else {
          response = await createFormula(this.formData)
        }
        
        if (response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.handleBack()
          }, 1500)
        } else {
          this.$message.error(response.message || (this.isEdit ? '更新失败' : '创建失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error(this.isEdit ? '更新失败' : '创建失败')
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      const from = this.$route.query.from
      if (from === 'list') {
        this.$router.push({ name: 'formulaEngineIndex' })
      } else {
        this.$router.go(-1)
      }
    },
    
    handleBreadcrumbClick(breadcrumb) {
      if (breadcrumb.to) {
        this.$router.push(breadcrumb.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式可以根据需要添加
</style>