<template>
  <div class="formula-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="el-icon-edit"></i>
            {{ isEdit ? '编辑公式' : '新增公式' }}
          </h1>
          <p class="page-subtitle">{{ isEdit ? '修改公式配置信息' : '创建新的数学公式配置' }}</p>
        </div>
        <div class="action-section">
          <el-button 
            icon="el-icon-back" 
            @click="goBack"
            class="back-btn"
          >
            返回列表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="config-content" v-loading="loading">
      <div class="form-container">
        <FormulaForm
          :form-data="formData"
          :is-edit="isEdit"
          :is-view="false"
          @save="handleSave"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <!-- 加载状态 -->
    <!-- <div class="loading-container" v-if="loading">
      <el-loading 
        :visible="loading" 
        text="正在加载公式信息..."
        background="rgba(251, 246, 238, 0.8)"
      />
    </div> -->
  </div>
</template>

<script>
import { getFormulaDetail, createFormula, updateFormula } from '@/api/formulaEngine';
import FormulaForm from './components/FormulaForm.vue';

export default {
  name: 'FormulaConfig',
  components: {
    FormulaForm
  },
  data() {
    return {
      loading: false,
      formData: {
        name: '',
        description: '',
        formula: '',
        variables: [],
        enterpriseType: [], // 添加企业类型字段，替换原来的category字段
        status: 1
      }
    };
  },
  computed: {
    formulaId() {
      return this.$route.params.id;
    },
    isEdit() {
      return !!this.formulaId;
    }
  },
  mounted() {
    if (this.isEdit) {
      this.loadFormulaData();
    }
  },
  methods: {
    // 加载公式数据
    async loadFormulaData() {
      this.loading = true;
      try {
        const response = await getFormulaDetail(this.formulaId);
        if (response.code === 200) {
          this.formData = { ...response.data };
        } else {
          this.$message.error(response.message || '加载公式失败');
          this.goBack();
        }
      } catch (error) {
        this.$message.error('加载公式失败');
        console.error('加载公式失败:', error);
        this.goBack();
      } finally {
        this.loading = false;
      }
    },

    // 保存公式
    async handleSave(formData) {
      try {
        let response;
        if (this.isEdit) {
          response = await updateFormula(this.formulaId, formData);
        } else {
          response = await createFormula(formData);
        }

        if (response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功');
          this.goBack();
        } else {
          this.$message.error(response.message || (this.isEdit ? '更新失败' : '创建失败'));
        }
      } catch (error) {
        this.$message.error(this.isEdit ? '更新失败' : '创建失败');
        console.error('保存失败:', error);
      }
    },

    // 取消操作
    handleCancel() {
      this.goBack();
    },

    // 返回列表
    goBack() {
      this.$router.push({ name: 'formulaEngineIndex' });
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';

.formula-config-container {
  min-height: 100vh;
  background: #fbf6ee;

  // 页面头部
  .page-header {
    background: white;
    padding: 24px 32px;
    border-bottom: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            color: #D7A256;
            font-size: 28px;
          }
        }

        .page-subtitle {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
          line-height: 1.5;
        }
      }

      .action-section {
        .back-btn {
          height: 36px;
          padding: 0 20px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  // 配置内容
  .config-content {

    .form-container {
      background: white;
      padding: 24px;
    }
  }

  // 加载状态
  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
}
</style> 