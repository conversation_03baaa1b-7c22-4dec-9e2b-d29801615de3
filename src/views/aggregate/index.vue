<template>
  <div class="aggregate-container">
    <!-- 左侧导航栏 -->
    <DemoNavigation
      :title="'系统演示'"
      :active-module="currentModule"
      :menu-groups="menuGroups"
      @module-change="switchModule"
    />

    <!-- 右侧内容区 -->
    <div class="main-content">
      <div class="content-header">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ getBreadcrumbTitle() }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ currentModuleTitle }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="content-body">
        <!-- 模块详情页面 -->
        <template v-if="viewingDetail">
          <ModuleDetail
            :module="getCurrentModuleDetail()"
            :related-modules="getRelatedModules()"
            @back="viewingDetail = false"
            @open-module="openModuleIframe"
            @view-module="viewOtherModuleDetail"
          />
        </template>

          <!-- iframe 嵌入区域 -->
        <template v-else>
          <div class="iframe-container" :class="{ loading: iframeLoading }">
            <iframe
              :src="getIframeSrc()"
              frameborder="0"
              class="module-iframe"
              @load="onIframeLoad"
              @error="onIframeError">
            </iframe>

            <div v-if="iframeLoading" class="iframe-loading">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载模块...</div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import DemoNavigation from './components/DemoNavigation.vue';
import WelcomePanel from './components/WelcomePanel.vue';
import ModuleGrid from './components/ModuleGrid.vue';
import ModuleDetail from './components/ModuleDetail.vue';

export default {
  name: 'AggregateIndex',
  components: {
    DemoNavigation,
    WelcomePanel,
    ModuleGrid,
    ModuleDetail
  },
  data() {
    return {
      currentModule: 'enterprise-info',
      iframeLoading: true,
      viewingDetail: false,
      detailModuleKey: null,

      // 菜单分组
      menuGroups: [
        {
          title: '企业目录',
          items: [
            { key: 'enterprise-info', title: '企业信息管理', icon: 'el-icon-office-building' },
            { key: 'enterprise-customer', title: '企业客户管理', icon: 'el-icon-user-solid' },
            { key: 'enterprise-type', title: '企业类型管理', icon: 'el-icon-s-data' }
          ]
        },
        {
          title: '业务配置',
          items: [
            { key: 'industry-limit', title: '行业限制管理', icon: 'el-icon-s-custom' },
            { key: 'online-product', title: '线上产品配置', icon: 'el-icon-goods' }
          ]
        },
        {
          title: '风险管理',
          items: [
            { key: 'basic-parameter', title: '常数设置', icon: 'el-icon-setting' },
            { key: 'formula', title: '公式管理', icon: 'el-icon-data-analysis' },
            { key: 'questionnaire', title: '问卷管理', icon: 'el-icon-s-order' },
            { key: 'risk-matrix', title: '风险矩阵管理', icon: 'el-icon-warning' },
            { key: 'risk-score-item', title: '风险评分项管理', icon: 'el-icon-s-marketing' },
            { key: 'industry-risk', title: '行业风险管理', icon: 'el-icon-s-custom' }
          ]
        },
        {
          title: '基础配置',
          items: [
            { key: 'data-template', title: '数据模板配置', icon: 'el-icon-document-copy' },
            { key: 'service-process', title: '服务流程配置', icon: 'el-icon-s-operation' }
          ]
        }
      ],

      // 模块配置
      moduleConfig: {
        'enterprise-info': {
          title: '企业信息管理',
          description: '企业基本信息维护、企业档案管理',
          url: '/enterprise/info',
          icon: 'el-icon-office-building',
          iconBackground: 'linear-gradient(135deg, #3498db, #2980b9)',
          fullDescription: '企业信息管理模块提供全面的企业信息维护功能，包括企业基本信息、联系人信息、财务信息等。支持企业信息的创建、编辑、查询和导出，以及企业档案的管理和维护。',
          features: [
            '企业基本信息管理',
            '企业联系人管理',
            '企业财务信息管理',
            '企业档案管理',
            '企业信息导入导出'
          ],
          tags: [
            { text: '基础数据', type: 'primary' },
            { text: '信息管理', type: '' }
          ],
          relatedModules: ['enterprise-type']
        },
        'enterprise-customer': {
                  title: '企业客户管理',
                  description: '企业客户信息维护、客户关系管理',
                  url: '/enterprise/customer',
                  icon: 'el-icon-user-solid',
                  iconBackground: 'linear-gradient(135deg, #e74c3c, #c0392b)',
                  fullDescription: '企业客户管理模块提供全面的客户信息维护功能，包括客户基本信息、联系人信息、业务往来记录等。支持客户信息的创建、编辑、查询和导出，以及客户关系的管理和维护。',
                  features: [
                    '客户基本信息管理',
                    '客户联系人管理',
                    '客户业务往来记录',
                    '客户关系维护',
                    '客户信息导入导出'
                  ],
                  tags: [
                    { text: '客户管理', type: 'primary' },
                    { text: '关系维护', type: 'success' }
                  ],
                  relatedModules: ['enterprise-type']
                },
        'enterprise-type': {
          title: '企业类型管理',
          description: '企业类型定义、分类管理、区间配置',
          url: '/enterprise/type',
          icon: 'el-icon-s-data',
          iconBackground: 'linear-gradient(135deg, #9b59b6, #8e44ad)',
          fullDescription: '企业类型管理模块用于定义和管理企业类型，支持企业类型的创建、编辑和删除，以及类型区间的配置和管理。',
          features: [
            '企业类型定义',
            '企业分类管理',
            '企业类型区间配置',
            '企业类型关联规则'
          ],
          tags: [
            { text: '基础数据', type: 'primary' },
            { text: '分类管理', type: '' }
          ],
          relatedModules: ['enterprise-info']
        },
        'formula': {
          title: '公式管理',
          description: '公式配置、测试验证、变量管理',
          url: '/formulaEngine/index',
          icon: 'el-icon-data-analysis',
          iconBackground: 'linear-gradient(135deg, #e74c3c, #c0392b)',
          fullDescription: '公式管理模块提供强大的公式配置和管理功能，支持公式的创建、编辑、测试和验证，以及公式变量的管理和维护。',
          features: [
            '公式创建与编辑',
            '公式测试与验证',
            '公式变量管理',
            '公式分类管理',
            '公式版本控制'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '计算引擎', type: '' }
          ],
          relatedModules: ['basic-parameter', 'risk-matrix']
        },
        'questionnaire': {
          title: '问卷管理',
          description: '问卷设计、题目管理、填写统计',
          url: '/questionnaire',
          icon: 'el-icon-s-order',
          iconBackground: 'linear-gradient(135deg, #f1c40f, #f39c12)',
          fullDescription: '问卷管理模块提供全面的问卷设计和管理功能，支持问卷的创建、编辑、发布和统计，以及问卷题目的管理和维护。',
          features: [
            '问卷设计与编辑',
            '题目管理',
            '问卷发布',
            '填写统计分析',
            '问卷模板管理'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '数据采集', type: '' }
          ],
          relatedModules: ['risk-matrix', 'formula']
        },
        'risk-matrix': {
          title: '风险矩阵管理',
          description: '风险矩阵配置、核心类别设置',
          url: '/risk-matrix',
          icon: 'el-icon-warning',
          iconBackground: 'linear-gradient(135deg, #e67e22, #d35400)',
          fullDescription: '风险矩阵管理模块提供风险矩阵的配置和管理功能，支持风险矩阵的创建、编辑和删除，以及核心类别的设置和管理。',
          features: [
            '风险矩阵配置',
            '核心类别设置',
            '风险等级定义',
            '风险矩阵可视化',
            '风险矩阵版本管理'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '评估工具', type: '' }
          ],
          relatedModules: ['risk-score-item', 'industry-risk', 'formula']
        },
        'risk-score-item': {
          title: '风险评分项管理',
          description: '评分项配置、权重设置、关联管理',
          url: '/risk-matrix/score-item',
          icon: 'el-icon-s-marketing',
          iconBackground: 'linear-gradient(135deg, #1abc9c, #16a085)',
          fullDescription: '风险评分项管理模块提供风险评分项的配置和管理功能，支持评分项的创建、编辑和删除，以及权重设置和关联管理。',
          features: [
            '评分项配置',
            '权重设置',
            '关联管理',
            '评分规则定义',
            '评分项分类管理'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '评分配置', type: '' }
          ],
          relatedModules: ['risk-matrix', 'formula']
        },
        'industry-risk': {
          title: '行业风险管理',
          description: '行业风险分级、行业风险规则配置',
          url: '/industry-risk',
          icon: 'el-icon-s-custom',
          iconBackground: 'linear-gradient(135deg, #2ecc71, #27ae60)',
          fullDescription: '行业风险管理模块提供行业风险的分级和规则配置功能，支持行业风险的创建、编辑和删除，以及行业风险规则的配置和管理。',
          features: [
            '行业风险分级',
            '行业风险规则配置',
            '行业风险评估',
            '行业风险报告',
            '行业风险监控'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '行业配置', type: '' }
          ],
          relatedModules: ['risk-matrix', 'industry-limit']
        },
        'basic-parameter': {
          title: '常数设置',
          description: '数学常数、权重系数配置，可在公式中直接使用',
          url: '/constant-setting',
          icon: 'el-icon-setting',
          iconBackground: 'linear-gradient(135deg, #34495e, #2c3e50)',
          fullDescription: '常数设置模块提供系统常数的配置和管理功能，支持数学常数、权重系数等的创建、编辑和删除，可在公式中直接使用。',
          features: [
            '数学常数配置',
            '权重系数配置',
            '常数分类管理',
            '常数版本控制',
            '常数使用统计'
          ],
          tags: [
            { text: '风险管理', type: 'danger' },
            { text: '参数配置', type: '' }
          ],
          relatedModules: ['formula']
        },
        'data-template': {
          title: '数据模板配置',
          description: '数据模板管理、字段配置',
          url: '/data-template',
          icon: 'el-icon-document-copy',
          iconBackground: 'linear-gradient(135deg, #3498db, #2980b9)',
          fullDescription: '数据模板配置模块提供数据模板的配置和管理功能，支持数据模板的创建、编辑和删除，以及字段配置和管理。',
          features: [
            '数据模板管理',
            '字段配置',
            '模板版本控制',
            '模板导入导出',
            '模板使用统计'
          ],
          tags: [
            { text: '基础配置', type: 'primary' },
            { text: '数据管理', type: '' }
          ],
          relatedModules: ['service-process']
        },
        'service-process': {
          title: '服务流程配置',
          description: '服务流程管理、步骤配置',
          url: '/service-process',
          icon: 'el-icon-s-operation',
          iconBackground: 'linear-gradient(135deg, #9b59b6, #8e44ad)',
          fullDescription: '服务流程配置模块提供服务流程的配置和管理功能，支持服务流程的创建、编辑和删除，以及步骤配置和管理。',
          features: [
            '服务流程管理',
            '步骤配置',
            '流程版本控制',
            '流程可视化',
            '流程执行监控'
          ],
          tags: [
            { text: '基础配置', type: 'primary' },
            { text: '流程管理', type: '' }
          ],
          relatedModules: ['data-template']
        },
        'industry-limit': {
          title: '行业限制管理',
          description: '行业限制规则配置、行业白名单/黑名单管理',
          url: '/industry-limit',
          icon: 'el-icon-s-custom',
          iconBackground: 'linear-gradient(135deg, #e74c3c, #c0392b)',
          fullDescription: '行业限制管理模块提供行业限制规则的配置和管理功能，支持行业限制规则的创建、编辑和删除，以及行业白名单/黑名单的管理。',
          features: [
            '行业限制规则配置',
            '行业白名单管理',
            '行业黑名单管理',
            '行业限制评估',
            '行业限制报告'
          ],
          tags: [
            { text: '业务配置', type: 'warning' },
            { text: '规则管理', type: '' }
          ],
          relatedModules: ['industry-risk']
        },
        'online-product': {
          title: '线上产品配置',
          description: '线上产品服务流程配置',
          url: '/online-product',
          icon: 'el-icon-goods',
          iconBackground: 'linear-gradient(135deg, #f1c40f, #f39c12)',
          fullDescription: '线上产品配置模块提供线上产品服务流程的配置和管理功能，支持线上产品服务流程的创建、编辑和删除。',
          features: [
            '线上产品配置',
            '服务流程配置',
            '产品参数设置',
            '产品版本管理',
            '产品上下架管理'
          ],
          tags: [
            { text: '业务配置', type: 'warning' },
            { text: '产品管理', type: '' }
          ],
          relatedModules: ['service-process']
        }
      },

      // 欢迎页统计数据
      welcomeStats: [
        { value: '12', label: '功能模块', icon: 'el-icon-s-grid', color: 'linear-gradient(135deg, #3498db, #2980b9)' },
        { value: '6', label: '业务分类', icon: 'el-icon-s-unfold', color: 'linear-gradient(135deg, #e74c3c, #c0392b)' },
        { value: '50+', label: '数据模拟', icon: 'el-icon-s-data', color: 'linear-gradient(135deg, #2ecc71, #27ae60)' }
      ],

      // 欢迎页功能特点
      welcomeFeatures: [
        { title: '全面的功能展示', description: '涵盖企业管理、风险评估、数据配置等多个领域', icon: 'el-icon-s-platform' },
        { title: '真实的数据模拟', description: '使用模拟数据展示系统功能，贴近实际业务场景', icon: 'el-icon-s-data' },
        { title: '直观的操作界面', description: '现代化UI设计，简洁易用的操作体验', icon: 'el-icon-s-operation' },
        { title: '完整的演示流程', description: '从数据录入到结果展示的完整业务流程演示', icon: 'el-icon-s-promotion' }
      ],

      // 欢迎页操作按钮
      welcomeActions: [
        { key: 'formula', label: '查看公式管理', icon: 'el-icon-data-analysis', type: '' },
        { key: 'enterprise-info', label: '开始体验', icon: 'el-icon-s-promotion', type: 'primary' }
      ]
    };
  },
  computed: {
    currentModuleTitle() {
      return this.moduleConfig[this.currentModule]?.title || '系统演示';
    }
  },
  methods: {
    switchModule(module) {
      this.currentModule = module;
      this.viewingDetail = false;
      this.iframeLoading = true;
    },

    getIframeSrc() {
      const moduleConfig = this.moduleConfig[this.currentModule];
      return moduleConfig ? moduleConfig.url : '';
    },

    onIframeLoad() {
      this.iframeLoading = false;
    },

    onIframeError() {
      this.iframeLoading = false;
      this.$message.error('页面加载失败，请检查网络连接');
    },

    getBreadcrumbTitle() {
      if (this.currentModule === 'enterprise-info' || this.currentModule === 'enterprise-info2' || this.currentModule === 'enterprise-type') {
        return '企业目录';
      } else if (['formula', 'questionnaire', 'risk-matrix', 'risk-score-item', 'industry-risk', 'basic-parameter'].includes(this.currentModule)) {
        return '风险管理';
      } else if (['data-template', 'service-process'].includes(this.currentModule)) {
        return '基础配置';
      } else if (['industry-limit', 'online-product'].includes(this.currentModule)) {
        return '业务配置';
      }
      return '';
    },

    viewModuleDetail(key) {
      this.detailModuleKey = key;
      this.viewingDetail = true;
    },

    viewOtherModuleDetail(key) {
      this.detailModuleKey = key;
    },

    getCurrentModuleDetail() {
      const key = this.detailModuleKey || this.currentModule;
      return {
        key,
        ...this.moduleConfig[key]
      };
    },

    getRelatedModules() {
      const key = this.detailModuleKey || this.currentModule;
      const module = this.moduleConfig[key];

      if (module && module.relatedModules && module.relatedModules.length) {
        return module.relatedModules.map(relatedKey => ({
          key: relatedKey,
          ...this.moduleConfig[relatedKey]
        }));
      }

      return [];
    },

    openModuleIframe(key) {
      this.currentModule = key;
      this.viewingDetail = false;
      this.iframeLoading = true;
    }
  }
};
</script>

<style lang="less" scoped>
.aggregate-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f8f6f3 0%, #eef2f7 100%);

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-header {
      padding: 12px 32px;
      background: white;
      border-bottom: 1px solid #f7ecdd;

      /deep/ .el-breadcrumb {
        font-size: 14px;

        .el-breadcrumb__item {
          .el-breadcrumb__inner {
            color: #7f8c8d;
            font-weight: 500;

            &:hover {
              color: #D7A256;
            }
          }

          &.is-link .el-breadcrumb__inner {
            color: #D7A256;

            &:hover {
              color: #E6B366;
            }
          }
        }

        .el-breadcrumb__separator {
          color: #bdc3c7;
          margin: 0 8px;
        }
      }
    }

    .content-body {
      flex: 1;
      padding: 20px;
      overflow-y: auto;

      .module-section {
        margin-top: 24px;
      }

      .all-modules-container {
          background: white;
          border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      }

      .iframe-container {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        height: 100%;
        position: relative;

          .module-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
          min-height: calc(100vh - 120px);
          }

        .iframe-loading {
            position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.8);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          z-index: 10;

          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #D7A256;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
          }

          .loading-text {
            color: #7f8c8d;
            font-size: 16px;
          }
        }
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  }
}
</style>
