<template>
  <div class="module-grid">
    <div class="grid-header" v-if="title || showSearch">
      <h3 v-if="title" class="grid-title">{{ title }}</h3>
      <div v-if="showSearch" class="grid-search">
        <el-input
          v-model="searchQuery"
          placeholder="搜索模块..."
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        ></el-input>
      </div>
    </div>
    
    <div class="grid-content">
      <div class="grid-items">
        <div 
          v-for="(module, index) in filteredModules" 
          :key="index"
          class="grid-item"
        >
          <ModuleCard
            :title="module.title"
            :description="module.description"
            :icon="module.icon"
            :icon-background="module.iconBackground || defaultIconBackground"
            :module-key="module.key"
            :tags="module.tags"
            @click="handleModuleClick(module.key)"
            @view-detail="handleViewDetail(module.key)"
          />
        </div>
      </div>
      
      <div v-if="filteredModules.length === 0" class="empty-state">
        <i class="el-icon-search"></i>
        <p>未找到匹配的模块</p>
      </div>
    </div>
    
    <div class="grid-footer" v-if="showPagination && totalPages > 1">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalItems"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        @current-change="handlePageChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import ModuleCard from './ModuleCard.vue';

export default {
  name: 'ModuleGrid',
  components: {
    ModuleCard
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    modules: {
      type: Array,
      required: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    pageSize: {
      type: Number,
      default: 12
    },
    defaultIconBackground: {
      type: String,
      default: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }
  },
  data() {
    return {
      searchQuery: '',
      currentPage: 1
    };
  },
  computed: {
    filteredModules() {
      let filtered = this.modules;
      
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(module => 
          module.title.toLowerCase().includes(query) || 
          module.description.toLowerCase().includes(query)
        );
      }
      
      if (this.showPagination) {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      }
      
      return filtered;
    },
    totalItems() {
      return this.modules.length;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.pageSize);
    }
  },
  methods: {
    handleSearch() {
      this.currentPage = 1;
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
    handleModuleClick(key) {
      this.$emit('module-click', key);
    },
    handleViewDetail(key) {
      this.$emit('view-detail', key);
    }
  }
}
</script>

<style lang="less" scoped>
.module-grid {
  display: flex;
  flex-direction: column;
  
  .grid-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    
    .grid-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
    }
    
    .grid-search {
      width: 240px;
    }
  }
  
  .grid-content {
    flex: 1;
    
    .grid-items {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 24px;
    }
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 0;
      color: #7f8c8d;
      
      i {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
  
  .grid-footer {
    margin-top: 32px;
    display: flex;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .module-grid {
    .grid-header {
      flex-direction: column;
      align-items: flex-start;
      
      .grid-title {
        margin-bottom: 16px;
      }
      
      .grid-search {
        width: 100%;
      }
    }
    
    .grid-content {
      .grid-items {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style> 