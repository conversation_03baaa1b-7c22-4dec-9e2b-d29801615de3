<template>
  <div class="module-card" @click="handleClick">
    <div class="card-icon" :style="{ background: iconBackground }">
      <i :class="icon"></i>
    </div>
    <div class="card-content">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
      <div class="card-tags" v-if="tags && tags.length">
        <el-tag v-for="(tag, index) in tags" :key="index" size="mini" :type="tag.type || ''">
          {{ tag.text }}
        </el-tag>
      </div>
    </div>
    <div class="card-actions">
      <el-button type="text" size="small" @click.stop="handleViewDetail">
        <i class="el-icon-view"></i> 查看详情
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleCard',
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'el-icon-s-grid'
    },
    iconBackground: {
      type: String,
      default: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    moduleKey: {
      type: String,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.moduleKey);
    },
    handleViewDetail() {
      this.$emit('view-detail', this.moduleKey);
    }
  }
}
</script>

<style lang="less" scoped>
.module-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
  
  .card-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    
    i {
      color: white;
      font-size: 24px;
    }
  }
  
  .card-content {
    flex: 1;
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }
  }
  
  .card-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: auto;
    
    .el-button {
      color: #D7A256;
      padding: 0;
      
      &:hover {
        color: #E6B366;
      }
    }
  }
}
</style> 