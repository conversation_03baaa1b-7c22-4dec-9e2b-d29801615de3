<template>
  <div class="demo-navigation">
    <div class="navigation-header">
      <i class="el-icon-s-grid"></i>
      <span>{{ title }}</span>
    </div>
    <div class="navigation-menu">
      <div v-for="(group, index) in menuGroups" :key="index" class="menu-group">
        <div class="group-title">{{ group.title }}</div>
        <div 
          v-for="item in group.items" 
          :key="item.key" 
          class="menu-item" 
          :class="{ active: activeModule === item.key }"
          @click="handleMenuClick(item.key)"
        >
          <i :class="item.icon"></i>
          <span>{{ item.title }}</span>
          <el-badge v-if="item.badge" :value="item.badge" class="item-badge" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DemoNavigation',
  props: {
    title: {
      type: String,
      default: '系统演示'
    },
    activeModule: {
      type: String,
      default: ''
    },
    menuGroups: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleMenuClick(key) {
      this.$emit('module-change', key);
    }
  }
}
</script>

<style lang="less" scoped>
.demo-navigation {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .navigation-header {
    padding: 24px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 16px;
    
    i {
      font-size: 20px;
    }
  }
  
  .navigation-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
    
    .menu-group {
      margin-bottom: 24px;
      
      .group-title {
        padding: 0 20px 8px 20px;
        font-size: 12px;
        color: #7f8c8d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .menu-item {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #2c3e50;
        margin: 0 8px;
        border-radius: 8px;
        position: relative;
        
        &:hover {
          background: rgba(215, 162, 86, 0.1);
          color: #D7A256;
        }
        
        &.active {
          background: #D7A256;
          color: white;
          
          .item-badge ::v-deep .el-badge__content {
            background-color: #fff;
            color: #D7A256;
          }
        }
        
        i {
          font-size: 16px;
          width: 20px;
          text-align: center;
        }
        
        span {
          font-size: 14px;
          font-weight: 500;
          flex: 1;
        }
        
        .item-badge {
          margin-left: auto;
        }
      }
    }
  }
}
</style> 