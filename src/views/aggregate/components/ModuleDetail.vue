<template>
  <div class="module-detail">
    <div class="detail-header">
      <div class="header-content">
        <div class="module-icon" :style="{ background: module.iconBackground || defaultIconBackground }">
          <i :class="module.icon"></i>
        </div>
        <div class="module-info">
          <h2>{{ module.title }}</h2>
          <p>{{ module.description }}</p>
          <div class="module-tags" v-if="module.tags && module.tags.length">
            <el-tag v-for="(tag, index) in module.tags" :key="index" size="small" :type="tag.type || ''">
              {{ tag.text }}
            </el-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="$emit('back')" icon="el-icon-back">返回</el-button>
        <el-button type="primary" @click="openModule">打开模块</el-button>
      </div>
    </div>
    
    <div class="detail-content">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="功能概述" name="overview">
          <div class="tab-content">
            <div class="overview-section">
              <h3 class="section-title">功能描述</h3>
              <p class="section-text">{{ module.fullDescription || module.description }}</p>
            </div>
            
            <div class="overview-section" v-if="module.features && module.features.length">
              <h3 class="section-title">主要功能</h3>
              <ul class="feature-list">
                <li v-for="(feature, index) in module.features" :key="index" class="feature-item">
                  <i class="el-icon-check"></i>
                  <span>{{ feature }}</span>
                </li>
              </ul>
            </div>
            
            <div class="overview-section" v-if="module.screenshot">
              <h3 class="section-title">功能预览</h3>
              <div class="screenshot-container">
                <img :src="module.screenshot" :alt="module.title" class="module-screenshot">
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="使用指南" name="guide" v-if="module.guide">
          <div class="tab-content">
            <div class="guide-content" v-html="module.guide"></div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="相关模块" name="related" v-if="relatedModules && relatedModules.length">
          <div class="tab-content">
            <div class="related-modules">
              <div 
                v-for="(relatedModule, index) in relatedModules" 
                :key="index"
                class="related-module-item"
                @click="$emit('view-module', relatedModule.key)"
              >
                <div class="related-module-icon" :style="{ background: relatedModule.iconBackground || defaultIconBackground }">
                  <i :class="relatedModule.icon"></i>
                </div>
                <div class="related-module-info">
                  <h4>{{ relatedModule.title }}</h4>
                  <p>{{ relatedModule.description }}</p>
                </div>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleDetail',
  props: {
    module: {
      type: Object,
      required: true
    },
    relatedModules: {
      type: Array,
      default: () => []
    },
    defaultIconBackground: {
      type: String,
      default: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }
  },
  data() {
    return {
      activeTab: 'overview'
    };
  },
  methods: {
    openModule() {
      this.$emit('open-module', this.module.key);
    }
  }
}
</script>

<style lang="less" scoped>
.module-detail {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  .detail-header {
    padding: 24px;
    background: linear-gradient(to right, #f8f9fa, white);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    
    .header-content {
      display: flex;
      align-items: center;
      
      .module-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24px;
        
        i {
          color: white;
          font-size: 32px;
        }
      }
      
      .module-info {
        h2 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
        }
        
        p {
          margin: 0 0 12px 0;
          color: #7f8c8d;
          font-size: 16px;
        }
        
        .module-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .detail-content {
    padding: 24px;
    
    .detail-tabs {
      /deep/ .el-tabs__header {
        margin-bottom: 24px;
      }
      
      /deep/ .el-tabs__item {
        font-size: 16px;
        height: 48px;
        line-height: 48px;
      }
      
      /deep/ .el-tabs__active-bar {
        background-color: #D7A256;
      }
      
      /deep/ .el-tabs__item.is-active {
        color: #D7A256;
      }
      
      /deep/ .el-tabs__item:hover {
        color: #E6B366;
      }
    }
    
    .tab-content {
      padding: 8px;
      
      .overview-section {
        margin-bottom: 32px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          margin: 0 0 16px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          
          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 18px;
            background: #D7A256;
            margin-right: 12px;
            border-radius: 2px;
          }
        }
        
        .section-text {
          margin: 0;
          color: #2c3e50;
          font-size: 15px;
          line-height: 1.6;
        }
        
        .feature-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            
            i {
              color: #D7A256;
              margin-right: 12px;
              margin-top: 3px;
            }
            
            span {
              color: #2c3e50;
              font-size: 15px;
              line-height: 1.5;
            }
          }
        }
        
        .screenshot-container {
          margin-top: 16px;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
          
          .module-screenshot {
            width: 100%;
            display: block;
          }
        }
      }
      
      .guide-content {
        color: #2c3e50;
        font-size: 15px;
        line-height: 1.6;
        
        /deep/ h3 {
          margin: 24px 0 16px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }
        
        /deep/ h4 {
          margin: 20px 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }
        
        /deep/ p {
          margin: 0 0 16px 0;
        }
        
        /deep/ ul, /deep/ ol {
          margin: 0 0 16px 0;
          padding-left: 24px;
          
          li {
            margin-bottom: 8px;
          }
        }
        
        /deep/ code {
          background: #f8f9fa;
          padding: 2px 6px;
          border-radius: 4px;
          color: #e83e8c;
          font-family: monospace;
        }
        
        /deep/ pre {
          background: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 0 0 16px 0;
          
          code {
            background: transparent;
            padding: 0;
            color: #2c3e50;
          }
        }
      }
      
      .related-modules {
        .related-module-item {
          display: flex;
          align-items: center;
          padding: 16px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 16px;
          
          &:hover {
            background: #f8f9fa;
            transform: translateX(4px);
          }
          
          .related-module-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            
            i {
              color: white;
              font-size: 24px;
            }
          }
          
          .related-module-info {
            flex: 1;
            
            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
            }
            
            p {
              margin: 0;
              color: #7f8c8d;
              font-size: 14px;
            }
          }
          
          .el-icon-arrow-right {
            color: #D7A256;
            font-size: 18px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .module-detail {
    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      
      .header-content {
        margin-bottom: 16px;
        width: 100%;
      }
      
      .header-actions {
        width: 100%;
        
        .el-button {
          flex: 1;
        }
      }
    }
  }
}
</style> 