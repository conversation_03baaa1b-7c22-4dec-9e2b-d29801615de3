<template>
  <div class="welcome-panel">
    <div class="welcome-header">
      <div class="welcome-icon">
        <i class="el-icon-s-home"></i>
      </div>
      <div class="welcome-text">
        <h2>{{ title }}</h2>
        <p>{{ subtitle }}</p>
      </div>
    </div>
    
    <div class="welcome-stats">
      <div v-for="(stat, index) in stats" :key="index" class="stat-card">
        <div class="stat-icon" :style="{ background: stat.color }">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
    
    <div class="welcome-features">
      <h3 class="section-title">
        <i class="el-icon-star-on"></i>
        系统功能
      </h3>
      <div class="features-grid">
        <div v-for="(feature, index) in features" :key="index" class="feature-item">
          <div class="feature-icon">
            <i :class="feature.icon"></i>
          </div>
          <div class="feature-content">
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="welcome-actions">
      <el-button 
        v-for="(action, index) in actions" 
        :key="index"
        :type="action.type || 'default'"
        @click="handleAction(action.key)"
      >
        <i :class="action.icon" v-if="action.icon"></i>
        {{ action.label }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WelcomePanel',
  props: {
    title: {
      type: String,
      default: '欢迎使用系统演示'
    },
    subtitle: {
      type: String,
      default: '本系统提供全面的功能展示和数据模拟'
    },
    stats: {
      type: Array,
      default: () => []
    },
    features: {
      type: Array,
      default: () => []
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleAction(key) {
      this.$emit('action', key);
    }
  }
}
</script>

<style lang="less" scoped>
.welcome-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  
  .welcome-header {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    
    .welcome-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      
      i {
        color: white;
        font-size: 32px;
      }
    }
    
    .welcome-text {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
      }
      
      p {
        margin: 0;
        color: #7f8c8d;
        font-size: 16px;
      }
    }
  }
  
  .welcome-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
    
    .stat-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        i {
          color: white;
          font-size: 20px;
        }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #7f8c8d;
        }
      }
    }
  }
  
  .section-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    
    i {
      color: #D7A256;
      margin-right: 8px;
      font-size: 20px;
    }
  }
  
  .welcome-features {
    margin-bottom: 32px;
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      
      .feature-item {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: flex-start;
        
        .feature-icon {
          width: 40px;
          height: 40px;
          background: #D7A256;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            color: white;
            font-size: 18px;
          }
        }
        
        .feature-content {
          flex: 1;
          
          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }
          
          p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }
  
  .welcome-actions {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    
    .el-button {
      i {
        margin-right: 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .welcome-panel {
    padding: 24px;
    
    .welcome-header {
      flex-direction: column;
      text-align: center;
      
      .welcome-icon {
        margin: 0 0 16px 0;
      }
    }
    
    .welcome-stats {
      grid-template-columns: 1fr;
    }
    
    .welcome-features {
      .features-grid {
        grid-template-columns: 1fr;
      }
    }
    
    .welcome-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style> 