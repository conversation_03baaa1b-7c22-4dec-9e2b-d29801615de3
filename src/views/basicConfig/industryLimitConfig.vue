<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import { getDataTemplateList, getServiceProcessList } from '@/api/basicConfig/index.js'
import { getIndustryLimitRuleDetail, getFieldOptions } from '@/api/industryLimit'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'

export default {
  name: 'IndustryLimitConfig',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        name: '',
        id: '',
        description: '',
        conditions: [],
        actionList: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '规则名称', type: 'input', placeholder: '请输入规则名称', maxlength: 50, showWordLimit: true },
              { prop: 'id', label: '规则ID', type: 'input', placeholder: '自动生成/可编辑', maxlength: 20 }
            ],
            [
              { prop: 'description', label: '描述', type: 'textarea', placeholder: '请输入描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '字段规则',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'conditions',
                label: '字段规则',
                icon: 'el-icon-s-operation',
                addText: '添加条件',
                min: 0,
                defaultRow: () => ({ field: '', operator: '', value: '' }),
                columns: [
                  {
                    prop: 'field',
                    label: '字段',
                    type: 'select',
                    width: 160,
                    options: [],
                    onChange: (value, row, index, formData) => {
                      row.value = ''
                    }
                  },
                  {
                    prop: 'operator',
                    label: '操作符',
                    type: 'select',
                    width: 120,
                    options: [
                      { label: '等于', value: 'eq' },
                      { label: '包含', value: 'contains' },
                      { label: '区间', value: 'range' }
                    ]
                  },
                  {
                    prop: 'value',
                    label: '值',
                    type: 'input',
                    minWidth: 160,
                    placeholder: '请输入值'
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.conditions.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        },
        {
          title: '执行动作',
          icon: 'el-icon-s-custom',
          fields: [
            [
              {
                type: 'list',
                prop: 'actionList',
                label: '执行动作',
                icon: 'el-icon-s-custom',
                addText: '添加动作',
                min: 0,
                defaultRow: () => ({ type: 'applyRule', serviceIds: [], ruleName: '' }),
                columns: [
                  {
                    prop: 'type',
                    label: '动作类型',
                    type: 'radio',
                    width: 200,
                    options: [
                      { label: '执行', value: 'applyRule' },
                      { label: '不执行', value: 'noAction' }
                    ]
                  },
                  {
                    prop: 'serviceIds',
                    label: '启用服务',
                    type: 'select',
                    minWidth: 180,
                    multiple: true,
                    placeholder: '请选择启用的企业服务',
                    options: [],
                    disabled: (row) => row.type !== 'applyRule'
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.actionList.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
      },
      fieldOptions: [],
      serviceOptions: []
    }
  },
  computed: {
    pageTitle() {
      return this.$route.params.id ? '编辑规则' : '新增规则'
    },
    pageIcon() {
      return 'el-icon-edit'
    },
    breadcrumbItems() {
      return [
        { text: '行业限制管理', to: { name: 'industryLimit' }, icon: 'el-icon-s-custom' },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    // 先加载字段选项和服务选项
    Promise.all([
      this.loadFieldOptions(),
      this.loadServiceOptions()
    ]).then(() => {
      // 然后再加载规则详情
      this.loadRule()
    })
    this.isView = this.$route.query.mode === 'view';
  },
  methods: {
    async loadRule() {
      const ruleId = this.$route.params.id
      if (!ruleId) return // 新增模式不需要加载数据
      
      this.loading = true
      try {
        const response = await getIndustryLimitRuleDetail(ruleId)
        if (response.code === 200) {
          const rule = response.data
          this.form = {
            name: rule.name,
            id: rule.id,
            description: rule.description,
            conditions: rule.conditions ? rule.conditions.map(c => ({ ...c })) : [],
            actionList: rule.actionList ? rule.actionList.map(a => ({
              ...a,
              serviceIds: Array.isArray(a.serviceIds)
                ? a.serviceIds.map(id => String(id))
                : (a.serviceIds ? String(a.serviceIds).split(',') : [])
            })) : []
          }
        } else {
          this.$message.error(response.message || '加载规则失败')
        }
      } catch (error) {
        console.error('加载规则失败:', error)
        this.$message.error('加载规则失败')
      } finally {
        this.loading = false
      }
    },
    async loadFieldOptions() {
      try {
        // 直接使用getFieldOptions API获取字段选项，而不是从数据模板中获取
        const res = await getFieldOptions();
        if (res && res.code === 200 && res.data) {
          this.fieldOptions = res.data;
          // 更新表单配置中的字段选项
          const fieldGroup = this.formGroups.find(group => group.title === '字段规则');
          if (fieldGroup) {
            const fieldField = fieldGroup.fields[0][0];
            fieldField.columns[0].options = this.fieldOptions.map(f => ({ label: f.name, value: f.code }));
          }
        } else {
          console.error('加载字段选项失败:', res.message);
        }
      } catch (error) {
        console.error('加载字段选项失败:', error);
      }
    },
    async loadServiceOptions() {
      const res = await getServiceProcessList()
      this.serviceOptions = res && res.data && res.data.list ? res.data.list : []
      // 更新表单配置中的服务选项
      const actionGroup = this.formGroups.find(group => group.title === '执行动作');
      if (actionGroup) {
        const actionField = actionGroup.fields[0][0];
        // value 强制为字符串，保证和serviceIds类型一致
        actionField.columns[1].options = this.serviceOptions.map(s => ({
          label: s.name,
          value: String(s.id)
        }));
      }
    },
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        // TODO: 调用保存API
        this.$message.success('保存成功')
        this.handleBack()
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      this.$router.back()
    },
    
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.industry-limit-config-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style> 