<template>
  <div class="fields-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <span class="page-title"><i class="el-icon-setting"></i> 字段管理</span>
          <p class="page-subtitle">配置和管理数据模板的字段信息</p>
        </div>
        <div class="action-section">
          <el-button @click="$router.back()" class="back-btn" icon="el-icon-arrow-left">返回</el-button>
        </div>
      </div>
    </div>
    <!-- 内容卡片 -->
    <div class="table-section">
      <div class="table-header-bar">
        <el-button type="primary" icon="el-icon-plus" class="add-btn" @click="handleAddField">添加字段</el-button>
      </div>
      <el-table
        :data="fields"
        v-loading="loading"
        stripe
        class="modern-table"
        empty-text="暂无字段数据"
      >
        <el-table-column prop="name" label="字段名称" min-width="150" align="center" />
        <el-table-column prop="code" label="字段编码" min-width="120" align="center" />
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="getFieldTypeTagType(scope.row.type)" 
              size="mini"
            >
              {{ getFieldTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="required" label="必填" width="60" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.required ? 'success' : 'info'" size="mini">
              {{ scope.row.required ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="validation" label="校验规则" min-width="120" align="center" />
        <el-table-column prop="defaultValue" label="默认值" min-width="100" align="center" />
        <el-table-column prop="options" label="选项" min-width="120" v-if="fields.some(f=>f.options)" align="center" />
        <el-table-column prop="relatedTemplateId" label="关联模板" min-width="120" v-if="fields.some(f=>f.relatedTemplateId)" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.relatedTemplateId">{{ getTemplateName(scope.row.relatedTemplateId) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" align="center" />
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" class="action-btn edit-btn" @click="handleEditField(scope.row, scope.$index)">编辑</el-button>
            <el-button type="text" icon="el-icon-delete" class="action-btn delete-btn" @click="handleDeleteField(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 字段编辑弹窗要移到table-section外部 -->
    </div>
    <el-dialog :title="editIndex === -1 ? '添加字段' : '编辑字段'" :visible.sync="fieldDialogVisible" width="600px" @close="editIndex = -1">
      <el-form ref="fieldForm" :model="currentField" :rules="fieldRules" label-width="100px">
        <el-form-item label="字段名称" prop="name">
          <el-input v-model="currentField.name" placeholder="请输入字段名称" />
        </el-form-item>
        <el-form-item label="字段编码" prop="code">
          <el-input v-model="currentField.code" placeholder="请输入字段编码" />
        </el-form-item>
        <el-form-item label="字段类型" prop="type">
          <el-select v-model="currentField.type" placeholder="请选择字段类型" class="full-width">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="选择" value="select" />
            <el-option label="日期" value="date" />
            <el-option label="文本域" value="textarea" />
            <el-option label="级联选择" value="cascader" />
            <el-option label="对象" value="object" />
            <el-option label="对象数组" value="objectArray" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="currentField.required" active-color="#67c23a" />
        </el-form-item>
        <el-form-item label="验证规则">
          <el-input v-model="currentField.validation" placeholder="请输入验证规则" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="currentField.defaultValue" placeholder="请输入默认值" />
        </el-form-item>
        <el-form-item label="选项配置" v-if="currentField.type === 'select'">
          <el-input v-model="currentField.options" placeholder="请输入选项，格式：选项1,选项2,选项3" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="关联模板" v-if="currentField.type === 'object' || currentField.type === 'objectArray'">
          <el-select v-model="currentField.relatedTemplateId" placeholder="请选择关联的数据模板" class="full-width">
            <el-option 
              v-for="template in templateOptions" 
              :key="template.id" 
              :label="template.name" 
              :value="template.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="currentField.description" placeholder="请输入字段描述" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fieldDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveField">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDataTemplateDetail, updateDataTemplate, getDataTemplateList } from '@/api/basicConfig';
export default {
  name: 'FieldsConfig',
  data() {
    return {
      fields: [],
      loading: false,
      templateId: null,
      fieldDialogVisible: false,
      currentField: {
        name: '',
        code: '',
        type: 'text',
        required: false,
        validation: '',
        defaultValue: '',
        options: '',
        description: '',
        relatedTemplateId: null
      },
      fieldRules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入字段编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择字段类型', trigger: 'change' }
        ]
      },
      editIndex: -1,
      templateOptions: []
    };
  },
  created() {
    this.templateId = this.$route.params.id;
    this.loadTemplateFields();
    this.loadTemplateOptions();
  },
  methods: {
    async loadTemplateFields() {
      this.loading = true;
      try {
        const res = await getDataTemplateDetail(this.templateId);
        if (res.code === 200 && Array.isArray(res.data.fields)) {
          this.fields = res.data.fields;
        } else {
          this.fields = [];
        }
      } catch (e) {
        this.fields = [];
      } finally {
        this.loading = false;
      }
    },
    async loadTemplateOptions() {
      try {
        const res = await getDataTemplateList({ pageSize: 100 });
        if (res.code === 200 && res.data && res.data.list) {
          // 过滤掉当前模板，防止循环引用
          this.templateOptions = res.data.list.filter(item => 
            item.id !== parseInt(this.templateId)
          );
        }
      } catch (error) {
        console.error('加载模板选项失败:', error);
        this.$message.error('加载模板选项失败');
      }
    },
    getTemplateName(templateId) {
      const template = this.templateOptions.find(t => t.id === templateId);
      return template ? template.name : '未知模板';
    },
    getFieldTypeName(type) {
      const typeMap = {
        'text': '文本',
        'number': '数字',
        'select': '选择',
        'date': '日期',
        'textarea': '文本域',
        'cascader': '级联选择',
        'object': '对象',
        'objectArray': '对象数组'
      };
      return typeMap[type] || type;
    },
    getFieldTypeTagType(type) {
      const typeMap = {
        'text': '',
        'number': 'info',
        'select': 'success',
        'date': 'warning',
        'textarea': '',
        'cascader': 'success',
        'object': 'primary',
        'objectArray': 'danger'
      };
      return typeMap[type] || '';
    },
    handleAddField() {
      this.currentField = {
        name: '',
        code: '',
        type: 'text',
        required: false,
        validation: '',
        defaultValue: '',
        options: '',
        description: '',
        relatedTemplateId: null
      };
      this.editIndex = -1;
      this.fieldDialogVisible = true;
    },
    handleEditField(row, index) {
      this.currentField = { ...row };
      this.editIndex = index;
      this.fieldDialogVisible = true;
    },
    handleDeleteField(index) {
      this.$confirm('确定要删除这个字段吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        this.fields.splice(index, 1);
        this.$message.success('删除成功');
      });
    },
    handleSaveField() {
      this.$refs.fieldForm.validate(async valid => {
        if (valid) {
          let res;
          if (this.editIndex === -1) {
            const field = { ...this.currentField };
            field.id = Date.now();
            this.fields.push(field);
          } else {
            this.$set(this.fields, this.editIndex, { ...this.currentField });
          }
          // 弹窗提交后直接入库
          res = await this.saveFieldsToServer();
          if (res && res.code === 200) {
            this.$message.success('保存成功');
            this.fieldDialogVisible = false;
            this.loadTemplateFields();
          } else {
            this.$message.error(res && res.message ? res.message : '保存失败');
          }
        }
      });
    },
    async saveFieldsToServer() {
      try {
        return await this.$options.methods.updateFields(this.templateId, this.fields);
      } catch (e) {
        return { code: 500 };
      }
    },
    async updateFields(templateId, fields) {
      // 直接调用接口保存字段
      const { updateDataTemplate } = await import('@/api/basicConfig');
      return await updateDataTemplate(templateId, { fields });
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/style/shared-styles.less";

.fields-config-container {
  min-height: 100vh;
  background: #fbf6ee;
}
.page-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #f7ecdd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-section {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 12px;
        i {
          color: #D7A256;
          font-size: 28px;
        }
      }
      .page-subtitle {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.5;
      }
    }
    .action-section {
      .back-btn {
        height: 40px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        margin-left: 12px;
      }
    }
  }
}
.table-section {
  background: white;
  overflow: hidden;
}
.table-header-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 24px 20px 24px;
}
.add-btn {
  height: 40px;
  padding: 0 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  background: #D7A256;
  border-color: #D7A256;
  transition: all 0.3s ease;
  &:hover {
    background: #E6B366;
    border-color: #E6B366;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
  }
  i {
    margin-right: 6px;
  }
}
.footer-bar {
  text-align: right;
  padding: 24px 32px 0 0;
  background: white;
}
.save-btn {
  background: #D7A256;
  border-color: #D7A256;
  color: #fff;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  margin-right: 12px;
  transition: all 0.3s;
  &:hover {
    background: #E6B366;
    border-color: #E6B366;
  }
}
.cancel-btn {
  background: #fff;
  color: #2c3e50;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s;
  &:hover {
    color: #D7A256;
    border-color: #D7A256;
  }
}
.modern-table {
  /deep/ .el-table__header-wrapper {
    .el-table__header {
      th {
        border-top: 1px solid #f7ecdd;
        background: #fbf6ee;
        font-weight: 600;
        color: #606266;
      }
    }
  }
  /deep/ .el-table__fixed-right {
    border-top: 1px solid #f7ecdd;
  }
}
</style> 