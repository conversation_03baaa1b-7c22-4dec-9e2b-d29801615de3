<template>
  <div class="fields-manager">
    <div class="fields-header">
      <div class="header-title">
        <i class="el-icon-setting"></i>
        字段配置
      </div>
      <el-button 
        v-if="!isView"
        type="primary" 
        size="small" 
        icon="el-icon-plus"
        @click="handleAddField"
      >
        添加字段
      </el-button>
    </div>
    
    <div class="fields-content">
      <el-table 
        :data="fields" 
        class="fields-table modern-table"
        row-key="id"
        border
        stripe
        size="medium"
        style="width:100%"
      >
        <el-table-column prop="name" label="字段名称" min-width="120" />
        <el-table-column prop="code" label="字段编码" min-width="120" />
        <el-table-column prop="type" label="字段类型" width="120" />
        <el-table-column prop="required" label="是否必填" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.required ? 'success' : 'info'" size="mini">
              {{ scope.row.required ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="validation" label="验证规则" min-width="150" />
        <el-table-column prop="defaultValue" label="默认值" min-width="120" />
        <el-table-column label="操作" width="120" align="center" v-if="!isView">
          <template slot-scope="scope">
            <el-button 
              type="text" 
              size="small"
              icon="el-icon-edit"
              @click="handleEditField(scope.row, scope.$index)"
              class="edit-btn action-btn"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small"
              icon="el-icon-delete"
              @click="handleDeleteField(scope.$index)"
              class="delete-btn action-btn"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 字段编辑对话框 -->
    <el-dialog
      :title="editIndex === -1 ? '添加字段' : '编辑字段'"
      :visible.sync="fieldDialogVisible"
      width="600px"
      @close="editIndex = -1"
      :close-on-click-modal="false"
      :show-close="!isView"
      :before-close="isView ? () => fieldDialogVisible = false : undefined"
    >
      <el-form 
        ref="fieldForm"
        :model="currentField"
        :rules="fieldRules"
        label-width="100px"
        :disabled="isView"
      >
        <el-form-item label="字段名称" prop="name">
          <el-input v-model="currentField.name" placeholder="请输入字段名称" :disabled="isView" />
        </el-form-item>
        
        <el-form-item label="字段编码" prop="code">
          <el-input v-model="currentField.code" placeholder="请输入字段编码" :disabled="isView" />
        </el-form-item>
        
        <el-form-item label="字段类型" prop="type">
          <el-select v-model="currentField.type" placeholder="请选择字段类型" class="full-width" :disabled="isView">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="选择" value="select" />
            <el-option label="日期" value="date" />
            <el-option label="文本域" value="textarea" />
            <el-option label="级联选择" value="cascader" />
            <el-option label="对象" value="object" />
            <el-option label="对象数组" value="objectArray" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否必填">
          <el-switch v-model="currentField.required" active-color="#67c23a" :disabled="isView" />
        </el-form-item>
        
        <el-form-item label="验证规则">
          <el-input 
            v-model="currentField.validation" 
            placeholder="请输入验证规则"
            type="textarea"
            :rows="2"
            :disabled="isView"
          />
        </el-form-item>
        
        <el-form-item label="默认值">
          <el-input v-model="currentField.defaultValue" placeholder="请输入默认值" :disabled="isView" />
        </el-form-item>
        
        <el-form-item label="选项配置" v-if="currentField.type === 'select'">
          <el-input 
            v-model="currentField.options" 
            placeholder="请输入选项，格式：选项1,选项2,选项3"
            type="textarea"
            :rows="3"
            :disabled="isView"
          />
        </el-form-item>
        
        <el-form-item label="关联模板" v-if="currentField.type === 'object' || currentField.type === 'objectArray'">
          <el-select v-model="currentField.relatedTemplateId" placeholder="请选择关联的数据模板" class="full-width" :disabled="isView">
            <el-option 
              v-for="template in templateOptions" 
              :key="template.id" 
              :label="template.name" 
              :value="template.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="currentField.description" 
            placeholder="请输入字段描述"
            type="textarea"
            :rows="2"
            :disabled="isView"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer" v-if="!isView">
        <el-button @click="fieldDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveField">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDataTemplateList } from '@/api/basicConfig';

export default {
  name: 'FieldsManager',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    currentTemplateId: {
      type: [Number, String],
      default: null
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fields: [],
      fieldDialogVisible: false,
      currentField: {
        name: '',
        code: '',
        type: 'text',
        required: false,
        validation: '',
        defaultValue: '',
        options: '',
        description: '',
        relatedTemplateId: null
      },
      fieldRules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入字段编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择字段类型', trigger: 'change' }
        ]
      },
      editIndex: -1,
      templateOptions: []
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.fields = [...newVal]
      },
      immediate: true
    },
    fields: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    },
    'currentField.type': function(newVal) {
      if (newVal === 'object' || newVal === 'objectArray') {
        this.loadTemplateOptions();
      }
    }
  },
  
  mounted() {
    this.initDefaultFields();
    this.loadTemplateOptions();
  },
  
  methods: {
    async loadTemplateOptions() {
      try {
        const res = await getDataTemplateList({ pageSize: 100 });
        if (res.code === 200 && res.data && res.data.list) {
          // 过滤掉当前模板，防止循环引用
          this.templateOptions = res.data.list.filter(item => 
            item.id !== this.currentTemplateId
          );
        }
      } catch (error) {
        console.error('加载模板选项失败:', error);
        this.$message.error('加载模板选项失败');
      }
    },
    
    initDefaultFields() {
      if (this.fields.length === 0) {
        this.fields = [
          {
            id: 1,
            name: '企业名称',
            code: 'name',
            type: 'text',
            required: true,
            validation: 'required|min:2|max:100',
            defaultValue: '',
            description: '企业全称'
          },
          {
            id: 2,
            name: '社会统一信用代码',
            code: 'creditCode',
            type: 'text',
            required: false,
            validation: 'pattern:/^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/',
            defaultValue: '',
            description: '18位社会统一信用代码'
          },
          {
            id: 3,
            name: '所属行业',
            code: 'industry',
            type: 'select',
            required: true,
            validation: 'required',
            defaultValue: '',
            options: '制造业,服务业,金融业,房地产业,信息技术,教育,医疗健康,其他',
            description: '企业所属行业分类'
          },
          {
            id: 4,
            name: '企业类型',
            code: 'enterpriseType',
            type: 'select',
            required: true,
            validation: 'required',
            defaultValue: '',
            options: '有限责任公司,股份有限公司,个人独资企业,合伙企业,国有企业,集体企业,外商投资企业,其他',
            description: '企业组织形式'
          },
          {
            id: 5,
            name: '企业所在城市',
            code: 'city',
            type: 'cascader',
            required: true,
            validation: 'required',
            defaultValue: '',
            description: '企业注册地或主要经营地'
          },
          {
            id: 6,
            name: '人员规模',
            code: 'employeeCount',
            type: 'select',
            required: true,
            validation: 'required',
            defaultValue: '',
            options: '1-50人,51-100人,101-500人,501-1000人,1001-5000人,5000人以上',
            description: '企业员工规模'
          },
          {
            id: 7,
            name: '企业年收入',
            code: 'annualRevenue',
            type: 'number',
            required: false,
            validation: 'min:0',
            defaultValue: '0',
            description: '企业年度营业收入（万元）'
          },
          {
            id: 8,
            name: '联系人姓名',
            code: 'contactPerson',
            type: 'text',
            required: true,
            validation: 'required|max:20',
            defaultValue: '',
            description: '企业联系人姓名'
          },
          {
            id: 9,
            name: '联系方式',
            code: 'contactPhone',
            type: 'text',
            required: true,
            validation: 'required|pattern:/^1[3-9]\\d{9}$/',
            defaultValue: '',
            description: '联系人手机号码'
          },
          {
            id: 10,
            name: '备注信息',
            code: 'remark',
            type: 'textarea',
            required: false,
            validation: 'max:500',
            defaultValue: '',
            description: '企业备注信息'
          }
        ]
      }
    },
    
    handleAddField() {
      if (this.isView) return;
      this.currentField = {
        name: '',
        code: '',
        type: 'text',
        required: false,
        validation: '',
        defaultValue: '',
        options: '',
        description: '',
        relatedTemplateId: null
      };
      this.editIndex = -1;
      this.fieldDialogVisible = true;
    },
    
    handleEditField(row, index) {
      if (this.isView) return;
      this.currentField = { ...row };
      if (row.type === 'object' || row.type === 'objectArray') {
        this.loadTemplateOptions();
      }
      this.editIndex = index;
      this.fieldDialogVisible = true;
    },
    
    handleDeleteField(index) {
      if (this.isView) return;
      this.$confirm('确定要删除这个字段吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        this.fields.splice(index, 1)
        this.$message.success('删除成功')
      })
    },
    
    handleSaveField() {
      if (this.isView) return;
      this.$refs.fieldForm.validate(valid => {
        if (valid) {
          if (this.editIndex === -1) {
            const field = { ...this.currentField }
            field.id = Date.now()
            this.fields.push(field)
          } else {
            this.$set(this.fields, this.editIndex, { ...this.currentField })
          }
          this.fieldDialogVisible = false
          this.$message.success('保存成功')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.fields-manager {
  padding: 24px;
  background: #fff;
  .fields-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }
  
  .fields-content {
    .fields-table {
      font-size: 14px;
      th {
        background: #f5f7fa;
        font-weight: 600;
        color: #606266;
      }
      td {
        padding: 10px 0;
      }
      .delete-btn {
        color: #f56c6c;
        &:hover {
          background: rgba(245, 108, 108, 0.1);
        }
      }
    }
  }
}

.full-width {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
.action-btn {
  padding: 6px 8px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.3s ease;
  min-width: 45px;
  height: 28px;
  line-height: 1;
  white-space: nowrap;
  flex-shrink: 0;
}
.edit-btn {
  color: #D7A256;
  &:hover {
    background: rgba(215, 162, 86, 0.1);
  }
}
.delete-btn {
  color: #f56c6c;
  &:hover {
    background: rgba(245, 108, 108, 0.1);
  }
}
</style> 