<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="700px"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      :disabled="isView"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入模板名称" />
      </el-form-item>
      <el-form-item label="模板描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
        />
      </el-form-item>
      <el-form-item label="模板类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择模板类型">
          <el-option label="企业信息" value="enterprise" />
          <el-option label="风险评估" value="risk" />
          <el-option label="财务数据" value="finance" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 字段区已移除 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" v-if="!isView">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FieldsManager from './FieldsManager.vue';
export default {
  name: 'TemplateForm',
  components: { FieldsManager },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    templateData: {
      type: Object,
      default: () => ({})
    },
    isView: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isEdit: false,
      form: {
        name: '',
        description: '',
        type: '',
        status: 1,
        fields: []
      },
      rules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
    templateData: {
      handler(val) {
        this.initForm()
      },
      deep: true
    }
  },
  methods: {
    initForm() {
      if (this.templateData && this.templateData.id) {
        this.isEdit = true
        this.form = { ...this.templateData }
        if (!Array.isArray(this.form.fields)) this.form.fields = []
      } else {
        this.isEdit = false
        this.form = {
          name: '',
          description: '',
          type: '',
          status: 1,
          fields: []
        }
      }
    },
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.form)
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
.fields-section {
  margin: 24px 0 0 0;
}
.fields-title {
  font-weight: 600;
  color: #b08a3a;
  margin-bottom: 8px;
  font-size: 15px;
}
.fields-table-view {
  margin-top: 8px;
}
</style> 