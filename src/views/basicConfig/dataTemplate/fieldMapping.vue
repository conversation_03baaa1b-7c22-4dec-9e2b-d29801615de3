<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="'el-icon-link'"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getDataTemplateDetail, saveFieldMapping } from '@/api/basicConfig'

export default {
  name: 'FieldMapping',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isView: false,
      loading: false,
      templateId: null,
      templateFields: [],
      form: {
        name: '',
        url: '',
        method: '',
        params: '',
        desc: '',
        externalFields: []
      },
      formGroups: [
        {
          title: '接口API配置',
          icon: 'el-icon-link',
          fields: [
            [
              { prop: 'name', label: '接口名称', type: 'input', required: true },
              { prop: 'url', label: '接口URL', type: 'input', required: true },
              { prop: 'method', label: '请求方式', type: 'select', options: [
                { label: 'GET', value: 'GET' },
                { label: 'POST', value: 'POST' }
              ], required: true }
            ],
            [
              { prop: 'params', label: '请求参数', type: 'input', span: 16 },
              { prop: 'desc', label: '描述', type: 'input', span: 8 }
            ]
          ]
        },
        {
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'externalFields',
                label: '外部字段映射',
                icon: 'el-icon-s-operation',
                addText: '添加外部字段',
                min: 0,
                defaultRow: () => ({ name: '', code: '', mappedField: '' }),
                columns: [
                  { prop: 'name', label: '外部字段名称', type: 'input', required: true, minWidth: 120 },
                  { prop: 'code', label: '外部字段编码', type: 'input', required: true, minWidth: 120 },
                  { prop: 'mappedField', label: '关联模板字段', type: 'select', options: [], required: true, minWidth: 180 }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    show: (row, index, isView) => !isView,
                    onClick: (row, index, formData) => {
                      formData.externalFields.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      breadcrumbItems: [
        { text: '数据模板配置', icon: 'el-icon-document', to: { name: 'dataTemplate' } },
        { text: '字段映射配置', icon: 'el-icon-link' }
      ]
    }
  },
  computed: {
    pageTitle() {
      return '字段映射配置'
    }
  },
  created() {
    this.templateId = this.$route.params.id
    this.isView = this.$route.query.mode === 'view'
    this.loadTemplateFields()
  },
  methods: {
    async loadTemplateFields() {
      this.loading = true
      try {
        const res = await getDataTemplateDetail(this.templateId)
        if (res.code === 200) {
          // 加载模板字段
          this.templateFields = Array.isArray(res.data.fields) ? res.data.fields : []
          // 设置外部字段映射下拉选项
          const mappedFieldOptions = this.templateFields.map(f => ({
            label: `${f.name}（${f.code}）`,
            value: f.code
          }))
          // 设置 options
          this.formGroups[1].fields[0][0].columns[2].options = mappedFieldOptions
          // 加载API配置
          if (res.data.apiConfig) {
            this.form = {
              ...res.data.apiConfig,
              externalFields: Array.isArray(res.data.mappings)
                ? res.data.mappings.map(m => ({
                    name: m.externalFieldName,
                    code: m.externalFieldCode,
                    mappedField: m.templateFieldCode
                  }))
                : []
            }
          } else {
            this.form.externalFields = []
          }
        } else {
          this.templateFields = []
          this.form.externalFields = []
        }
      } catch (e) {
        this.templateFields = []
        this.form.externalFields = []
        this.$message.error('加载模板字段失败')
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      // 校验
      await this.$refs.universalForm.validate()
      this.loading = true
      try {
        // 组装 API 配置和映射关系
        const apiConfig = {
          name: this.form.name,
          url: this.form.url,
          method: this.form.method,
          params: this.form.params,
          desc: this.form.desc
        }
        const mappings = (this.form.externalFields || []).map(f => ({
          externalFieldName: f.name,
          externalFieldCode: f.code,
          templateFieldCode: f.mappedField
        }))
        const res = await saveFieldMapping(this.templateId, apiConfig, mappings)
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.$router.back()
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/shared-styles.less";

.field-mapping-container {
  min-height: 100vh;
  background: #fbf6ee;
}
.page-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #f7ecdd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-section {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 12px;
        i {
          color: #D7A256;
          font-size: 28px;
        }
      }
      .page-subtitle {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.5;
      }
    }
    .action-section {
      .back-btn {
        height: 40px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        margin-left: 12px;
      }
    }
  }
}
.api-config-section {
  background: white;
  padding: 24px 32px 0 32px;
}
.api-config-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}
.api-config-form {
  margin-bottom: 0;
}
.mapping-section {
  border-top: 1px solid #f7ecdd;
  background: white;
  padding-bottom: 24px;
}
.mapping-main {
  display: flex;
  gap: 24px;
  padding: 32px 24px 0 24px;
}
.template-fields, .external-fields {
  flex: 1;
  min-width: 0;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.fields-table {
  margin-bottom: 0;
}
.footer-bar {
  text-align: right;
  padding: 24px 32px;
  background: white;
  border-radius: 0 0 12px 12px;
}
.save-btn {
  background: #D7A256;
  border-color: #D7A256;
  color: #fff;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  margin-right: 12px;
  transition: all 0.3s;
  &:hover {
    background: #E6B366;
    border-color: #E6B366;
  }
}
.cancel-btn {
  background: #fff;
  color: #2c3e50;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s;
  &:hover {
    color: #D7A256;
    border-color: #D7A256;
  }
}
.delete-btn {
  color: #f56c6c;
  &:hover {
    background: rgba(245, 108, 108, 0.1);
  }
}
.action-btn {
  padding: 6px 8px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.3s ease;
  min-width: 45px;
  height: 28px;
  line-height: 1;
  white-space: nowrap;
  flex-shrink: 0;
}
.modern-table {
  /deep/ .el-table__header-wrapper {
    .el-table__header {
      th {
        background: #fbf6ee;
        font-weight: 600;
        color: #606266;
      }
    }
  }
}
</style> 