<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
      @export-list="handleExportFields"
      @import-list="handleImportFields"
    />
    <!-- 隐藏的文件上传组件 -->
    <input
      ref="fileInput"
      type="file"
      accept=".xlsx,.xls"
      style="display: none"
      @change="handleFileChange"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import FieldsManager from './components/FieldsManager.vue'
import { getDataTemplateDetail, addDataTemplate, updateDataTemplate, importTemplateFields } from '@/api/basicConfig'
import fileDownload from "js-file-download";
import Axios from "axios";
import store from "@/store";
import {rootPath} from "@/utils/globalParam";

export default {
  name: 'DataTemplateEdit',
  components: { EditPageContainer, UniversalForm, FieldsManager },
  data() {
    return {
      downloadURL: "/api/dataTemplate/fields/export",
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: this.$route.params.id || null,
        templateName: '',
        bizCode: '',
        type: '',
        remark: '',
        status: "1",
        fields: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'templateName', label: '模板名称', type: 'input', placeholder: '请输入模板名称', maxlength: 100, showWordLimit: true, required: true },
              { prop: 'bizCode', label: '模板编码', type: 'input', placeholder: '请输入模板编码', maxlength: 50, required: true }
            ],
            [
              { prop: 'type', label: '分类', type: 'input', placeholder: '请输入分类', maxlength: 50, required: true },
              { prop: 'status', label: '状态', type: 'radio', options: [ { label: '启用', value: "1" }, { label: '禁用', value: "0" } ] }
            ],
            [
              { prop: 'remark', label: '描述', type: 'textarea', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'fields',
                label: '字段列表',
                icon: 'el-icon-s-operation',
                addText: '添加字段',
                showImportExport: true, // 显示导入导出按钮
                min: 0,
                sortable: true, // 启用排序功能
                wrapActions: true, // 启用操作按钮换行
                defaultRow: () => ({
                  fieldName: '',
                  fieldCode: '',
                  fieldType: 'varchar',
                  fieldLength: '',
                  showType: '1',
                  isIndex: '0',
                  change: '1',
                  required: '0',
                  validation: '',
                  defaultValue: '',
                  additional: '',
                  additionalPlaceholder: '请输入单位',
                  remark: ''
                }),
                columns: [
                  { prop: 'fieldName', label: '字段名称', type: 'input', required: true, width: 120 },
                  { prop: 'fieldCode', label: '字段编码', type: 'input', required: true, width: 120 },
                  {
                    prop: 'fieldType',
                    label: '字段类型',
                    type: 'select',
                    options: [
                      { label: '字符串', value: 'varchar' },
                      { label: '整数', value: 'int' },
                      { label: '浮点数', value: 'double' },
                      { label: '日期时间', value: 'datetime' },
                      { label: '日期', value: 'date' },
                      { label: '时间', value: 'time' }
                    ],
                    width: 130,
                    required: true,
                  },
                  {
                    prop: 'fieldLength',
                    label: '字段长度',
                    type: 'input',
                    width: 100,
                    required: true,
                  },
                  {
                    prop: 'isIndex',
                    label: '是否添加索引',
                    type: 'select',
                    options: [
                      { label: '是', value: '1' },
                      { label: '否', value: '0' }
                    ],
                    width: 110
                  },
                  {
                    prop: 'showType',
                    label: '展示类型',
                    type: 'select',
                    options: [
                      { label: '不可见', value: '0' },
                      { label: '输入框', value: '1' },
                      { label: '单选框', value: '2' },
                      { label: '下拉框', value: '3' },
                      { label: '日期选择', value: '4' },
                      { label: '日期范围', value: '5' },
                      { label: '文本域', value: '6' }
                    ],
                    width: 130,
                    required: true,
                    onChange: (val, row) => {
                      if (val === '1') {
                        row.additionalPlaceholder = '请输入单位';
                      } else if (val === '3') {
                        row.additionalPlaceholder = '请输入下拉取值，逗号分隔';
                      } else if (val === '0') {
                        row.additionalPlaceholder = '不需要输入';
                        row.change = '0';
                        row.validation = '';
                        row.additional = '';
                      } else {
                        row.additionalPlaceholder = '不需要输入';
                      }
                    }
                  },
                  {
                    prop: 'change',
                    label: '是否可修改',
                    type: 'select',
                    options: [
                      { label: '是', value: '1' },
                      { label: '否', value: '0' }
                    ],
                    width: 110,
                    disabled: row => row.showType === '0'
                  },
                  {
                    prop: 'required',
                    label: '必填',
                    type: 'select',
                    options: [ { label: '是', value: '1' }, { label: '否', value: '0' } ],
                    width: 110,
                    disabled: row => row.showType === '0'
                  },
                  {
                    prop: 'validation',
                    label: '校验规则',
                    type: 'input',
                    width: 150,
                    disabled: row => row.showType === '0'
                  },
                  {
                    prop: 'defaultValue',
                    label: '默认值',
                    type: 'input',
                    width: 80,
                    disabled: row => row.showType === '0'
                  },
                  {
                    prop: 'additional',
                    label: '附加属性',
                    type: 'input',
                    width: 250,
                    placeholder: row => row.additionalPlaceholder || '请输入单位',
                    disabled: row => row.showType === '0'
                  },
                  { prop: 'remark', label: '描述', type: 'input', width: 200 }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    show: (row, index, isView) => !isView,
                    onClick: (row, index, formData) => {
                      formData.fields.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        templateName: [ { required: true, message: '请输入模板名称', trigger: 'blur' } ],
        bizCode: [ { required: true, message: '请输入模板编码', trigger: 'blur' } ],
        type: [ { required: true, message: '请输入分类', trigger: 'blur' } ]
      }
    }
  },
  computed: {
    pageTitle() {
      return this.isView ? '查看数据模板' : (this.isEdit ? '编辑数据模板' : '新增数据模板')
    },
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    breadcrumbItems() {
      return [
        { text: '数据模板配置', icon: 'el-icon-document-copy', to: { name: 'dataTemplate' } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    if (id) {
      this.isEdit = mode !== 'view'
      this.isView = mode === 'view'
      this.loadData(id)
    }
  },
  methods: {
    async loadData(id) {
      this.loading = true
      try {
        const response = await getDataTemplateDetail(id)
        if (response) {
          // 回显处理additional
          if (Array.isArray(response.fields)) {
            response.fields = response.fields.map(f => {
              if (f.showType === '1' && f.additional && f.additional.inputUnit) {
                f.additional = f.additional.inputUnit
              } else if (f.showType === '3' && f.additional && Array.isArray(f.additional.selectOptions)) {
                f.additional = f.additional.selectOptions.join(',')
              } else {
                f.additional = ''
              }
              return f
            })
          }
          this.form = { ...response }
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        // 校验 fieldCode 不能重复
        if (Array.isArray(this.form.fields)) {
          const codes = this.form.fields.map(f => f.fieldCode && f.fieldCode.trim()).filter(Boolean)
          const codeSet = new Set(codes)
          if (codes.length !== codeSet.size) {
            this.$message.error('字段编码（fieldCode）不能重复！')
            return
          }
        }
        this.loading = true
        // 保存前处理additional，使用新变量，避免污染页面数据
        let fields = []
        if (Array.isArray(this.form.fields)) {
          fields = this.form.fields.map(f => {
            const newField = { ...f }
            if (f.additional) {
              if (f.showType === '1') {
                newField.additional = { inputUnit: f.additional || '' }
              } else if (f.showType === '3') {
                newField.additional = { selectOptions: (f.additional || '').split(',').map(s => s.trim()).filter(Boolean) }
              } else {
                newField.additional = {}
              }
            } else {
              newField.additional = {}
            }
            return newField
          })
        }
        // 构造新form对象用于保存
        const saveForm = { ...this.form, fields }
        let response
        if (this.isEdit) {
          response = await updateDataTemplate(saveForm)
        } else {
          response = await addDataTemplate(saveForm)
        }
        console.log("saveDataTemplate response", response)
        if (response) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          this.$router.back()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    },
    // 导出字段列表
    async handleExportFields() {
          let fileurl = rootPath + this.downloadURL;
          const loadingInstance = this.$loading({
            fullscreen: true,
            lock: true,
            text: "加载中...",
            target: document.getElementsByTagName("body")[0]
          });
          Axios({
            method: "post",
            url: fileurl,
            headers: {
              access_token: sessionStorage.getItem("LoginAccessToken"),
              tenantId:
                store.state.layoutStore.currentLoginUser.tenantId ||
                sessionStorage.getItem("tenantId"),
              funcId:
                store.state.layoutStore.currentLoginUser.funcId ||
                sessionStorage.getItem("funcId")
            },
            data: {id: this.form.id},
            responseType: "blob"
          }).then(res => {
            if (res.status === 200) {
              fileDownload(res.data, decodeURI(res.headers["file-name"] || "数据明细导出.xlsx"));
              setTimeout(function() {
                loadingInstance.close();
              }, 1000);
            } else {
              loadingInstance.close();
            }
          }).catch(() => {
            loadingInstance.close();
            this.$message.error("导出失败");
          });
        },
    // 导入字段列表
    handleImportFields(field) {
      if (!this.form.id) {
        this.$message.warning('请先保存模板后再导入字段')
        return
      }
      this.$refs.fileInput.click()
    },
    // 处理文件选择
    async handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        this.loading = true
        const response = await importTemplateFields(this.form.id, file)

        // 检查返回的数据
        if (response && response.length > 0) {
          // 将导入的数据覆盖到字段列表中，并处理additional字段的回显
          this.form.fields = response.map(field => {
            let additional = ''
            // 处理additional字段的回显，与loadData方法保持一致
            if (field.showType === '1' && field.additional && field.additional.inputUnit) {
              additional = field.additional.inputUnit
            } else if (field.showType === '3' && field.additional && Array.isArray(field.additional.selectOptions)) {
              additional = field.additional.selectOptions.join(',')
            } else if (typeof field.additional === 'string') {
              additional = field.additional
            }

            return {
              id: field.id,
              fieldName: field.fieldName,
              fieldCode: field.fieldCode,
              fieldType: field.fieldType,
              fieldLength: field.fieldLength,
              isIndex: field.isIndex,
              showType: field.showType,
              required: field.required,
              defaultValue: field.defaultValue,
              validation: field.validation,
              change: field.change,
              additional: additional,
              additionalPlaceholder: field.showType === '1' ? '请输入单位' :
                                   field.showType === '3' ? '请输入下拉取值，逗号分隔' : '不需要输入',
              remark: field.remark,
              sort: field.sort
            }
          })

          this.$message.success('导入成功，字段列表已更新，请保存模板以确认修改')
        } else {
          this.$message.warning('导入的文件中没有有效的字段数据')
        }
      } catch (error) {
        this.$message.error('导入失败')
        console.error('导入失败:', error)
      } finally {
        this.loading = false
        // 清空文件输入
        event.target.value = ''
      }
    }
  }
}
</script>
