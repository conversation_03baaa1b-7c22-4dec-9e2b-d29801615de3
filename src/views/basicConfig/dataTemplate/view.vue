<template>
  <div class="data-template-view-container">
    <DataTemplateView
      :template-code="templateCode"
      :template-name="templateName"
      :show-breadcrumb="true"
      :breadcrumb-items="breadcrumbItems"
      :show-add-button="false"
      :show-actions="false"
      :show-selection="false"
      @breadcrumb-click="handleBreadcrumbClick"
    />
  </div>
</template>

<script>
import DataTemplateView from '@/components/business/DataTemplateView.vue'

export default {
  name: 'DataTemplateViewPage',
  components: { DataTemplateView },
  data() {
    return {
      templateCode: '',
      templateName: ''
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { text: '数据模板配置', icon: 'el-icon-document-copy', to: 'dataTemplate' },
        { text: '查看数据', icon: 'el-icon-view' }
      ]
    }
  },
  created() {
    const templateCode = this.$route.params.templateCode
    const templateName = this.$route.query.templateName

    if (templateCode) {
      this.templateCode = templateCode
      this.templateName = templateName || ''
    }
  },
  methods: {
    handleBreadcrumbClick(item) {
      console.log('面包屑点击:', item);
      // 面包屑点击事件会自动处理路由跳转
    }
  }
}
</script>

<style lang="less" scoped>
.data-template-view-container {
  height: 100vh;
  overflow: hidden;

  .template-info-card {
    background: rgba(215, 162, 86, 0.1);
    border: 1px solid rgba(215, 162, 86, 0.2);
    border-radius: 6px;
    padding: 12px 16px;

    .info-content {
      display: flex;
      align-items: center;
      gap: 24px;
      flex-wrap: wrap;

      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .label {
          font-size: 12px;
          color: #8b7355;
          font-weight: 500;
        }

        .value {
          font-size: 13px;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
