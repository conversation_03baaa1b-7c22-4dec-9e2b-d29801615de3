<template>
  <div class="data-template-container">
    <UniversalTable
      title="数据模板配置"
      subtitle="管理和配置企业信息、客户机会等数据模板，支持动态字段管理"
      title-icon="el-icon-document"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="400"
      :search-label-width="'100px'"
      add-button-text="新增模板"
      empty-title="暂无模板数据"
      empty-description="点击上方新增模板按钮开始创建"
      show-selection
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
      @selection-change="handleSelectionChange"
    >
      <template #category="{ row }">
        <el-tag :type="getCategoryTagType(row.type)" size="small">{{ row.type }}</el-tag>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status === '1' ? 'success' : 'danger'" size="small">
          <i :class="row.status === '1' ? 'el-icon-check' : 'el-icon-close'"></i>
          {{ row.status === '1' ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.updateTime }}
        </div>
      </template>
      <template #fields="{ row }">
        <el-tag type="info" size="small">{{ row.fields ? row.fields.length : 0 }} 个</el-tag>
      </template>
      <template #relatedTemplates="{ row }">
        <div v-if="getRelatedTemplates(row).length > 0" class="related-templates">
          <el-tooltip v-for="template in getRelatedTemplates(row)" :key="template.id" :content="template.fieldName + ' -> ' + template.name" placement="top">
            <el-tag size="small" type="primary" style="margin-right: 5px; margin-bottom: 5px;">{{ template.name }}</el-tag>
          </el-tooltip>
        </div>
        <span v-else>-</span>
      </template>
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue';
import { getDataTemplateList, deleteDataTemplate } from '@/api/basicConfig';

export default {
  name: 'DataTemplate',
  components: { UniversalTable },
  data() {
    return {
      searchForm: {
        param: {
          templateName: '',
          type: '',
          status: ''
        }
      },
      tableData: [],
      loading: false,
      selectedRows: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableColumns: [
        { prop: 'templateName', label: '模板名称', minWidth: 180, align: 'center' },
        { prop: 'bizCode', label: '模板编号', minWidth: 120, align: 'center' },
        { prop: 'remark', label: '描述', minWidth: 250, align: 'center' },
        { prop: 'type', label: '分类', width: 120, align: 'center', slot: true },
        { prop: 'status', label: '状态', width: 100, align: 'center', slot: true },
        { prop: 'updateTime', label: '更新时间', width: 180, align: 'center', slot: true }
      ],
      tableActions: [
        { key: 'view', label: '查看数据', icon: 'el-icon-view', class: 'view-btn', size: 'mini' },
        { key: 'config', label: '配置', icon: 'el-icon-setting', class: 'config-btn', size: 'mini' },
        { key: 'mapping', label: '字段映射', icon: 'el-icon-link', class: 'mapping-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ],
      searchFormConfig: [
        { label: '模板名称', name: 'templateName', type: 'input', placeholder: '请输入模板名称' },
        { label: '分类', name: 'type', type: 'input', placeholder: '请输入分类' }
      ]
    };
  },
  mounted() {
    console.log('index.vue searchFormConfig:', this.searchFormConfig);
    this.loadTableData();
  },
  methods: {
    async loadTableData() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        };
        const response = await getDataTemplateList(params);
        console.log('loadTableData response:', response);
        if (response) {
          this.tableData = response.list || [];
          this.pagination.total = response.total || 0;
        }
      } catch (error) {
        this.$message.error('加载数据失败');
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },
    handleSearch(searchData) {
      this.searchForm = { ...searchData };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleReset() {
      this.searchForm = { name: '', category: '', status: '' };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleAdd() {
      this.$router.push({ name: 'dataTemplateEdit' });
    },
    handleAction({ action, row }) {
      if (action === 'view') {
        this.$router.push({
          name: 'dataTemplateView',
          params: { templateCode: row.bizCode },
          query: { templateName: row.templateName }
        });
      } else if (action === 'config') {
        this.$router.push({ name: 'dataTemplateEdit', params: { id: row.id } });
      } else if (action === 'mapping') {
        this.$router.push(`/data-template/field-mapping/${row.id}`);
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadTableData();
    },
    getCategoryTagType(category) {
      const typeMap = {
        '企业信息': 'primary',
        '客户机会': 'success',
        '产品信息': 'warning',
        '合同信息': 'info'
      };
      return typeMap[category] || 'default';
    },
    getRelatedTemplates(template) {
      const relatedTemplates = [];
      if (template.fields && template.fields.length > 0) {
        template.fields.forEach(field => {
          if ((field.type === 'object' || field.type === 'objectArray') && field.relatedTemplate) {
            relatedTemplates.push({
              id: field.relatedTemplate.id,
              name: field.relatedTemplate.name,
              fieldName: field.name
            });
          }
        });
      }
      return relatedTemplates;
    },
    async handleDelete(row) {
      try {
        await this.$confirm(`确定要删除模板\"${row.templateName}\"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        const response = await deleteDataTemplate({ id: row.id });
        if (response) {
          this.$message.success('删除成功');
          this.loadTableData();
        } else {
          this.$message.error((response && response.message) || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
          console.error('删除失败:', error);
        }
      }
    }
  }
};
</script>
