<template>
  <div class="steps-manager">
    <div class="steps-header">
      <h3>流程步骤管理</h3>
      <el-button type="primary" size="small" @click="addStep">新增步骤</el-button>
    </div>
    
    <el-table :data="steps" style="width: 100%">
      <el-table-column prop="order" label="序号" width="80" />
      <el-table-column prop="name" label="步骤名称" />
      <el-table-column prop="type" label="步骤类型">
        <template slot-scope="scope">
          <el-tag :type="getStepTypeTag(scope.row.type)">
            {{ getStepTypeName(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="负责人" />
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" @click="editStep(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteStep(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 步骤表单对话框 -->
    <el-dialog
      :title="isEdit ? '编辑步骤' : '新增步骤'"
      :visible.sync="stepFormVisible"
      width="500px"
    >
      <el-form ref="stepForm" :model="stepForm" :rules="stepRules" label-width="100px">
        <el-form-item label="步骤名称" prop="name">
          <el-input v-model="stepForm.name" placeholder="请输入步骤名称" />
        </el-form-item>
        <el-form-item label="步骤类型" prop="type">
          <el-select v-model="stepForm.type" placeholder="请选择步骤类型">
            <el-option label="数据采集" value="data" />
            <el-option label="风险评估" value="risk" />
            <el-option label="审批确认" value="approval" />
            <el-option label="结果输出" value="output" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="assignee">
          <el-input v-model="stepForm.assignee" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="stepForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stepFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitStep">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StepsManager',
  props: {
    processId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      steps: [],
      stepFormVisible: false,
      isEdit: false,
      stepForm: {
        name: '',
        type: '',
        assignee: '',
        status: 1,
        order: 1
      },
      stepRules: {
        name: [
          { required: true, message: '请输入步骤名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择步骤类型', trigger: 'change' }
        ],
        assignee: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadSteps()
  },
  methods: {
    loadSteps() {
      // 模拟加载步骤数据
      this.steps = [
        {
          id: 1,
          name: '数据采集',
          type: 'data',
          assignee: '张三',
          status: 1,
          order: 1
        },
        {
          id: 2,
          name: '风险评估',
          type: 'risk',
          assignee: '李四',
          status: 1,
          order: 2
        },
        {
          id: 3,
          name: '审批确认',
          type: 'approval',
          assignee: '王五',
          status: 1,
          order: 3
        }
      ]
    },
    getStepTypeName(type) {
      const typeMap = {
        data: '数据采集',
        risk: '风险评估',
        approval: '审批确认',
        output: '结果输出'
      }
      return typeMap[type] || type
    },
    getStepTypeTag(type) {
      const tagMap = {
        data: 'primary',
        risk: 'warning',
        approval: 'success',
        output: 'info'
      }
      return tagMap[type] || 'info'
    },
    addStep() {
      this.isEdit = false
      this.stepForm = {
        name: '',
        type: '',
        assignee: '',
        status: 1,
        order: this.steps.length + 1
      }
      this.stepFormVisible = true
    },
    editStep(step) {
      this.isEdit = true
      this.stepForm = { ...step }
      this.stepFormVisible = true
    },
    deleteStep(step) {
      this.$confirm('确定要删除这个步骤吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.steps.findIndex(s => s.id === step.id)
        if (index > -1) {
          this.steps.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },
    submitStep() {
      this.$refs.stepForm.validate(valid => {
        if (valid) {
          if (this.isEdit) {
            const index = this.steps.findIndex(s => s.id === this.stepForm.id)
            if (index > -1) {
              this.steps.splice(index, 1, { ...this.stepForm })
            }
          } else {
            this.stepForm.id = Date.now()
            this.steps.push({ ...this.stepForm })
          }
          this.stepFormVisible = false
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
        }
      })
    }
  }
}
</script>

<style scoped>
.steps-manager {
  padding: 20px;
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.steps-header h3 {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}
</style> 