<template>
  <el-dialog
    :title="isEdit ? '编辑流程' : '新增流程'"
    :visible.sync="visible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="流程名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入流程名称" />
      </el-form-item>
      <el-form-item label="流程描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入流程描述"
        />
      </el-form-item>
      <el-form-item label="流程类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择流程类型">
          <el-option label="风险评估" value="risk" />
          <el-option label="审批流程" value="approval" />
          <el-option label="数据采集" value="data" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProcessForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    processData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isEdit: false,
      form: {
        name: '',
        description: '',
        type: '',
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入流程名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择流程类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      if (this.processData.id) {
        this.isEdit = true
        this.form = { ...this.processData }
      } else {
        this.isEdit = false
        this.form = {
          name: '',
          description: '',
          type: '',
          status: 1
        }
      }
    },
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.form)
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 