<template>
  <div class="service-process-container">
    <UniversalTable
      title="服务流程配置"
      subtitle="管理和配置员工福利、企业保障、线上产品等服务流程"
      title-icon="el-icon-s-operation"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="220"
      :search-label-width="'100px'"
      add-button-text="新增流程"
      empty-title="暂无流程数据"
      empty-description="点击上方新增流程按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue';
import { getServiceProcessList, deleteServiceProcess } from '@/api/basicConfig';
export default {
  name: 'ServiceProcess',
  components: { UniversalTable },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        name: '',
        category: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableColumns: [
        { prop: 'name', label: '流程名称', minWidth: 180, align: 'center' },
        { prop: 'code', label: '流程编码', minWidth: 120, align: 'center' },
        { prop: 'description', label: '描述', minWidth: 250, align: 'center' },
        { prop: 'category', label: '分类', width: 120, align: 'center' },
        { prop: 'status', label: '状态', width: 100, align: 'center' },
        { prop: 'updateTime', label: '更新时间', width: 180, align: 'center' }
      ],
      tableActions: [
        { key: 'config', label: '配置', icon: 'el-icon-setting', class: 'config-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ],
      searchFormConfig: [
        { label: '流程名称', name: 'name', type: 'input', placeholder: '请输入流程名称' },
        { label: '分类', name: 'category', type: 'input', placeholder: '请输入分类' }
      ],
      selectedRows: []
    }
  },
  methods: {
    handleSearch(searchData) {
      this.searchForm = { ...searchData };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleReset() {
      this.searchForm = { name: '', category: '' };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleAdd() {
      // 跳转到新增流程配置页面
      this.$router.push({ name: 'ServiceProcessConfig' });
    },
    handleAction({ action, row }) {
      if (action === 'config') {
        this.$router.push({ name: 'ServiceProcessConfig', params: { id: row.id } });
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadTableData();
    },
    async loadTableData() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        };
        const response = await getServiceProcessList(params);
        if (response.code === 200) {
          this.tableData = response.data.list;
          this.pagination.total = response.data.total;
        } else {
          this.$message.error(response.message || '加载数据失败');
        }
      } catch (error) {
        this.$message.error('加载数据失败');
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm(`确定要删除流程“${row.name}”吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        const response = await deleteServiceProcess(row.id);
        if (response.code === 200) {
          this.$message.success('删除成功');
          this.loadTableData();
        } else {
          this.$message.error(response.message || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
          console.error('删除失败:', error);
        }
      }
    }
  },
  mounted() {
    this.loadTableData();
  }
}
</script>

<style scoped>
.service-process-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style> 