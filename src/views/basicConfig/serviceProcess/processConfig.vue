<!-- eslint-disable no-self-assign -->
<template>
  <!-- 流程配置页面 -->
  <div class="match-source-container">
    <!-- 页面头部和导航区域 -->
    <SecondaryPageHeader
      title="流程配置画布"
      subtitle="拖拽节点连线，编排企业服务流程"
      icon="el-icon-s-operation"
      :show-actions="false"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布区域最大化 -->
      <div class="canvas-container">
        <!-- 左侧组件面板 -->
        <ComponentPanel
          :visible="showComponentDrawer"
          :input-data-list="paneList"
          :process-data-list="paneList1"
          @close="showComponentDrawer = false"
          @drag-start="drag"
          @drag-end="handleDragEnd"
          @item-hover="handleItemHover"
          @section-toggle="handleSectionToggle"
        />

        <div
          id="flowWrap"
          ref="flowWrap"
          class="flow-canvas"
          @drop="drop($event)"
          @dragover="allowDrop($event)"
        >
          <!-- 悬浮标题 -->
          <div class="floating-title">
            <div class="title-content">
              <span class="subtitle-text">拖拽节点编排服务流程</span>
            </div>
          </div>

          <div id="flow" class="flow-content">
            <!-- 辅助线 -->
            <div
              v-show="auxiliaryLine.isShowXLine"
              :style="{
                borderColor: themeObj.color,
                width: auxiliaryLinePos.width,
                top: auxiliaryLinePos.y + 'px',
                left: auxiliaryLinePos.offsetX + 'px'
              }"
              class="auxiliary-line-x"
            />
            <div
              v-show="auxiliaryLine.isShowYLine"
              :style="{
                borderColor: themeObj.color,
                height: auxiliaryLinePos.height,
                left: auxiliaryLinePos.x + 'px',
                top: auxiliaryLinePos.offsetY + 'px'
              }"
              class="auxiliary-line-y"
            />
            
            <!-- 节点列表 -->
            <nodeItem
              v-for="item in data.nodeList"
              :id="item.nodeId"
              :key="item.nodeId"
              :node="item"
              :active-node-id="activeNodeId"
              @deleteNode="deleteNode"
              @changeLineState="changeLineState"
              @nodeClick="handleNodeClick"
            />

            <!-- 空画布提示 -->
            <div v-if="data.nodeList.length === 0" class="empty-canvas">
              <div class="empty-content">
                <i class="el-icon-edit-outline empty-icon"></i>
                <h3>开始构建您的服务流程</h3>
                <p>从左侧拖拽节点到画布中开始配置</p>
              </div>
            </div>
          </div>
        </div>

          <!-- 底部悬浮工具栏 -->
          <div class="floating-toolbar">
            <div class="toolbar-content">
              <el-button
                type="text"
                class="toolbar-icon"
                :class="{ active: showComponentDrawer }"
                @click="toggleComponentDrawer"
                :title="showComponentDrawer ? '关闭组件面板 (Esc)' : '打开组件面板 (Ctrl+M)'"
              >
                <i class="el-icon-menu"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="clearCanvas"
                title="清空画布"
              >
                <i class="el-icon-delete"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="autoLayout"
                title="自动布局"
              >
                <i class="el-icon-s-grid"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="resetProcessConfig"
                title="重置配置"
              >
                <i class="el-icon-refresh-left"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon primary save-btn"
                @click="saveProcessConfig"
                title="保存配置"
              >
                <i class="el-icon-upload"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="testConnections"
                title="测试连线"
              >
                <i class="el-icon-connection"></i>
              </el-button>
          </div>
        </div>

        <!-- 底部配置抽屉组件 -->
        <ConfigDrawer
          :visible="showConfigDrawer"
          :selected-node="selectedNode"
          :active-tab="select"
          :type-get="typeGet"
          :type-title="typeTitle"
          :id-key="idKey"
          :id-name="idName"
          :data-item="dataItem"
          :tool-type="toolType"
          :out-put-list="outPutList"
          :filed-out-put-list="filedOutPutList"
          :child-list="childList"
          :filed-list="filedList"
          @close="closeConfigDrawer"
          @tab-change="handleTabChange"
          @changefiled="filedChange"
          @outPutChange="outPutChange"
          @outPutAdd="outPutAdd"
          @outPutDel="outPutDel"
          @outGroupChange="outGroupChange"
          @outGroupAdd="outGroupAdd"
          @outGroupDel="outGroupDel"
          @outConnectChange="outConnectChange"
          @outConnectAdd="outConnectAdd"
          @outConnectDel="outConnectDel"
          @outCoFnChange="outCoFnChange"
          @outCoFnAdd="outCoFnAdd"
          @outCoFnDel="outCoFnDel"
          @outFilterChange="outFilterChange"
          @outFilterAdd="outFilterAdd"
          @outFilterDel="outFilterDel"
        />
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />

    <!-- 清空画布确认弹窗 -->
    <ConfirmDialog
      ref="clearCanvasDialog"
      title="确认清空画布"
      message="确认清空画布吗？此操作将删除所有节点和连线。"
      icon="el-icon-warning"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="confirmClearCanvas"
    />

    <!-- 保存弹窗 -->
    <el-dialog
      :visible.sync="showAdd"
      title="保存配置"
      size="small"
      width="30%"
      :append-to-body="true"
      :footer="false"
      :center="true"
      class="modern-dialog"
    >
      <el-form
        :model="addForm"
        label-width="70px"
        ref="addUserForm"
        label-position="left"
      >
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="addForm.remark" 
            placeholder="请输入备注信息"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          plain
          :style="{
            color: $store.state.layoutStore.themeObj.color,
            textAlign: 'center'
          }"
          @click="closePopup"
        >
          取消
        </el-button>
        <el-button type="primary" @click="toAddForm">
          保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { guid } from "@/utils/utils";
import { jsPlumb } from "jsplumb";
import nodeItem from "@/components/canvas/nodeItem";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
import ComponentPanel from "@/components/canvas/ComponentPanel";
import ConfigDrawer from "@/components/canvas/ConfigDrawer";
import ConfirmDialog from "@/components/layouts/ConfirmDialog";
import { getProcessConfig } from '@/api/basicConfig';

export default {
  name: "processConfig",
  components: {
    nodeItem,
    SecondaryPageHeader,
    ComponentPanel,
    ConfigDrawer,
    ConfirmDialog
  },
  provide() {
    return {
      baseInfoConfig: this
    };
  },
  data() {
    return {
      jsPlumb: null,
      data: {
        nodeList: []
      },
      // 添加连线源配置
      jsplumbSourceOptions: {
        filter: ".anchor-point, .anchor-dot, .anchor-ring", 
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("已达到最大连接数 (" + info.maxConnections + ")");
        },
        paintStyle: { 
          fill: "rgba(215, 162, 86, 0.8)",
          outlineStroke: "rgba(215, 162, 86, 0.8)",
          strokeWidth: 1 
        },
        hoverPaintStyle: { 
          fill: "rgba(215, 162, 86, 1)",
          outlineStroke: "rgba(215, 162, 86, 1)",
          strokeWidth: 2 
        },
        connector: ["Flowchart", { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }],
        connectorStyle: { stroke: "#D7A256", strokeWidth: 2 },
        connectorHoverStyle: { stroke: "#E6B366", strokeWidth: 4 }
      },
      // 添加连线目标配置
      jsplumbTargetOptions: {
        filter: ".anchor-point, .anchor-dot, .anchor-ring",
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("已达到最大连接数 (" + info.maxConnections + ")");
        },
        dropOptions: { hoverClass: "drop-hover" },
        paintStyle: { 
          fill: "rgba(215, 162, 86, 0.8)",
          outlineStroke: "rgba(215, 162, 86, 0.8)",
          strokeWidth: 1 
        },
        hoverPaintStyle: { 
          fill: "rgba(215, 162, 86, 1)", 
          outlineStroke: "rgba(215, 162, 86, 1)",
          strokeWidth: 2 
        }
      },
      processInfo: {
        id: '',
        name: '',
        code: '',
        description: '',
        category: '',
        status: 1,
        version: '1.0.0',
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      connectionList: [],
      paneList: [
        {
          logImg: "el-icon-video-play",
          nodeName: "开始",
          nodeType: "-1",
          type: "-1"
        },
        {
          logImg: "el-icon-video-pause",
          nodeName: "结束",
          nodeType: "-2",
          type: "-2"
        },
        {
          logImg: "el-icon-s-unfold",
          nodeName: "自定义节点",
          nodeType: "5",
          type: "1"
        }
      ],
      paneList1: [
        {
          logImg: "el-icon-search",
          nodeName: "企业KYC查询",
          nodeType: "kyc",
          type: "kyc"
        },
        {
          logImg: "el-icon-s-check",
          nodeName: "企业合规审批",
          nodeType: "approval",
          type: "approval"
        },
        {
          logImg: "el-icon-document",
          nodeName: "企业问卷收集",
          nodeType: "questionnaire",
          type: "questionnaire"
        },
        {
          logImg: "el-icon-warning-outline",
          nodeName: "企业风险管理方案",
          nodeType: "risk_management",
          type: "risk_management"
        }
      ],
      showComponentDrawer: false,
      showConfigDrawer: false,
      activeNodeId: null,
      selectedNode: null,
      toolType: "add",
      outPutList: [],
      filedOutPutList: [],
      childList: [],
      filedList: [],
      select: "1",
      typeGet: null,
      typeTitle: "基本信息",
      idKey: "",
      idName: "",
      dataItem: {},
      breadcrumbItems: [
        { text: '基础配置', to: { name: 'serviceProcess' } },
        { text: '流程配置画布' }
      ],
      auxiliaryLine: {
        isShowXLine: false,
        isShowYLine: false
      },
      auxiliaryLinePos: {
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        offsetX: 0,
        offsetY: 0
      },
      showAdd: false,
      addForm: {
        remark: ""
      },
      deleteNodeId: "",
      tipShown: false // 控制线段删除提示的显示
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  created() {
    const id = this.$route.params.id;
    if (id) {
      this.loadProcessConfig(id);
    } else {
      this.data.nodeList = [];
      // 可选：自动添加开始/结束节点
    }
  },
  mounted() {
    // 初始化jsPlumb实例，它会在ready回调中加载数据
    this.initNode();
    this.bindKeyboardEvents();
    
    // 自动添加开始和结束节点
    this.$nextTick(() => {
      setTimeout(() => {
        this.initRequiredNodes();
      }, 500);
    });
  },
  beforeDestroy() {
    this.unbindKeyboardEvents();
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'serviceProcess' });
    },
    
    toggleComponentDrawer() {
      this.showComponentDrawer = !this.showComponentDrawer;
    },
    
    handleItemHover(item, isHover) {
      // 处理组件悬停
    },
    
    handleSectionToggle(section, isExpanded) {
      // 处理区域展开/收起
    },
    
    drag(event, item) {
      event.dataTransfer.setData('nodeType', item.type);
      event.dataTransfer.setData('nodeName', item.nodeName);
      event.dataTransfer.setData('logImg', item.logImg);
    },
    
    allowDrop(event) {
      event.preventDefault();
    },
    
    drop(event) {
      event.preventDefault();
      const nodeType = event.dataTransfer.getData('nodeType');
      const nodeName = event.dataTransfer.getData('nodeName');
      const logImg = event.dataTransfer.getData('logImg');
      const canvasRect = this.$refs.flowWrap.getBoundingClientRect();
      const left = event.clientX - canvasRect.left;
      const top = event.clientY - canvasRect.top;
      const nodeId = guid();
      
      this.data.nodeList.push({
        nodeId,
        nodeType,
        nodeName,
        logImg,
        positionLeft: left + 'px',
        positionTop: top + 'px',
        fixed: false
      });
      
      this.$nextTick(() => {
        this.initJsPlumb();
      });
    },
    
    handleNodeClick(node) {
      this.selectedNode = node;
      this.activeNodeId = node.nodeId;
      this.showConfigDrawer = true;
    },
    
    deleteNode(nodeId) {
      const node = this.data.nodeList.find(n => n.nodeId === nodeId);
      // 如果是开始或结束节点，不允许删除
      if (node && (node.nodeType === "-1" || node.nodeType === "-2")) {
        this.$message.warning("开始和结束节点不能删除");
        return;
      }
      this.deleteNodeId = nodeId;
      this.$refs.confirmDialog.show();
    },
    confirmUpdate() {
      const index = this.data.nodeList.findIndex(node => node.nodeId === this.deleteNodeId);
      if (index > -1) {
        this.data.nodeList.splice(index, 1);
        this.activeNodeId = '';
        this.$nextTick(() => {
          this.initJsPlumb();
        });
        this.$message.success('节点删除成功');
      }
      this.$refs.confirmDialog.hide();
    },
    
    clearCanvas() {
      if (this.data.nodeList.length === 0) {
        this.$message.info('画布已经是空的了');
        return;
      }
      this.$refs.clearCanvasDialog.show();
    },
    
    confirmClearCanvas() {
      // 保留开始和结束节点
      this.data.nodeList = this.data.nodeList.filter(node => 
        node.nodeType === "-1" || node.nodeType === "-2"
      );
      
      this.activeNodeId = '';
      this.$nextTick(() => {
        this.initJsPlumb();
      });
      this.$refs.clearCanvasDialog.hide();
      this.$message.success('画布已清空');
    },
    
    autoLayout() {
      if (this.data.nodeList.length === 0) {
        this.$message.info('画布中没有节点');
        return;
      }
      
      const gapX = 200;
      const startX = 100;
      const y = 200;
      
      this.data.nodeList.forEach((node, idx) => {
        node.positionLeft = startX + idx * gapX + 'px';
        node.positionTop = y + 'px';
      });
      
      this.$nextTick(() => {
        this.initJsPlumb();
      });
      
      this.$message.success('自动布局完成');
    },
    
    handleTool(tool) {
      this.toolType = tool.type;
      this.showAdd = true;
    },
    
    closePopup() {
      this.showAdd = false;
      this.addForm.remark = "";
    },
    
    toAddForm() {
      if (this.data.nodeList.length === 0) {
        this.$message.warning('请先添加节点到画布');
        return;
      }
      this.$message.success('保存成功');
      this.closePopup();
    },
    
    closeConfigDrawer() {
      this.showConfigDrawer = false;
      this.activeNodeId = null;
      this.selectedNode = null;
    },
    
    handleTabChange(activeTab) {
      this.select = activeTab;
    },
    
    filedChange() {
      // 字段变化处理
    },
    
    outPutChange() {
      // 输出变化处理
    },
    
    outPutAdd() {
      // 添加输出
    },
    
    outPutDel() {
      // 删除输出
    },
    
    outGroupChange() {
      // 分组变化处理
    },
    
    outGroupAdd() {
      // 添加分组
    },
    
    outGroupDel() {
      // 删除分组
    },
    
    outConnectChange() {
      // 连接变化处理
    },
    
    outConnectAdd() {
      // 添加连接
    },
    
    outConnectDel() {
      // 删除连接
    },
    
    outCoFnChange() {
      // 函数变化处理
    },
    
    outCoFnAdd() {
      // 添加函数
    },
    
    outCoFnDel() {
      // 删除函数
    },
    
    outFilterChange() {
      // 过滤变化处理
    },
    
    outFilterAdd() {
      // 添加过滤
    },
    
    outFilterDel() {
      // 删除过滤
    },
    
    changeLineState() {
      // 连线状态变化
    },
    
    handleDragEnd() {
      document.body.style.cursor = '';
      document.body.classList.remove('dragging', 'drag-over');
    },
    
    initNode() {
      try {
        if (typeof jsPlumb === 'undefined') {
          console.error('jsPlumb库未加载');
          this.$message.error('无法初始化画布：jsPlumb库未加载');
          return;
        }
        
        // 确保只创建一个jsPlumb实例
        if (this.jsPlumb) {
          console.log('使用现有jsPlumb实例');
          this.jsPlumb.reset();
        } else {
          console.log('创建新的jsPlumb实例');
      this.jsPlumb = jsPlumb.getInstance();
        }
      
      // 等待jsPlumb准备就绪
      this.jsPlumb.ready(() => {
        console.log('jsPlumb准备就绪');
        
        // 导入默认配置
        this.jsPlumb.importDefaults({
          Container: 'flow',
          Connector: ['Flowchart', { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }],
          PaintStyle: { stroke: '#D7A256', strokeWidth: 2 },
          HoverPaintStyle: { stroke: '#E6B366', strokeWidth: 4 },
          Endpoint: ['Dot', { radius: 5 }],
          EndpointStyle: { 
            fill: 'rgba(215, 162, 86, 0.8)', 
            outlineStroke: 'rgba(215, 162, 86, 0.8)', 
            outlineWidth: 1 
          },
          EndpointHoverStyle: { 
            fill: 'rgba(215, 162, 86, 1)', 
            outlineStroke: 'rgba(215, 162, 86, 1)', 
            outlineWidth: 1 
          },
          Anchors: ['TopCenter', 'RightMiddle', 'BottomCenter', 'LeftMiddle'],
          Overlays: [
            ['Arrow', { 
              width: 14, 
              length: 14, 
              location: 1, 
              foldback: 0.8, 
              cssClass: 'connection-arrow' 
            }]
            // 移除连线上的标签
          ],
          RenderMode: 'svg',
          ConnectionsDetachable: true, // 允许断开连接
          ReattachConnections: true    // 允许重新连接
        });
        
        // 完成连线前的校验
        this.jsPlumb.bind("beforeDrop", evt => {
          console.log('Before drop event:', evt);
          console.log('Source:', evt.sourceId, 'Target:', evt.targetId);
          return true; // 允许连线
        });
        
        // 连线创建成功后，维护本地数据
        this.jsPlumb.bind("connection", evt => {
          console.log('连线创建成功:', evt);
          this.addLine(evt);
        });
        
        // 连线点击事件
        this.jsPlumb.bind("click", (conn, originalEvent) => {
          console.log('连线点击:', conn);
          this.handleLine(conn, originalEvent);
        });
        
          // 连线悬停事件 - 添加删除提示样式
        this.jsPlumb.bind("connectionMouseover", (conn, originalEvent) => {
          // 添加悬停样式
          if (conn.canvas) {
              conn.canvas.style.filter = 'drop-shadow(0 2px 8px rgba(215, 162, 86, 0.6))';
              conn.setPaintStyle({ stroke: '#E6B366', strokeWidth: 4, strokeDasharray: '4 2' }); // 虚线样式
              conn.canvas.style.cursor = 'pointer'; // 修改鼠标指针为手型
              
              // 添加连线提示信息
              if (!this.tipShown) {
                this.$message.info('点击连线可删除', { duration: 2000 });
                this.tipShown = true;
                setTimeout(() => {
                  this.tipShown = false;
                }, 5000); // 5秒内不再显示提示
              }
          }
        });
        
          // 连线鼠标离开事件 - 移除悬停样式
        this.jsPlumb.bind("connectionMouseout", (conn, originalEvent) => {
          // 移除悬停样式
          if (conn.canvas) {
            conn.canvas.style.filter = '';
              conn.setPaintStyle({ stroke: '#D7A256', strokeWidth: 2 }); // 恢复原始样式
              conn.canvas.style.cursor = 'default'; // 恢复鼠标指针
          }
        });
        
        // 断开连线后，维护本地数据
        this.jsPlumb.bind("connectionDetached", evt => {
          console.log('连线断开:', evt);
          this.deleteLine(evt);
        });
        
          // 加载流程数据
          // this.loadEmployeeBenefitProcess(); // 移除此行
        });
      } catch (error) {
        console.error('初始化jsPlumb实例失败:', error);
        this.$message.error('初始化画布失败');
      }
    },
    
    initDefaultNodes() {
      this.data.nodeList = [];
    },
    
    initRequiredNodes() {
      // 检查是否已有开始节点
      const hasStartNode = this.data.nodeList.some(node => node.nodeType === "-1");
      // 检查是否已有结束节点
      const hasEndNode = this.data.nodeList.some(node => node.nodeType === "-2");
      
      // 如果没有开始节点，添加一个
      if (!hasStartNode) {
        const startNodeId = guid();
        this.data.nodeList.push({
          nodeId: startNodeId,
          nodeType: "-1",
          nodeName: "开始",
          logImg: "el-icon-video-play",
          positionLeft: '100px',
          positionTop: '150px',
          fixed: true
        });
      }
      
      // 如果没有结束节点，添加一个
      if (!hasEndNode) {
        const endNodeId = guid();
        this.data.nodeList.push({
          nodeId: endNodeId,
          nodeType: "-2",
          nodeName: "结束",
          logImg: "el-icon-video-pause",
          positionLeft: '400px',
          positionTop: '150px',
          fixed: true
        });
      }
      
      // 初始化jsPlumb连接
      this.$nextTick(() => {
        this.initJsPlumb();
        
        // 延迟执行诊断，确保DOM完全渲染
        setTimeout(() => {
          this.diagnoseConnectionIssues();
        }, 1000);
      });
    },
    
    initJsPlumb() {
      if (!this.jsPlumb) return;
      this.jsPlumb.reset();
      
      // 应用默认配置
      this.jsPlumb.importDefaults({
        Container: 'flow',
        // 使用统一的连接器和样式
        Connector: ['Flowchart', { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }],
        PaintStyle: { stroke: '#D7A256', strokeWidth: 2 },
        HoverPaintStyle: { stroke: '#E6B366', strokeWidth: 4 },
        ConnectionsDetachable: true, // 允许断开连接
        ReattachConnections: true    // 允许重新连接
      });
      
      this.$nextTick(() => {
        if (!this.data.nodeList || !this.data.nodeList.length) return;
        
        this.data.nodeList.forEach(node => {
          const el = document.getElementById(node.nodeId);
          if (!el) {
            console.warn('jsPlumb节点DOM未找到:', node.nodeId, node);
          } else {
            // 设置节点可拖拽，并绑定拖拽事件
            this.draggableNode(node.nodeId);
            
            // 设置节点为连线源 - 使用与matchSource相同的选择器
            this.jsPlumb.makeSource(node.nodeId, {
              filter: '.anchor-point, .anchor-dot, .anchor-ring', // 修改选择器匹配nodeItem组件
              filterExclude: false,
              anchor: ['TopCenter', 'RightMiddle', 'BottomCenter', 'LeftMiddle'],
              connector: ['Flowchart', { cornerRadius: 5 }],
              connectorStyle: { stroke: '#D7A256', strokeWidth: 2 },
              maxConnections: -1,
              allowLoopback: false,
              // 添加拖拽连线时的样式
              paintStyle: { 
                fill: "rgba(215, 162, 86, 0.8)",
                outlineStroke: "rgba(215, 162, 86, 0.8)",
                strokeWidth: 1 
              },
              hoverPaintStyle: { 
                fill: "rgba(215, 162, 86, 1)", 
                outlineStroke: "rgba(215, 162, 86, 1)",
                strokeWidth: 2 
              },
              endpoint: ["Dot", { radius: 5 }],
              endpointStyle: { 
                fill: "rgba(215, 162, 86, 0.8)",
                outlineStroke: "rgba(215, 162, 86, 0.8)"
              }
            });
            
            // 设置节点为连线目标
            this.jsPlumb.makeTarget(node.nodeId, {
              filter: '.anchor-point, .anchor-dot, .anchor-ring', // 修改选择器匹配nodeItem组件
              filterExclude: false,
              dropOptions: { hoverClass: 'drop-hover' },
              anchor: ['TopCenter', 'RightMiddle', 'BottomCenter', 'LeftMiddle'],
              allowLoopback: false,
              // 添加目标节点的样式
              paintStyle: { 
                fill: "rgba(215, 162, 86, 0.8)",
                outlineStroke: "rgba(215, 162, 86, 0.8)",
                strokeWidth: 1 
              },
              hoverPaintStyle: { 
                fill: "rgba(215, 162, 86, 1)", 
                outlineStroke: "rgba(215, 162, 86, 1)",
                strokeWidth: 2 
              },
              endpoint: ["Dot", { radius: 5 }],
              endpointStyle: { 
                fill: "rgba(215, 162, 86, 0.8)",
                outlineStroke: "rgba(215, 162, 86, 0.8)"
              }
            });
            
            // 添加连线事件监听
            this.jsPlumb.bind("beforeDrop", (params) => {
              console.log('连线拖拽到目标:', params);
              return true; // 允许连线
            });
            
            this.jsPlumb.bind("beforeDetach", (params) => {
              console.log('连线即将断开:', params);
              return true; // 允许断开
            });
            
            // 验证jsPlumb设置
            const isSource = this.jsPlumb.isSource(node.nodeId);
            const isTarget = this.jsPlumb.isTarget(node.nodeId);
            console.log(`节点 ${node.nodeId} - isSource: ${isSource}, isTarget: ${isTarget}`);
            
            // 检查锚点元素
            const nodeEl = document.getElementById(node.nodeId);
            let anchorElements = [];
            if (nodeEl) {
              anchorElements = nodeEl.querySelectorAll('.anchor-point');
              console.log(`节点 ${node.nodeId} 的锚点元素数量:`, anchorElements.length);
            } else {
              console.warn(`节点 ${node.nodeId} 的 DOM 未找到，无法统计锚点元素数量`);
            }
            
            // 添加辅助调试信息
            if (anchorElements.length === 0) {
              console.warn(`节点 ${node.nodeId} 未找到锚点元素，检查DOM结构`);
              // 检查更具体的锚点位置
              const topAnchor = document.querySelector(`#${node.nodeId} .anchor-top`);
              const rightAnchor = document.querySelector(`#${node.nodeId} .anchor-right`);
              const bottomAnchor = document.querySelector(`#${node.nodeId} .anchor-bottom`);
              const leftAnchor = document.querySelector(`#${node.nodeId} .anchor-left`);
              console.log('锚点检查结果:', {
                top: !!topAnchor,
                right: !!rightAnchor,
                bottom: !!bottomAnchor,
                left: !!leftAnchor
              });
            }
          }
        });
        
        this.jsPlumb.repaintEverything();
      });
    },
    
    // 设置节点可拖拽
    draggableNode(nodeId) {
      this.jsPlumb.draggable(nodeId, {
        grid: [20, 20], // 20px网格对齐
        drag: params => {
          // 拖拽过程中显示辅助线
          this.alignForLine(nodeId, params.pos);
        },
        start: () => {
          // 拖拽开始
          console.log('开始拖拽节点:', nodeId);
        },
        stop: params => {
          // 拖拽结束，更新节点位置
          this.auxiliaryLine.isShowXLine = false;
          this.auxiliaryLine.isShowYLine = false;
          this.changeNodePosition(nodeId, params.pos);
          console.log('节点拖拽结束，新位置:', params.pos);
        }
      });
    },
    
    // 移动节点时，动态显示对齐线
    alignForLine(nodeId, position) {
      let showXLine = false,
        showYLine = false;
      this.data.nodeList.some(el => {
        if (el.nodeId !== nodeId && el.positionLeft == position[0] + "px") {
          this.auxiliaryLinePos.x = position[0] + 60;
          showYLine = true;
        }
        if (el.nodeId !== nodeId && el.positionTop == position[1] + "px") {
          this.auxiliaryLinePos.y = position[1] + 20;
          showXLine = true;
        }
      });
      this.auxiliaryLine.isShowYLine = showYLine;
      this.auxiliaryLine.isShowXLine = showXLine;
    },
    
    // 更新节点位置
    changeNodePosition(nodeId, pos) {
      this.data.nodeList.some(v => {
        if (nodeId == v.nodeId) {
          v.positionLeft = pos[0] + "px";
          v.positionTop = pos[1] + "px";
          return true;
        } else {
          return false;
        }
      });
    },
    
    // 初始化连线
    initConnections() {
      if (!this.jsPlumb || !this.connectionList || !this.connectionList.length) {
        console.warn('无法初始化连线：jsPlumb实例不存在或连接列表为空');
        return;
      }
      
      console.log('开始初始化连线，连接列表:', this.connectionList);
      console.log('当前节点列表:', this.data.nodeList);
      
      // 确保DOM已完全渲染
      setTimeout(() => {
        try {
        // 只清除现有连线，不清除节点设置
        this.jsPlumb.deleteEveryConnection();
        
          // 检查所有节点是否已经在DOM中
          const nodeIds = this.data.nodeList.map(node => node.nodeId);
          const missingNodes = [];
          
          nodeIds.forEach(id => {
            if (!document.getElementById(id)) {
              missingNodes.push(id);
            }
          });
          
          if (missingNodes.length > 0) {
            console.warn('以下节点在DOM中未找到:', missingNodes);
          }
          
          // 为每个连接创建jsPlumb连接
        this.connectionList.forEach(connection => {
          try {
            const sourceEl = document.getElementById(connection.sourceId);
            const targetEl = document.getElementById(connection.targetId);
            
            console.log(`检查连线 ${connection.id}:`, {
              sourceId: connection.sourceId,
              targetId: connection.targetId,
              sourceEl: sourceEl ? '找到' : '未找到',
              targetEl: targetEl ? '找到' : '未找到'
            });
            
            if (sourceEl && targetEl) {
              const connectionObj = this.jsPlumb.connect({
                source: connection.sourceId,
                target: connection.targetId,
                  anchors: [
                    // 使用动态锚点，提高连线灵活性
                    ['TopCenter', 'RightMiddle', 'BottomCenter', 'LeftMiddle'],
                    ['TopCenter', 'RightMiddle', 'BottomCenter', 'LeftMiddle']
                  ],
                id: connection.id,
                  connector: ['Flowchart', { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }],
                overlays: [
                    ['Arrow', { width: 14, length: 14, location: 1, foldback: 0.8 }]
                    // 移除线段上的文字
                ],
                paintStyle: { stroke: '#D7A256', strokeWidth: 2 },
                hoverPaintStyle: { stroke: '#E6B366', strokeWidth: 4 }
              });
              
              console.log(`连线 ${connection.id} 创建成功:`, connectionObj);
            } else {
              console.warn('连线节点未找到:', connection.sourceId, connection.targetId);
            }
          } catch (error) {
            console.error('创建连线失败:', connection, error);
          }
        });
        
          // 确保所有连线都正确渲染
        this.jsPlumb.repaintEverything();
        console.log('连线初始化完成');
        } catch (error) {
          console.error('初始化连线过程中发生错误:', error);
        }
      }, 1000); // 延长等待时间，确保DOM已完全渲染
    },
    
    bindKeyboardEvents() {
      document.addEventListener('keydown', this.handleKeydown);
    },
    
    unbindKeyboardEvents() {
      document.removeEventListener('keydown', this.handleKeydown);
    },
    
    // 诊断连线功能
    diagnoseConnectionIssues() {
      console.log('开始诊断连线功能问题...');
      
      // 检查节点数量
      console.log(`画布上有 ${this.data.nodeList.length} 个节点`);
      
      // 检查每个节点的DOM和锚点
      this.data.nodeList.forEach(node => {
        const nodeEl = document.getElementById(node.nodeId);
        console.log(`节点 ${node.nodeId} (${node.nodeName}) 的DOM元素:`, !!nodeEl);
        
        if (nodeEl) {
          // 检查锚点元素
          const anchors = nodeEl.querySelectorAll('.anchor-point');
          console.log(`节点 ${node.nodeId} 的锚点数量:`, anchors.length);
          
          // 检查是否设置为源和目标
          const isSource = this.jsPlumb.isSource(node.nodeId);
          const isTarget = this.jsPlumb.isTarget(node.nodeId);
          console.log(`节点 ${node.nodeId} - isSource: ${isSource}, isTarget: ${isTarget}`);
          
          // 如果没有正确设置，尝试重新设置
          if (!isSource || !isTarget) {
            console.log(`尝试重新设置节点 ${node.nodeId} 的源和目标属性`);
            this.jsPlumb.makeSource(node.nodeId, this.jsplumbSourceOptions);
            this.jsPlumb.makeTarget(node.nodeId, this.jsplumbTargetOptions);
          }
        }
      });
      
      // 检查jsPlumb实例
      console.log('jsPlumb实例:', !!this.jsPlumb);
      if (this.jsPlumb) {
        console.log('jsPlumb连接数:', this.jsPlumb.getConnections().length);
      }
      
      // 修复锚点问题
      this.$nextTick(() => {
        // 强制重新初始化
        console.log('尝试强制重新初始化jsPlumb实例...');
        this.initJsPlumb();
      });
    },
    
    handleKeydown(event) {
      // Ctrl+M 切换组件面板
      if (event.ctrlKey && event.key === 'm') {
        event.preventDefault();
        this.toggleComponentDrawer();
      }
      
      // Esc 关闭组件面板
      if (event.key === 'Escape') {
        this.showComponentDrawer = false;
        this.showConfigDrawer = false;
      }
      
      // Delete 删除选中节点
      if (event.key === 'Delete' && this.activeNodeId) {
        this.deleteNode(this.activeNodeId);
      }
    },
    
    // 加载员工福利保障流程MOCK数据
    async loadProcessConfig(id) {
      try {
        const res = await getProcessConfig(id);
        if (res.code === 200 && res.data) {
          this.processInfo = res.data.processInfo || {};
          this.data.nodeList = res.data.nodeList || [];
          this.connectionList = res.data.connectionList || [];
          // 更新面包屑
          this.breadcrumbItems = [
            { text: '基础配置', to: { name: 'serviceProcess' } },
            { text: '流程配置画布' },
            { text: this.processInfo.name }
          ];
          this.$nextTick(() => {
            this.initJsPlumb();
            setTimeout(() => {
              this.initConnections && this.initConnections();
            }, 500);
          });
        } else {
          this.$message.error('加载流程配置失败');
        }
      } catch (error) {
        this.$message.error('加载流程配置失败');
        console.error('加载流程配置失败:', error);
      }
    },
    
    // 保存流程配置
    async saveProcessConfig() {
      try {
        const configData = {
          processInfo: this.processInfo,
          nodeList: this.data.nodeList,
          connectionList: this.connectionList,
          processRules: {
            maxDuration: 15,
            timeoutAction: "escalate",
            escalationLevel: "manager",
            autoSave: true,
            versionControl: true
          }
        };
        
        // 模拟保存API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('保存的流程配置:', configData);
        this.$message.success('流程配置保存成功');
        
        return configData;
      } catch (error) {
        console.error('保存流程配置失败:', error);
        this.$message.error('保存流程配置失败');
        throw error;
      }
    },
    
    // 重置流程配置
    resetProcessConfig() {
      this.$confirm('确定要重置流程配置吗？这将清空所有节点和连接。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data.nodeList = [];
        this.connectionList = [];
        this.activeNodeId = '';
        this.$nextTick(() => {
          this.initJsPlumb();
        });
        this.$message.success('流程配置已重置');
      }).catch(() => {
        // 用户取消
      });
    },
    
    // 预览流程
    previewProcess() {
      if (this.data.nodeList.length === 0) {
        this.$message.warning('请先添加流程节点');
        return;
      }
      
      this.$message.info('流程预览功能开发中...');
      console.log('预览流程配置:', {
        processInfo: this.processInfo,
        nodeList: this.data.nodeList,
        connectionList: this.connectionList
      });
    },
    
    // 关闭配置抽屉
    closeConfigDrawer() {
      this.showConfigDrawer = false;
      this.selectedNode = null;
      this.activeNodeId = '';
    },
    
    // 处理标签页切换
    handleTabChange(tab) {
      this.select = tab;
    },
    
    // 字段变更处理
    filedChange(data) {
      console.log('字段变更:', data);
    },
    
    // 输出变更处理
    outPutChange(data) {
      console.log('输出变更:', data);
    },
    
    // 输出添加处理
    outPutAdd(data) {
      console.log('输出添加:', data);
    },
    
    // 输出删除处理
    outPutDel(data) {
      console.log('输出删除:', data);
    },
    
    // 输出分组变更处理
    outGroupChange(data) {
      console.log('输出分组变更:', data);
    },
    
    // 输出分组添加处理
    outGroupAdd(data) {
      console.log('输出分组添加:', data);
    },
    
    // 输出分组删除处理
    outGroupDel(data) {
      console.log('输出分组删除:', data);
    },
    
    // 输出连接变更处理
    outConnectChange(data) {
      console.log('输出连接变更:', data);
    },
    
    // 输出连接添加处理
    outConnectAdd(data) {
      console.log('输出连接添加:', data);
    },
    
    // 输出连接删除处理
    outConnectDel(data) {
      console.log('输出连接删除:', data);
    },
    
    // 输出条件函数变更处理
    outCoFnChange(data) {
      console.log('输出条件函数变更:', data);
    },
    
    // 输出条件函数添加处理
    outCoFnAdd(data) {
      console.log('输出条件函数添加:', data);
    },
    
    // 输出条件函数删除处理
    outCoFnDel(data) {
      console.log('输出条件函数删除:', data);
    },
    
    // 输出过滤变更处理
    outFilterChange(data) {
      console.log('输出过滤变更:', data);
    },
    
    // 输出过滤添加处理
    outFilterAdd(data) {
      console.log('输出过滤添加:', data);
    },
    
    // 输出过滤删除处理
    outFilterDel(data) {
      console.log('输出过滤删除:', data);
    },
    
    // 添加连线
    addLine(line) {
      console.log('添加连线:', line);
      const from = line.source.id;
      const to = line.target.id;
      this.setLine(from, to, line);
    },
    
    // 删除连线
    deleteLine(line) {
      console.log('删除连线:', line);
      // 从连接列表中移除对应的连接
      const index = this.connectionList.findIndex(conn => 
        conn.sourceId === line.sourceId && conn.targetId === line.targetId
      );
      if (index > -1) {
        this.connectionList.splice(index, 1);
      }
    },
    
    // 设置连线
    setLine(from, to, line) {
      console.log('设置连线:', { from, to, line });
      // 添加新的连接到连接列表
      const newConnection = {
        id: `conn_${Date.now()}`,
        sourceId: from,
        targetId: to,
        label: line.label || '',
        condition: null
      };
      this.connectionList.push(newConnection);
    },
    
    // 处理连线点击 - 直接删除连线
    handleLine(conn, originalEvent) {
      console.log('处理连线点击:', conn);
      // 直接删除连线，无需确认
      this.deleteConnection(conn);
    },
    
    // 删除连线
    deleteConnection(conn) {
      if (this.jsPlumb && conn) {
        this.jsPlumb.deleteConnection(conn);
        
        // 从连接列表中移除对应的连接
        const index = this.connectionList.findIndex(connection => 
          connection.id === conn.id || 
          (connection.sourceId === conn.sourceId && connection.targetId === conn.targetId)
        );
        
        if (index > -1) {
          this.connectionList.splice(index, 1);
          console.log('连线已删除:', conn);
          this.$message.success('连线已删除');
        }
      }
    },
    
    // 确认删除连线（保留兼容性）
    confirmUpdate() {
      if (this.jsPlumb && this.delConn) {
        this.deleteConnection(this.delConn);
      }
      if (this.$refs.confirmDialog) {
      this.$refs.confirmDialog.hide();
      }
    },
    
    // 测试连线功能
    testConnections() {
      console.log('开始测试连线功能');
      console.log('jsPlumb实例:', this.jsPlumb);
      console.log('节点列表:', this.data.nodeList);
      console.log('连接列表:', this.connectionList);
      
      // 检查页面上的节点元素
      const nodeElements = document.querySelectorAll('.node-item');
      console.log('页面上的节点元素:', nodeElements.length);
      nodeElements.forEach(el => {
        console.log('节点元素ID:', el.id, '节点元素:', el);
        
        // 检查锚点元素
        const anchorElements = el.querySelectorAll('.node-anchor');
        console.log(`节点 ${el.id} 的锚点元素:`, anchorElements.length);
        anchorElements.forEach(anchor => {
          console.log('锚点元素:', anchor);
        });
      });
      
      // 检查jsPlumb设置
      this.data.nodeList.forEach(node => {
        const isSource = this.jsPlumb.isSource(node.nodeId);
        const isTarget = this.jsPlumb.isTarget(node.nodeId);
        console.log(`节点 ${node.nodeId} - isSource: ${isSource}, isTarget: ${isTarget}`);
      });
      
      // 检查所有带有ID的元素
      const allElementsWithId = document.querySelectorAll('[id]');
      console.log('所有带有ID的元素:', allElementsWithId.length);
      allElementsWithId.forEach(el => {
        console.log('元素ID:', el.id, '元素:', el);
      });
      
      // 尝试手动创建一条连线
      if (this.data.nodeList.length >= 2) {
        const firstNode = this.data.nodeList[0];
        const secondNode = this.data.nodeList[1];
        
        console.log('尝试连接节点:', firstNode.nodeId, '->', secondNode.nodeId);
        
        const sourceEl = document.getElementById(firstNode.nodeId);
        const targetEl = document.getElementById(secondNode.nodeId);
        
        console.log('源节点元素:', sourceEl);
        console.log('目标节点元素:', targetEl);
        
        if (sourceEl && targetEl) {
          try {
            const connection = this.jsPlumb.connect({
              source: firstNode.nodeId,
              target: secondNode.nodeId,
              id: 'test_connection',
              overlays: [
                ['Arrow', { 
                  width: 14, 
                  length: 14, 
                  location: 1, 
                  foldback: 0.8,
                  cssClass: 'connection-arrow'
                }]
                // 移除线段上的文字
              ],
              paintStyle: { stroke: '#D7A256', strokeWidth: 3 },
              hoverPaintStyle: { stroke: '#E6B366', strokeWidth: 5 },
              endpoint: ["Dot", { radius: 5 }],
              endpointStyle: { 
                fill: "rgba(215, 162, 86, 0.8)",
                outlineStroke: "rgba(215, 162, 86, 0.8)" 
              },
              connector: ['Flowchart', { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }]
            });
            
            console.log('测试连线创建成功:', connection);
            this.$message.success('测试连线创建成功');
          } catch (error) {
            console.error('测试连线创建失败:', error);
            this.$message.error('测试连线创建失败: ' + error.message);
          }
        } else {
          console.error('节点元素未找到');
          this.$message.error('节点元素未找到');
        }
      } else {
        this.$message.warning('需要至少2个节点才能测试连线');
      }
    },
    
    // 测试锚点连线功能
    testAnchorConnections() {
      console.log('开始测试锚点连线功能');
      
      if (this.data.nodeList.length < 2) {
        this.$message.warning('需要至少2个节点才能测试连线');
        return;
      }
      
      const firstNode = this.data.nodeList[0];
      const secondNode = this.data.nodeList[1];
      
      console.log('测试节点:', firstNode.nodeId, '->', secondNode.nodeId);
      
      // 检查锚点元素是否存在
      const sourceAnchors = document.querySelectorAll(`#${firstNode.nodeId} .node-anchor`);
      const targetAnchors = document.querySelectorAll(`#${secondNode.nodeId} .node-anchor`);
      
      console.log('源节点锚点数量:', sourceAnchors.length);
      console.log('目标节点锚点数量:', targetAnchors.length);
      
      if (sourceAnchors.length === 0 || targetAnchors.length === 0) {
        console.error('锚点元素未找到');
        this.$message.error('锚点元素未找到，请检查节点渲染');
        return;
      }
      
      // 尝试从第一个锚点连接到第二个锚点
      const sourceAnchor = sourceAnchors[0]; // 第一个锚点
      const targetAnchor = targetAnchors[0]; // 第一个锚点
      
      console.log('源锚点:', sourceAnchor);
      console.log('目标锚点:', targetAnchor);
      
      // 模拟鼠标事件来测试连线
      try {
        // 创建鼠标事件
        const mouseDownEvent = new MouseEvent('mousedown', {
          bubbles: true,
          cancelable: true,
          clientX: sourceAnchor.getBoundingClientRect().left + 5,
          clientY: sourceAnchor.getBoundingClientRect().top + 5
        });
        
        const mouseUpEvent = new MouseEvent('mouseup', {
          bubbles: true,
          cancelable: true,
          clientX: targetAnchor.getBoundingClientRect().left + 5,
          clientY: targetAnchor.getBoundingClientRect().top + 5
        });
        
        // 触发事件
        sourceAnchor.dispatchEvent(mouseDownEvent);
        setTimeout(() => {
          targetAnchor.dispatchEvent(mouseUpEvent);
        }, 100);
        
        console.log('锚点事件已触发');
        this.$message.success('锚点事件已触发，请检查连线是否创建');
      } catch (error) {
        console.error('锚点事件触发失败:', error);
        this.$message.error('锚点事件触发失败');
      }
    }
  }
};
</script>

<style scoped>
.match-source-container {
  background: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 80px); /* 减去header高度 */
}

.flow-canvas {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.flow-canvas::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at center, transparent 0%, rgba(215, 162, 86, 0.01) 50%, rgba(215, 162, 86, 0.02) 100%),
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.flow-canvas::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(215, 162, 86, 0.04);
  border-radius: 12px;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(215, 162, 86, 0.02);
  pointer-events: none;
  z-index: 1;
}

.flow-content {
  width: 2001px;
  height: 1501px;
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, 
    #fefdfb 0%, 
    #ffffff 25%, 
    #ffffff 50%, 
    #ffffff 75%, 
    #fefdfb 100%);
  
  background-image: 
    linear-gradient(to right, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
    linear-gradient(to right, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
    radial-gradient(circle, rgba(215, 162, 86, 0.04) 0.5px, transparent 0.5px);
  
  background-size: 
    100px 100%,
    100% 100px,
    20px 100%,
    100% 20px,
    20px 20px;
  
  background-position:
    0 0,
    0 0,
    0 0,
    0 0,
    0 0;
  
  border-radius: 12px;
  box-shadow: 
    0 4px 16px rgba(215, 162, 86, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(215, 162, 86, 0.04);
}

.flow-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  background: 
    radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.floating-title {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  pointer-events: none;
}

.title-content {
  color: #7f8c8d;
  padding: 0;
  font-size: 13px;
}

.subtitle-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.empty-content h3 {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin: 0;
}

.empty-content p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.floating-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
}

.toolbar-content {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(215, 162, 86, 0.1);
}

.toolbar-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.2s ease;
}

.toolbar-icon:hover {
  background: rgba(215, 162, 86, 0.1);
  color: #D7A256;
}

.toolbar-icon.active {
  background: rgba(215, 162, 86, 0.2);
  color: #D7A256;
}

.toolbar-icon.primary {
  background: #D7A256;
  color: white;
}

.toolbar-icon.primary:hover {
  background: #c4924a;
  color: white;
}

.toolbar-icon.save-btn {
  background: #409EFF !important;
  color: white !important;
}

.toolbar-icon.save-btn:hover {
  background: #337ecc !important;
  color: white !important;
}

.auxiliary-line-x {
  position: absolute;
  border-top: 1px dashed;
  z-index: 10;
  pointer-events: none;
}

.auxiliary-line-y {
  position: absolute;
  border-left: 1px dashed;
  z-index: 10;
  pointer-events: none;
}

/* 自定义滚动条样式 */
.flow-canvas::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.flow-canvas::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
}

.flow-canvas::-webkit-scrollbar-thumb {
  background: rgba(215, 162, 86, 0.2);
  border-radius: 10px;
}

.flow-canvas::-webkit-scrollbar-thumb:hover {
  background: rgba(215, 162, 86, 0.3);
}

/* 简化滚动条交互 */
.flow-canvas {
  scrollbar-width: thin;
  scrollbar-color: rgba(215, 162, 86, 0.2) rgba(0, 0, 0, 0.03);
}

/* 自定义滚动条样式 */
.flow-canvas::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.flow-canvas::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.flow-canvas::-webkit-scrollbar-thumb {
  background: rgba(215, 162, 86, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.flow-canvas::-webkit-scrollbar-thumb:hover {
  background: rgba(215, 162, 86, 0.5);
}

.flow-canvas::-webkit-scrollbar-corner {
  background: transparent;
}

/* 连线样式覆盖 */
::v-deep .jtk-connector {
  z-index: 4;
}

::v-deep .jtk-endpoint {
  z-index: 5;
}

::v-deep .jtk-overlay {
  z-index: 6;
}

/* 连线标签已移除 */

/* 连线箭头样式 */
::v-deep .connection-arrow {
  fill: #D7A256 !important;
  stroke: #D7A256 !important;
}

/* 连线悬停样式 */
::v-deep .jtk-connector:hover {
  stroke: #E6B366 !important;
  stroke-width: 4px !important;
  cursor: pointer !important;
  filter: drop-shadow(0 2px 8px rgba(215, 162, 86, 0.6)) !important;
  stroke-dasharray: 4 2 !important; /* 虚线效果 */
  animation: line-blink 1s infinite !important; /* 添加闪烁动画 */
}

@keyframes line-blink {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 连线端点样式 */
::v-deep .jtk-endpoint {
  background: #D7A256 !important;
  border: 2px solid white !important;
  box-shadow: 0 0 6px rgba(215, 162, 86, 0.6) !important;
}

::v-deep .jtk-endpoint:hover {
  background: #E6B366 !important;
  box-shadow: 0 0 8px rgba(215, 162, 86, 0.8) !important;
  transform: scale(1.2) !important;
}

/* 添加锚点悬停样式 */
::v-deep .anchor-point {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

::v-deep .anchor-point:hover .anchor-dot {
  background: #D7A256 !important;
  transform: scale(1.3) !important;
  box-shadow: 0 0 12px rgba(215, 162, 86, 0.3) !important;
}

::v-deep .anchor-point:hover .anchor-ring {
  opacity: 1 !important;
  transform: scale(1.5) !important;
  border-color: rgba(215, 162, 86, 0.4) !important;
}

/* 添加拖拽中的连线样式 */
::v-deep .jtk-connector-outline {
  stroke: rgba(215, 162, 86, 0.3) !important;
  stroke-width: 6px !important;
}

/* 连线拖拽样式 */
::v-deep .jtk-dragging {
  cursor: grabbing !important;
}

::v-deep .jtk-drag-select * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 拖拽连线时的辅助样式 */
::v-deep .jtk-drag-active {
  outline: 2px solid rgba(215, 162, 86, 0.4) !important;
  border-radius: 4px !important;
}

::v-deep .drop-hover {
  background-color: rgba(215, 162, 86, 0.1) !important;
  border-color: rgba(215, 162, 86, 0.5) !important;
  box-shadow: 0 0 10px rgba(215, 162, 86, 0.4) !important;
}
</style>

