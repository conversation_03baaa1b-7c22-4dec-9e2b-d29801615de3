<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>
<script>
import { industryRiskConfig } from '@/mock'
import { updateIndustryRiskConfig } from '@/api/riskMatrix'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'

export default {
  name: 'IndustryRiskConfig',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        industryName: '',
        industryCode: '',
        matrixDesc: '',
        matrixConfig: [],
        toolsConfig: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'industryName', label: '行业名称', type: 'input', placeholder: '请输入行业名称', disabled: true },
              { prop: 'industryCode', label: '行业编码', type: 'input', placeholder: '请输入行业编码', disabled: true }
            ],
            [
              { prop: 'matrixDesc', label: '矩阵说明', type: 'textarea', placeholder: '请输入该行业风险矩阵的核心说明...', maxlength: 500, rows: 4, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '风险矩阵配置',
          icon: 'el-icon-s-grid',
          fields: [
            [
              {
                type: 'list',
                prop: 'matrixConfig',
                label: '风险矩阵',
                icon: 'el-icon-s-grid',
                addText: '添加风险项',
                min: 0,
                defaultRow: () => ({ type: '', level: '', description: '', impact: '' }),
                columns: [
                  {
                    prop: 'type',
                    label: '风险类型',
                    type: 'input',
                    width: 150,
                    placeholder: '请输入风险类型'
                  },
                  {
                    prop: 'level',
                    label: '风险等级',
                    type: 'select',
                    width: 120,
                    options: [
                      { label: '低风险', value: 'low' },
                      { label: '中风险', value: 'medium' },
                      { label: '高风险', value: 'high' },
                      { label: '极高风险', value: 'critical' }
                    ]
                  },
                  {
                    prop: 'description',
                    label: '风险描述',
                    type: 'input',
                    minWidth: 200,
                    placeholder: '请输入风险描述'
                  },
                  {
                    prop: 'impact',
                    label: '影响程度',
                    type: 'input',
                    width: 120,
                    placeholder: '请输入影响程度'
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.matrixConfig.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        },
        {
          title: '防控工具配置',
          icon: 'el-icon-s-tools',
          fields: [
            [
              {
                type: 'list',
                prop: 'toolsConfig',
                label: '防控工具',
                icon: 'el-icon-s-tools',
                addText: '添加工具',
                min: 0,
                defaultRow: () => ({ toolName: '', applicableRisk: '', description: '', usage: '' }),
                columns: [
                  {
                    prop: 'toolName',
                    label: '工具名称',
                    type: 'input',
                    width: 150,
                    placeholder: '请输入工具名称'
                  },
                  {
                    prop: 'applicableRisk',
                    label: '适用风险',
                    type: 'input',
                    width: 150,
                    placeholder: '请输入适用风险'
                  },
                  {
                    prop: 'description',
                    label: '工具描述',
                    type: 'input',
                    minWidth: 200,
                    placeholder: '请输入工具描述'
                  },
                  {
                    prop: 'usage',
                    label: '使用方法',
                    type: 'input',
                    minWidth: 180,
                    placeholder: '请输入使用方法'
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.toolsConfig.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        industryName: [{ required: true, message: '请输入行业名称', trigger: 'blur' }],
        industryCode: [{ required: true, message: '请输入行业编码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    pageTitle() {
      return this.form.industryName ? `${this.form.industryName} - 风险配置` : '行业风险配置'
    },
    pageIcon() {
      return 'el-icon-s-flag'
    },
    breadcrumbItems() {
      return [
        { text: '行业风险管理', to: { name: 'industryRisk' }, icon: 'el-icon-s-flag' },
        { text: this.pageTitle, icon: 'el-icon-edit' }
      ]
    }
  },
  created() {
    this.isView = this.$route.query.mode === 'view'
    this.loadIndustryData()
  },
  methods: {
    loadIndustryData() {
      const industryCode = this.$route.params.industryCode
      const found = industryRiskConfig.find(item => item.industryCode === industryCode)
      
      if (found) {
        this.form.industryName = found.industryName
        this.form.industryCode = found.industryCode
        this.form.matrixDesc = found.matrixDesc || ''
        
        // 加载矩阵配置
        if (found.matrixConfig && found.matrixConfig.rowData) {
          this.form.matrixConfig = found.matrixConfig.rowData.map(item => ({
            type: item.type || '',
            level: item.level || '',
            description: item.description || item.desc || '',
            impact: item.impact || ''
          }))
        } else if (found.matrix) {
          // 兼容旧数据结构
          this.form.matrixConfig = found.matrix.map(item => ({
            type: item.type || '',
            level: item.level || '',
            description: item.desc || item.description || '',
            impact: item.impact || ''
          }))
        } else {
          this.form.matrixConfig = []
        }
        
        // 加载工具配置
        if (found.toolsConfig && found.toolsConfig.rowData) {
          this.form.toolsConfig = found.toolsConfig.rowData.map(item => ({
            toolName: item.toolName || item.tool || '',
            applicableRisk: item.applicableRisk || item.risk || '',
            description: item.description || '',
            usage: item.usage || ''
          }))
        } else if (found.tools) {
          // 兼容旧数据结构
          this.form.toolsConfig = found.tools.map(item => ({
            toolName: item.tool || item.toolName || '',
            applicableRisk: item.risk || item.applicableRisk || '',
            description: item.description || '',
            usage: item.usage || ''
          }))
        } else {
          this.form.toolsConfig = []
        }
      } else {
        this.$message.error('未找到行业风险配置数据')
        this.form.industryName = industryCode || ''
        this.form.industryCode = industryCode || ''
        this.form.matrixDesc = ''
        this.form.matrixConfig = []
        this.form.toolsConfig = []
      }
    },
    handleBack() {
      this.$router.push({ name: 'industryRisk' })
    },
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    },
    async handleSave() {
      if (!this.form.industryCode) {
        this.$message.error('缺少行业编码，无法保存')
        return
      }
      
      try {
        // 验证表单
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.error('请检查表单填写是否正确')
          return
        }
        
        this.loading = true
        
        // 构建更新数据
        const updateData = {
          matrixConfig: {
            columnDefs: [
              { key: 'type', label: '风险类型' },
              { key: 'level', label: '风险等级' },
              { key: 'description', label: '风险描述' },
              { key: 'impact', label: '影响程度' }
            ],
            rowData: this.form.matrixConfig
          },
          toolsConfig: {
            columnDefs: [
              { key: 'toolName', label: '工具名称' },
              { key: 'applicableRisk', label: '适用风险' },
              { key: 'description', label: '工具描述' },
              { key: 'usage', label: '使用方法' }
            ],
            rowData: this.form.toolsConfig
          },
          matrixDesc: this.form.matrixDesc,
          // 兼容旧数据结构
          matrix: this.form.matrixConfig,
          tools: this.form.toolsConfig
        }
        
        const response = await updateIndustryRiskConfig(this.form.industryCode, updateData)
        
        if (response.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
<style lang="less" scoped>
// 风险矩阵配置页面样式
</style>