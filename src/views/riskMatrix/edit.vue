<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import { getRiskMatrixDetail, saveRiskMatrix } from '@/api/riskMatrix/index.js'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'

export default {
  name: 'RiskMatrixEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        name: '',
        description: '',
        enterpriseTypes: [],
        categories: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '矩阵名称', type: 'input', placeholder: '请输入风险矩阵名称', maxlength: 50, showWordLimit: true, required: true },
              { prop: 'enterpriseTypes', label: '关联企业类型', type: 'select', multiple: true, placeholder: '请选择企业类型', options: [], required: true }
            ],
            [
              { prop: 'description', label: '描述', type: 'textarea', placeholder: '请输入描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入矩阵名称', trigger: 'blur' },
          { min: 1, max: 50, message: '矩阵名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        enterpriseTypes: [
          { required: true, message: '请选择至少一个企业类型', trigger: 'change' }
        ]
      },
      typeOptions: [
        { label: 'A类', value: 'A' },
        { label: 'B类', value: 'B' },
        { label: 'C类', value: 'C' },
        { label: 'D类', value: 'D' },
        { label: 'E类', value: 'E' }
      ],
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) return '查看风险矩阵'
      return this.form.id ? '编辑风险矩阵' : '新建风险矩阵'
    },
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.form.id ? 'el-icon-edit' : 'el-icon-plus')
    },
    breadcrumbItems() {
      return [
        { text: '风险矩阵管理', icon: 'el-icon-s-grid', to: { name: 'riskMatrixList' } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    this.isView = this.$route.query.mode === 'view'
    
    const id = this.$route.params.id
    if (id) {
      this.loadData(id)
    }
  },
  methods: {
    
    async loadData(id) {
      this.loading = true
      try {
        const response = await getRiskMatrixDetail(id)
        if (response.code === 200 && response.data) {
          this.form = {
            id: response.data.id,
            name: response.data.name,
            description: response.data.description,
            enterpriseTypes: response.data.enterpriseTypes || [],
            categories: response.data.categories || []
          }
        } else {
          this.$message.error('加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      if (this.isView) return
      
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        const response = await saveRiskMatrix(this.form)
        if (response.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>