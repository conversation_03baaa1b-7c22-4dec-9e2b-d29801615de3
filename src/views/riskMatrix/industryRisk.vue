<template>
  <div class="industry-risk-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="行业风险管理"
      subtitle="配置行业风险矩阵、防控工具等，支持分级管理和动态配置"
      title-icon="el-icon-s-flag"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="220"
      :search-label-width="'100px'"
      add-button-text="新增行业风险"
      empty-title="暂无行业风险数据"
      empty-description="点击上方新增按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 是否配置方案列插槽 -->
      <template #hasConfig="{ row }">
        <el-tag
          :type="row.hasConfig ? 'success' : 'info'"
          size="small"
        >
          {{ row.hasConfig ? '已配置' : '未配置' }}
        </el-tag>
      </template>
      
      <!-- 创建时间列插槽 -->
      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
        </div>
      </template>
    </UniversalTable>
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该行业风险？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>
<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
import { industryRiskConfig } from '@/mock'
export default {
  name: 'IndustryRisk',
  components: { UniversalTable, ConfirmDialog },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      searchForm: {
        industryLevel1Name: '',
        industryLevel2Name: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'industryLevel1Name',
          label: '一级行业',
          minWidth: 140,
          align: 'center'
        },
        {
          prop: 'industryLevel1Code',
          label: '一级行业编码',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'industryLevel2Name',
          label: '二级行业',
          minWidth: 180,
          align: 'center'
        },
        {
          prop: 'industryLevel2Code',
          label: '二级行业编码',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'hasConfig',
          label: '是否配置方案',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'config',
          label: '配置',
          icon: 'el-icon-setting',
          class: 'config-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '一级行业',
          name: 'industryLevel1Name',
          type: 'input',
          placeholder: '请输入一级行业'
        },
        {
          label: '二级行业',
          name: 'industryLevel2Name',
          type: 'input',
          placeholder: '请输入二级行业'
        }
      ],
      currentRow: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      setTimeout(() => {
        // 兼容mock数据，补充一二级行业字段和创建时间
        let data = industryRiskConfig.map(item => {
          let industryLevel1Name = '', industryLevel1Code = '', industryLevel2Name = '', industryLevel2Code = ''
          if (item.industryLevel === 1) {
            industryLevel1Name = item.industryName
            industryLevel1Code = item.industryCode
          } else if (item.industryLevel === 2) {
            const arr = item.industryName.split('-')
            industryLevel1Name = arr[0] || ''
            industryLevel2Name = arr[1] || ''
            industryLevel1Code = item.industryCode ? item.industryCode[0] : ''
            industryLevel2Code = item.industryCode
          }
          return {
            ...item,
            industryLevel1Name,
            industryLevel1Code,
            industryLevel2Name,
            industryLevel2Code,
            hasConfig: item.configs && item.configs.length > 0,
            createTime: item.createTime || '2024-01-15 10:30:00'
          }
        })
        // 搜索过滤
        if (this.searchForm.industryLevel1Name) {
          data = data.filter(d => d.industryLevel1Name.includes(this.searchForm.industryLevel1Name))
        }
        if (this.searchForm.industryLevel2Name) {
          data = data.filter(d => d.industryLevel2Name.includes(this.searchForm.industryLevel2Name))
        }
        // 分页
        this.pagination.total = data.length
        const start = (this.pagination.pageNum - 1) * this.pagination.pageSize
        const end = start + this.pagination.pageSize
        this.tableData = data.slice(start, end)
        this.loading = false
      }, 300)
    },
    handleSearch(searchData) {
      this.searchForm = { ...searchData }
      this.pagination.pageNum = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = { industryLevel1Name: '', industryLevel2Name: '' }
      this.pagination.pageNum = 1
      this.loadData()
    },
    handleAdd() {
      this.$router.push({ name: 'industryRiskConfig' })
    },
    handleAction({ action, row }) {
      this.currentRow = row
      if (action === 'view') {
        this.$router.push({ name: 'industryRiskConfig', params: { industryCode: row.industryCode }, query: { mode: 'view' } })
      } else if (action === 'config') {
        this.$router.push({ name: 'industryRiskConfig', params: { industryCode: row.industryCode } })
      } else if (action === 'delete') {
        this.$refs.confirmDialog.show()
      }
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },
    confirmDelete() {
      // TODO: 删除API
      this.$message.success('删除成功')
      this.loadData()
    }
  }
}
</script>
<style lang="less" scoped>
.industry-risk-container {
  min-height: 100vh;
  background: #fbf6ee;
}

.time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  
  i {
    color: #909399;
    font-size: 12px;
  }
}
</style>