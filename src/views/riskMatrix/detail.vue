<template>
  <div class="risk-matrix-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <span class="page-title"><i class="el-icon-s-data"></i> 风险矩阵分析报告</span>
          <p class="page-subtitle">详细展示风险评估计算过程和结果分析</p>
        </div>
        <div class="action-section">
          <el-button @click="handleBack" class="back-btn">返回</el-button>
          <el-button type="primary" icon="el-icon-download" @click="exportReport" class="export-btn">导出报告</el-button>
        </div>
      </div>
    </div>
    
    <!-- 基本信息 -->
    <div class="basic-info-section">
      <div class="section-header">
        <span class="section-title">基本信息</span>
      </div>
      <div class="info-cards">
        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="card-content">
            <div class="card-label">企业类型</div>
            <div class="card-value">
              <el-tag :type="getTypeTagType(reportData.enterpriseType)" size="medium">
                {{ getTypeName(reportData.enterpriseType) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-s-data"></i>
          </div>
          <div class="card-content">
            <div class="card-label">总分</div>
            <div class="card-value">{{ reportData.totalScore.toFixed(2) }}</div>
          </div>
        </div>
        
        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-s-marketing"></i>
          </div>
          <div class="card-content">
            <div class="card-label">平均分</div>
            <div class="card-value">{{ reportData.averageScore.toFixed(2) }}</div>
          </div>
        </div>
        
        <div class="info-card">
          <div class="card-icon">
            <i class="el-icon-warning"></i>
          </div>
          <div class="card-content">
            <div class="card-label">风险等级</div>
            <div class="card-value">
              <el-tag :type="getRiskLevelTagType(reportData.overallRiskLevel)" size="medium">
                {{ reportData.overallRiskLevel }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 雷达图 -->
    <div class="radar-chart-section">
      <div class="section-header">
        <span class="section-title">风险矩阵雷达图</span>
      </div>
      <div class="chart-container">
        <div ref="radarChart" class="radar-chart"></div>
      </div>
    </div>
    
    <!-- 计算过程详情 -->
    <div class="calculation-details-section">
      <div class="section-header">
        <span class="section-title">计算过程详情</span>
        <p class="section-subtitle">详细展示从问卷答案到最终风险等级的完整计算过程</p>
      </div>
      
      <!-- 问卷答案 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-edit"></i>
          问卷答案分析
        </div>
        <div class="detail-content">
          <el-table :data="reportData.calculationProcess.questionnaireAnswers" stripe class="detail-table">
            <el-table-column prop="scoreItem" label="评分项" width="200" align="center" />
            <el-table-column prop="question" label="问题" min-width="300" align="center" />
            <el-table-column label="选择答案" width="200" align="center">
              <template slot-scope="scope">
                <div v-for="option in scope.row.selectedOptions" :key="option.label" class="selected-option">
                  <span class="option-label">{{ option.label }}</span>
                  <span class="option-text">{{ option.text }}</span>
                  <span class="option-score">({{ option.score }}分)</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalScore" label="得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="score-value">{{ scope.row.totalScore }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 评分项计算 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-s-flag"></i>
          评分项计算过程
        </div>
        <div class="detail-content">
          <el-table :data="reportData.calculationProcess.scoreItemResults" stripe class="detail-table">
            <el-table-column prop="scoreItemName" label="评分项" width="200" align="center" />
            <el-table-column prop="formulaName" label="关联公式" width="200" align="center" />
            <el-table-column prop="questionnaireScore" label="问卷得分" width="100" align="center" />
            <el-table-column prop="calculatedScore" label="公式计算得分" width="120" align="center" />
            <el-table-column prop="coefficient" label="系数" width="80" align="center" />
            <el-table-column prop="finalScore" label="最终得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="final-score">{{ scope.row.finalScore.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计算详情" min-width="200" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="showCalculationDetails(scope.row)" class="detail-btn">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 核心类别计算 -->
      <div class="detail-section">
        <div class="detail-title">
          <i class="el-icon-s-grid"></i>
          核心类别计算结果
        </div>
        <div class="detail-content">
          <el-table :data="reportData.categoryResults" stripe class="detail-table">
            <el-table-column prop="categoryName" label="类别名称" width="200" align="center" />
            <el-table-column prop="calculationMethod" label="计算方式" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.calculationMethod === 'sum' ? 'success' : 'warning'" size="small">
                  {{ scope.row.calculationMethod === 'sum' ? '加和' : '平均数' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="categoryScore" label="类别得分" width="100" align="center">
              <template slot-scope="scope">
                <span class="category-score">{{ scope.row.categoryScore.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="档次" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="getLevelTagType(scope.row.level.name)" size="small">
                  {{ scope.row.level.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="level.description" label="档次描述" min-width="300" align="center" />
            <el-table-column label="包含评分项" width="150" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="showCategoryDetails(scope.row)" class="detail-btn">
                  查看详情 ({{ scope.row.scoreItems ? scope.row.scoreItems.length : 0 }}个)
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    
    <!-- 建议措施 -->
    <div class="suggestions-section">
      <div class="section-header">
        <span class="section-title">风险建议措施</span>
      </div>
      <div class="suggestions-content">
        <div class="suggestion-card">
          <div class="suggestion-header">
            <i class="el-icon-lightbulb"></i>
            <span>整体建议</span>
          </div>
          <div class="suggestion-content">
            {{ getSuggestion(reportData.overallRiskLevel) }}
          </div>
        </div>
        
        <div v-for="category in getHighRiskCategories()" :key="category.categoryId" class="suggestion-card">
          <div class="suggestion-header">
            <i class="el-icon-warning"></i>
            <span>{{ category.categoryName }} - {{ category.level.name }}</span>
          </div>
          <div class="suggestion-content">
            {{ category.level.description }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 计算详情对话框 -->
    <el-dialog title="评分项计算详情" :visible.sync="calculationDetailVisible" width="800px">
      <div v-if="selectedCalculationDetail">
        <div class="detail-item">
          <label>评分项名称：</label>
          <span>{{ selectedCalculationDetail.scoreItemName }}</span>
        </div>
        <div class="detail-item">
          <label>关联公式：</label>
          <span>{{ selectedCalculationDetail.formulaName }}</span>
        </div>
        <div class="detail-item">
          <label>公式表达式：</label>
          <span>{{ selectedCalculationDetail.calculationDetails.formula }}</span>
        </div>
        <div class="detail-item">
          <label>输入变量：</label>
          <div class="variables-list">
            <div v-for="(value, key) in selectedCalculationDetail.variables" :key="key" class="variable-item">
              <span class="variable-name">{{ key }}:</span>
              <span class="variable-value">{{ value }}</span>
            </div>
          </div>
        </div>
        <div class="detail-item">
          <label>计算结果：</label>
          <span class="result-value">{{ selectedCalculationDetail.calculatedScore }}</span>
        </div>
        <div class="detail-item">
          <label>系数调整：</label>
          <span>{{ selectedCalculationDetail.calculatedScore }} × {{ selectedCalculationDetail.coefficient }} = {{ selectedCalculationDetail.finalScore.toFixed(2) }}</span>
        </div>
      </div>
    </el-dialog>
    
    <!-- 类别详情对话框 -->
    <el-dialog title="类别详情" :visible.sync="categoryDetailVisible" width="800px">
      <div v-if="selectedCategory">
        <div class="detail-item">
          <label>类别名称：</label>
          <span>{{ selectedCategory.categoryName }}</span>
        </div>
        <div class="detail-item">
          <label>计算方式：</label>
          <span>{{ selectedCategory.calculationMethod === 'sum' ? '加和' : '平均数' }}</span>
        </div>
        <div class="detail-item">
          <label>类别得分：</label>
          <span class="score-value">{{ selectedCategory.categoryScore.toFixed(2) }}</span>
        </div>
        <div class="detail-item">
          <label>评分项列表：</label>
          <div class="score-items-list">
            <el-table :data="selectedCategory.scoreItems" stripe>
              <el-table-column prop="name" label="评分项名称" min-width="200" />
              <el-table-column prop="score" label="得分" width="100" align="center" />
              <el-table-column prop="weight" label="权重" width="100" align="center" />
              <el-table-column prop="weightedScore" label="加权得分" width="100" align="center">
                <template slot-scope="scope">
                  {{ (scope.row.score * scope.row.weight).toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RiskMatrixDetail',
  data() {
    return {
      reportData: {
        enterpriseType: 'CDE',
        totalScore: 0,
        averageScore: 0,
        overallRiskLevel: '中风险',
        categoryResults: [],
        calculationProcess: {
          questionnaireAnswers: [],
          scoreItemResults: [],
          basicParameters: {},
          riskMatrix: null,
          categoryResults: []
        },
        createTime: new Date().toLocaleString()
      },
      calculationDetailVisible: false,
      categoryDetailVisible: false,
      selectedCalculationDetail: null,
      selectedCategory: null
    }
  },
  mounted() {
    this.loadReportData()
    this.$nextTick(() => {
      this.initRadarChart()
    })
  },
  methods: {
    loadReportData() {
      // 从路由参数获取数据
      const { reportData } = this.$route.params
      if (reportData) {
        this.reportData = reportData
      }
    },
    
    initRadarChart() {
      if (!this.reportData.categoryResults || this.reportData.categoryResults.length === 0) {
        return
      }
      
      const chartDom = this.$refs.radarChart
      const myChart = echarts.init(chartDom)
      
      const option = {
        title: {
          text: '风险矩阵雷达图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['当前得分', '满分']
        },
        radar: {
          indicator: this.reportData.categoryResults.map(category => ({
            name: category.categoryName,
            max: 100
          })),
          radius: '65%'
        },
        series: [
          {
            name: '风险矩阵',
            type: 'radar',
            data: [
              {
                value: this.reportData.categoryResults.map(category => category.categoryScore),
                name: '当前得分',
                areaStyle: {
                  color: 'rgba(215, 162, 86, 0.3)'
                },
                lineStyle: {
                  color: '#D7A256'
                },
                itemStyle: {
                  color: '#D7A256'
                }
              },
              {
                value: this.reportData.categoryResults.map(() => 100),
                name: '满分',
                lineStyle: {
                  color: '#E0E0E0',
                  type: 'dashed'
                },
                itemStyle: {
                  color: '#E0E0E0'
                }
              }
            ]
          }
        ]
      }
      
      myChart.setOption(option)
      
      // 响应式调整
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    
    getTypeTagType(type) {
      const typeMap = {
        'A类': 'success',
        'B类': 'warning',
        'C类': 'info',
        'D类': 'danger',
        'E类': 'info',
        'CDE': 'success'
      }
      return typeMap[type] || 'info'
    },
    
    getTypeName(type) {
      if (type === 'CDE') return 'CDE类'
      return type
    },
    
    getRiskLevelTagType(level) {
      const levelMap = {
        '低风险': 'success',
        '中风险': 'warning',
        '高风险': 'danger',
        '极高风险': 'danger'
      }
      return levelMap[level] || 'info'
    },
    
    getLevelTagType(levelName) {
      const levelMap = {
        '一档': 'danger',
        '二档': 'warning',
        '三档': 'info',
        '四档': 'success',
        '五档': 'success'
      }
      return levelMap[levelName] || 'info'
    },
    
    getSuggestion(level) {
      const suggestionMap = {
        '低风险': '继续保持现有管理措施，定期监控风险指标',
        '中风险': '加强监控，完善管理制度，制定改进计划',
        '高风险': '立即采取措施，制定改进计划，加强风险管控',
        '极高风险': '紧急处理，全面整改，建立应急响应机制'
      }
      return suggestionMap[level] || '建议加强管理'
    },
    
    getHighRiskCategories() {
      return this.reportData.categoryResults.filter(cat => 
        cat.level.name === '一档' || cat.level.name === '二档'
      )
    },
    
    showCalculationDetails(detail) {
      this.selectedCalculationDetail = detail
      this.calculationDetailVisible = true
    },
    
    showCategoryDetails(category) {
      this.selectedCategory = category;
      this.categoryDetailVisible = true;
    },
    
    handleBack() {
      this.$router.back()
    },
    
    exportReport() {
      // 实现导出报告功能
      this.$message.success('报告导出功能开发中...')
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.risk-matrix-detail-container {
  min-height: 100vh;
  background: #fbf6ee;
  
  .page-header {
    background: white;
    padding: 24px 32px;
    border-bottom: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          gap: 12px;
          
          i {
            color: #D7A256;
            font-size: 28px;
          }
        }
        
        .page-subtitle {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
        }
      }
      
      .action-section {
        .back-btn {
          margin-right: 12px;
        }
      }
    }
  }
  
  .basic-info-section,
  .radar-chart-section,
  .calculation-details-section,
  .suggestions-section {
    margin: 32px auto;
    max-width: 1200px;
    
    .section-header {
      background: white;
      padding: 20px 24px;
      border-radius: 8px 8px 0 0;
      border-bottom: 1px solid #f0f0f0;
      
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #D7A256;
        }
      }
      
      .section-subtitle {
        margin: 8px 0 0 0;
        color: #7f8c8d;
        font-size: 14px;
      }
    }
  }
  
  .basic-info-section {
    .info-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      padding: 24px;
      background: white;
      border-radius: 0 0 8px 8px;
      
      .info-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        
        .card-icon {
          margin-right: 16px;
          
          i {
            font-size: 24px;
            color: #D7A256;
          }
        }
        
        .card-content {
          .card-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 4px;
          }
          
          .card-value {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
          }
        }
      }
    }
  }
  
  .radar-chart-section {
    .chart-container {
      background: white;
      padding: 24px;
      border-radius: 0 0 8px 8px;
      
      .radar-chart {
        width: 100%;
        height: 500px;
      }
    }
  }
  
  .calculation-details-section {
    .detail-section {
      background: white;
      margin-bottom: 24px;
      border-radius: 8px;
      overflow: hidden;
      
      .detail-title {
        padding: 16px 24px;
        background: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #D7A256;
        }
      }
      
      .detail-content {
        padding: 24px;
        
        .detail-table {
          .selected-option {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            
            .option-label {
              font-weight: 600;
              color: #D7A256;
            }
            
            .option-text {
              color: #2c3e50;
            }
            
            .option-score {
              color: #7f8c8d;
              font-size: 12px;
            }
          }
          
          .score-value {
            font-weight: 600;
            color: #D7A256;
          }
          
          .final-score {
            font-weight: 600;
            color: #27ae60;
          }
          
          .category-score {
            font-weight: 600;
            color: #D7A256;
          }
          
          .detail-btn {
            color: #D7A256;
          }
        }
      }
    }
  }
  
  .suggestions-section {
    .suggestions-content {
      background: white;
      padding: 24px;
      border-radius: 0 0 8px 8px;
      
      .suggestion-card {
        margin-bottom: 16px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #D7A256;
        
        .suggestion-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-weight: 600;
          color: #2c3e50;
          
          i {
            color: #D7A256;
          }
        }
        
        .suggestion-content {
          color: #7f8c8d;
          line-height: 1.6;
        }
      }
    }
  }
  
  .detail-item {
    margin-bottom: 16px;
    
    label {
      font-weight: 600;
      color: #2c3e50;
      margin-right: 8px;
    }
    
    .variables-list {
      margin-top: 8px;
      
      .variable-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        
        .variable-name {
          font-weight: 600;
          color: #D7A256;
          min-width: 60px;
        }
        
        .variable-value {
          color: #2c3e50;
        }
      }
    }
    
    .result-value {
      font-weight: 600;
      color: #27ae60;
      font-size: 16px;
    }
    
    .score-items-list {
      margin-top: 8px;
      
      .score-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 4px;
        
        .item-name {
          color: #2c3e50;
        }
        
        .item-score {
          font-weight: 600;
          color: #D7A256;
        }
      }
    }
  }
}
</style> 