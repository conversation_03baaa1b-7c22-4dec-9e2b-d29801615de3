<template>
  <div class="category-edit-container">
    <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
      :loading="loading"
      @back="handleBack"
      @breadcrumb-click="handleBreadcrumbClick"
    >
      <div class="category-edit-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="category-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info-section">
              <UniversalForm
                ref="basicForm"
                :form-data="form"
                :form-rules="formRules"
                :form-groups="computedFormGroups"
                :is-view="isViewMode"
                label-width="120px"
              />
            </div>
          </el-tab-pane>
          
          <!-- 档次配置 -->
          <el-tab-pane label="档次配置" name="levels">
            <div class="levels-section">
              <div class="levels-header">
                <div class="header-left">
                  <h3>档次配置</h3>
                  <span class="level-count">当前共 {{ form.levels.length }} 个档次</span>
                </div>
                <div class="header-right" v-if="!isViewMode">
                  <el-button type="primary" size="small" icon="el-icon-plus" @click="addLevel">添加档次</el-button>
                </div>
              </div>
              
              <div class="levels-table-wrapper">
                <el-table :data="form.levels" class="levels-table" empty-text="暂无档次配置">
                  <el-table-column label="序号" type="index" width="60" align="center" />
                  
                  <el-table-column label="区间范围" width="200" align="center">
                    <template slot-scope="scope">
                      <div class="range-inputs" v-if="!isViewMode">
                        <el-input-number
                          v-model="scope.row.min"
                          :precision="2"
                          :step="0.1"
                          size="mini"
                          style="width: 80px"
                          placeholder="最小值"
                        />
                        <span class="range-separator">~</span>
                        <el-input-number
                          v-model="scope.row.max"
                          :precision="2"
                          :step="0.1"
                          size="mini"
                          style="width: 80px"
                          placeholder="最大值"
                        />
                      </div>
                      <div v-else class="range-display">
                        {{ scope.row.min }} ~ {{ scope.row.max }}
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="档次名称" min-width="120">
                    <template slot-scope="scope">
                      <el-input
                        v-if="!isViewMode"
                        v-model="scope.row.name"
                        size="mini"
                        placeholder="请输入档次名称"
                        maxlength="20"
                      />
                      <span v-else>{{ scope.row.name }}</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="对应文案" min-width="200">
                    <template slot-scope="scope">
                      <el-input
                        v-if="!isViewMode"
                        v-model="scope.row.description"
                        size="mini"
                        placeholder="请输入对应文案"
                        maxlength="100"
                      />
                      <span v-else>{{ scope.row.description }}</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="操作" width="80" align="center" v-if="!isViewMode">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="removeLevel(scope.$index)"
                        class="delete-btn"
                        :disabled="form.levels.length <= 1"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              
              <div class="levels-tips" v-if="!isViewMode">
                <el-alert
                  title="配置提示"
                  type="info"
                  :closable="false"
                  show-icon
                >
                  <div slot="description">
                    <p>• 区间范围不能重叠，系统会自动验证</p>
                    <p>• 至少需要配置一个档次</p>
                    <p>• 建议按照分数从低到高的顺序配置档次</p>
                  </div>
                </el-alert>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        
        <!-- 操作按钮 -->
        <div class="action-buttons" v-if="!isViewMode">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
        </div>
      </div>
    </EditPageContainer>
  </div>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { mockScoreItemList } from '@/mock'
import { getRiskMatrixDetail, updateRiskMatrix } from '@/api/riskMatrix'

export default {
  name: 'CategoryEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      loading: false,
      saving: false,
      activeTab: 'basic',
      matrixId: null,
      categoryId: null,
      matrixInfo: {},
      scoreItemOptions: [],
      form: {
        id: null,
        name: '',
        description: '',
        calculationMethod: 'sum',
        scoreItems: [],
        levels: [
          {
            min: 0,
            max: 100,
            name: '',
            description: ''
          }
        ]
      },
      formRules: {
        name: [
          { required: true, message: '请输入类别名称', trigger: 'blur' },
          { min: 2, max: 50, message: '类别名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        calculationMethod: [
          { required: true, message: '请选择计算方式', trigger: 'change' }
        ],
        scoreItems: [
          { required: true, message: '请选择关联的评分项', trigger: 'change' }
        ]
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              {
                type: 'input',
                prop: 'name',
                label: '类别名称',
                placeholder: '请输入类别名称',
                maxlength: 50,
                showWordLimit: true,
                span: 12
              },
              {
                type: 'select',
                prop: 'calculationMethod',
                label: '计算方式',
                placeholder: '请选择计算方式',
                options: [
                  { label: '加和', value: 'sum' },
                  { label: '平均数', value: 'average' }
                ],
                span: 12
              }
            ],
            [
              {
                type: 'select',
                prop: 'scoreItems',
                label: '关联评分项',
                placeholder: '请选择关联的评分项',
                multiple: true,
                options: [],
                span: 24
              }
            ],
            [
              {
                type: 'textarea',
                prop: 'description',
                label: '类别描述',
                placeholder: '请输入类别描述（可选）',
                rows: 3,
                maxlength: 200,
                showWordLimit: true,
                span: 16
              }
            ]
          ]
        }
      ]
    }
  },
  computed: {
    isNewCategory() {
      return this.categoryId === 'new'
    },
    isViewMode() {
      return this.$route.query.mode === 'view'
    },
    pageTitle() {
      if (this.isViewMode) {
        return `查看类别 - ${this.form.name || '核心类别'}`
      }
      return this.isNewCategory ? '新增核心类别' : `编辑类别 - ${this.form.name || '核心类别'}`
    },
    pageIcon() {
      return this.isViewMode ? 'el-icon-view' : (this.isNewCategory ? 'el-icon-plus' : 'el-icon-edit')
    },
    breadcrumbItems() {
      return [
        { text: '风险矩阵管理', icon: 'el-icon-s-grid', to: { name: 'riskMatrixList' } },
        { text: '核心类别管理', icon: 'el-icon-s-operation', to: { name: 'categoryList', params: { matrixId: this.matrixId } } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    },
    computedFormGroups() {
      const groups = JSON.parse(JSON.stringify(this.formGroups))
      // 更新关联评分项的选项
      groups[0].fields[1][0].options = this.scoreItemOptions
      return groups
    }
  },
  created() {
    this.matrixId = this.$route.params.matrixId
    this.categoryId = this.$route.params.categoryId
    
    // 如果有指定标签页，则切换到对应标签页
    if (this.$route.query.tab) {
      this.activeTab = this.$route.query.tab
    }
    
    this.loadScoreItemOptions()
    this.loadData()
  },
  methods: {
    loadScoreItemOptions() {
      this.scoreItemOptions = mockScoreItemList.map(item => ({
        label: item.name,
        value: item.id
      }))
    },
    async loadData() {
      if (!this.matrixId) {
        this.$message.error('缺少风险矩阵ID')
        return
      }
      
      this.loading = true
      try {
        const response = await getRiskMatrixDetail(this.matrixId)
        if (response.code === 200 && response.data) {
          this.matrixInfo = {
            id: response.data.id,
            name: response.data.name,
            description: response.data.description
          }
          
          if (!this.isNewCategory) {
            // 编辑或查看模式，加载现有类别数据
            const categories = response.data.categories || []
            const category = categories.find(cat => cat.id === this.categoryId)
            
            if (category) {
              this.form = {
                id: category.id,
                name: category.name || '',
                description: category.description || '',
                calculationMethod: category.calculationMethod || 'sum',
                scoreItems: category.scoreItems || [],
                levels: category.levels && category.levels.length > 0 ? category.levels : [
                  {
                    min: 0,
                    max: 100,
                    name: '',
                    description: ''
                  }
                ]
              }
            } else {
              this.$message.error('未找到指定的类别')
              this.handleBack()
            }
          } else {
            // 新增模式，生成新的ID
            this.form.id = 'category_' + Date.now()
          }
        } else {
          this.$message.error('加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    addLevel() {
      this.form.levels.push({
        min: 0,
        max: 100,
        name: '',
        description: ''
      })
    },
    removeLevel(index) {
      if (this.form.levels.length > 1) {
        this.form.levels.splice(index, 1)
      }
    },
    validateLevels() {
      const levels = this.form.levels
      
      // 检查是否有空的档次名称
      for (let i = 0; i < levels.length; i++) {
        if (!levels[i].name || !levels[i].name.trim()) {
          this.$message.error(`第 ${i + 1} 个档次的名称不能为空`)
          return false
        }
      }
      
      // 检查区间是否有效
      for (let i = 0; i < levels.length; i++) {
        const level = levels[i]
        if (level.min >= level.max) {
          this.$message.error(`第 ${i + 1} 个档次的区间范围无效，最小值应小于最大值`)
          return false
        }
      }
      
      // 检查区间是否重叠
      for (let i = 0; i < levels.length; i++) {
        for (let j = i + 1; j < levels.length; j++) {
          const level1 = levels[i]
          const level2 = levels[j]
          
          if ((level1.min < level2.max && level1.max > level2.min)) {
            this.$message.error(`第 ${i + 1} 个档次与第 ${j + 1} 个档次的区间范围重叠`)
            return false
          }
        }
      }
      
      return true
    },
    async handleSave() {
      try {
        // 验证基本信息表单
        await this.$refs.basicForm.validate()
        
        // 验证档次配置
        if (!this.validateLevels()) {
          this.activeTab = 'levels'
          return
        }
        
        this.saving = true
        
        // 获取当前风险矩阵数据
        const response = await getRiskMatrixDetail(this.matrixId)
        if (response.code !== 200 || !response.data) {
          this.$message.error('获取风险矩阵数据失败')
          return
        }
        
        const matrixData = response.data
        let categories = matrixData.categories || []
        
        if (this.isNewCategory) {
          // 新增类别
          categories.push({
            ...this.form,
            createTime: new Date().toISOString()
          })
        } else {
          // 编辑类别
          const index = categories.findIndex(cat => cat.id === this.categoryId)
          if (index > -1) {
            categories[index] = {
              ...categories[index],
              ...this.form,
              updateTime: new Date().toISOString()
            }
          }
        }
        
        // 更新风险矩阵
        const updateResponse = await updateRiskMatrix(this.matrixId, {
          ...matrixData,
          categories
        })
        
        if (updateResponse.code === 200) {
          this.$message.success(this.isNewCategory ? '新增成功' : '保存成功')
          this.handleBack()
        } else {
          this.$message.error('保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },
    handleCancel() {
      this.handleBack()
    },
    handleBack() {
      this.$router.push({
        name: 'categoryList',
        params: { matrixId: this.matrixId }
      })
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.category-edit-container {
  min-height: 100vh;
  background: #fbf6ee;
}

.category-edit-content {
  padding: 24px;
  
  .category-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    /deep/ .el-tabs__header {
      margin: 0;
      background: #fbf6ee;
      border-radius: 8px 8px 0 0;
      
      .el-tabs__nav-wrap {
        padding: 0 24px;
        
        .el-tabs__item {
          font-size: 16px;
          font-weight: 500;
          color: #666;
          
          &.is-active {
            color: #D7A256;
          }
        }
        
        .el-tabs__active-bar {
          background-color: #D7A256;
        }
      }
    }
    
    /deep/ .el-tab-pane {
      padding: 24px;
    }
  }
  
  .basic-info-section {
    .category-form {
      max-width: 800px;
      
      /deep/ .el-form-item__label {
        font-weight: 500;
        color: #2c3e50;
      }
    }
  }
  
  .levels-section {
    .levels-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .header-left {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }
        
        .level-count {
          color: #666;
          font-size: 14px;
        }
      }
      
      .header-right {
        .el-button {
          background: #D7A256;
          border-color: #D7A256;
          
          &:hover {
            background: #E6B366;
            border-color: #E6B366;
          }
        }
      }
    }
    
    .levels-table-wrapper {
      margin-bottom: 20px;
      border: 1px solid #f7ecdd;
      border-radius: 6px;
      overflow: hidden;
      
      .levels-table {
        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #fbf6ee;
              font-weight: 600;
              font-size: 14px;
              color: #2c3e50;
              border-bottom: 1px solid #f7ecdd;
            }
          }
        }
        
        /deep/ .el-table__body-wrapper {
          .el-table__row {
            td {
              border-bottom: 1px solid #f7ecdd;
              padding: 12px 0;
            }
          }
        }
        
        .range-inputs {
          display: flex;
          align-items: center;
          justify-content: center;
          
          .range-separator {
            margin: 0 8px;
            color: #666;
            font-weight: 500;
          }
        }
        
        .range-display {
          text-align: center;
          color: #2c3e50;
          font-weight: 500;
        }
        
        .delete-btn {
          color: #f56c6c;
          
          &:hover {
            color: #f56c6c;
            background: rgba(245, 108, 108, 0.1);
          }
          
          &.is-disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }
    
    .levels-tips {
      /deep/ .el-alert {
        border-radius: 6px;
        
        .el-alert__description {
          p {
            margin: 4px 0;
            color: #666;
          }
        }
      }
    }
  }
  
  .action-buttons {
    margin-top: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    text-align: right;
    
    .el-button {
      margin-left: 12px;
      
      &[type="primary"] {
        background: #D7A256;
        border-color: #D7A256;
        
        &:hover {
          background: #E6B366;
          border-color: #E6B366;
        }
      }
    }
  }
}
</style>