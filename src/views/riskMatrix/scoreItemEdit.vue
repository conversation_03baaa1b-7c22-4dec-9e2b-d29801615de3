<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>
<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getScoreItemList, saveScoreItem } from '@/api/riskMatrix/index.js'
import { getFormulaList } from '@/api/formulaEngine'
export default {
  name: 'ScoreItemEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        id: '',
        name: '',
        formulaId: '',
        formulaName: '',
        coefficient: 1.00,
        enterpriseTypes: []
      },
      formGroups: [
        {
          title: '评分项信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '评分项名称', type: 'input', placeholder: '请输入评分项名称', maxlength: 50, showWordLimit: true, required: true },
              { prop: 'coefficient', label: '系数', type: 'number', min: 0, max: 999, precision: 2, step: 0.01, required: true, placeholder: '请输入系数，支持两位小数' }
            ],
            [
              { prop: 'formulaId', label: '关联公式', type: 'select', placeholder: '请选择关联公式', options: [], required: true },
              { prop: 'enterpriseTypes', label: '企业类型', type: 'select', multiple: true, options: [], placeholder: '请选择企业类型', required: true }
            ]
          ]
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入评分项名称', trigger: 'blur' },
          { min: 1, max: 50, message: '评分项名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        formulaId: [
          { required: true, message: '请选择关联公式', trigger: 'change' }
        ],
        coefficient: [
          { required: true, message: '请输入系数', trigger: 'blur' },
          { type: 'number', min: 0, message: '系数不能小于0', trigger: 'blur' }
        ],
        enterpriseTypes: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ]
      },
      formulaOptions: [],
      enterpriseTypeOptions: [
        { value: 'A', label: 'A类' },
        { value: 'B', label: 'B类' },
        { value: 'C', label: 'C类' },
        { value: 'D', label: 'D类' },
        { value: 'E', label: 'E类' }
      ]
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) return '查看评分项'
      return this.form.id ? '编辑评分项' : '新建评分项'
    },
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.form.id ? 'el-icon-edit' : 'el-icon-plus')
    },
    breadcrumbItems() {
      return [
        { text: '核心评分项管理', icon: 'el-icon-s-flag', to: { name: 'scoreItemList' } },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    this.isView = this.$route.query.mode === 'view'
    this.loadOptions()
    const id = this.$route.params.id
    if (id) {
      this.loadDetail(id)
    }
  },
  methods: {
    async loadOptions() {
      // 加载公式和企业类型选项
      const res = await getFormulaList({ pageNum: 1, pageSize: 100 })
      if (res.code === 200) {
        this.formulaOptions = res.data.list.map(f => ({ label: f.name, value: f.id }))
        // 更新表单配置
        const group = this.formGroups[0]
        group.fields[1][0].options = this.formulaOptions
      }
      // 企业类型
      const group = this.formGroups[0]
      group.fields[1][1].options = this.enterpriseTypeOptions
    },
    async loadDetail(id) {
      this.loading = true
      try {
        const res = await getScoreItemList({ pageNum: 1, pageSize: 100 })
        if (res.code === 200 && res.data && res.data.list) {
          const found = res.data.list.find(item => String(item.id) === String(id))
          if (found) {
            this.form = {
              id: found.id,
              name: found.name,
              formulaId: found.formulaId,
              formulaName: found.formulaName,
              coefficient: found.coefficient,
              enterpriseTypes: found.enterpriseTypes || []
            }
          }
        }
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      if (this.isView) return
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        // 设置公式名称
        const formula = this.formulaOptions.find(f => f.value === this.form.formulaId)
        this.form.formulaName = formula ? formula.label : ''
        const res = await saveScoreItem(this.form)
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.score-item-edit-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style> 