<template>
  <div class="online-product-config-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="线上产品配置"
      subtitle="配置行业风险等级和险种类型，支持动态调整产品配置规则"
      title-icon="el-icon-goods"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="220"
      :search-label-width="'100px'"
      add-button-text="新增配置"
      empty-title="暂无产品配置"
      empty-description="点击上方新增配置按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 自定义列内容插槽 -->
      <template #industryCode="{ row }">
        <span>{{ getIndustryName(row.industryCode) }}</span>
          </template>
      
      <template #probability="{ row }">
        <el-tag :type="row.probability === '高' ? 'danger' : row.probability === '中' ? 'warning' : 'info'" size="small">
          {{ row.probability }}
        </el-tag>
          </template>
      
      <template #impact="{ row }">
        <el-tag :type="row.impact === '高' ? 'danger' : row.impact === '中' ? 'warning' : 'info'" size="small">
          {{ row.impact }}
        </el-tag>
          </template>
      
      <template #level="{ row }">
        <el-tag :type="row.level === '高' ? 'danger' : row.level === '中' ? 'warning' : 'info'" size="small">
          {{ row.level }}
        </el-tag>
          </template>
      
      <template #insuranceTypes="{ row }">
        <div v-if="row.insuranceTypes && row.insuranceTypes.length" >
              <el-tag 
              type="info"
              size="small" 
              v-for="type in row.insuranceTypes" 
                :key="type" 
              style="margin-bottom: 4px; margin-left: 4px;"
              >
                {{ getInsuranceTypeName(type) }}
              </el-tag>
            </div>
        <span v-else style="color:#bbb;">未配置</span>
          </template>
      
      <template #enabled="{ row }">
        <el-tag :type="row.enabled ? 'success' : 'info'" size="small">
          {{ row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
      
      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
      </div>
      </template>
    </UniversalTable>



    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该配置？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { 
  getIndustryOptions, 
  getOnlineProductConfig,
  saveIndustryConfig, 
  getInsuranceTypeOptions 
} from '@/api/onlineProduct'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'OnlineProductConfig',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        industryCode: '',
        probability: '',
        level: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'industryCode',
          label: '行业分类',
          width: 120,
          align: 'center'
        },
        {
          prop: 'probability',
          label: '风险发生概率',
          width: 120,
          align: 'center'
        },
        {
          prop: 'impact',
          label: '风险影响程度',
          width: 120,
          align: 'center'
        },
        {
          prop: 'level',
          label: '风险等级',
          width: 120,
          align: 'center'
        },
        {
          prop: 'insuranceTypes',
          label: '险种类型',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'enabled',
          label: '状态',
          width: 100,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '行业类别',
          name: 'industryCode',
          type: 'select',
          placeholder: '请选择行业类别',
          options: []
        },
        {
          label: '风险概率',
          name: 'probability',
          type: 'select',
          placeholder: '请选择风险概率',
          options: [
            { label: '高', value: '高' },
            { label: '中', value: '中' },
            { label: '低', value: '低' }
          ]
        },
        {
          label: '风险等级',
          name: 'level',
          type: 'select',
          placeholder: '请选择风险等级',
          options: [
            { label: '高', value: '高' },
            { label: '中', value: '中' },
            { label: '低', value: '低' }
          ]
        }
      ],
      industryOptions: [],
      insuranceTypeOptions: [],
      currentConfig: null
    }
  },
  mounted() {
    this.loadIndustryOptions();
    this.loadInsuranceTypeOptions();
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      try {
        const response = await getOnlineProductConfig();
        if (response.code === 200 && response.data) {
          // 将所有行业的配置项合并到一个扁平数组中，并添加行业代码
          let allConfigs = [];
          response.data.forEach(industry => {
            if (industry.configs && Array.isArray(industry.configs)) {
              const industryConfigs = industry.configs.map(config => ({
                ...config,
                industryCode: industry.industryCode,
                createTime: config.createTime || new Date().toLocaleString()
              }));
              allConfigs = [...allConfigs, ...industryConfigs];
            }
          });
          this.tableData = allConfigs;
          this.pagination.total = allConfigs.length;
        }
      } catch (error) {
        console.error('加载配置失败:', error);
        this.$message.error('加载配置失败');
        this.tableData = [];
      } finally {
        this.loading = false;
      }
    },
    async loadIndustryOptions() {
      try {
        const response = await getIndustryOptions();
        if (response.code === 200) {
          this.industryOptions = response.data;
          // 更新搜索表单配置中的行业选项
          this.searchFormConfig[0].options = response.data.map(item => ({
            label: item.dicItemName,
            value: item.dicItemCode
          }));
        }
      } catch (error) {
        console.error('加载行业选项失败:', error);
        this.$message.error('加载行业选项失败');
      }
    },
    async loadInsuranceTypeOptions() {
      try {
        const response = await getInsuranceTypeOptions();
        if (response.code === 200) {
          this.insuranceTypeOptions = response.data;
        }
      } catch (error) {
        console.error('加载险种类型选项失败:', error);
        this.$message.error('加载险种类型选项失败');
      }
    },
    getIndustryName(code) {
      const item = this.industryOptions.find(i => i.dicItemCode === code);
      return item ? item.dicItemName : code;
    },
    getInsuranceTypeName(value) {
      const item = this.insuranceTypeOptions.find(i => i.value === value);
      return item ? item.label : value;
    },
    getProbabilityTagType(probability) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[probability] || 'info';
    },
    getImpactTagType(impact) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[impact] || 'info';
    },
    getLevelTagType(level) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[level] || 'info';
    },
    // UniversalTable 事件处理方法
    handleSearch(params) {
      this.searchForm = { ...params.param };
      this.loadData();
    },
    handleReset() {
      this.searchForm = {
        industryCode: '',
        probability: '',
        level: ''
      };
      this.loadData();
    },
    handleAdd() {
      this.$router.push({
        name: 'onlineProductConfigEdit'
      });
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.loadData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadData();
    },
    handleAction(actionData) {
      const { action, row } = actionData;
      if (action === 'view') {
        this.handleView(row);
      } else if (action === 'edit') {
        this.handleEdit(row);
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },
    handleView(row) {
      this.$router.push({
        name: 'onlineProductConfigEdit',
        params: { id: row.id },
        query: { mode: 'view' }
      });
    },
    handleEdit(row) {
      this.$router.push({
        name: 'onlineProductConfigEdit',
        params: { id: row.id },
        query: { mode: 'edit' }
      });
    },
    handleDelete(row) {
      this.$refs.confirmDialog.show();
      this.currentConfig = row;
    },
    async confirmDelete() {
      if (this.currentConfig) {
        try {
          // 从本地数组中移除
          const index = this.tableData.findIndex(item => 
              item.id === this.currentConfig.id && item.industryCode === this.currentConfig.industryCode
            );
            
            if (index !== -1) {
            this.tableData.splice(index, 1);
            this.pagination.total = this.tableData.length;
            
            // 调用API保存更改
            await this.saveChanges();
            this.$message.success('删除成功');
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
          // 删除失败时重新加载数据
          this.loadData();
        }
      }
    },

    async saveChanges() {
      try {
        // 将扁平的配置列表按行业分组
        const industryConfigs = {};
        
        this.tableData.forEach(config => {
          const { industryCode } = config;
          if (!industryConfigs[industryCode]) {
            industryConfigs[industryCode] = [];
          }
          // 创建一个不包含industryCode的配置副本
          const { industryCode: _, ...configWithoutIndustry } = config;
          industryConfigs[industryCode].push(configWithoutIndustry);
        });
        
        // 保存每个行业的配置
        const savePromises = Object.keys(industryConfigs).map(industryCode => 
          saveIndustryConfig(industryCode, industryConfigs[industryCode])
        );
        
        await Promise.all(savePromises);
        
        // 重新加载所有配置
        this.loadData();
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      }
    }
  }
}
</script>