<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { 
  getIndustryOptions, 
  getInsuranceTypeOptions,
  getOnlineProductConfigDetail,
  saveIndustryConfig
} from '@/api/onlineProduct'

export default {
  name: 'OnlineProductConfigEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: '',
        industryCode: '',
        probability: '',
        impact: '',
        level: '',
        insuranceTypes: [],
        enabled: true,
        description: '',
        createTime: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { 
                prop: 'industryCode', 
                label: '行业分类', 
                type: 'select', 
                placeholder: '请选择行业分类', 
                options: [],
                disabled: (formData) => this.isEdit // 编辑模式下行业分类不可修改
              },
              { 
                prop: 'probability', 
                label: '风险发生概率', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              },
              { 
                prop: 'impact', 
                label: '风险影响程度', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              }
            ],
            [
              { 
                prop: 'level', 
                label: '风险等级', 
                type: 'radio', 
                options: [
                  { label: '高', value: '高' },
                  { label: '中', value: '中' },
                  { label: '低', value: '低' }
                ]
              },
              { 
                prop: 'insuranceTypes', 
                label: '险种类型', 
                type: 'select', 
                placeholder: '请选择险种类型', 
                multiple: true,
                options: []
              },
                             { 
                 prop: 'enabled', 
                 label: '是否启用', 
                 type: 'radio',
                 options: [
                   { label: '启用', value: true },
                   { label: '禁用', value: false }
                 ]
               }
            ],
            [
              { 
                prop: 'description', 
                label: '描述信息', 
                type: 'textarea', 
                placeholder: '请输入描述信息', 
                maxlength: 500, 
                rows: 3, 
                showWordLimit: true, 
                span: 24 
              }
            ]
          ]
        }
      ],
      formRules: {
        industryCode: [
          { required: true, message: '请选择行业分类', trigger: 'change' }
        ],
        probability: [
          { required: true, message: '请选择风险发生概率', trigger: 'change' }
        ],
        impact: [
          { required: true, message: '请选择风险影响程度', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ],
        insuranceTypes: [
          { required: true, message: '请选择至少一个险种类型', trigger: 'change' }
        ]
      },
      industryOptions: [],
      insuranceTypeOptions: []
    }
  },
  
  computed: {
    // 页面标题
    pageTitle() {
      return this.isView ? '查看产品配置' : (this.isEdit ? '编辑产品配置' : '新增产品配置')
    },
    
    // 页面图标
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    
    // 面包屑导航
    breadcrumbItems() {
      return [
        {
          text: '线上产品配置',
          icon: 'el-icon-goods',
          to: { name: 'onlineProductConfig' }
        },
        {
          text: this.pageTitle,
          icon: this.pageIcon
        }
      ]
    }
  },
  
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    
    if (id) {
      this.isEdit = mode !== 'view'
      this.isView = mode === 'view'
      this.loadData(id)
    }
    
    // 加载选项数据
    this.loadOptions()
  },
  
  methods: {
    async loadOptions() {
      try {
        // 并行加载行业选项和险种类型选项
        const [industryResponse, insuranceResponse] = await Promise.all([
          getIndustryOptions(),
          getInsuranceTypeOptions()
        ])
        
        if (industryResponse.code === 200) {
          this.industryOptions = industryResponse.data
          // 更新表单配置中的行业选项
          this.formGroups[0].fields[0][0].options = industryResponse.data.map(item => ({
            label: item.dicItemName,
            value: item.dicItemCode
          }))
        }
        
        if (insuranceResponse.code === 200) {
          this.insuranceTypeOptions = insuranceResponse.data
          // 更新表单配置中的险种类型选项
          this.formGroups[0].fields[1][1].options = insuranceResponse.data
        }
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },
    
    async loadData(id) {
      this.loading = true
      try {
        const response = await getOnlineProductConfigDetail(id)
        if (response.code === 200) {
          this.form = { 
            ...response.data,
            createTime: response.data.createTime || new Date().toLocaleString()
          }
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        // 准备保存的数据
        const saveData = { ...this.form }
        
        // 如果是编辑模式，需要按行业分组保存
        if (this.isEdit) {
          // 获取当前配置的行业代码
          const industryCode = this.form.industryCode
          
          // 创建新配置对象
          const updatedConfig = {
            id: this.form.id,
            probability: this.form.probability,
            impact: this.form.impact,
            level: this.form.level,
            insuranceTypes: this.form.insuranceTypes,
            enabled: this.form.enabled,
            description: this.form.description,
            createTime: this.form.createTime
          }
          
          // 保存配置
          const response = await saveIndustryConfig(industryCode, [updatedConfig])
          
          if (response.code === 200) {
            this.$message.success('更新成功')
            this.handleBack()
          } else {
            this.$message.error(response.message || '更新失败')
          }
        } else {
          // 新增模式
          const industryCode = this.form.industryCode
          
          // 创建新配置
          const newConfig = {
            id: Date.now(),
            probability: this.form.probability,
            impact: this.form.impact,
            level: this.form.level,
            insuranceTypes: this.form.insuranceTypes,
            enabled: this.form.enabled,
            description: this.form.description,
            createTime: new Date().toLocaleString()
          }
          
          // 保存配置
          const response = await saveIndustryConfig(industryCode, [newConfig])
          
          if (response.code === 200) {
            this.$message.success('创建成功')
            this.handleBack()
          } else {
            this.$message.error(response.message || '创建失败')
          }
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      this.$router.push({ name: 'onlineProductConfig' })
    },
    
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style> 