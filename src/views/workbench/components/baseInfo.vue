<template>
  <div class="base-info">
    <div class="tool-wrap">
      <el-button type="primary" v-for="(item, index) in toolList" :key="index" @click="handleTool(item)">{{ item.name
      }}</el-button>
    </div>
    <TableToolTemp :toolListProps="{ toolTitle: '机会信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">机会名称：海底捞-员福-2025</div>
      <div class="item">机会ID：海底捞-员福-2025</div>
      <div class="item">提交时间：海底捞-员福-2025</div>
      <div class="item">业务渠道：海底捞-员福-2025</div>
      <div class="item">机会状态：海底捞-员福-2025</div>
      <div class="item">历史保单：海底捞-员福-2025</div>
      <div class="item">历史保单到期时间：海底捞-员福-2025</div>
      <div class="item">期望保障生效时间：海底捞-员福-2025</div>
      <div class="item">保费预算：海底捞-员福-2025</div>
      <div class="item">预估投保人数：海底捞-员福-2025</div>
      <div class="item">顾问姓名：海底捞-员福-2025</div>
      <div class="item">顾问工号：海底捞-员福-2025</div>
      <div class="item">所属机构：海底捞-员福-2025</div>
      <div class="item">是否需要投标：海底捞-员福-2025</div>
      <div class="item">投标结束时间：海底捞-员福-2025</div>
      <div class="item">客户需求：海底捞-员福-2025</div>
    </div>
    <div class="item-tips">备注：此处是顾问填写的备注内容，客户期望解决的风险</div>

    <TableToolTemp :toolListProps="{ toolTitle: '客户信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">企业名称：海底捞-员福-2025</div>
      <div class="item">社会统一信用代码：海底捞-员福-2025</div>
      <div class="item">企业所在城市：海底捞-员福-2025</div>
      <div class="item">企业所属行业：海底捞-员福-2025</div>
      <div class="item">企业年收入：海底捞-员福-2025</div>
      <div class="item">企业人员规模：海底捞-员福-2025</div>
      <div class="item">企业类型：海底捞-员福-2025</div>
      <div class="item">企业对接人姓名：海底捞-员福-2025</div>
      <div class="item">企业对接人职务：海底捞-员福-2025</div>
      <div class="item">KYC报告：海底捞-员福-2025</div>
      <div class="item">KYC 报告生成时间：海底捞-员福-2025</div>
      <div class="item">风险评估报告：海底捞-员福-2025</div>
      <div class="item">风险评估报告生成时间：海底捞-员福-2025</div>
      <div class="item">风险评估结果：海底捞-员福-2025</div>
      <div class="item">第三方校验信息：海底捞-员福-2025</div>
      <div class="item">第三方报告查询时间：海底捞-员福-2025</div>
    </div>

    <TableToolTemp :toolListProps="{ toolTitle: '机会进度' }" class="log-tool">
      <template #tool>
        <el-button type="primary" size="small" @click="showDemoOptions">演示不同任务组合</el-button>
      </template>
    </TableToolTemp>
    <div class="step-wrap">
      <el-steps :active="active">
        <el-step v-for="(item, index) in processList" :key="index" :title="item.name"></el-step>
      </el-steps>

      <div class="step-list">
        <div class="step-item" v-for="(item, index) in stepList" :key="index">
          <div class="radius" :style="{ background: themeObj.color }"></div>
          <div class="item-date" v-if="item.date">{{ item.date }}</div>

          <!-- 历史记录 -->
          <div class="step-info-wrap">
            <div class="info-title">{{ item.title }}</div>
            <div class="info-text" v-if="item.description">
              <span>{{ item.description }}</span>
            </div>

            <!-- 任务块列表 -->
            <div class="task-blocks" v-if="item.tasks && item.tasks.length">
              <div class="task-block" v-for="(task, taskIndex) in item.tasks" :key="taskIndex">
                <div class="task-title">{{ task.title }}</div>
                <div class="task-content">
                  <!-- 需执行事项 -->
                  <div class="execution-items" v-if="task.actions && task.actions.length">
                    <div v-for="action in task.actions" :key="action.id" class="action-row">
                      <span class="item-label">需执行事项：</span>
                      <span class="action-item">{{ action.name }}</span>
                      <span class="required-text">({{ action.required ? '必填' : '可选' }})</span>
                    </div>
                  </div>

                  <!-- 确认完成按钮 -->
                  <div class="task-completion">
                    <el-button type="primary" size="small" :disabled="taskCompletionStatus[task.title]"
                      @click="confirmTaskCompletion(task.title)" :loading="taskLoadingStatus[task.title]">
                      <i class="el-icon-check"></i>
                      确认已完成
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 领取机会确认弹窗 -->
    <DtPopup :isShow.sync="showClaimPopup" @close="closeClaimPopup" title="领取机会" center :footer="false" width="600px">
      <div class="claim-confirm">
        <div class="confirm-content">
          <p>领取成功后,该机会归属您来跟进,其他人员不可见,确认是否领取该机会?</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeClaimPopup">取消</el-button>
          <el-button type="primary" @click="confirmClaim" :loading="claimLoading">确认领取</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 暂停机会弹窗 -->
    <DtPopup :isShow.sync="showPausePopup" @close="closePausePopup" title="暂停机会" center :footer="false" width="600px">
      <div class="pause-form">
        <div class="pause-description">
          <p>如遇一些特殊原因导致该机会暂时无法推进,可通过本功能标记为"暂停状态",该状态和原因会同步至所有项目参与人员可见,暂停的机会可以随时开启。</p>
        </div>
        <el-form ref="pauseFormRef" :model="pauseForm" :rules="pauseRules" label-width="0">
          <el-form-item prop="pauseReason" required>
            <el-input type="textarea" v-model="pauseForm.pauseReason" placeholder="请输入暂停原因 (必填)" :rows="4"
              maxlength="150" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closePausePopup">取消</el-button>
          <el-button type="primary" @click="confirmPause" :loading="pauseLoading">确认暂停</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 启动机会弹窗 -->
    <DtPopup :isShow.sync="showRestartPopup" @close="closeRestartPopup" title="重启机会" center :footer="false"
      width="600px">
      <div class="restart-form">
        <div class="restart-description">
          <p>确认重启后,该机会将恢复至正常推进状态,同时所有项目参与人员可见,是否确认本次操作?</p>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartPopup">取消</el-button>
          <el-button type="primary" @click="confirmRestart" :loading="restartLoading">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 关闭机会弹窗 -->
    <DtPopup :isShow.sync="showClosePopup" @close="closeClosePopup" title="关闭机会" center :footer="false" width="600px">
      <div class="close-form">
        <el-form ref="closeFormRef" :model="closeForm" :rules="closeRules">
          <el-form-item prop="closeReasonType" required>
            <el-radio-group v-model="closeForm.closeReasonType">
              <el-radio label="成交">机会已成交</el-radio>
              <el-radio label="失败">机会推进失败</el-radio>
              <el-radio label="无效">无效机会</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="关闭原因" prop="closeReason" :rules="closeReasonRules">
            <el-input type="textarea" v-model="closeForm.closeReason" :placeholder="closeReasonPlaceholder" :rows="4"
              maxlength="150" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closeClosePopup">取消</el-button>
          <el-button type="primary" @click="confirmClose" :loading="closeLoading">确认关闭</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 重启关闭机会弹窗 -->
    <DtPopup :isShow.sync="showRestartClosePopup" @close="closeRestartClosePopup" title="重启机会" center :footer="false"
      width="900px">
      <div class="assign-content">
        <!-- 提示文案 -->
        <div class="restart-close-tip">
          <p :style="{ color: themeObj.color }">该机会已被 北京分公司项目经理 王年金（wangnj）变更为"已关闭-机会推进失败"，是否确认重启并指派新的项目经理？</p>
        </div>

        <SearchForm :searchForm="restartCloseSearchForm" :searchFormTemp="restartCloseSearchFormTemp"
          @normalSearch="handleRestartCloseSearch" @normalResetQuery="resetRestartCloseSearch" />
        <div class="table-container">
          <el-table :data="restartCloseUserList" style="width: 100%;">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'restartCloseUserSelect'" :value="scope.row.name"
                  v-model="selectedRestartCloseUserName" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="name" label="人员姓名" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="email" label="邮箱" align="center" />
            <el-table-column prop="belong" label="人员归属" align="center" />
          </el-table>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartClosePopup">取消</el-button>
          <el-button type="primary" @click="confirmRestartClose" :loading="restartCloseLoading">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 指派统筹弹窗 -->
    <DtPopup :isShow.sync="showAssignPopup" @close="closeAssignPopup" title="指派统筹" center :footer="false" width="900px">
      <div class="assign-content">
        <SearchForm :searchForm="assignSearchForm" :searchFormTemp="assignSearchFormTemp"
          @normalSearch="handleAssignSearch" @normalResetQuery="resetAssignSearch" />
        <div class="table-container">
          <el-table :data="assignUserList" style="width: 100%;">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'assignUserSelect'" :value="scope.row.name" v-model="selectedAssignUserName"
                  style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="name" label="人员姓名" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="email" label="邮箱" align="center" />
            <el-table-column prop="belong" label="人员归属" align="center" />
          </el-table>
        </div>
        <div class="form-actions">
          <el-button @click="closeAssignPopup">取消</el-button>
          <el-button type="primary" @click="confirmAssign" :loading="assignLoading">确认指派</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 指派项目经理弹窗 -->
    <DtPopup :isShow.sync="showManagerPopup" @close="closeManagerPopup" title="指派项目经理" center :footer="false"
      width="900px">
      <div class="assign-content">
        <!-- 业务主体选择 -->
        <div class="business-type-section">
          <div class="section-title">业务主体</div>
          <el-radio-group v-model="managerBusinessType" @change="handleBusinessTypeChange">
            <el-radio label="经纪">经纪</el-radio>
            <el-radio label="销售">销售</el-radio>
          </el-radio-group>
        </div>

        <SearchForm :searchForm="managerSearchForm" :searchFormTemp="managerSearchFormTemp"
          @normalSearch="handleManagerSearch" @normalResetQuery="resetManagerSearch" />
        <div class="table-container">
          <el-table :data="managerUserList" style="width: 100%;">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'managerUserSelect'" :value="scope.row.name"
                  v-model="selectedManagerUserName" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="name" label="人员姓名" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="email" label="邮箱" align="center" />
            <el-table-column prop="belong" label="人员归属" align="center" />
          </el-table>
        </div>
        <div class="form-actions">
          <el-button @click="closeManagerPopup">取消</el-button>
          <el-button type="primary" @click="confirmManager" :loading="managerLoading">确认指派</el-button>
        </div>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import { getDicItemList } from "@/config/tool.js";
// import * as api from "@/api/invigilationManagement/index";
import { rootPath } from "@/utils/globalParam";
export default {
  name: "baseInfo",
  data() {
    return {
      // 机会状态：normal-正常推进，paused-暂停状态，closed-已关闭状态
      opportunityStatus: "normal", // 默认正常状态
      active: 9,
      processList: [
        {
          name: "统筹跟进"
        },
        {
          name: "立项组队"
        },
        {
          name: "分配比例"
        },
        {
          name: "投标阶段"
        },
        {
          name: "客户授权"
        },
        {
          name: "询价阶段"
        },
        {
          name: "排分阶段"
        },
        {
          name: "成交出单"
        },
        {
          name: "服务阶段"
        }
      ],
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          agentNo: "",
          kbaoNo: "",
          stuName: "",
          examId: ""
        }
      },
      searchFormTemp: [
        {
          lable: "登录用户姓名",
          name: "stuName",
          type: "input",
          width: "200px"
        },
        {
          lable: "工号",
          name: "agentNo",
          type: "input",
          width: "200px"
        },
        {
          lable: "快保账号",
          name: "kbaoNo",
          type: "input",
          width: "200px"
        }
      ],
      total: 0,
      exportUrl: "",
      paramObj: {
        agentNo: "",
        kbaoNo: "",
        stuName: ""
      },

      // 领取机会相关
      showClaimPopup: false,
      claimLoading: false,

      // 暂停机会相关
      showPausePopup: false,
      pauseForm: {
        pauseReason: ""
      },
      pauseRules: {
        pauseReason: [
          { required: true, message: "请输入暂停原因", trigger: "blur" },
          { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
        ]
      },
      pauseLoading: false,

      // 启动机会相关
      showRestartPopup: false,
      restartLoading: false,

      // 关闭机会相关
      showClosePopup: false,
      closeForm: {
        closeReasonType: "",
        closeReason: ""
      },
      closeRules: {
        closeReasonType: [
          { required: true, message: "请选择关闭原因", trigger: "change" }
        ]
      },
      closeLoading: false,

      // 重启关闭机会相关
      showRestartClosePopup: false,
      restartCloseSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          target: "",
          org: "",
          name: ""
        }
      },
      restartCloseSearchFormTemp: [
        { label: '指派对象', name: 'target', type: 'select', width: '140px', list: [{ dicItemCode: 'all', dicItemName: '全部' }, { dicItemCode: '顾问', dicItemName: '顾问' }, { dicItemCode: '业务员', dicItemName: '业务员' }] },
        { label: '机构', name: 'org', type: 'select', width: '140px', list: [{ dicItemCode: 'orgA', dicItemName: '机构A' }, { dicItemCode: 'orgB', dicItemName: '机构B' }] },
        { label: '人员姓名', name: 'name', type: 'input', width: '140px' }
      ],
      restartCloseUserList: [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ],
      selectedRestartCloseUserName: "",
      restartCloseLoading: false,

      // 指派统筹相关
      showAssignPopup: false,
      assignSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          name: "",
          phone: "",
          email: "",
          belong: ""
        }
      },
      assignSearchFormTemp: [
        { label: '指派对象', name: 'target', type: 'select', width: '140px', list: [{ dicItemCode: 'all', dicItemName: '全部' }, { dicItemCode: '顾问', dicItemName: '顾问' }, { dicItemCode: '业务员', dicItemName: '业务员' }] },
        { label: '机构', name: 'org', type: 'select', width: '140px', list: [{ dicItemCode: 'orgA', dicItemName: '机构A' }, { dicItemCode: 'orgB', dicItemName: '机构B' }] },
        { label: '人员姓名', name: 'name', type: 'input', width: '140px' }
      ],
      assignUserList: [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ],
      selectedAssignUserName: "",
      assignLoading: false,

      // 指派项目经理相关
      showManagerPopup: false,
      managerBusinessType: "经纪", // 默认选择经纪
      managerSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          businessType: "经纪", // 默认选择经纪
          target: "",
          org: "",
          name: ""
        }
      },
      managerSearchFormTemp: [
        { label: '指派对象', name: 'target', type: 'select', width: '140px', list: [{ dicItemCode: 'all', dicItemName: '全部' }, { dicItemCode: '项目经理', dicItemName: '项目经理' }, { dicItemCode: '项目助理', dicItemName: '项目助理' }] },
        { label: '机构', name: 'org', type: 'select', width: '140px', list: [{ dicItemCode: 'orgA', dicItemName: '机构A' }, { dicItemCode: 'orgB', dicItemName: '机构B' }] },
        { label: '人员姓名', name: 'name', type: 'input', width: '140px' }
      ],
      managerUserList: [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ],
      selectedManagerUserName: "",
      managerLoading: false,

      // 任务完成状态
      taskCompletionStatus: {},
      // 任务加载状态
      taskLoadingStatus: {},

      // 步骤列表数据
      stepList: [
        // 历史记录1 - 包含统筹跟进任务
        {
          type: 'history',
          date: '2024-04-14 10:22:20',
          title: '顾问徐晨旭(手机号码?) 成功提交机会',
          description: '系统分配机会-北京分公司统筹',
          tasks: [
            {
              title: '统筹跟进',
              actions: [
                { id: 'claim', name: '领取机会', required: true },
                { id: 'assign_overall', name: '指派统筹', required: false },
                { id: 'assign_manager', name: '指派项目经理', required: false }
              ]
            }
          ]
        },
        // 历史记录2 - 包含立项组队任务
        {
          type: 'history',
          date: '2024-04-15 09:12:06',
          title: '北京分公司统筹 王年金(wangnj)领取机会',
          tasks: [
            {
              title: '立项组队',
              actions: [
                { id: 'config_members', name: '配置项目成员', required: true }
              ]
            }
          ]
        },
        // 历史记录3 - 包含分配比例和投标阶段两个任务
        {
          type: 'history',
          date: '2024-04-16 09:12:06',
          title: '北京分公司统筹 王年金(wangnj)指派机会给北京分公司项目经理 李增额(lize001)',
          tasks: [
            {
              title: '分配比例',
              actions: [
                { id: 'config_ratio', name: '配置项目分工/比例', required: true },
                { id: 'confirm_ratio', name: '确认分工/比例', required: true }
              ]
            },
            {
              title: '投标阶段',
              actions: [
                { id: 'maintain_bid', name: '维护投标信息', required: true },
                { id: 'upload_bid', name: '上传标书文件', required: true },
                { id: 'config_ecosystem', name: '配置生态服务', required: true }
              ]
            }
          ]
        }
      ],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup

  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    closeReasonRules() {
      // 当选择"机会已成交"时，关闭原因不是必填
      if (this.closeForm.closeReasonType === '成交') {
        return [
          { min: 0, max: 150, message: "长度不能超过 150 个字符", trigger: "blur" }
        ];
      }
      // 其他选项时，关闭原因是必填的
      return [
        { required: true, message: "请输入关闭原因", trigger: "blur" },
        { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
      ];
    },
    closeReasonPlaceholder() {
      // 当选择"机会已成交"时，关闭原因不是必填
      if (this.closeForm.closeReasonType === '成交') {
        return "关闭原因 (选填)";
      }
      // 其他选项时，关闭原因是必填的
      return "关闭原因 (必填)";
    },
    toolList() {
      return [
        {
          name: "领取机会",
          btnCode: ""
        },
        {
          name: "指派统筹",
          btnCode: ""
        },
        {
          name: "指派项目经理",
          btnCode: ""
        },
        {
          name: this.opportunityStatus === "paused" ? "启动机会" : "暂停机会",
          btnCode: ""
        },
        {
          name: this.opportunityStatus === "closed" ? "重启机会" : "关闭机会",
          btnCode: ""
        }
      ];
    }
  },
  watch: {
    'closeForm.closeReasonType'() {
      // 当关闭原因类型改变时，重新验证关闭原因字段
      this.$nextTick(() => {
        if (this.$refs.closeFormRef) {
          this.$refs.closeFormRef.validateField('closeReason');
        }
      });
    }
  },
  async created() {
    this.initTaskStatus();
  },
  methods: {
    // 初始化任务状态
    initTaskStatus() {
      this.stepList.forEach(item => {
        if (item.tasks && item.tasks.length) {
          item.tasks.forEach(task => {
            this.taskCompletionStatus[task.title] = false;
            this.taskLoadingStatus[task.title] = false;
          });
        }
      });
    },

    async getDicFun() {
      await getDicItemList("mes.room.stu.forceSubmit");
      await getDicItemList("mes.room.stu.manCheck");
      await getDicItemList("mes.room.stu.cardType");
      await getDicItemList("mes.room.stu.systemCheck");

    },
    async initData() {
      this.initParam.param.examId = this.$route.query.examId;
      let res = await api.getMesroomstuslist(this.initParam);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [];
        }
      }
    },
    handleTool(item) {
      let m = {
        "领取机会": this.handleReceive,
        "指派统筹": this.handleOverallPlanning,
        "指派项目经理": this.handleProjectManager,
        "暂停机会": this.handlePause,
        "启动机会": this.handleRestart,
        "关闭机会": this.handleClose,
        "重启机会": this.handleRestartClose,
      }
      return m[item.name]()
    },
    handleReceive() {
      this.showClaimPopup = true;
    },
    handleOverallPlanning() {
      this.showAssignPopup = true;
    },
    handleProjectManager() {
      this.showManagerPopup = true;
    },
    handlePause() {
      this.showPausePopup = true;
    },
    handleRestart() {
      this.showRestartPopup = true;
    },
    handleClose() {
      this.showClosePopup = true;
    },

    // 领取机会确认
    async confirmClaim() {
      try {
        this.claimLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.$message.success("机会领取成功");
        this.closeClaimPopup();
      } catch (error) {
        this.$message.error("领取失败");
      } finally {
        this.claimLoading = false;
      }
    },

    // 暂停机会确认
    async confirmPause() {
      try {
        await this.$refs.pauseFormRef.validate();
        this.pauseLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.opportunityStatus = "paused"; // 改变状态为暂停
        this.$message.success("机会暂停成功");
        this.closePausePopup();
      } catch (error) {
        console.error("表单验证失败:", error);
      } finally {
        this.pauseLoading = false;
      }
    },

    // 关闭机会确认
    async confirmClose() {
      try {
        await this.$refs.closeFormRef.validate();
        this.closeLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.opportunityStatus = "closed"; // 改变状态为已关闭
        this.$message.success("机会关闭成功");
        this.closeClosePopup();
      } catch (error) {
        console.error("表单验证失败:", error);
      } finally {
        this.closeLoading = false;
      }
    },

    // 关闭领取弹窗
    closeClaimPopup() {
      this.showClaimPopup = false;
    },

    // 关闭暂停弹窗
    closePausePopup() {
      this.showPausePopup = false;
      this.pauseForm.pauseReason = "";
      this.$nextTick(() => {
        if (this.$refs.pauseFormRef) {
          this.$refs.pauseFormRef.resetFields();
        }
      });
    },

    // 启动机会确认
    async confirmRestart() {
      try {
        this.restartLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.opportunityStatus = "normal"; // 改变状态为正常
        this.$message.success("机会重启成功");
        this.closeRestartPopup();
      } catch (error) {
        this.$message.error("重启失败");
      } finally {
        this.restartLoading = false;
      }
    },

    closeRestartPopup() {
      this.showRestartPopup = false;
    },

    // 重启关闭机会
    handleRestartClose() {
      this.showRestartClosePopup = true;
    },

    // 重启关闭机会搜索
    handleRestartCloseSearch(data) {
      this.restartCloseSearchForm = data;
      // 模拟搜索，实际项目中应该调用API
      const originalList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ];

      this.restartCloseUserList = originalList.filter(item => {
        const { target, org, name } = data.param;
        const matchesTarget = !target || item.target === target;
        const matchesOrg = !org || item.belong === (org === 'orgA' ? '机构A' : org === 'orgB' ? '机构B' : '');
        const matchesName = !name || item.name.includes(name);
        return matchesTarget && matchesOrg && matchesName;
      });
    },

    resetRestartCloseSearch() {
      this.restartCloseSearchForm = {
        pageNum: 1,
        pageSize: 10,
        param: {
          target: "",
          org: "",
          name: ""
        }
      };
      this.restartCloseUserList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ];
    },

    async confirmRestartClose() {
      if (!this.selectedRestartCloseUserName) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        this.restartCloseLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.opportunityStatus = "normal"; // 改变状态为正常
        this.$message.success(`已重启机会并指派 ${this.selectedRestartCloseUserName} 为新的项目经理`);
        this.closeRestartClosePopup();
      } catch (error) {
        this.$message.error("重启失败");
      } finally {
        this.restartCloseLoading = false;
      }
    },

    closeRestartClosePopup() {
      this.showRestartClosePopup = false;
      this.selectedRestartCloseUserName = "";
    },

    // 关闭关闭弹窗
    closeClosePopup() {
      this.showClosePopup = false;
      this.closeForm.closeReasonType = "";
      this.closeForm.closeReason = "";
      this.$nextTick(() => {
        if (this.$refs.closeFormRef) {
          this.$refs.closeFormRef.resetFields();
        }
      });
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.paramObj = Object.assign(this.paramObj, this.initParam.param);
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.paramObj = this.$options.data().paramObj;
      this.initData();
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },

    // 指派统筹搜索
    handleAssignSearch(data) {
      this.assignSearchForm = data;
      // 模拟搜索，实际项目中应该调用API
      const originalList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ];

      this.assignUserList = originalList.filter(item => {
        const { name, phone, email, belong } = data.param;
        const matchesName = !name || item.name.includes(name);
        const matchesPhone = !phone || item.phone.includes(phone);
        const matchesEmail = !email || item.email.includes(email);
        const matchesBelong = !belong || item.belong.includes(belong);
        return matchesName && matchesPhone && matchesEmail && matchesBelong;
      });
    },
    resetAssignSearch() {
      this.assignSearchForm = {
        pageNum: 1,
        pageSize: 10,
        param: {
          name: "",
          phone: "",
          email: "",
          belong: ""
        }
      };
      this.assignUserList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B" },
      ];
    },
    async confirmAssign() {
      if (!this.selectedAssignUserName) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        this.assignLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.$message.success(`已指派 ${this.selectedAssignUserName} 为统筹`);
        this.closeAssignPopup();
      } catch (error) {
        this.$message.error("指派失败");
      } finally {
        this.assignLoading = false;
      }
    },
    closeAssignPopup() {
      this.showAssignPopup = false;
      this.selectedAssignUserName = "";
    },

    // 业务主体变化处理
    handleBusinessTypeChange(value) {
      this.managerSearchForm.param.businessType = value;
      // 重新搜索
      this.handleManagerSearch(this.managerSearchForm);
    },

    // 指派项目经理搜索
    handleManagerSearch(data) {
      this.managerSearchForm = data;
      // 模拟搜索，实际项目中应该调用API
      const originalList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目助理" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目助理" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目助理" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目助理" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
      ];

      this.managerUserList = originalList.filter(item => {
        const { businessType, target, org, name } = data.param;
        const matchesBusinessType = !businessType || item.businessType === businessType;
        const matchesTarget = !target || item.target === target;
        const matchesOrg = !org || item.belong === (org === 'orgA' ? '机构A' : org === 'orgB' ? '机构B' : '');
        const matchesName = !name || item.name.includes(name);
        return matchesBusinessType && matchesTarget && matchesOrg && matchesName;
      });
    },
    resetManagerSearch() {
      this.managerBusinessType = "经纪"; // 重置为默认选择
      this.managerSearchForm = {
        pageNum: 1,
        pageSize: 10,
        param: {
          businessType: "经纪", // 重置为默认选择
          target: "",
          org: "",
          name: ""
        }
      };
      this.managerUserList = [
        { name: "张三", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "李四", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
        { name: "王五", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目助理" },
        { name: "赵六", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目助理" },
        { name: "钱七", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "孙八", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
        { name: "周九", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目助理" },
        { name: "吴十", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目助理" },
        { name: "郑十一", phone: "***********", email: "<EMAIL>", belong: "机构A", businessType: "经纪", target: "项目经理" },
        { name: "王十二", phone: "***********", email: "<EMAIL>", belong: "机构B", businessType: "销售", target: "项目经理" },
      ];
    },
    async confirmManager() {
      if (!this.selectedManagerUserName) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        this.managerLoading = true;
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.$message.success(`已指派 ${this.selectedManagerUserName} 为项目经理`);
        this.closeManagerPopup();
      } catch (error) {
        this.$message.error("指派失败");
      } finally {
        this.managerLoading = false;
      }
    },
    closeManagerPopup() {
      this.showManagerPopup = false;
      this.selectedManagerUserName = "";
      this.managerBusinessType = "经纪"; // 重置为默认选择
    },

    // 任务完成确认
    async confirmTaskCompletion(taskName) {
      if (this.taskLoadingStatus[taskName]) {
        return;
      }
      this.taskLoadingStatus[taskName] = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.taskCompletionStatus[taskName] = true;
        this.$message.success(`任务 "${taskName}" 确认完成！`);
      } catch (error) {
        this.$message.error("确认任务完成失败");
      } finally {
        this.taskLoadingStatus[taskName] = false;
      }
    },

    // 任务操作（现在只是展示，不需要点击）
    handleTaskAction(actionName) {
      // 这些操作现在只是展示，不需要点击
      this.$message.info(`${actionName} 是展示项，不需要点击操作`);
    },

    // 更新步骤列表（用于演示不同的任务组合）
    updateStepList(newStepList) {
      this.stepList = newStepList;
      this.initTaskStatus();
    },

    // 显示演示选项
    showDemoOptions() {
      this.$confirm('选择要演示的任务组合', '演示选项', {
        confirmButtonText: '组合1',
        cancelButtonText: '组合2',
        distinguishCancelAndClose: true,
        type: 'info'
      }).then(() => {
        // 组合1：统筹跟进 + 立项组队
        this.updateStepList([
          {
            type: 'history',
            date: '2024-04-14 10:22:20',
            title: '顾问徐晨旭(手机号码?) 成功提交机会',
            description: '系统分配机会-北京分公司统筹',
            tasks: [
              {
                title: '统筹跟进',
                actions: [
                  { id: 'claim', name: '领取机会', required: true },
                  { id: 'assign_overall', name: '指派统筹', required: false },
                  { id: 'assign_manager', name: '指派项目经理', required: false }
                ]
              },
              {
                title: '立项组队',
                actions: [
                  { id: 'config_members', name: '配置项目成员', required: true }
                ]
              }
            ]
          }
        ]);
        this.$message.success('已切换到组合1：统筹跟进 + 立项组队');
      }).catch(action => {
        if (action === 'cancel') {
          // 组合2：分配比例 + 投标阶段
          this.updateStepList([
            {
              type: 'history',
              date: '2024-04-14 10:22:20',
              title: '顾问徐晨旭(手机号码?) 成功提交机会',
              description: '系统分配机会-北京分公司统筹',
              tasks: [
                {
                  title: '分配比例',
                  actions: [
                    { id: 'config_ratio', name: '配置项目分工/比例', required: true },
                    { id: 'confirm_ratio', name: '确认分工/比例', required: true }
                  ]
                },
                {
                  title: '投标阶段',
                  actions: [
                    { id: 'maintain_bid', name: '维护投标信息', required: true },
                    { id: 'upload_bid', name: '上传标书文件', required: true },
                    { id: 'config_ecosystem', name: '配置生态服务', required: true }
                  ]
                }
              ]
            }
          ]);
          this.$message.success('已切换到组合2：分配比例 + 投标阶段');
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.base-info {
  .log-tool {
    margin-top: 20px;
  }

  .info-item {
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;

    .item {
      font-size: 14px;
      color: #333;
      width: 25%;
      padding: 0 10px;
      margin-bottom: 20px;
    }
  }

  .tool-wrap {
    padding: 20px 20px 0 20px;
  }

  .item-tips {
    font-size: 14px;
    color: #333;
    padding-left: 20px;
  }

  .step-wrap {
    padding: 20px;
          .step-list {
        margin: 40px auto 0 auto;
        border-left: 1px dashed #999;
        padding-left: 20px;
        max-width: 800px;

      .step-item {
        position: relative;
        margin-bottom: 20px;

        .radius {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          position: absolute;
          top: 2px;
          left: -28px;
        }

        .item-date {
          margin-bottom: 20px;
        }

        .step-info-wrap {

          width: 100%;
          border-radius: 10px;
          background: #f0f2f5;
          padding: 10px;

          .info-title {
            font-size: 16px;
            font-weight: bold;
          }

          .info-text {
            font-size: 14px;
            line-height: 38px;

            span {
              color: #409EFF;
            }
          }
        }
      }
    }
  }

  // 任务块样式
  .step-item {
    .step-info-wrap {
      .info-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
      }

      .task-blocks {
        margin-top: 15px;

        .task-block {
          background: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          padding: 15px;
          margin-bottom: 10px;

          .task-title {
            // font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
          }

          .task-content {
            .execution-items {
              margin-bottom: 8px;

              .action-row {
                margin-bottom: 5px;
                display: flex;
                align-items: center;

                .item-label {
                  font-size: 14px;
                  color: #666;
                  margin-right: 8px;
                  flex-shrink: 0;
                }

                .action-item {
                  font-size: 14px;
                  color: #333;
                  margin-right: 8px;
                }

                .required-text {
                  font-size: 14px;
                  color: #999;
                }
              }
            }

            .task-completion {
              margin-top: 10px;
              text-align: right;
            }
          }
        }
      }
    }
  }

  // 领取机会弹窗样式
  .claim-confirm {
    padding: 0 20px 20px 20px;

    .confirm-content {
      margin-bottom: 20px;
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  // 暂停机会弹窗样式
  .pause-form {
    padding: 0 20px 20px 20px;

    .pause-description {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 启动机会弹窗样式
  .restart-form {
    padding: 0 20px 20px 20px;

    .restart-description {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 关闭机会弹窗样式
  .close-form {
    padding: 0 20px 20px 20px;

    .close-description {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 指派统筹弹窗样式
  .assign-content {
    padding: 0 20px 20px 20px;

    .business-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
        color: #333;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .table-container {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 10px;

      // 隐藏滚动条
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }

      // Firefox
      scrollbar-width: none;

      // IE
      -ms-overflow-style: none;
    }

    .restart-close-tip {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      ;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }
}
</style>