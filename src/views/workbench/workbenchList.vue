<template>
  <div class="workbench-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: activited == index }"
        :style="{
          color: activited == index ? themeObj.color : '',
          'border-color': activited == index ? themeObj.color : '',
        }"
        @click="navChange(index)"
      >
        {{ item.name }}
      </div>
    </div>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="opportunityName" label="机会名称"></el-table-column>
      <el-table-column align="center" prop="bizCode" label="机会ID"></el-table-column>
      <el-table-column align="center" label="服务顾问">
        <template slot-scope="scope">
          {{ scope.row.agentName }}({{ scope.row.agentCode }})
        </template>
      </el-table-column>
      <el-table-column align="center" prop="legalName" label="所属机构"></el-table-column>
      <el-table-column align="center" prop="salesCenterName" label="所属营业部"></el-table-column>
      <el-table-column align="center" prop="companyName" label="企业名称"></el-table-column>
      <el-table-column align="center" prop="roleName" label="社会统一信用代码"></el-table-column>
      <el-table-column align="center" prop="roleName" label="客户需求"></el-table-column>
      <el-table-column align="center" prop="industryId" label="业务渠道"></el-table-column>
      <el-table-column align="center" prop="roleName" label="预估投保人数"></el-table-column>
      <el-table-column align="center" prop="roleName" label="保费预算"></el-table-column>
      <el-table-column align="center" prop="roleName" label="是否需要投标"></el-table-column>
      <el-table-column align="center" prop="createTime" label="提交时间"></el-table-column>
      <el-table-column align="center" prop="status" label="机会状态"></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="detail(scope.row)">机会详情</el-button>
          <el-button class="btn-center" type="text" @click="accept(scope.row)" v-if="activited == 0">领取机会</el-button>
          <el-button class="btn-center" type="text" @click="involved(scope.row)" v-if="activited == 1">是否参与</el-button>
          <el-button class="btn-center" type="text" @click="confirm(scope.row)" v-if="activited == 1">确认比例</el-button>
          <el-button class="btn-center" type="text" @click="restart(scope.row)" v-if="activited == 3">重启机会</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <!-- 领取机会 -->
    <DtPopup :isShow.sync="showAcceptPopup" @close="closeAcceptPopup" title="领取机会" center :footer="false" width="600px">
      <div class="claim-confirm">
        <div class="confirm-content">
          <p>领取成功后,该机会归属您来跟进,其他人员不可见,确认是否领取该机会?</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeAcceptPopup">取消</el-button>
          <el-button type="primary" @click="confirmAccept">确认领取</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 是否参与 -->
    <DtPopup :isShow.sync="showInvolvedPopup" @close="closeInvolvedPopup" title="领取机会" center :footer="false" width="600px">
      <div class="involved-confirm">
        <div class="involved-content">
          <div>机会名称:{{ involvedData.opportunityName }}</div>
          <div>机会ID:{{ involvedData.opportunityId }}</div>
          <div>企业名称:{{ involvedData.companyName }}</div>
        </div>
        <div class="involved-content">
          <div>机会名称:{{ involvedData.companyName }}</div>
          <div>机会ID:{{ involvedData.agentName }}</div>
          <div>邀请您加入项目，可点击以下按钮，进行「参与」或「拒绝」操作。</div>
        </div>
        <div class="involved-actions">
          <el-button @click="rejectInvolved">拒绝</el-button>
          <el-button type="primary" @click="confirmInvolved">参与</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- “暂停机会”重启弹窗 -->
    <DtPopup :isShow.sync="showRestartPopup" @close="closeRestartPopup" title="重启机会" center :footer="false"
             width="600px">
      <div class="restart-form">
        <div class="restart-description">
          <p>确认重启后,该机会将恢复至正常推进状态,同时所有项目参与人员可见,是否确认本次操作?</p>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartPopup">取消</el-button>
          <el-button type="primary" @click="confirmRestart">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- “已关闭 - 机会推进失败”重启弹窗 -->
    <DtPopup :isShow.sync="showRestartClosePopup" @close="closeRestartClosePopup" title="重启机会" center :footer="false"
             width="900px">
      <div class="assign-content">
        <!-- 提示文案 -->
        <div class="restart-close-tip">
          <p :style="{ color: themeObj.color }">该机会已被 北京分公司项目经理 王年金（wangnj）变更为"已关闭-机会推进失败"，是否确认重启并指派新的项目经理？</p>
        </div>

        <SearchForm :searchForm="restartCloseSearchForm" :searchFormTemp="restartCloseSearchFormTemp"
                    @normalSearch="handleRestartCloseSearch" @normalResetQuery="resetRestartCloseSearch" />
        <div class="table-container">
          <el-table :data="restartCloseUserList" style="width: 100%;">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'restartCloseUserSelect'" :value="scope.row.name"
                       v-model="selectedRestartCloseUserName" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="name" label="人员姓名" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="email" label="邮箱" align="center" />
            <el-table-column prop="belong" label="人员归属" align="center" />
          </el-table>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartClosePopup">取消</el-button>
          <el-button type="primary" @click="confirmRestartClose">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/workbench/index.js";
import { getDicItemList } from "@/config/tool.js";
import { findLegalOrgData } from "@/api/userManagement/index.js";
import {opportunityPage} from "@/api/workbench/index.js";

export default {
  name: "workbenchList",
  data() {
    return {
      toolListProps: {
        toolTitle: "综合工作台",
        toolList: [
        ]
      },
      navBarlist: [
        { name: "待领取" },
        { name: "待参与" },
        { name: "待处理" },
        { name: "我参与" },
        { name: "全部机会" },
      ],
      activited: 0,
      tableData: [{}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          activited: 0,
          tenantId: "",
          agentName: "",
          legalCode: "",
          opportunityName: "",
          bizCode: "",
          startTime: "",
          endTime: "",
          companyName: "",
          companyId: "",
          industryId: "",
          status: "",
        }
      },
      searchFormTemp: [
        {
          label: "顾问姓名/用户名",
          name: "agentName",
          type: "input",
          width: "200px"
        },
        {
          label: "所属机构",
          name: "legalCode",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会名称",
          name: "opportunityName",
          type: "input",
          width: "200px"
        },
        {
          label: "机会ID",
          name: "bizCode",
          type: "input",
          width: "200px"
        },
        {
          label: "企业名称",
          name: "companyName",
          type: "input",
          width: "200px"
        },
        {
          label: "企业代码",
          name: "companyId",
          type: "input",
          width: "200px"
        },
        {
          label: "业务渠道",
          name: "industryId",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会状态",
          name: "status",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          name: "createTime",
          type: "doubleDate",
          label: "机会提交时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "startTime",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "endTime",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        },
      ],
      total: 0,
      legalList: [],
      showAcceptPopup: false,
      opportunityId: "",
      showInvolvedPopup: false,
      involvedData: {
        opportunityName: "",
        opportunityId: "",
        companyName: "",
        agentName: "",
      },
      showRestartPopup:false,
      restartData: {
        opportunityId: "",
        reason: ""
      },
      showRestartClosePopup:false,
      restartCloseSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          target: "",
          org: "",
          name: ""
        }
      },
      restartCloseSearchFormTemp: [
        { label: '指派对象', name: 'target', type: 'select', width: '140px', list: [{ dicItemCode: 'all', dicItemName: '全部' }, { dicItemCode: '顾问', dicItemName: '顾问' }, { dicItemCode: '业务员', dicItemName: '业务员' }] },
        { label: '机构', name: 'org', type: 'select', width: '140px', list: [{ dicItemCode: 'orgA', dicItemName: '机构A' }, { dicItemCode: 'orgB', dicItemName: '机构B' }] },
        { label: '人员姓名', name: 'name', type: 'input', width: '140px' }
      ],
      restartCloseUserList: [
        { name: "张三", phone: "13800138000", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "13800138001", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "13800138002", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "13800138003", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "13800138004", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "13800138005", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "13800138006", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "13800138007", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "13800138008", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "13800138009", email: "<EMAIL>", belong: "机构B" },
      ],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      let legalListTemp = await findLegalOrgData({});

      legalListTemp.forEach(item => {
        this.legalList.push({
          dicItemName: item.orgName,
          dicItemCode: item.orgCode
        });
      });
      // 所属机构
      this.searchFormTemp[1].list = this.legalList;
      // 业务渠道
      this.searchFormTemp[6].list = this.legalList;
      // 状态
      this.searchFormTemp[7].list = this.legalList;
    },
    navChange(num) {
      this.activited = num;
      // 重置查询条件
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initParam.param.activited = num;
      // this.initData();
    },
    async initData() {
      this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      this.tableData = [
        { "opportunityName": "海底捞-员福-2025", "bizCode": "qyxs202509110878", "agentCode": "6400000001", "agentName": "徐晨旭", "legalName": "北京分公司","createTime":"2025-07-31 11:22:00","status":"1" },
      ]
      this.total = 1;
      console.log(this.tableData,"=====")
      // let res = await api.opportunityPage(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    handleTool(item) {
    },
    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    detail(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.id
        }
      });
    },
    accept(row){
      this.showAcceptPopup = true;
    },
    closeAcceptPopup() {
      this.showAcceptPopup = false;
    },
    async confirmAccept() {
      let res = await api.acceptTask({});
      if(res) {
        this.$message.success("机会领取成功");
      }
      this.initData();
      this.showAcceptPopup = false;
    },
    involved(row) {
    },
    confirmInvolved() {
      this.showInvolvedPopup = false;
    },
    rejectInvolved() {
      this.showInvolvedPopup = false;
    },
    closeInvolvedPopup() {
      this.showInvolvedPopup = false;
    },
    async confirm(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.id,
          componentName:""
        }
      });
    },
    restart(row) {
      this.restartData.opportunityId = row.opportunityId;
      // 判断是暂停机会重启还是关闭机会重启 , 显示不同的弹窗窗口
      if ("1" == row.status) {
        this.showRestartPopup = true;
        this.restartData.reason = "暂停机会重启";
      } else {
        this.showRestartClosePopup = true;
      }
    },
    async confirmRestart() {
      let res = await api.resume(this.restartData);
      this.$message.success("机会重启成功");
      this.initData();
    },
    closeRestartPopup() {
      this.showRestartPopup = false;
    },


    // 重启关闭机会搜索
    handleRestartCloseSearch(data) {
      this.restartCloseSearchForm = data;
      // 模拟搜索，实际项目中应该调用API
      const originalList = [
        { name: "张三", phone: "13800138000", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "13800138001", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "13800138002", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "13800138003", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "13800138004", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "13800138005", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "13800138006", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "13800138007", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "13800138008", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "13800138009", email: "<EMAIL>", belong: "机构B" },
      ];

      this.restartCloseUserList = originalList.filter(item => {
        const { target, org, name } = data.param;
        const matchesTarget = !target || item.target === target;
        const matchesOrg = !org || item.belong === (org === 'orgA' ? '机构A' : org === 'orgB' ? '机构B' : '');
        const matchesName = !name || item.name.includes(name);
        return matchesTarget && matchesOrg && matchesName;
      });
    },
    resetRestartCloseSearch() {
      this.restartCloseSearchForm = {
        pageNum: 1,
        pageSize: 10,
        param: {
          target: "",
          org: "",
          name: ""
        }
      };
      this.restartCloseUserList = [
        { name: "张三", phone: "13800138000", email: "<EMAIL>", belong: "机构A" },
        { name: "李四", phone: "13800138001", email: "<EMAIL>", belong: "机构B" },
        { name: "王五", phone: "13800138002", email: "<EMAIL>", belong: "机构A" },
        { name: "赵六", phone: "13800138003", email: "<EMAIL>", belong: "机构B" },
        { name: "钱七", phone: "13800138004", email: "<EMAIL>", belong: "机构A" },
        { name: "孙八", phone: "13800138005", email: "<EMAIL>", belong: "机构B" },
        { name: "周九", phone: "13800138006", email: "<EMAIL>", belong: "机构A" },
        { name: "吴十", phone: "13800138007", email: "<EMAIL>", belong: "机构B" },
        { name: "郑十一", phone: "13800138008", email: "<EMAIL>", belong: "机构A" },
        { name: "王十二", phone: "13800138009", email: "<EMAIL>", belong: "机构B" },
      ];
    },
    closeRestartClosePopup() {
      this.showRestartClosePopup = false;
    },
    confirmRestartClose() {
    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.workbench-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;
    margin-bottom: 20px;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }

  // 领取机会弹窗样式
  .claim-confirm {
    padding: 0 20px 20px 20px;

    .confirm-content {
      margin-bottom: 20px;
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  // 参与机会弹窗样式
  .involved-confirm {
    padding: 0 20px 20px 20px;
    display: flex;

    .involved-content {
      margin-bottom: 20px;
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .involved-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  // 重启暂停机会弹窗样式
  .restart-form {
    padding: 0 20px 20px 20px;

    .restart-description {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 重启关闭机会弹窗样式
  .assign-content {
    padding: 0 20px 20px 20px;

    .business-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
        color: #333;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .table-container {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 10px;

      // 隐藏滚动条
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }

      // Firefox
      scrollbar-width: none;

      // IE
      -ms-overflow-style: none;
    }

    .restart-close-tip {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
    ;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

}

</style>
