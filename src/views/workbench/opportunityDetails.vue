<template>
  <div class="opportunity-details">
    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '' }"> 综合工作台 </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        机会详情
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: currentIndex == index }"
        :style="{
          color: currentIndex == index ? themeObj.color : '',
          'border-color': currentIndex == index ? themeObj.color : '',
        }"
        @click="navChange(item,index)"
      >
        {{ item.name }}
      </div>
    </div>
    <component :is="currentItem.componentName"></component>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";

import baseInfo from "./components/baseInfo";
import projectLog from "./components/projectLog";
import projectSummary from "./components/projectSummary";

export default {
  name: "opportunityDetails",
  provide() {
    return {
      userDetail: this,
    };
  },
  components: {
    TableToolTemp,
    baseInfo,
    projectLog,
    projectSummary
  },
  data() {
    return {
      navBarlist: [
       {
        name:"基本信息",
        componentName:"baseInfo"
       },
       {
        name:"成员管理",
        componentName:""
       },
       {
        name:"资料管理",
        componentName:""
       },
       {
        name:"项目日志",
        componentName:"projectLog"
       },
       {
        name:"项目总结",
        componentName:"projectSummary"
       }
      ],
      currentItem:{
        name:"基本信息",
        componentName:"baseInfo"
      },
      currentIndex:0,
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  watch: {},
  created() {
  },
  mounted() {
   
  },
  methods: {
    navChange(item,index) {
      
      this.currentItem = item;
      this.currentIndex = index
    },
  },
};
</script>

<style lang="less">
.opportunity-details {
  .breadcrumb {
    padding: 13px 20px;
    border-bottom: 1px solid #eeeeee;
  }
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    margin-top: 20px;
    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;
      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}
</style>
