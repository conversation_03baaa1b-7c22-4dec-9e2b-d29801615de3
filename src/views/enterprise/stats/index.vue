<template>
  <div class="enterprise-stats-container">
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <span class="page-title"><i class="el-icon-s-data"></i> 企业统计分析</span>
          <p class="page-subtitle">企业数据统计概览，支持多维度分析和可视化展示</p>
        </div>
      </div>
    </div>
    
    <div class="stats-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div v-for="item in statsData" :key="item.type" class="stats-card" :class="item.type">
          <div class="card-icon">
            <i :class="item.icon" :style="{ color: item.iconColor }"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ item.value }}</div>
            <div class="card-title">{{ item.title }}</div>
            <div class="card-description">{{ item.description }}</div>
          </div>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="chart-container">
          <div class="chart-header">
            <h3>企业类型分布</h3>
          </div>
          <div class="chart-content" ref="pieChart"></div>
        </div>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>行业分布统计</h3>
          </div>
          <div class="chart-content" ref="barChart"></div>
        </div>
      </div>
      
      <!-- 详细统计表格 -->
      <div class="stats-tables">
        <div class="table-container">
          <div class="table-header">
            <h3>企业类型详细统计</h3>
          </div>
          <el-table :data="tableData" stripe class="modern-table" v-loading="loading">
            <el-table-column prop="typeName" label="企业类型" width="150" align="center" />
            <el-table-column prop="count" label="企业数量" width="120" align="center" />
            <el-table-column prop="percent" label="占比" width="100" align="center">
              <template slot-scope="scope">
                {{ scope.row.percent }}%
              </template>
            </el-table-column>
            <el-table-column prop="avgEmployee" label="平均员工数" width="140" align="center">
              <template slot-scope="scope">
                {{ scope.row.avgEmployee }}人
              </template>
            </el-table-column>
            <el-table-column prop="avgRevenue" label="平均营收" width="150" align="center">
              <template slot-scope="scope">
                {{ formatRevenue(scope.row.avgRevenue) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" align="center" />
          </el-table>
        </div>
        
        <div class="table-container">
          <div class="table-header">
            <h3>风险矩阵统计</h3>
          </div>
          <el-table :data="riskMatrixData" stripe class="modern-table" v-loading="loading">
            <el-table-column prop="matrixName" label="风险矩阵" width="180" align="center" />
            <el-table-column prop="enterpriseCount" label="企业数量" width="120" align="center" />
            <el-table-column prop="avgScore" label="平均评分" width="120" align="center" />
            <el-table-column prop="riskLevel" label="风险等级" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="getRiskLevelType(scope.row.riskLevel)" size="small">
                  {{ scope.row.riskLevel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdate" label="最后更新" width="150" align="center" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCompleteStats } from '@/api/enterprise/stats.js'
import * as echarts from 'echarts'

export default {
  name: 'EnterpriseStats',
  data() {
    return {
      loading: false,
      statsData: [],
      tableData: [],
      riskMatrixData: [],
      chartData: [],
      industryData: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const response = await getCompleteStats()
        if (response.code === 200) {
          const data = response.data
          
          // 设置统计卡片数据
          this.statsData = [
            {
              type: 'total',
              title: '企业总数',
              value: data.overview.total,
              description: '已录入企业数量',
              icon: 'el-icon-office-building',
              iconColor: '#409EFF'
            },
            {
              type: 'a-class',
              title: 'A类企业',
              value: data.overview.large,
              description: `占比 ${data.overview.largePercent}%`,
              icon: 'el-icon-s-grid',
              iconColor: '#67C23A'
            },
            {
              type: 'b-class',
              title: 'B类企业',
              value: data.overview.medium,
              description: `占比 ${data.overview.mediumPercent}%`,
              icon: 'el-icon-s-custom',
              iconColor: '#E6A23C'
            },
            {
              type: 'c-class',
              title: 'C类企业',
              value: data.tableData[2].count,
              description: `占比 ${data.tableData[2].percent}%`,
              icon: 'el-icon-s-home',
              iconColor: '#409EFF'
            }
          ]
          
          this.tableData = data.tableData
          this.riskMatrixData = data.riskMatrixStats
          this.chartData = data.chartData
          this.industryData = data.industryStats
          
          this.$nextTick(() => {
            this.initCharts()
          })
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    formatRevenue(revenue) {
      if (revenue >= 100000000) {
        return `${(revenue / 100000000).toFixed(1)}亿元`
      } else if (revenue >= 10000) {
        return `${(revenue / 10000).toFixed(0)}万元`
      } else {
        return `${revenue}元`
      }
    },
    getRiskLevelType(level) {
      const levelMap = {
        '较低': 'success',
        '中等': 'warning',
        '较高': 'danger'
      }
      return levelMap[level] || 'info'
    },
    initCharts() {
      this.initPieChart()
      this.initBarChart()
    },
    initPieChart() {
      const chartDom = this.$refs.pieChart
      if (!chartDom) return
      
      const myChart = echarts.init(chartDom)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.chartData.map(item => item.name)
        },
        series: [
          {
            name: '企业类型',
            type: 'pie',
            radius: '50%',
            data: this.chartData.map(item => ({
              value: item.count,
              name: item.name,
              itemStyle: { color: item.color }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    initBarChart() {
      const chartDom = this.$refs.barChart
      if (!chartDom) return
      
      const myChart = echarts.init(chartDom)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.industryData.map(item => item.industry),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '企业数量',
            type: 'bar',
            data: this.industryData.map(item => item.count),
            itemStyle: {
              color: '#D7A256'
            }
          }
        ]
      }
      myChart.setOption(option)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/style/shared-styles.less";

.enterprise-stats-container {
  min-height: 100vh;
  background: #fbf6ee;
  
  .page-header {
    background: white;
    padding: 24px 32px;
    border-bottom: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          gap: 12px;
          
          i {
            color: #D7A256;
            font-size: 28px;
          }
        }
        
        .page-subtitle {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
  
  .stats-section {
    margin: 20px 32px;
    
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            font-size: 24px;
            color: white;
          }
          
          &.total-icon {
            background: linear-gradient(135deg, #D7A256, #E6B366);
          }
          
          &.small-icon {
            background: linear-gradient(135deg, #909399, #A6A6A6);
          }
          
          &.medium-icon {
            background: linear-gradient(135deg, #E6A23C, #F0AD4E);
          }
          
          &.large-icon {
            background: linear-gradient(135deg, #67C23A, #85CE61);
          }
        }
        
        .card-content {
          flex: 1;
          
          .card-title {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 4px;
          }
          
          .card-value {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
          }
          
          .card-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .chart-section {
    margin: 20px 32px;
    
    .chart-container {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .chart-header {
        margin-bottom: 20px;
        
        .chart-title {
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }
      }
      
      .chart-content {
        .pie-chart {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          
          .chart-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 200px;
            
            .chart-color {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              margin-right: 12px;
            }
            
            .chart-info {
              flex: 1;
              
              .chart-name {
                font-size: 14px;
                font-weight: 500;
                color: #2c3e50;
                margin-bottom: 2px;
              }
              
              .chart-count {
                font-size: 12px;
                color: #606266;
                margin-bottom: 2px;
              }
              
              .chart-percent {
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
    }
  }
  
  .table-section {
    background: white;
    padding: 24px 32px;
    
    .section-header {
      margin-bottom: 16px;
      
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
    
    .modern-table {
      /deep/ .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #fbf6ee;
            font-weight: 600;
            font-size: 14px;
            color: #2c3e50;
            border-bottom: 1px solid #f7ecdd;
          }
        }
      }
      
      /deep/ .el-table__body-wrapper {
        .el-table__row {
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(215, 162, 86, 0.05) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
          }
          
          td {
            border-bottom: 1px solid #f7ecdd;
            padding: 16px 0;
          }
        }
      }
      
      /deep/ .el-table__fixed-body-wrapper .el-table__row td {
        padding: 16px 0;
      }
      
      /deep/ .el-table__fixed-body-wrapper .el-table__row,
      /deep/ .el-table__body-wrapper .el-table__row {
        min-height: 48px;
        line-height: 1.5;
      }
    }
  }
}
</style> 