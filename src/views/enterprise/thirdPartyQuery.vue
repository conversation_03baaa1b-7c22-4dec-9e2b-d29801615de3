<template>
  <InfoPageLayout
    title="第三方信息查询"
    subtitle="基于天眼查数据的企业信息查询服务"
    icon="el-icon-connection"
    :breadcrumbItems="breadcrumbItems"
    @back="handleBack"
  >
      <!-- 企业基本信息卡片 -->
    <InfoCard 
      title="企业基本信息" 
      icon="el-icon-office-building"
      tagText="数据来源：天眼查"
      tagType="success"
    >
          <el-row :gutter="20">
            <el-col :span="8">
          <InfoItem label="企业名称" :value="enterpriseInfo.name" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="统一社会信用代码" :value="enterpriseInfo.creditCode" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="法定代表人" :value="enterpriseInfo.legalPerson" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
          <InfoItem label="注册资本" :value="enterpriseInfo.registeredCapital" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="成立日期" :value="enterpriseInfo.establishDate" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="经营状态">
                <el-tag :type="enterpriseInfo.status === '在业' ? 'success' : 'danger'" size="small">
                  {{ enterpriseInfo.status }}
                </el-tag>
          </InfoItem>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
          <InfoItem label="企业地址" :value="enterpriseInfo.address" fullWidth />
            </el-col>
          </el-row>
    </InfoCard>

      <!-- 工商信息 -->
    <InfoCard title="工商信息" icon="el-icon-document">
          <el-table :data="businessInfo" stripe class="modern-table">
            <el-table-column prop="item" label="信息项" width="200" />
            <el-table-column prop="value" label="信息内容" min-width="300" />
            <el-table-column prop="updateTime" label="更新时间" width="180" />
          </el-table>
    </InfoCard>

      <!-- 股东信息 -->
    <InfoCard title="股东信息" icon="el-icon-user">
          <el-table :data="shareholders" stripe class="modern-table">
            <el-table-column prop="name" label="股东名称" min-width="200" />
            <el-table-column prop="type" label="股东类型" width="120" />
            <el-table-column prop="investment" label="认缴出资" width="150" />
            <el-table-column prop="percentage" label="持股比例" width="120" />
            <el-table-column prop="date" label="出资日期" width="180" />
          </el-table>
    </InfoCard>

      <!-- 对外投资 -->
    <InfoCard title="对外投资" icon="el-icon-s-finance">
          <el-table :data="investments" stripe class="modern-table">
            <el-table-column prop="companyName" label="被投资企业" min-width="200" />
            <el-table-column prop="investmentAmount" label="投资金额" width="150" />
            <el-table-column prop="percentage" label="持股比例" width="120" />
            <el-table-column prop="status" label="投资状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === '在业' ? 'success' : 'info'" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="投资日期" width="180" />
          </el-table>
    </InfoCard>

      <!-- 法律风险 -->
    <InfoCard title="法律风险" icon="el-icon-warning">
          <el-table :data="legalRisks" stripe class="modern-table">
            <el-table-column prop="type" label="风险类型" width="150" />
            <el-table-column prop="count" label="数量" width="100" align="center" />
            <el-table-column prop="description" label="描述" min-width="300" />
            <el-table-column prop="date" label="最新时间" width="180" />
          </el-table>
    </InfoCard>

      <!-- 知识产权 -->
    <InfoCard title="知识产权" icon="el-icon-s-opportunity">
          <el-row :gutter="20">
            <el-col :span="6">
          <StatCard 
            :value="ipInfo.patents" 
            label="专利数量" 
            icon="el-icon-document" 
            bgColor="gold"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="ipInfo.trademarks" 
            label="商标数量" 
            icon="el-icon-picture" 
            bgColor="primary"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="ipInfo.copyrights" 
            label="著作权" 
            icon="el-icon-reading" 
            bgColor="success"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="ipInfo.certificates" 
            label="资质证书" 
            icon="el-icon-medal" 
            bgColor="warning"
          />
            </el-col>
          </el-row>
    </InfoCard>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import StatCard from '@/components/layouts/StatCard'

export default {
  name: 'ThirdPartyQuery',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem,
    StatCard
  },
  data() {
    return {
      breadcrumbItems: [
        { text: '企业信息管理', icon: 'el-icon-office-building', to: 'EnterpriseManagement' },
        { text: '第三方信息查询', icon: 'el-icon-connection' }
      ],
      enterpriseInfo: {
        name: '北京科技有限公司',
        creditCode: '91110000123456789X',
        legalPerson: '张三',
        registeredCapital: '1000万元',
        establishDate: '2018-03-15',
        status: '在业',
        address: '北京市朝阳区建国路88号'
      },
      businessInfo: [
        { item: '经营范围', value: '技术开发、技术咨询、技术服务；软件开发；计算机系统服务', updateTime: '2023-12-01' },
        { item: '营业期限', value: '2018-03-15至2038-03-14', updateTime: '2023-12-01' },
        { item: '登记机关', value: '北京市朝阳区市场监督管理局', updateTime: '2023-12-01' },
        { item: '企业类型', value: '有限责任公司(自然人投资或控股)', updateTime: '2023-12-01' }
      ],
      shareholders: [
        { name: '张三', type: '自然人', investment: '600万元', percentage: '60%', date: '2018-03-15' },
        { name: '李四', type: '自然人', investment: '400万元', percentage: '40%', date: '2018-03-15' }
      ],
      investments: [
        { companyName: '上海分公司', investmentAmount: '500万元', percentage: '100%', status: '在业', date: '2020-06-01' },
        { companyName: '深圳子公司', investmentAmount: '300万元', percentage: '51%', status: '在业', date: '2021-03-15' }
      ],
      legalRisks: [
        { type: '行政处罚', count: 0, description: '暂无行政处罚记录', date: '-' },
        { type: '司法案件', count: 1, description: '合同纠纷案件1起', date: '2023-08-15' },
        { type: '被执行人', count: 0, description: '暂无被执行人记录', date: '-' },
        { type: '失信记录', count: 0, description: '暂无失信记录', date: '-' }
      ],
      ipInfo: {
        patents: 15,
        trademarks: 8,
        copyrights: 12,
        certificates: 5
      }
    }
  },
  created() {
    // 从路由参数获取企业信息
    const { id, enterpriseName } = this.$route.params
    if (enterpriseName) {
      this.enterpriseInfo.name = enterpriseName
    }
    // TODO: 根据企业ID调用天眼查API获取真实数据
    this.loadThirdPartyData(id)
  },
  methods: {
    loadThirdPartyData(enterpriseId) {
      // TODO: 调用天眼查API获取企业详细信息
      console.log('加载企业ID:', enterpriseId, '的第三方数据')
    },
    handleBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
        .modern-table {
          /deep/ .el-table__header-wrapper {
            th {
              background: #f7ecdd;
              font-weight: 600;
              color: #2c3e50;
    }
  }
}
</style> 