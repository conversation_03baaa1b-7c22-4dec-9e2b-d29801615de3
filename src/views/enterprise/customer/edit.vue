<template>
  <div class="enterprise-customer-edit">
    <div class="page-header">
      <div class="header-left">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="$router.back()"
          class="back-btn"
        >
          返回
        </el-button>
        <div class="page-title">
          <h2>{{ pageTitle }}</h2>
          <p class="page-subtitle">{{ pageSubtitle }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="$router.back()">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="loading"
        >
          保存
        </el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="enterprise-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3>基本信息</h3>
          </div>
          <div class="form-grid">
            <el-form-item label="企业名称" prop="name" class="required">
              <el-input
                v-model="form.name"
                placeholder="请输入企业名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input
                v-model="form.creditCode"
                placeholder="请输入统一社会信用代码"
                maxlength="18"
              />
            </el-form-item>

            <el-form-item label="企业类型" prop="enterpriseType">
              <el-select
                v-model="form.enterpriseType"
                placeholder="请选择企业类型"
                style="width: 100%"
              >
                <el-option
                  v-for="type in enterpriseTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                v-model="form.legalRepresentative"
                placeholder="请输入法定代表人"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="注册资本" prop="registeredCapital">
              <el-input-number
                v-model="form.registeredCapital"
                :min="0"
                :max="999999999"
                :precision="2"
                placeholder="请输入注册资本"
                style="width: 100%"
              />
              <span class="input-suffix">万元</span>
            </el-form-item>

            <el-form-item label="成立时间" prop="establishDate">
              <el-date-picker
                v-model="form.establishDate"
                type="date"
                placeholder="请选择成立时间"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 联系信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3>联系信息</h3>
          </div>
          <div class="form-grid">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input
                v-model="form.contactPerson"
                placeholder="请输入联系人"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="联系电话" prop="contactPhone">
              <el-input
                v-model="form.contactPhone"
                placeholder="请输入联系电话"
                maxlength="20"
              />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱"
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="传真" prop="fax">
              <el-input
                v-model="form.fax"
                placeholder="请输入传真"
                maxlength="20"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 地址信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3>地址信息</h3>
          </div>
          <div class="form-grid">
            <el-form-item label="省份" prop="province">
              <el-select
                v-model="form.province"
                placeholder="请选择省份"
                style="width: 100%"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinces"
                  :key="province.value"
                  :label="province.label"
                  :value="province.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="城市" prop="city">
              <el-select
                v-model="form.city"
                placeholder="请选择城市"
                style="width: 100%"
                @change="handleCityChange"
              >
                <el-option
                  v-for="city in cities"
                  :key="city.value"
                  :label="city.label"
                  :value="city.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="区县" prop="district">
              <el-select
                v-model="form.district"
                placeholder="请选择区县"
                style="width: 100%"
              >
                <el-option
                  v-for="district in districts"
                  :key="district.value"
                  :label="district.label"
                  :value="district.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="详细地址" prop="address" class="full-width">
              <el-input
                v-model="form.address"
                placeholder="请输入详细地址"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3>其他信息</h3>
          </div>
          <div class="form-grid">
            <el-form-item label="经营范围" prop="businessScope" class="full-width">
              <el-input
                v-model="form.businessScope"
                type="textarea"
                :rows="4"
                placeholder="请输入经营范围"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="备注" prop="remark" class="full-width">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterpriseCustomerEdit',
  data() {
    return {
      loading: false,
      form: {
        name: '',
        creditCode: '',
        enterpriseType: '',
        legalRepresentative: '',
        registeredCapital: null,
        establishDate: '',
        contactPerson: '',
        contactPhone: '',
        email: '',
        fax: '',
        province: '',
        city: '',
        district: '',
        address: '',
        businessScope: '',
        status: 1,
        remark: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { min: 2, max: 100, message: '企业名称长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        creditCode: [
          { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
        ],
        contactPhone: [
          { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的联系电话', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      enterpriseTypes: [
        { label: '微型企业', value: '微型企业' },
        { label: '小型企业', value: '小型企业' },
        { label: '中型企业', value: '中型企业' },
        { label: '大型企业', value: '大型企业' }
      ],
      provinces: [
        { label: '北京市', value: '北京市' },
        { label: '上海市', value: '上海市' },
        { label: '广东省', value: '广东省' },
        { label: '浙江省', value: '浙江省' }
      ],
      cities: [],
      districts: []
    }
  },
  computed: {
    isEdit() {
      return this.$route.query.mode === 'edit'
    },
    isCopy() {
      return this.$route.query.mode === 'copy'
    },
    pageTitle() {
      if (this.isEdit) return '编辑企业客户'
      if (this.isCopy) return '复制企业客户'
      return '新增企业客户'
    },
    pageSubtitle() {
      if (this.isEdit) return '修改企业客户信息'
      if (this.isCopy) return '基于现有客户创建新客户'
      return '创建新的企业客户'
    }
  },
  created() {
    if (this.$route.query.id) {
      this.loadData(this.$route.query.id)
    }
  },
  methods: {
    // 加载数据
    async loadData(id) {
      try {
        // 这里调用获取详情接口
        console.log('加载企业客户数据:', id)
      } catch (error) {
        this.$message.error('加载数据失败')
      }
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.form.validate()
        this.loading = true

        if (this.isEdit) {
          // 更新
          console.log('更新企业客户:', this.form)
        } else {
          // 新增
          console.log('新增企业客户:', this.form)
        }

        this.$message.success(this.isEdit ? '更新成功' : '创建成功')
        this.$router.back()
      } catch (error) {
        if (error !== false) {
          this.$message.error('保存失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 省份改变
    handleProvinceChange(value) {
      this.form.city = ''
      this.form.district = ''
      this.cities = []
      this.districts = []

      // 这里可以根据省份加载城市数据
      if (value === '广东省') {
        this.cities = [
          { label: '广州市', value: '广州市' },
          { label: '深圳市', value: '深圳市' },
          { label: '东莞市', value: '东莞市' }
        ]
      }
    },

    // 城市改变
    handleCityChange(value) {
      this.form.district = ''
      this.districts = []

      // 这里可以根据城市加载区县数据
      if (value === '广州市') {
        this.districts = [
          { label: '天河区', value: '天河区' },
          { label: '越秀区', value: '越秀区' },
          { label: '海珠区', value: '海珠区' }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-customer-edit {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;

    .header-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 16px;
        font-size: 14px;

        &:hover {
          color: #409eff;
        }
      }

      .page-title {
        h2 {
          margin: 0 0 4px 0;
          font-size: 20px;
          font-weight: 500;
          color: #303133;
        }

        .page-subtitle {
          margin: 0;
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .form-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: #f5f7fa;

    .enterprise-form {
      max-width: 1200px;
      margin: 0 auto;

      .form-section {
        background: #fff;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .section-header {
          margin-bottom: 24px;
          padding-bottom: 12px;
          border-bottom: 1px solid #ebeef5;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }

        .form-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;

          .el-form-item {
            margin-bottom: 0;

            &.full-width {
              grid-column: 1 / -1;
            }

            &.required {
              .el-form-item__label::before {
                content: '*';
                color: #f56c6c;
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }

  .input-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .enterprise-customer-edit {
    .form-container {
      .enterprise-form {
        .form-section {
          .form-grid {
            grid-template-columns: 1fr;

            .el-form-item {
              &.full-width {
                grid-column: 1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
