<template>
  <div class="enterprise-customer-container">
    <DataTemplateView
      :template-code="templateCode"
      :template-name="templateName"
      :custom-title="pageTitle"
      :show-breadcrumb="false"
      :actions="tableActions"
      :action-column-width="430"
      :show-selection="false"
      @action-click="handleAction"
      @add="handleAdd"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      ref="dataTemplateView"
    >
      <!-- 自定义列内容插槽 -->
      <template #name="{ row }">
        <div class="name-cell">
          <div class="name-content">
            <div class="name-icon">
                        <i class="el-icon-office-building"></i>
                      </div>
            <div class="name-text">{{ row.name }}</div>
          </div>
        </div>
      </template>

      <!-- 企业类型列 -->
      <template #enterpriseType="{ row }">
        <div class="type-cell">
          <el-tag
            :type="getEnterpriseTypeTagType(row.enterpriseType)"
            size="small"
            effect="light"
          >
            {{ row.enterpriseType || '未分类' }}
          </el-tag>
        </div>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <div class="status-cell">
          <el-tag
            :type="row.status === 1 ? 'success' : 'danger'"
            size="small"
            effect="light"
          >
            {{ row.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </div>
      </template>

      <!-- 联系人列 -->
      <template #contactPerson="{ row }">
        <div class="contact-cell">
          <div class="contact-name">{{ row.contactPerson || '-' }}</div>
          <div class="contact-phone" v-if="row.contactPhone">
            <i class="el-icon-phone"></i>
            {{ row.contactPhone }}
          </div>
        </div>
      </template>

      <!-- 地址列 -->
      <template #address="{ row }">
        <div class="address-cell">
          <el-tooltip
            :content="getFullAddress(row)"
            placement="top"
            :disabled="!getFullAddress(row)"
          >
            <div class="address-text">
              {{ getShortAddress(row) }}
            </div>
          </el-tooltip>
        </div>
      </template>

      <!-- 注册资本列 -->
      <template #registeredCapital="{ row }">
        <div class="capital-cell">
          <span class="capital-amount">
            {{ formatCapital(row.registeredCapital) }}
          </span>
        </div>
      </template>

      <!-- 成立时间列 -->
      <template #establishDate="{ row }">
        <div class="date-cell">
          <i class="el-icon-date"></i>
          {{ formatDate(row.establishDate) }}
        </div>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="action-buttons">
          <el-button
            type="text"
            size="small"
            @click="handleView(row)"
            class="action-btn view-btn"
          >
            <i class="el-icon-view"></i>
            查看
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleEdit(row)"
            class="action-btn edit-btn"
          >
            <i class="el-icon-edit"></i>
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleCopy(row)"
            class="action-btn copy-btn"
          >
            <i class="el-icon-document-copy"></i>
            复制
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleToggleStatus(row)"
            :class="['action-btn', row.status === 1 ? 'disable-btn' : 'enable-btn']"
          >
            <i :class="row.status === 1 ? 'el-icon-close' : 'el-icon-check'"></i>
            {{ row.status === 1 ? '停用' : '启用' }}
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleDelete(row)"
            class="action-btn delete-btn"
          >
            <i class="el-icon-delete"></i>
            删除
          </el-button>
        </div>
      </template>
    </DataTemplateView>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="企业客户详情"
      :visible.sync="viewDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="detail-content" v-if="currentRow">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>企业名称：</label>
              <span>{{ currentRow.name }}</span>
            </div>
            <div class="detail-item">
              <label>统一社会信用代码：</label>
              <span>{{ currentRow.creditCode || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>企业类型：</label>
              <span>{{ currentRow.enterpriseType || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>法定代表人：</label>
              <span>{{ currentRow.legalRepresentative || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>注册资本：</label>
              <span>{{ formatCapital(currentRow.registeredCapital) }}</span>
            </div>
            <div class="detail-item">
              <label>成立时间：</label>
              <span>{{ formatDate(currentRow.establishDate) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>联系信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>联系人：</label>
              <span>{{ currentRow.contactPerson || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ currentRow.contactPhone || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>邮箱：</label>
              <span>{{ currentRow.email || '-' }}</span>
            </div>
            <div class="detail-item full-width">
              <label>地址：</label>
              <span>{{ getFullAddress(currentRow) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>其他信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag
                :type="currentRow.status === 1 ? 'success' : 'danger'"
                size="small"
              >
                {{ currentRow.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(currentRow.createTime) }}</span>
            </div>
            <div class="detail-item full-width">
              <label>经营范围：</label>
              <span>{{ currentRow.businessScope || '-' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEditFromView">编辑</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="delete-content">
        <i class="el-icon-warning delete-icon"></i>
        <div class="delete-text">
          <p>确定要删除企业客户 <strong>{{ currentRow?.name }}</strong> 吗？</p>
          <p class="delete-warning">删除后将无法恢复，请谨慎操作！</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DataTemplateView from '@/components/business/DataTemplateView'

export default {
  name: 'EnterpriseCustomer',
  components: {
    DataTemplateView
  },
  data() {
    return {
      templateCode: 'ENTERPRISE_CUSTOMER',
      templateName: '企业客户管理',
      pageTitle: '企业客户管理',
      viewDialogVisible: false,
      deleteDialogVisible: false,
      currentRow: null,
      tableActions: [
        {
          label: '查看',
          key: 'view',
          type: 'primary',
          icon: 'el-icon-view'
        },
        {
          label: '编辑',
          key: 'edit',
          type: 'success',
          icon: 'el-icon-edit'
        },
        {
          label: '复制',
          key: 'copy',
          type: 'info',
          icon: 'el-icon-document-copy'
        },
        {
          label: '删除',
          key: 'delete',
          type: 'danger',
          icon: 'el-icon-delete'
        }
      ]
    }
  },
  methods: {
    // 处理操作按钮点击
    handleAction(action, row) {
      this.currentRow = row
      switch (action.key) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'copy':
          this.handleCopy(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    // 查看详情
    handleView(row) {
      this.currentRow = row
      this.viewDialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.$router.push({
        path: '/enterprise/customer/edit',
        query: { id: row.id, mode: 'edit' }
      })
    },

    // 从详情页面编辑
    handleEditFromView() {
      this.viewDialogVisible = false
      this.handleEdit(this.currentRow)
    },

    // 复制
    handleCopy(row) {
      this.$router.push({
        path: '/enterprise/customer/edit',
        query: { id: row.id, mode: 'copy' }
      })
    },

    // 删除
    handleDelete(row) {
      this.currentRow = row
      this.deleteDialogVisible = true
    },

    // 确认删除
    async confirmDelete() {
      try {
        // 这里调用删除接口
        this.$message.success('删除成功')
        this.deleteDialogVisible = false
        this.refreshData()
      } catch (error) {
        this.$message.error('删除失败')
      }
    },

    // 切换状态
    async handleToggleStatus(row) {
      try {
        const newStatus = row.status === 1 ? 0 : 1
        const statusText = newStatus === 1 ? '启用' : '停用'

        await this.$confirm(`确定要${statusText}企业客户 "${row.name}" 吗？`, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 这里调用状态切换接口
        row.status = newStatus
        this.$message.success(`${statusText}成功`)
        this.refreshData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('操作失败')
        }
      }
    },

    // 新增
    handleAdd() {
      this.$router.push('/enterprise/customer/edit')
    },

    // 搜索
    handleSearch(searchData) {
      console.log('搜索条件:', searchData)
    },

    // 重置
    handleReset() {
      console.log('重置搜索条件')
    },

    // 分页大小改变
    handleSizeChange(size) {
      console.log('分页大小改变:', size)
    },

    // 当前页改变
    handleCurrentChange(page) {
      console.log('当前页改变:', page)
    },

    // 刷新数据
    refreshData() {
      this.$refs.dataTemplateView?.loadData()
    },

    // 获取企业类型标签类型
    getEnterpriseTypeTagType(type) {
      const typeMap = {
        '微型企业': 'info',
        '小型企业': 'success',
        '中型企业': 'warning',
        '大型企业': 'danger'
      }
      return typeMap[type] || 'info'
    },

    // 格式化注册资本
    formatCapital(capital) {
      if (!capital) return '-'
      if (capital >= 10000) {
        return (capital / 10000).toFixed(2) + '万元'
      }
      return capital + '元'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString()
    },

    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime) return '-'
      return new Date(datetime).toLocaleString()
    },

    // 获取完整地址
    getFullAddress(row) {
      const parts = [row.province, row.city, row.district, row.address].filter(Boolean)
      return parts.join('') || '-'
    },

    // 获取短地址
    getShortAddress(row) {
      const fullAddress = this.getFullAddress(row)
      if (fullAddress === '-') return '-'
      return fullAddress.length > 20 ? fullAddress.substring(0, 20) + '...' : fullAddress
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-customer-container {
  height: 100%;

  .name-cell {
    .name-content {
      display: flex;
      align-items: center;

      .name-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 16px;
      }

      .name-text {
        font-weight: 500;
      }
    }
  }

  .type-cell {
    text-align: center;
  }

  .status-cell {
    text-align: center;
  }

  .contact-cell {
    .contact-name {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .contact-phone {
      font-size: 12px;
      color: #666;

      i {
        margin-right: 4px;
      }
    }
  }

  .address-cell {
    .address-text {
      cursor: pointer;

      &:hover {
        color: #409eff;
      }
    }
  }

  .capital-cell {
    text-align: right;

    .capital-amount {
      font-weight: 500;
      color: #f56c6c;
    }
  }

  .date-cell {
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
      color: #909399;
    }
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .action-btn {
      padding: 4px 8px;
      font-size: 12px;

      &.view-btn {
        color: #409eff;
      }

      &.edit-btn {
        color: #67c23a;
      }

      &.copy-btn {
        color: #909399;
      }

      &.enable-btn {
        color: #67c23a;
      }

      &.disable-btn {
        color: #f56c6c;
      }

      &.delete-btn {
        color: #f56c6c;
      }
    }
  }
}

.detail-content {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      color: #303133;
      font-size: 16px;
      font-weight: 500;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .detail-item {
        display: flex;

        &.full-width {
          grid-column: 1 / -1;
        }

        label {
          min-width: 120px;
          color: #606266;
          font-weight: 500;
        }

        span {
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }
}

.delete-content {
  display: flex;
  align-items: flex-start;

  .delete-icon {
    font-size: 24px;
    color: #f56c6c;
    margin-right: 12px;
    margin-top: 2px;
  }

  .delete-text {
    flex: 1;

    p {
      margin: 0 0 8px 0;

      &.delete-warning {
        font-size: 12px;
        color: #909399;
      }
    }

    strong {
      color: #f56c6c;
    }
  }
}
</style>
