<template>
  <InfoPageLayout
    title="KYC信息查询"
    subtitle="基于数据模板映射字段的企业KYC信息查询"
    icon="el-icon-user"
    :breadcrumbItems="breadcrumbItems"
    @back="handleBack"
  >
      <!-- 企业基本信息 -->
    <InfoCard title="企业基本信息" icon="el-icon-office-building">
          <el-row :gutter="20">
            <el-col :span="8">
          <InfoItem label="企业名称" :value="kycInfo.enterpriseName" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="统一社会信用代码" :value="kycInfo.creditCode" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="法定代表人" :value="kycInfo.legalPerson" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
          <InfoItem label="注册资本" :value="kycInfo.registeredCapital" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="成立日期" :value="kycInfo.establishDate" />
            </el-col>
            <el-col :span="8">
          <InfoItem label="经营状态">
                <el-tag :type="kycInfo.status === '在业' ? 'success' : 'danger'" size="small">
                  {{ kycInfo.status }}
                </el-tag>
          </InfoItem>
            </el-col>
          </el-row>
    </InfoCard>

      <!-- 身份验证信息 -->
    <InfoCard title="身份验证信息" icon="el-icon-s-check">
          <el-table :data="identityVerification" stripe class="modern-table">
            <el-table-column prop="fieldName" label="字段名称" width="200" />
            <el-table-column prop="fieldValue" label="字段值" min-width="300" />
            <el-table-column prop="verificationStatus" label="验证状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="scope.row.verificationStatus === '已验证' ? 'success' : 'warning'" size="small">
                  {{ scope.row.verificationStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" width="180" />
          </el-table>
    </InfoCard>

      <!-- 风险评估 -->
    <InfoCard title="风险评估" icon="el-icon-warning">
          <el-row :gutter="20">
            <el-col :span="6">
          <StatCard 
            :value="riskAssessment.overallScore" 
            label="综合风险评分" 
            icon="el-icon-data-analysis" 
            :bgColor="getRiskBgColor(riskAssessment.overallScore)"
            :tagText="getRiskLevel(riskAssessment.overallScore)"
            :tagType="getRiskTagType(riskAssessment.overallScore)"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="riskAssessment.businessRisk" 
            label="经营风险" 
            icon="el-icon-office-building" 
            :bgColor="getRiskBgColor(riskAssessment.businessRisk)"
            :tagText="getRiskLevel(riskAssessment.businessRisk)"
            :tagType="getRiskTagType(riskAssessment.businessRisk)"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="riskAssessment.financialRisk" 
            label="财务风险" 
            icon="el-icon-money" 
            :bgColor="getRiskBgColor(riskAssessment.financialRisk)"
            :tagText="getRiskLevel(riskAssessment.financialRisk)"
            :tagType="getRiskTagType(riskAssessment.financialRisk)"
          />
            </el-col>
            <el-col :span="6">
          <StatCard 
            :value="riskAssessment.complianceRisk" 
            label="合规风险" 
            icon="el-icon-s-check" 
            :bgColor="getRiskBgColor(riskAssessment.complianceRisk)"
            :tagText="getRiskLevel(riskAssessment.complianceRisk)"
            :tagType="getRiskTagType(riskAssessment.complianceRisk)"
          />
            </el-col>
          </el-row>
    </InfoCard>

      <!-- 合规检查 -->
    <InfoCard title="合规检查" icon="el-icon-s-order">
          <el-table :data="complianceChecks" stripe class="modern-table">
            <el-table-column prop="checkItem" label="检查项目" width="200" />
            <el-table-column prop="checkResult" label="检查结果" width="120">
              <template slot-scope="scope">
                <el-tag :type="scope.row.checkResult === '通过' ? 'success' : 'danger'" size="small">
                  {{ scope.row.checkResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="300" />
            <el-table-column prop="checkDate" label="检查日期" width="180" />
          </el-table>
    </InfoCard>

      <!-- 验真历史记录 -->
    <InfoCard title="验真历史记录" icon="el-icon-time">
          <el-table :data="verificationHistory" stripe class="modern-table">
            <el-table-column prop="verificationType" label="验真类型" width="140" min-width="140">
              <template slot-scope="scope">
                <el-tag :type="getVerificationTypeTag(scope.row.verificationType)" size="small">
                  {{ scope.row.verificationType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="操作人" width="120" />
            <el-table-column prop="operationTime" label="操作时间" width="180" />
            <el-table-column prop="queryStatus" label="查询状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="getQueryStatusTag(scope.row.queryStatus)" size="small">
                  {{ scope.row.queryStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="complianceApproval" label="合规审批" width="120">
              <template slot-scope="scope">
                <el-tag :type="getApprovalTag(scope.row.complianceApproval)" size="small">
                  {{ scope.row.complianceApproval }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="verificationResult" label="验真结果" width="120">
              <template slot-scope="scope">
                <el-tag :type="getResultTag(scope.row.verificationResult)" size="small">
                  {{ scope.row.verificationResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="验真描述" min-width="200" />
            <el-table-column prop="approver" label="审批人" width="120" />
            <el-table-column prop="approvalTime" label="审批时间" width="180" />
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewVerificationDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
    </InfoCard>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import StatCard from '@/components/layouts/StatCard'

export default {
  name: 'KYCQuery',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem,
    StatCard
  },
  data() {
    return {
      breadcrumbItems: [
        { text: '企业信息管理', icon: 'el-icon-office-building', to: 'EnterpriseManagement' },
        { text: 'KYC信息查询', icon: 'el-icon-user' }
      ],
      kycInfo: {
        enterpriseName: '北京科技有限公司',
        creditCode: '91110000123456789X',
        legalPerson: '张三',
        registeredCapital: '1000万元',
        establishDate: '2018-03-15',
        status: '在业'
      },
      identityVerification: [
        { fieldName: '企业名称', fieldValue: '北京科技有限公司', verificationStatus: '已验证', updateTime: '2023-12-01' },
        { fieldName: '统一社会信用代码', fieldValue: '91110000123456789X', verificationStatus: '已验证', updateTime: '2023-12-01' },
        { fieldName: '法定代表人', fieldValue: '张三', verificationStatus: '已验证', updateTime: '2023-12-01' },
        { fieldName: '注册资本', fieldValue: '1000万元', verificationStatus: '已验证', updateTime: '2023-12-01' },
        { fieldName: '经营范围', fieldValue: '技术开发、技术咨询、技术服务', verificationStatus: '待验证', updateTime: '2023-11-15' }
      ],
      riskAssessment: {
        overallScore: 75,
        businessRisk: 80,
        financialRisk: 70,
        complianceRisk: 65
      },
      complianceChecks: [
        { checkItem: '反洗钱检查', checkResult: '通过', description: '企业反洗钱合规检查通过', checkDate: '2023-12-01' },
        { checkItem: '身份验证', checkResult: '通过', description: '法定代表人身份信息验证通过', checkDate: '2023-12-01' },
        { checkItem: '经营资质', checkResult: '通过', description: '企业经营资质检查通过', checkDate: '2023-11-30' },
        { checkItem: '信用记录', checkResult: '通过', description: '企业信用记录检查通过', checkDate: '2023-11-30' }
      ],
      verificationHistory: [
        { 
          verificationType: '身份验真', 
          operator: '李四', 
          operationTime: '2023-12-01 14:30:00', 
          queryStatus: '查询成功', 
          complianceApproval: '已通过', 
          verificationResult: '验真通过', 
          description: '法定代表人身份信息验真通过', 
          approver: '王五', 
          approvalTime: '2023-12-01 15:00:00' 
        },
        { 
          verificationType: '工商验真', 
          operator: '张三', 
          operationTime: '2023-11-30 16:20:00', 
          queryStatus: '查询成功', 
          complianceApproval: '已通过', 
          verificationResult: '验真通过', 
          description: '企业工商信息验真通过', 
          approver: '赵六', 
          approvalTime: '2023-11-30 17:00:00' 
        },
        { 
          verificationType: '税务验真', 
          operator: '系统管理员', 
          operationTime: '2023-11-29 10:15:00', 
          queryStatus: '查询中', 
          complianceApproval: '待审批', 
          verificationResult: '验真中', 
          description: '企业税务信息验真进行中', 
          approver: '', 
          approvalTime: '' 
        },
        { 
          verificationType: '银行验真', 
          operator: '风控专员', 
          operationTime: '2023-11-28 09:45:00', 
          queryStatus: '查询失败', 
          complianceApproval: '已拒绝', 
          verificationResult: '验真失败', 
          description: '企业银行账户信息验真失败', 
          approver: '合规经理', 
          approvalTime: '2023-11-28 11:00:00' 
        },
        { 
          verificationType: '信用验真', 
          operator: '数据专员', 
          operationTime: '2023-11-27 14:20:00', 
          queryStatus: '查询成功', 
          complianceApproval: '已通过', 
          verificationResult: '验真通过', 
          description: '企业信用记录验真通过', 
          approver: '风控经理', 
          approvalTime: '2023-11-27 15:30:00' 
        }
      ]
    }
  },
  created() {
    // 从路由参数获取企业信息
    const { id, enterpriseName } = this.$route.params
    if (enterpriseName) {
      this.kycInfo.enterpriseName = enterpriseName
    }
    // TODO: 根据企业ID和数据模板配置加载KYC数据
    this.loadKYCData(id)
  },
  methods: {
    loadKYCData(enterpriseId) {
      // TODO: 根据企业ID和数据模板配置加载KYC数据
      console.log('加载企业ID:', enterpriseId, '的KYC数据')
    },
    getRiskTagType(score) {
      if (score >= 80) return 'danger'
      if (score >= 60) return 'warning'
      return 'success'
    },
    getRiskBgColor(score) {
      if (score >= 80) return 'danger'
      if (score >= 60) return 'warning'
      return 'success'
    },
    getRiskLevel(score) {
      if (score >= 80) return '高风险'
      if (score >= 60) return '中风险'
      return '低风险'
    },
    getVerificationTypeTag(type) {
      const typeMap = {
        '身份验真': 'primary',
        '工商验真': 'success',
        '税务验真': 'warning',
        '银行验真': 'info',
        '信用验真': 'danger'
      }
      return typeMap[type] || 'info'
    },
    getQueryStatusTag(status) {
      const statusMap = {
        '查询成功': 'success',
        '查询中': 'warning',
        '查询失败': 'danger'
      }
      return statusMap[status] || 'info'
    },
    getApprovalTag(approval) {
      const approvalMap = {
        '已通过': 'success',
        '待审批': 'warning',
        '已拒绝': 'danger'
      }
      return approvalMap[approval] || 'info'
    },
    getResultTag(result) {
      const resultMap = {
        '验真通过': 'success',
        '验真中': 'warning',
        '验真失败': 'danger'
      }
      return resultMap[result] || 'info'
    },
    viewVerificationDetail(record) {
      this.$message.info(`查看验真详情：${record.verificationType} - ${record.description}`)
    },
    handleBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
        .modern-table {
          /deep/ .el-table__header-wrapper {
            th {
              background: #f7ecdd;
              font-weight: 600;
              color: #2c3e50;
    }
  }
}
</style> 