<template>
  <div class="enterprise-type-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业类型管理"
      subtitle="定义企业分类标准，支持区间配置和优先级设置"
      title-icon="el-icon-s-data"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="225"
      :search-label-width="'100px'"
      add-button-text="新增类型"
      empty-title="暂无企业类型数据"
      empty-description="点击上方新增类型按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 自定义列内容插槽 -->
      <template #priority="{ row }">
        <el-tag type="info" size="small">
          {{ row.priority }}
        </el-tag>
      </template>

      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
        </div>
      </template>
    </UniversalTable>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该企业类型？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getEnterpriseTypeList, deleteEnterpriseType } from '@/api/enterprise/type.js'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'EnterpriseTypeList',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        name: '',
        code: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '类型名称',
          width: 120,
          align: 'center'
        },
        {
          prop: 'code',
          label: '类型编码',
          width: 100,
          align: 'center'
        },
        {
          prop: 'employeeRangeText',
          label: '员工规模',
          width: 150,
          align: 'center'
        },
        {
          prop: 'revenueRangeText',
          label: '营收规模',
          width: 150,
          align: 'center'
        },
        {
          prop: 'priority',
          label: '优先级',
          width: 100,
          align: 'center'
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '类型名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入类型名称'
        },
        {
          label: '类型编码',
          name: 'code',
          type: 'input',
          placeholder: '请输入类型编码'
        }
      ],
      currentRow: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const data = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            name: this.searchForm.name || null,
            code: this.searchForm.code || null
          }
        }

        const response = await getEnterpriseTypeList(data)
        // 后端返回的数据格式：response.list, response.total
        this.tableData = response.list || []
        this.pagination.total = response.total || 0
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 处理操作按钮点击
    handleAction({ action, row }) {
      this.currentRow = row

      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    handleSearch(searchData) {
      this.searchForm = { ...searchData }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleReset() {
      this.searchForm = {
        name: '',
        code: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    getPriorityTagType(priority) {
      // 统一使用info类型，保持简洁
      return 'info'
    },

    handleAdd() {
      this.$router.push({ name: 'enterpriseTypeEdit' })
    },

    handleView(row) {
      this.$router.push({ name: 'enterpriseTypeEdit', params: { id: row.id }, query: { mode: 'view' } })
    },

    handleEdit(row) {
      this.$router.push({ name: 'enterpriseTypeEdit', params: { id: row.id } })
    },

    handleDelete(row) {
      this.$refs.confirmDialog.show(`确定要删除企业类型「${row.name}」吗？`)
    },

    async confirmDelete() {
      try {
        await deleteEnterpriseType({ id: this.currentRow.id })
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        this.$message.error('删除失败')
        console.error('删除失败:', error)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-type-container {
  min-height: 100vh;
  background: #fbf6ee;

  /* 必要的表格单元格样式 */
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;

    i {
      margin-right: 4px;
    }
  }
}
</style>
