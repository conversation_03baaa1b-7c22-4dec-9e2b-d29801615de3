<template>
  <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
      @back="handleBack"
    @save="handleSave"
      @breadcrumb-click="handleBreadcrumbClick"
    >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'100px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getEnterpriseDetail, createEnterprise, updateEnterprise } from '@/api/enterprise/index.js'

export default {
  name: 'EnterpriseInfoEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        name: '',
        code: '',
        type: '',
        industry: '',
        employeeCount: '',
        revenue: 0,
        establishDate: '',
        address: '',
        contactPerson: '',
        contactPhone: '',
        remark: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
        [
              { prop: 'name', label: '企业名称', type: 'input', placeholder: '请输入企业名称', maxlength: 100, showWordLimit: true },
              { prop: 'code', label: '企业编码', type: 'input', placeholder: '请输入企业编码', maxlength: 20 },
              { prop: 'industry', label: '所属行业', type: 'select', placeholder: '请选择所属行业', options: [
              { value: '制造业', label: '制造业' },
              { value: '服务业', label: '服务业' },
              { value: '金融业', label: '金融业' },
              { value: '房地产业', label: '房地产业' },
              { value: '信息技术', label: '信息技术' },
              { value: '教育', label: '教育' },
              { value: '医疗健康', label: '医疗健康' },
              { value: '其他', label: '其他' }
              ] }
            ],
            [
              { prop: 'type', label: '企业类型', type: 'select', placeholder: '请选择企业类型', options: [
              { value: 'A类', label: 'A类' },
              { value: 'B类', label: 'B类' },
              { value: 'C类', label: 'C类' },
              { value: 'D类', label: 'D类' },
              { value: 'E类', label: 'E类' }
              ] },
              { prop: 'address', label: '企业地址', type: 'input', placeholder: '请输入企业地址', maxlength: 200 },
              { prop: 'employeeCount', label: '人员规模', type: 'select', placeholder: '请选择人员规模', options: [
              { value: '1-50人', label: '1-50人' },
              { value: '51-100人', label: '51-100人' },
              { value: '101-500人', label: '101-500人' },
              { value: '501-1000人', label: '501-1000人' },
              { value: '1001-5000人', label: '1001-5000人' },
              { value: '5000人以上', label: '5000人以上' }
              ] }
        ],
        [
              { prop: 'revenue', label: '企业年收入', type: 'number', placeholder: '请输入企业年收入', min: 0, precision: 2, step: 10000, unit: '万元' },
              { prop: 'establishDate', label: '成立日期', type: 'date', placeholder: '请选择成立日期' }
        ]
          ]
        },
        {
          title: '联系信息',
          icon: 'el-icon-phone',
          fields: [
        [
              { prop: 'contactPerson', label: '联系人姓名', type: 'input', placeholder: '请输入联系人姓名', maxlength: 20 },
              { prop: 'contactPhone', label: '联系方式', type: 'input', placeholder: '请输入联系电话', maxlength: 20 }
            ],
            [
              { prop: 'remark', label: '备注信息', type: 'textarea', placeholder: '请输入备注信息', maxlength: 500, rows: 3, showWordLimit: true, span: 24 }
        ]
          ]
        }
      ],
      formRules: {
        name: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入企业编码', trigger: 'blur' }
        ],
        industry: [
          { required: true, message: '请选择所属行业', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ],
        employeeCount: [
          { required: true, message: '请选择人员规模', trigger: 'change' }
        ],
        contactPerson: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    // 页面标题
    pageTitle() {
      return this.isView ? '查看企业信息' : (this.isEdit ? '编辑企业信息' : '新建企业信息')
    },
    
    // 页面图标
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    
    // 面包屑导航
    breadcrumbItems() {
      return [
        {
          text: '企业信息管理',
          icon: 'el-icon-office-building',
          to: { name: 'enterpriseInfo' }
        },
        {
          text: this.pageTitle,
          icon: this.pageIcon
        }
      ]
    }
  },
  
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    
    if (id) {
      this.isEdit = mode !== 'view'
      this.isView = mode === 'view'
      this.loadData(id)
    }
  },
  
  methods: {
    async loadData(id) {
      this.loading = true
      try {
        const response = await getEnterpriseDetail(id)
        if (response.code === 200) {
          this.form = { ...response.data }
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        let response
        if (this.isEdit) {
          response = await updateEnterprise(this.$route.params.id, this.form)
        } else {
          response = await createEnterprise(this.form)
        }
        if (response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          this.$router.back()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>