<template>
  <div class="permission-config">
    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>
    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"></SearchForm>

    <!-- 树形表格 -->
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column align="center" prop="authName" label="权限名称" min-width="200">
        <template slot-scope="scope">
          <span class="permission-name">{{ scope.row.authName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="authCode" label="权限编码" min-width="200">
        <template slot-scope="scope">
          <span class="permission-code">{{ scope.row.authCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" width="200" label="创建时间">
        <template slot-scope="scope">
          <div class="time-cell">
            {{ scope.row.createTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建人" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.createId | getNickName(scope.row.createId) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button class="btn-center" type="text" @click="addPermission(scope.row)">
              新增
            </el-button>
            <el-button class="btn-center" type="text" @click="editPermission(scope.row)">
              编辑
            </el-button>
            <el-button class="btn-center" type="text" @click="deletePermission(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑权限弹窗 -->
    <DtPopup :isShow.sync="showPermissionPopup" @close="closePermissionPopup"
      :title="permissionForm.id ? '编辑权限' : '新增权限'" center :footer="false" width="500px">
      <div class="permission-form">
        <el-form ref="permissionFormRef" :model="permissionForm" :rules="permissionRules" label-width="100px">
          <el-form-item label="权限名称" class="dt-input-width" prop="authName">
            <el-input v-model="permissionForm.authName" placeholder="请输入权限名称" maxlength="30"
              show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="权限编码" class="dt-input-width" prop="authCode">
            <el-input v-model="permissionForm.authCode" :disabled="codeDisabled" placeholder="请输入权限编码" maxlength="50"
              show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="权限说明" class="dt-input-width" prop="authDesc">
            <el-input v-model="permissionForm.authDesc" type="textarea" placeholder="请输入权限说明（可选）" 
              :rows="3" maxlength="200" show-word-limit></el-input>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closePermissionPopup">取消</el-button>
          <el-button type="primary" @click="submitPermissionForm" :loading="submitLoading">
            {{ permissionForm.id ? '保存' : '新增' }}
          </el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 删除确认弹窗 -->
    <DtPopup :isShow.sync="showDeletePopup" @close="closeDeletePopup" title="确认删除" center :footer="false" width="400px">
      <div class="delete-confirm">
        <div class="confirm-content">
          <p>删除后无法恢复，请确认是否删除该权限？</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeDeletePopup">取消</el-button>
          <el-button :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }"
            @click="confirmDelete" :loading="deleteLoading">删除</el-button>
        </div>
      </div>
    </DtPopup>
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import { deletePermission, permissionSaveOrUpdate, selectAuthTree } from "@/api/roleManagement/index";
export default {
  name: "permissionConfig",
  data() {
    return {
      toolListProps: {
        toolTitle: "权限位配置页",
        toolList: [
          {
            name: "新增权限位",
            icon: "el-icon-plus",
            btnCode: ""
          }
        ]
      },
      // 表格数据 - 树形结构
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          authName: "",
          authCode: "",
          parentCode: "",
        }
      },
      searchFormTemp: [
        {
          label: "权限名称",
          name: "authName",
          type: "input",
          placeholder: "请输入权限名称"
        },
        {
          label: "权限编码",
          name: "authCode",
          type: "input",
          placeholder: "请输入权限编码"
        }
      ],

      // 权限表单相关
      showPermissionPopup: false,
      permissionForm: {
        id: "",
        authName: "",
        authCode: "",
        parentCode: "",
        authDesc:''
      },
      permissionRules: {
        authName: [
          { required: true, message: "请输入权限名称", trigger: "blur" },
          { min: 2, max: 30, message: "长度在 2 到 30 个字符", trigger: "blur" }
        ],
        authCode: [
          { required: true, message: "请输入权限编码", trigger: "blur" },
          { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" }
        ],
        authDesc: [
          { max: 200, message: "说明长度不能超过 200 个字符", trigger: "blur" }
        ]
      },
      submitLoading: false,

      // 删除相关
      showDeletePopup: false,
      deleteItem: {},
      deleteLoading: false,
      codeDisabled:false
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup,
    Pagination
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  async created() {
    this.initData();
  },
  methods: {
    async initData() {
      let res = await selectAuthTree(this.initParam);
      if(res){
        this.tableData = res
      }
    },

    handleTool(item) {
      if (item.name === "新增权限位") {
        this.addPermission();
      }
    },

    // 新增权限
    addPermission(parentRow = null) {
      this.permissionForm = {
        id: "",
        authName: "",
        authCode: "",
        parentCode: parentRow ? parentRow.authCode : "",
        authDesc:''
      };
      this.showPermissionPopup = true;
      this.codeDisabled = false;
    },

    // 编辑权限
    editPermission(row) {
      this.permissionForm = {
        id: row.id,
        authName: row.authName,
        authCode: row.authCode,
        parentCode: row.parentCode || "",
        authDesc:row.authDesc
      };
      this.showPermissionPopup = true;
      this.codeDisabled = true;
    },

    // 删除权限
    deletePermission(row) {
      this.deleteItem = { ...row };
      this.showDeletePopup = true;
    },

    // 提交权限表单
    async submitPermissionForm() {
      try {
        await this.$refs.permissionFormRef.validate();
        this.submitLoading = true;

        let res = await permissionSaveOrUpdate(this.permissionForm);
        if (res) {
          if (this.permissionForm.id) {
            // 编辑
            this.$message.success("权限编辑成功");
          } else {
            // 新增
            this.$message.success("权限新增成功");
          }
          this.closePermissionPopup();
          this.initData(); // 重新加载数据
        }

      } catch (error) {
      } finally {
        this.submitLoading = false;
      }
    },

    // 确认删除
    async confirmDelete() {
      try {
        this.deleteLoading = true;

        let res = await deletePermission({id:this.deleteItem.id});
        if(res){
          this.$message.success("权限删除成功");
          this.closeDeletePopup();
          this.initData(); // 重新加载数据
        }
      } catch (error) {
      } finally {
        this.deleteLoading = false;
      }
    },

    // 关闭权限弹窗
    closePermissionPopup() {
      this.showPermissionPopup = false;
      this.$nextTick(() => {
        if (this.$refs.permissionFormRef) {
          this.$refs.permissionFormRef.resetFields();
        }
      });
    },

    // 关闭删除弹窗
    closeDeletePopup() {
      this.showDeletePopup = false;
      this.deleteItem = {};
    },

    // 搜索
    normalSearch(data) {
      console.log("搜索参数:", data);
      this.initData();
    },

    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },


  }
};
</script>

<style lang="less" scoped>
.permission-config {
  .permission-name {
    font-weight: 500;
    color: #303133;
  }

  .permission-code {
    font-family: 'Courier New', monospace;
    color: #606266;
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }

  .time-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #909399;
    font-size: 13px;

    i {
      font-size: 14px;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .permission-form {
    padding-bottom: 20px;

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-icon {
      margin-bottom: 16px;

      i {
        font-size: 48px;
        color: #e6a23c;
      }
    }

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;

        &.delete-item {
          color: #909399;
          font-size: 13px;
          background: #f5f7fa;
          padding: 8px 12px;
          border-radius: 4px;
          margin: 4px 0;
        }
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

// 表格样式优化
.dt-table {
  /deep/ .el-table__row {
    .el-button--text {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }

      &.el-button--danger {
        color: #f56c6c;

        &:hover {
          color: #f78989;
        }
      }
    }
  }
}
</style>