
.clearfix:before,
.clearfix:after {
  display: table;
  content: " ";
}

.clearfix:after {
  clear: both;
}

.el-loading-spinner{
  margin-top: -40px !important;
  .system-loading-gif{
    display: block;
    background: url(../imgs/loading.gif);
    background-size: 100%;
    width: 66px;
    height: 66px;
    margin: 0 auto;
  }
}

::-webkit-scrollbar{
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track-piece{
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:vertical{
  height: 5px;
  background-color: rgba(175, 175, 175, 0.70);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:horizontal{
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}


// 覆盖 element-ui 弹窗样式
.el-popover{
  padding: 10px !important;
}
//一行文本溢出函数...
.ellipsis1{
  overflow:hidden;
  display: -webkit-box;
  -webkit-line-clamp:1;
  -webkit-box-orient: vertical; 
  word-wrap: break-word;
  word-break: break-all; 
  white-space: normal!important;
}
//三行文本溢出函数...
.ellipsis3{
  overflow:hidden;
  display: -webkit-box;
  -webkit-line-clamp:3;
  -webkit-box-orient: vertical; 
  word-wrap: break-word;
  word-break: break-all; 
  white-space: normal!important;
}
//鼠标样式
.pointer{
  cursor: pointer;
  &:active{
    opacity: .8;
  }
}
//提示
.toolTip{
  width:100%;
  height:100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color:#909399;
}
//文本换行
.lineFeed{
  word-break: break-all; 
  word-wrap:break-word; 
}
// 输入框宽度
.dt-input-width{
  // width: 217px !important;
  width: 372px !important;
}
.dt-input-width-max{
  //width: 300px !important;
  width: 400px !important;
}
// textarea 宽度
.dt-textarea-width{
  width: 426px !important;
}
.el-textarea{
  .el-textarea__inner{
    font-family: Arial, Helvetica, sans-serif !important;
  }
}
// 内容区菜单
.dt-el-tabs{
  margin-left:-15px;
  margin-right:-15px;
}
.dt-fz14{
  font-size: 14px;
}
.dt-fz15{
  font-size: 15px;
}
.dt-fz16{
  font-size: 16px;
}
.dt-fz18 {
  font-size: 18px;
}
.dt-fz22{
  font-size: 22px;
}

//加粗
.dt-bl{
  font-weight: 600;
}
//鼠标样式
.pointer{
  cursor: pointer;
  &:active{
    opacity: .8;
  }
}
//文本
.text-c {
  text-align: center;
}
.text-l {
  text-align: left;
}
.text-r {
  text-align: right;
}

//字体颜色
.c-999 {
  color: #999;
}
.c-666 {
  color: #666;
}
.c-333 {
  color: #333;
}

//间距
.mr-5 {
  margin-right: 5px;
}
.ml-10 {
  margin-left: 10px;
}
.mr-10 {
    margin-right: 10px;
}
.mr-20 {
  margin-right: 20px;
}
.mt-10{
    margin-top:10px;
}
.mt-15{
  margin-top:15px;
}
.mt-20 {
    margin-top:20px;
}
.mb-5 {
  margin-bottom:5px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mb-15 {
  margin-bottom: 15px;
}
.mb-20 {
  margin-bottom: 20px;
}
.pd-15 {
  padding: 15px;
}
.pdl-10 {
    padding-left: 10px;
}
.pdl-15 {
  padding-left: 15px;
}
.ptb-20 {
  padding: 20px 0;
}
.pdl-20 {
  padding-left: 20px;
}
.pdb-20 {
    padding-bottom: 20px;
}

//布局
.df {
  display: flex;
}
.df-ac {
  display: flex;
  align-items: center;
}
.df-cc {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dfc-cc {
    display: flex;
    flex-direction:column;
    align-items: center;
    justify-content: center;
}

.break-word{
  word-break: break-all; 
  word-wrap:break-word;
}
//表格
.dt-table.el-table {
  &::before {
    height: 0;
    width: 0;
  }
  thead {
    color: #333;
  }
  .el-table__row td {
    border:none
  }
  tr {
      th {
        .cell {
          padding-left: 14px;
        }
        background-color: #F9F9F9;
        &.is-leaf {
          border:none;
        }
      }
  }

}



.dt-bread {
  padding: 14px 0 14px 20px;
  border-bottom: 1px solid #eeeeee;
  .el-breadcrumb__inner.is-link {
    color: #999 !important;
    font-size: 14px;
    font-weight: 400 !important;
  }
  .el-breadcrumb__inner {
    color: inherit !important;
  }
  .trendsTitle {
    .el-breadcrumb__inner{
      color: #999 !important;
      font-size: 14px;
      font-weight: 400 !important;
      cursor: pointer;
    }
  }

}

.el-button {
  &.dt-btn {
    padding: 14px 66px;
    font-size: 18px;
  }
  &.dt-btn-large {
    padding: 12px 75px;
  }
  &.common {
    padding: 12px 35px;
  }
  &.el-button--primary.is-plain {
    background-color: #fff !important;
    &:hover {
      //color: inherit;
      //background-color: #fff;
      //border-color: inherit;
    }
  }
  font-weight: 400 !important;
}

.cell-wrap{
  margin-left: -40px;
  margin-right: -40px;
  .cell-com{
    height: 50px;
    line-height: 50px;
    padding-left:40px;
    padding-right:40px;
  }
  .cell-item {
    color: #333;
    width: 30%;
    float: left;
    padding-right: 3%;
    div{
      display: inline-block;
    }
    .label {
      margin-right: 8px;
    }
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }
  .cell-bg{
    background: rgba(249,249,249,1)
  }
}


.el-table .el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: transparent;
}

.dt-select {
  width: 372px;
}
.puoup-del {
  padding: 20px 0 25px 0;
}


.el-form-item {
  .el-form-item__label {
    position: relative;
    padding-left: 10px;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}


.title-tenantName {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-left: 23px;
}

.el-tabs--border-card>.el-tabs__content {
  padding: 0;
}


.icon-pop{
  .dt-tabs{
    &.el-tabs--border-card {
      .el-tabs__header {
        background-color: #f5f5f5;
        .el-tabs__item {
          margin-right: 0;
          .tab-label {
            color: #333;
            font-weight: normal;
          }
        }
      }
    }
  }
}

.log-com-title{
  // padding: 0 0 10px 88px;
  // padding-top: 10px;
  font-size: 14px;
}


.dt-subTable-title {
  padding-left: 20px;
  font-weight: 600;
  font-size: 16px;
  height: 48px;
  line-height: 48px;
  .title-icon{
    width: 10px;
    height: 10px;
    margin-right: 6px;
    display: inline-block;
    transform: rotate(45deg);
  }
  span{
    margin-right: 10px;
  }
  .iconfont{
    position: relative;
    font-size: 22px;
    top:3px;
  }
}


.dt-dialog-img{
  .el-dialog{
    width: fit-content;
    background: transparent;
    box-shadow:none;
    position: absolute;
    margin: 0!important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .cont{
      // width: 372px;
      // height: 740px;
      width: 321px;
      height: 640px;
      background: url("../imgs/content-bg.png");
      background-size: cover;
      position: relative;
      .tab{
        position: absolute;
        top: 46px;
        left: 34px;
        display: flex;
        .tab-item{
          width: 92px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          font-size: 15px;
          color: #333;
          background: #fff;
          margin-left: 24px;
          border-radius: 20px;
          cursor: pointer;
        }
      }
    }
    .img-pd{
      padding: 80px 26px 24px 26px;
    }
    .img-wrap{
      text-align: center;
      border-radius: 10px;
      height: 536px;
      overflow-y: auto;
      background-size: cover;
      position: relative;
      .img-box{
        img{
          width: 100%;
          display:block;
        }
      }
      .pos-img{
        position: absolute;
      }
      .img-scroll {
        width: 100%;
        overflow-x: scroll;
        img{
          width: 100%;
        }
        white-space: nowrap;
        justify-content: space-between;
      }
    }
    .img-wrap::-webkit-scrollbar{
      width: 0;
    }
    .dt-dialog-img-btn{
      color: #fff;
      line-height: 30px;
      height: 30px;
      text-align: center;
      border-radius: 4px;
      font-size: 12px;
      width: 100px;
      margin: 20px auto 0;
      cursor: pointer;
    }
  }
  .el-dialog__header{
    display: none;
  }
  .el-dialog__body{
    padding: 0;
  }
}

.breadcrum {
    height: 35px;
    line-height: 35px !important;
    font-size: initial;
    padding-left: 20px;
    border-bottom: 1px solid #DCDFE6;

    .el-breadcrumb__inner.is-link {
        font-weight: initial;
    }
}
.popup-text {
    text-align: center;
    font-size: 16px;
    padding: 20px 0 25px 0;
}

.image-size{
    width:80px;
    height:80px;
}
.imageUrl{
  width:70px;
  height:auto;
}

.tabs-container{
  &.no-animation{
    .el-tabs__active-bar {
      display: none;
    }
    .is-active .underline{
      position: relative;
      display: inline-block;
      vertical-align: bottom;
      &::after{
        position: absolute;
        bottom:-15px;
        left:0px;
        font-size:35px;
        display: inline-block;
        content: "——";
        width:100%;
        overflow:hidden;
      }
    }
  }
}

.tab-tool.second-tab-tool {
  padding-left: 20px;
  .tab-title {
      i {
          width: 8px;
          height: 8px;
          transform: rotate(45deg);
          top: -3px;
          margin-right: 5px;
      }
      font-size: 16px;
  }
}