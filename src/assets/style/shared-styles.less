// 指标管理组件统一样式
// 适用于：分组聚合、数据连接、条件函数、数据过滤等组件

// 主题色彩变量
@primary-color: #D7A256;
@primary-hover: #E6B366;
@primary-light: rgba(215, 162, 86, 0.1);
@primary-shadow: rgba(215, 162, 86, 0.3);

@bg-light: #fbf6ee;
@border-light: #f7ecdd;
@text-primary: #2c3e50;
@text-secondary: #909399;
@text-disabled: #c0c4cc;

@hover-bg: #f0f9ff;
@table-bg: #fafbfc;
@disabled-bg: #f5f7fa;
@error-color: #f56c6c;
@error-bg: #fef0f0;

// 统一的配置区域样式
.config-section {
  background: white;
  border-radius: 12px;
  border: 1px solid @border-light;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  margin-bottom: 20px;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  // 区域头部样式
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: @bg-light;
    border-bottom: 1px solid @border-light;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.default {
          i {
            color: @primary-color;
          }
        }

        i {
          font-size: 18px;
          color: @primary-color;
        }
      }

      .section-title {
        h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: @text-primary;
        }

        .section-subtitle {
          font-size: 13px;
          color: @text-secondary;
          font-weight: 400;
        }
      }
    }

    .header-actions {
      .add-btn {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        background: @primary-color;
        color: white;
        border: none;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px @primary-shadow;
          background: @primary-hover;
        }

        i {
          margin-right: 4px;
        }
      }
    }
  }

  // 区域内容样式
  .section-content {
    padding: 20px;

    .editUserForm {
      /deep/ .el-form-item {
        .el-form-item__label {
          font-weight: 500;
          color: @text-primary;
          font-size: 14px;
          line-height: 36px;
        }

        .el-form-item__content {
          .modern-input {
            .el-input__inner {
              border-radius: 8px;
              border-color: #dcdfe6;
              font-size: 14px;
              padding: 0 12px;
              height: 36px;
              line-height: 36px;
              transition: all 0.3s ease;

              &:focus {
                border-color: @primary-color;
                box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
              }

              &:hover {
                border-color: #c0c4cc;
              }
            }
          }
        }
      }
    }
  }

  // 表格包装器样式
  .table-wrapper {
    .modern-table {
      border-radius: 0;
      overflow: hidden;
      border: none;
      font-size: 14px;

      // 表头样式
      /deep/ .el-table__header-wrapper {
        .el-table__header {
          th {
            background: @bg-light;
            font-weight: 600;
            font-size: 13px;
            color: @text-primary;
            border-bottom: 2px solid @border-light;
            padding: 12px 0;

            .cell {
              padding: 0 16px;
            }
          }
        }
      }

      // 表体样式
      /deep/ .el-table__body-wrapper {
        .el-table__body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: @hover-bg !important;
            }

            td {
              padding: 12px 0;
              border-bottom: 1px solid #f5f7fa;

              .cell {
                padding: 0 16px;
              }

              // 现代化选择框样式
              .modern-select,
              .el-select {
                .el-input__inner {
                  border-radius: 6px;
                  border-color: #dcdfe6;
                  height: 32px;
                  line-height: 32px;
                  transition: all 0.3s ease;

                  &:focus {
                    border-color: @primary-color;
                    box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                  }

                  &:hover {
                    border-color: #c0c4cc;
                  }
                }
              }

              // 现代化输入框样式
              .modern-input,
              .el-input {
                .el-input__inner {
                  border-radius: 6px;
                  border-color: #dcdfe6;
                  height: 32px;
                  line-height: 32px;
                  transition: all 0.3s ease;

                  &:focus {
                    border-color: @primary-color;
                    box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                  }

                  &:hover {
                    border-color: #c0c4cc;
                  }

                  &:disabled {
                    background: @disabled-bg;
                    color: @text-secondary;
                  }
                }
              }

              // 删除按钮样式
              .delete-btn {
                color: @error-color;
                font-size: 14px;
                padding: 4px 8px;
                border-radius: 4px;
                transition: all 0.3s ease;
                background: transparent;
                border: none;

                &:hover {
                  background: @error-bg;
                  color: @error-color;
                  transform: scale(1.05);
                }
              }

              // 操作按钮组样式
              .action-buttons {
                display: flex;
                gap: 8px;
                align-items: center;

                .action-btn {
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 12px;
                  font-weight: 500;
                  transition: all 0.3s ease;
                  border: 1px solid transparent;

                  &.primary {
                    background: @primary-light;
                    color: @primary-color;
                    border-color: @primary-color;

                    &:hover {
                      background: @primary-color;
                      color: white;
                      transform: translateY(-1px);
                    }
                  }

                  &.danger {
                    background: rgba(245, 108, 108, 0.1);
                    color: @error-color;
                    border-color: @error-color;

                    &:hover {
                      background: @error-color;
                      color: white;
                      transform: translateY(-1px);
                    }
                  }
                }
              }
            }
          }
        }
      }

      // 空状态样式
      /deep/ .el-table__empty-block {
        background: @table-bg;
        
        .el-table__empty-text {
          color: @text-secondary;
          font-size: 14px;
          padding: 40px 0;
        }
      }
    }
  }
}

// 统一的表单样式
.unified-form {
  /deep/ .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
      color: @text-primary;
      font-size: 14px;
      line-height: 36px;
    }

    .el-form-item__content {
      .el-input {
        .el-input__inner {
          border-radius: 8px;
          border-color: #dcdfe6;
          font-size: 14px;
          padding: 0 12px;
          height: 36px;
          line-height: 36px;
          transition: all 0.3s ease;

          &:focus {
            border-color: @primary-color;
            box-shadow: 0 0 0 2px @primary-light;
          }

          &:hover {
            border-color: #c0c4cc;
          }

          &:disabled {
            background: @disabled-bg;
            color: @text-secondary;
          }
        }
      }

      .el-select {
        width: 100%;

        .el-input__inner {
          border-radius: 8px;
          border-color: #dcdfe6;
          font-size: 14px;
          height: 36px;
          line-height: 36px;
          transition: all 0.3s ease;

          &:focus {
            border-color: @primary-color;
            box-shadow: 0 0 0 2px @primary-light;
          }

          &:hover {
            border-color: #c0c4cc;
          }
        }
      }

      .el-button {
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }

        &.el-button--primary {
          background: @primary-color;
          border-color: @primary-color;

          &:hover {
            background: @primary-hover;
            border-color: @primary-hover;
            box-shadow: 0 4px 12px @primary-shadow;
          }
        }
      }
    }
  }
}

// 统一的卡片样式
.unified-card {
  background: white;
  border-radius: 12px;
  border: 1px solid @border-light;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .card-header {
    padding: 16px 20px;
    background: @bg-light;
    border-bottom: 1px solid @border-light;
    border-radius: 12px 12px 0 0;

    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: @text-primary;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: @primary-color;
        font-size: 18px;
      }
    }

    .card-subtitle {
      margin: 4px 0 0 0;
      font-size: 13px;
      color: @text-secondary;
      font-weight: 400;
    }
  }

  .card-body {
    padding: 20px;
  }

  .card-footer {
    padding: 16px 20px;
    background: @table-bg;
    border-top: 1px solid @border-light;
    border-radius: 0 0 12px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 统一的按钮样式
.unified-buttons {
  .btn-primary {
    background: @primary-color;
    color: white;
    border: 1px solid @primary-color;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background: @primary-hover;
      border-color: @primary-hover;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px @primary-shadow;
    }

    &:disabled {
      background: @disabled-bg;
      border-color: @disabled-bg;
      color: @text-disabled;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  .btn-secondary {
    background: white;
    color: @text-primary;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
      transform: translateY(-1px);
    }
  }

  .btn-danger {
    background: @error-color;
    color: white;
    border: 1px solid @error-color;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background: #f78989;
      border-color: #f78989;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-section {
    margin-bottom: 16px;
    border-radius: 8px;

    .section-header {
      padding: 12px 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .header-left {
        .section-icon {
          width: 32px;
          height: 32px;

          i {
            font-size: 16px;
          }
        }

        .section-title {
          h4 {
            font-size: 13px;
          }

          .section-subtitle {
            font-size: 12px;
          }
        }
      }

      .header-actions {
        .add-btn {
          height: 28px;
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }

    .section-content {
      padding: 16px;
    }

    .table-wrapper {
      .modern-table {
        font-size: 13px;

        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              font-size: 12px;
              padding: 8px 0;

              .cell {
                padding: 0 12px;
              }
            }
          }
        }

        /deep/ .el-table__body-wrapper {
          .el-table__body {
            tr {
              td {
                padding: 8px 0;

                .cell {
                  padding: 0 12px;
                }

                .el-input__inner {
                  height: 28px;
                  line-height: 28px;
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }
  }
} 