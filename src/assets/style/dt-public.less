body,
div,
span,
header,
footer,
nav,
section,
aside,
article,
ul,
dl,
dt,
dd,
li,
p,
h1,
h2,
h3,
h4,
h5,
h6,
b,
textarea,
button,
input,
select,
figure,
figcaption {
    padding: 0;
    margin: 0;
    list-style: none;
    font-style: normal;
    text-decoration: none;
    box-sizing: border-box;
    border: none;
    font-family: Microsoft Yahei, Arial, 'Hiragino Sans GB', Helvetica Neue, Helvetica, sans-serif !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased;

    &:focus {
        outline: none;
    }
}

html,
body {
    background-color: #f2f2f2;
}

a {
    padding: 0;
    margin: 0;
    list-style: none;
    font-style: normal;
    text-decoration: none;
    box-sizing: border-box;
    border: none;

    &:focus {
        outline: none;
    }
}
/************************************************************  dt- public css ****************************************************************************/
.iconfont:before {
    content: "\e6ec";
}

// 输入框宽度
.dt-input-width {
    width: 310px !important;
    &.age {
        width: 145px !important;
        .el-input {
            width: 100% !important;
        }
    }
}
.dt-input-small {
    width: 100px !important;
    .el-input {
        width: 100% !important;
    }
}
.dt-input-big {
    width: 433px !important;
}
.dt-input-large {
    width: 900px !important;
}
.dt-input-medium {
    width: 200px !important;
}

.dt-input-width-max {
    //width: 300px !important;
    width: 400px !important;
}

// textarea 宽度
.dt-textarea-width {
    width: 426px !important;
}

// 内容区菜单
.dt-el-tabs {
    margin-left: -15px;
    margin-right: -15px;
}

.dt-fz14 {
    font-size: 14px;
}

.dt-fz15 {
    font-size: 15px;
}

.dt-fz16 {
    font-size: 16px;
}

.dt-fz18 {
    font-size: 18px;
}

.dt-fz22 {
    font-size: 22px;
}

.dt-fz30 {
    font-size: 30px;
}

//加粗
.dt-bl {
    font-weight: 600;
}

//文本
.text-c {
    text-align: center;
}

//字体颜色
.c-999 {
    color: #999;
}

.c-666 {
    color: #666;
}

.c-333 {
    color: #333;
}

.c-ccc {
    color: #ccc;
}
.c-fff {
    color: #fff;
}

//间距
.mr-5 {
    margin-right: 5px;
}

.ml-5 {
    margin-left: 5px;
}

.ml-10 {
    margin-left: 10px;
}

.ml-20 {
    margin-left: 20px;
}

.mr-10 {
    margin-right:10px;
}

.mr-20 {
    margin-right: 20px;
}

.mb-10 {
    margin-bottom: 10px;
}

.ml-10 {
    margin-left: 10px;
}

.ml-20 {
    margin-left:20px;
}

.mt-10 {
    margin-top:10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.ptb-20 {
    padding-top: 20px;
    padding-bottom: 20px;
}
.ptl-20 {
    padding-top: 20px;
    padding-left: 20px;
}

.plr-20{
    padding-left: 20px;
    padding-right: 20px;
}
.pt-10 {
    padding-top: 10px;
}
.ptb-10{
    padding-top:10px;
    padding-bottom:10px;
}
.pt-20 {
    padding-top: 20px;
}
.pd-20 {
    padding: 20px;
}

.plr-10 {
    padding: 0 10px;
}

.plr-20 {
    padding:0 20px;
}

.pd-10 {
    padding: 10px;
}

.pdl-20,.pl-20 {
    padding-left: 20px;
}

//布局
.df {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}

.df-ac {
    display: flex;
    align-items: center;
    
}
.df-sb {
    display: flex;
    justify-content: space-between;
}
.df-cc {
    display: flex;
    align-items: center;
    justify-content: center;
}

.dt-table-row{
    background-color: #FFF;
    padding: 10px 0;
    font-size:14px;
    &:hover{
        background-color: #F5F7FA;
    }
    position:relative;
    &:after{
        content:"";
        position:absolute;
        width:100%;
        height:1px;
        left:0;
        bottom:0;
        background:#ccc;
    }
}
.dt-btn{
    cursor: pointer;
}
.dt-inline{
    display:inline-block;
}
.ellipsis1{
    overflow:hidden;
    display: -webkit-box;
    -webkit-line-clamp:1;
    -webkit-box-orient: vertical; 
    word-wrap: break-word;
    word-break: break-all; 
    white-space: normal!important;
}

div.bg-f5.list {
    background-color: #f5f5f5;
}

.bg-title {
    .title-icon {
        width: 10px;
        height: 10px;
        margin-right: 6px;
        display: inline-block;
        transform: rotate(45deg);
    }
    background-color: rgb(245, 245, 245);
    padding: 10px 0 10px 20px;
    color: #333;
    font-size: 14px;
    margin-bottom: 20px;
}


.el-button.hover-none {
    &.is-plain {
        cursor: text;
        margin: 0 10px 20px 0;
        &:hover {
            background-color: #fff;
            border-color: #DCDFE6;
            color:#606266;
        }
    }
}

.tip-container.el-popover {
    background-color: rgba(0,0,0,.8);
    color: #fff;
    .tip-text {
        margin-bottom: 10px;
        &:last-child {
            margin-bottom: 0px;
        }
    }
    .popper__arrow {
        &::after {
            // background-color:  rgba(0,0,0,.8);
            border-top-color:rgba(0,0,0,.8) !important;
            border-bottom-color:rgba(0,0,0,.8) !important;
        }
    }
}

.el-icon-arrow-right.up {
    &:hover {
        transform: rotate(-90deg);
    }
}
// .el-select-dropdown{
//     min-width:372px !important;
// }

.dt-table .el-table__fixed::before {
    height: 0;
}
.dt-look-more{
    width:100%;
    height:45px;
    line-height: 45px;
    text-align: center;
    background:#F9F9F9;
    color:#999999;
    font-size:12px;
    cursor: pointer;
    margin-top:20px;
    &>i{
        margin-left:6px;
    }
    &:active{opacity:.8}
}
//自定义tabs样式
.tabs-crad-custom {
    background: #F9F9F9;
    padding: 0 20px;

    .el-tabs__nav-wrap.is-scrollable {
        padding: 0 30px;
    }

    .el-tabs__header {
        margin: 0px;
        padding-top: 15px;
    }

    .el-tabs__content {
        background: #fff;
    }

    .el-tabs__nav-prev {
        left: 8px;
    }

    .el-tabs__nav-next {
        right: 8px;
    }

    .el-tabs__nav-wrap::after {
        height: 0px;
    }

    .el-tabs__item {
        background: #ECECEC;
        top: 3px;
        margin: 0 10px;
        border-radius: 6px 6px 0 0;
        line-height: 43px;
        box-sizing: border-box;
        overflow: hidden;
        box-shadow: 8px -8px 10px rgba(246, 246, 246, 1);

        &:nth-child(2) {
            padding-left: 20px !important;
            margin-left: 0px;
        }

        &:last-child {
            padding-right: 20px !important;
            margin-right: 0px;
        }

        &.is-active {
            background: #fff;
        }

        &.is-active::after {
            display: inline-block;
            content: "——————";
            position: absolute;
            top: -24px;
            left: 0px;
            width: 100%;
            height: 6px;
            font-size: 50px;
        }
    }

    .el-tabs__active-bar {
        display: none;
    }
}
.noData{
    text-align: center;
    color:#999;
    padding:15px 0;
    font-size:14px;
}
.dt-emoji-img{
    width:22px;
    height:22px;
    vertical-align: middle;
    margin:0 1px;
}

.foot-btn{
    margin:20px 0 0 20px;
}
.form-popup{
    text-align: center;
    .popup-contant{
        max-height:70vh;
        overflow:auto
    }
    .el-form-item__label{
        width:120px;
        text-align: left;
    }
    .el-form-item__content{
        text-align: left;
    }
}
