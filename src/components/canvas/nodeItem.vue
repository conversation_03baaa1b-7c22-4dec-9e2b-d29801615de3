<template>
  <div
    ref="node"
    :class="[
      'node-item',
      { 
        'active': isActive || isSelected,
        'mouse-enter': mouseEnter,
        'node-selected': isSelected
      }
    ]"
    :style="{ top: node.positionTop, left: node.positionLeft }"
    @click="setActive"
    @mousedown="onMouseDown"
    @mouseenter="showAnchor"
    @mouseleave="hideAnchor"
    @contextmenu.prevent="onContextmenu"
  >
    <!-- 节点主体内容 - 中心拖拽区域 -->
    <div 
      class="node-content"
    >
      <!-- 图标区域 -->
      <div class="icon-section">
        <div class="icon-wrapper" :class="getNodeTypeClass()">
          <i :class="node.logImg" class="node-icon" />
          <div class="icon-glow"></div>
        </div>
      </div>
      
      <!-- 文本区域 -->
      <div class="text-section">
        <div class="node-name">{{ node.nodeName }}</div>
      </div>
    </div>

    <!-- 悬停信息提示 -->
    <div v-show="mouseEnter" class="node-tooltip" @click.stop @mousedown.stop>
      <div class="tooltip-content">
        <div class="tooltip-title">{{ node.nodeName }}</div>
        <div class="tooltip-info">ID: {{ node.nodeId }}</div>
        <div class="tooltip-type">类型: {{ getNodeTypeText() }}</div>
      </div>
      <div class="tooltip-arrow"></div>
    </div>

    <!-- 连接锚点 - 边缘拖拽区域 -->
    <div class="connection-anchors" :class="{ 'anchors-visible': mouseEnter }">
      <div 
        class="anchor-point anchor-top" 
        data-position="top"
      >
        <div class="anchor-dot"></div>
        <div class="anchor-ring"></div>
      </div>
      <div 
        class="anchor-point anchor-right" 
        data-position="right"
      >
        <div class="anchor-dot"></div>
        <div class="anchor-ring"></div>
      </div>
      <div 
        class="anchor-point anchor-bottom" 
        data-position="bottom"
      >
        <div class="anchor-dot"></div>
        <div class="anchor-ring"></div>
      </div>
      <div 
        class="anchor-point anchor-left" 
        data-position="left"
      >
        <div class="anchor-dot"></div>
        <div class="anchor-ring"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NodeItem",
  props: {
    node: Object,
    activeNodeId: {
      type: String,
      default: null
    }
  },
  watch: {
    node(newValue, oldValue) {
      // 监听节点变化
    }
  },
  data() {
    return {
      mouseEnter: false,
      isSelected: false,
      startX: 0,
      startY: 0,
      mouseDownTime: 0,
      hasMoved: false
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 根据activeNodeId计算是否激活
    isActive() {
      return this.activeNodeId === this.node.nodeId;
    }
  },
  methods: {
    getNodeTypeText() {
      // 根据节点类型返回对应的文本描述
      const typeMap = {
        '0': '数据源',
        '1': '分组聚合',
        '2': '数据连接', 
        '3': '条件函数',
        '4': '数据过滤',
        '5': '输出'
      };
      return typeMap[this.node.type] || '处理组件';
    },
    getNodeTypeClass() {
      // 根据节点类型返回对应的样式类名，与组件面板保持一致
      const typeClassMap = {
        '0': 'source-item',     // 数据源 - 默认金色
        '1': 'group-item',      // 分组聚合 - 绿色
        '2': 'connect-item',    // 数据连接 - 蓝色
        '3': 'function-item',   // 条件函数 - 橙色
        '4': 'filter-item',     // 数据过滤 - 红色
        '5': 'output-item'      // 输出 - 默认金色
      };
      return typeClassMap[this.node.type] || 'default-item';
    },
    showAnchor() {
      this.mouseEnter = true;
    },
    hideAnchor() {
      this.mouseEnter = false;
    },
    onMouseDown(event) {
      // 记录鼠标按下的时间和位置
      this.mouseDownTime = Date.now();
      this.startX = event.clientX;
      this.startY = event.clientY;
      this.hasMoved = false;
      
      // 添加鼠标移动和松开事件监听
      const handleMouseMove = (e) => {
        const deltaX = Math.abs(e.clientX - this.startX);
        const deltaY = Math.abs(e.clientY - this.startY);
        
        // 如果鼠标移动超过5像素，认为是拖拽
        if (deltaX > 5 || deltaY > 5) {
          this.hasMoved = true;
        }
      };
      
      const handleMouseUp = () => {
        // 移除事件监听
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        
        // 延迟重置状态，确保点击事件能正确判断
        setTimeout(() => {
          this.hasMoved = false;
          this.mouseDownTime = 0;
        }, 50);
      };
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    setActive(event) {
      // 检查是否刚刚进行了拖拽操作
      const timeSinceMouseDown = Date.now() - this.mouseDownTime;
      if (this.hasMoved || timeSinceMouseDown > 200) {
        // 如果鼠标移动过或者按下时间过长，认为是拖拽操作，不触发点击
        return false;
      }
      
      // 检查父组件的拖拽状态，如果正在拖拽则不触发点击
      if (this.$parent.isDragging) {
        return false;
      }
      
      if (window.event && window.event.ctrlKey) {
        this.isSelected = !this.isSelected;
        return false;
      }
      
      this.isSelected = false;
      
      // 触发节点点击事件，让父组件来管理active状态
      this.$emit("nodeClick", this.node);
      
      setTimeout(() => {
        this.$emit("changeLineState", this.node, true);
      }, 0);
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
    },
    deleteNode() {
      this.$emit("deleteNode", this.node);
    }
  }
};
</script>

<style lang="less" scoped>
// 现代化节点样式设计
.node-item {
  position: absolute;
  width: 140px; // 增加宽度以容纳更多文本
  min-height: 36px; // 允许高度自适应内容
  height: auto;
  cursor: move;
  z-index: 100;
  
  
  &:hover {
    z-index: 200;
    
    .node-content {
      box-shadow: 
        0 8px 25px rgba(128, 128, 128, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  // 鼠标进入状态
  &.mouse-enter {
    .node-content {
      border-color: rgba(128, 128, 128, 0.3);
      
      .icon-wrapper {
        transform: scale(1.1); // 轻微放大图标容器
        
        .icon-glow {
          opacity: 0.8;
          transform: translate(-50%, -50%) scale(1.2);
        }
        
        .node-icon {
          transform: scale(1.1); // 图标本身也轻微放大
        }
        
        // 不同类型的悬停发光效果
        &.source-item,
        &.output-item,
        &.default-item {
          .icon-glow {
            background: linear-gradient(135deg, rgba(215, 162, 86, 0.3) 0%, rgba(215, 162, 86, 0.1) 100%);
          }
        }
        
        &.group-item {
          .icon-glow {
            background: linear-gradient(135deg, rgba(103, 194, 58, 0.3) 0%, rgba(103, 194, 58, 0.1) 100%);
          }
        }
        
        &.connect-item {
          .icon-glow {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.3) 0%, rgba(64, 158, 255, 0.1) 100%);
          }
        }
        
        &.function-item {
          .icon-glow {
            background: linear-gradient(135deg, rgba(230, 162, 60, 0.3) 0%, rgba(230, 162, 60, 0.1) 100%);
          }
        }
        
        &.filter-item {
          .icon-glow {
            background: linear-gradient(135deg, rgba(245, 108, 108, 0.3) 0%, rgba(245, 108, 108, 0.1) 100%);
          }
        }
      }
    }
  }
  
  // 激活状态
  &.active {
    .node-content {
      border-color: #D7A256;
    }
  }
}

// 节点主体内容
.node-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 249, 250, 0.95) 100%);
  border: 1px solid rgba(220, 223, 230, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  // 内容发光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      rgba(215, 162, 86, 0.03) 0%, 
      transparent 50%, 
      rgba(215, 162, 86, 0.03) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  // 顶部高光
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.8) 50%, 
      transparent 100%);
  }
}

// 图标区域 - 紧凑设计
.icon-section {
  width: 32px; // 从44px减少到32px
  height: 100%;
  display: flex;
  align-items: right;
  justify-content: right;
  position: relative;
  flex-shrink: 0; // 防止图标区域被压缩
}

.icon-wrapper {
  position: relative;
  width: 24px; // 从32px减少到24px
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px; // 相应减少圆角
  transition: all 0.3s ease;
  // 默认金色背景
  background: rgba(215, 162, 86, 0.1);
  border: 1px solid rgba(215, 162, 86, 0.2);
  
  .node-icon {
    font-size: 16px; // 从16px减少到12px
    color: #D7A256;
    z-index: 2;
    transition: all 0.3s ease;
    font-weight: 500;
  }
  
  .icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 16px; // 从24px减少到16px
    height: 16px;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
  }
  
  // 不同类型的节点图标样式 - 与组件面板保持一致
  &.source-item,
  &.output-item,
  &.default-item {
    background: rgba(215, 162, 86, 0.1);
    border-color: rgba(215, 162, 86, 0.2);
    
    .node-icon {
      color: #D7A256;
    }
  }
  
  &.group-item {
    background: rgba(103, 194, 58, 0.1);
    border-color: rgba(103, 194, 58, 0.2);
    
    .node-icon {
      color: #67c23a;
    }
  }
  
  &.connect-item {
    background: rgba(64, 158, 255, 0.1);
    border-color: rgba(64, 158, 255, 0.2);
    
    .node-icon {
      color: #409eff;
    }
  }
  
  &.function-item {
    background: rgba(230, 162, 60, 0.1);
    border-color: rgba(230, 162, 60, 0.2);
    
    .node-icon {
      color: #e6a23c;
    }
  }
  
  &.filter-item {
    background: rgba(245, 108, 108, 0.1);
    border-color: rgba(245, 108, 108, 0.2);
    
    .node-icon {
      color: #f56c6c;
    }
  }
}

// 文本区域 - 扩大显示空间
.text-section {
  flex: 1;
  padding: 8px 8px; // 左侧内边距从0改为8px，增加与图标的间距
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
  min-height: 32px; // 确保最小高度
  position: relative; // 为移动指示器定位
}

.node-name {
  font-size: 12px; // 从13px增加到14px，充分利用空间
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3; // 稍微增加行高，提高可读性
  margin-bottom: 3px; // 增加与类型文本的间距
  // 允许文本换行显示完整内容
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  max-height: 2.6em; // 相应调整最大高度
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.node-type {
  font-size: 11px;
  color: #7f8c8d;
  font-weight: 400;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 2px; // 与节点名称保持适当间距
  flex-shrink: 0; // 防止类型文本被压缩
}

// 状态指示器
.status-indicator {
  width: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 移动拖拽指示器
.move-indicator {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(128, 128, 128, 0.1);
  border: 1px solid rgba(128, 128, 128, 0.3);
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s ease;
  margin-right: 2px;
  
  i {
    font-size: 10px;
    color: #D7A256;
    opacity: 0.8;
  }
  
  &:hover {
    background: rgba(128, 128, 128, 0.2);
    border-color: rgba(128, 128, 128, 0.5);
    transform: translateY(-50%) scale(1.1);
    
    i {
      opacity: 1;
    }
  }
}

// 悬停提示
.node-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 12px;
  z-index: 1000;
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
}

.tooltip-info,
.tooltip-type {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 工具提示使用说明
.tooltip-usage {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  
  .usage-item {
    display: flex;
    align-items: center;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 3px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    i {
      font-size: 9px;
      margin-right: 6px;
      color: #D7A256;
    }
  }
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.85);
}

// 连接锚点
.connection-anchors {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000; // 提高z-index确保在最上层
  
  // 悬停时显示锚点
  &.anchors-visible {
    .anchor-point {
      opacity: 1 !important;
    }
  }
}

.anchor-point {
  position: absolute;
  pointer-events: all;
  cursor: crosshair;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500; // 提高z-index确保在最上层
  // 添加用户选择禁用，防止意外选中
  user-select: none;
  -webkit-user-select: none;
  width: 20px;
  height: 20px;

  // 默认透明度
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.anchor-top {
    top: -8px; 
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.anchor-right {
    top: 50%;
    right: -8px;
    transform: translateY(-50%);
  }
  
  &.anchor-bottom {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.anchor-left {
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
  }
  
  &:hover {
    transform-origin: center;
    
    &.anchor-top {
      transform: translateX(-50%) scale(1.2);
    }
    
    &.anchor-right {
      transform: translateY(-50%) scale(1.2);
    }
    
    &.anchor-bottom {
      transform: translateX(-50%) scale(1.2);
    }
    
    &.anchor-left {
      transform: translateY(-50%) scale(1.2);
    }
    
    .anchor-dot {
      background: #D7A256;
      transform: scale(1.3);
      box-shadow: 0 0 12px rgba(215, 162, 86, 0.3);
    }
    
    .anchor-ring {
      opacity: 1;
      transform: scale(1.5);
      border-color: rgba(215, 162, 86, 0.4);
    }
  }
}

.anchor-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f0f2f5 100%);
  border: 2px solid #D7A256;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  box-shadow: 0 0 4px rgba(215, 162, 86, 0.2);
}

.anchor-ring {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(215, 162, 86, 0.2);
  border-radius: 50%;
  opacity: 0;
  transform: scale(1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}



// 中心区域拖拽时的样式
.node-content {
  &:active {
    cursor: grabbing;
  }
}

// 拖拽状态样式
.node-item {
  &.dragging-center {
    .node-content {
      cursor: grabbing;
      opacity: 0.8;
      transform: scale(1.05);
    }
    
    .connection-anchors {
      opacity: 0.3;
    }
  }
  
  &.dragging-edge {
    .connection-anchors {
      .anchor-point {
        opacity: 1;
        
        .anchor-dot {
          background: #D7A256;
          box-shadow: 0 0 15px rgba(128, 128, 128, 0.3);
        }
      }
    }
  }
}

// 动画定义
@keyframes nodeEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes selectionPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(128, 128, 128, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(128, 128, 128, 0.1);
  }
}

@keyframes anchorPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .node-item {
    width: 140px; // 移动端也保持足够宽度
    min-height: 32px;
    
    .node-name {
      font-size: 11x;
      max-height: 2.2em; // 移动端稍微减少行高
    }
    
    .node-type {
      font-size: 10px;
    }
    
    .icon-section {
      width: 28px; // 移动端进一步减少图标区域
    }
    
    .icon-wrapper {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      
      .node-icon {
        font-size: 10px; // 移动端图标更小
      }
    }
    
    .text-section {
      padding: 6px 10px 6px 6px; // 移动端减少内边距，左侧保持与图标间距
    }
    
    .tooltip-content {
      font-size: 11px;
      padding: 10px 12px;
    }
  }
}
</style>
