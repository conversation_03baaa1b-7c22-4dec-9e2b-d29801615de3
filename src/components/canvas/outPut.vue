<template>
  <div class="output-container">
    <!-- 原始输出区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-document-copy"></i>
          </div>
          <div class="section-title">
            <h4>原始输出</h4>
            <span class="section-subtitle">配置原始字段输出规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd1()"
            class="add-btn"
          >
            新增原始输出
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList1"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无原始输出数据"
        >
          <el-table-column
            v-if="
              optionIndexList &&
                optionIndexList.length > 0 &&
                toolTypeStatus == '2'
            "
            align="center"
            width="300"
            prop="fieldDesc"
            label="节点"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldNodeId"
                clearable
                placeholder="请选择节点"
                filterable
                style="width:100%"
                size="small"
                @change="blur(scope.row.fieldNodeId, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in optionIndexList"
                  :key="index"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.fieldNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            v-if="toolTypeStatus == '2'"
            prop="fieldDesc"
            label="输出字段名"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldDesc"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in optionAndList"
                  :key="index"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            v-if="toolTypeStatus != '2'"
            prop="fieldDesc"
            label="输出字段名"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldDesc"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in optionList"
                  :key="index"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldName" label="输出字段">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                :disabled="true"
                placeholder="自动生成"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="isIndex" label="是否添加索引">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.isIndex"
                clearable
                placeholder="请选择"
                filterable
                size="small"
                @change="blur3(scope.row.isIndex, scope.$index)"
              >
                <el-option
                  v-for="option in optionList5"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow1(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 自定义输出区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon custom">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="section-title">
            <h4>自定义输出</h4>
            <span class="section-subtitle">配置自定义字段输出规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增自定义输出
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无自定义输出数据"
        >
          <el-table-column align="center" prop="fileName1" label="输出字段名">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldDesc"
                @change="blurSelf1(scope.row.fieldDesc, scope.$index)"
                placeholder="请输入字段名"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fileName2" label="输出字段">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                @change="blurSelf2(scope.row.fieldName, scope.$index)"
                placeholder="请输入字段"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="400px"
            prop="fileName3"
            label="输出公式"
          >
            <template slot-scope="scope">
              <el-tooltip
                effect="dark"
                :content="scope.row.formula || ''"
                placement="top"
              >
                <el-autocomplete
                  class="inline-input"
                  style="width:100%"
                  v-model="scope.row.formula"
                  :fetch-suggestions="querySearch"
                  placeholder="请输入内容"
                  @change="blurSelf3(scope.row.formula, scope.$index)"
                  @select="blurSelf3(scope.row.formula, scope.$index)"
                ></el-autocomplete>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="isIndex" label="是否添加索引">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.isIndex"
                clearable
                placeholder="请选择"
                filterable
                size="small"
                @change="blurSelf4(scope.row.isIndex, scope.$index)"
              >
                <el-option
                  v-for="option in optionList5"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "outPut",
  props: {
    idKey: String,
    idName: String,
    dataItem: Object,
    outPutList: Array,
    toolType: String,
    childList: Array
  },
  watch: {
    dataItem(newValue, oldValue) {
      this.nodeObjList = newValue;
    },
    idName(newVal, oldVal) {
      this.editForm.name = newVal;
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        this.nodeObjList = newVal;
        // this.optionList = this.outPutList;
        // let that = this;
        // if (this.nodeObjList && this.toolType == "0") {
        //   setTimeout(function() {
        //     that.changeName(); // 3 秒后，打印 1
        //   }, 1000);
        // }
        //  else {
        //   setTimeout(function() {
        //     that.childrenName();
        //   }, 2000);
        // }
      }
    },
    childList(newValue, oldValue) {
      // console.log(newValue, oldValue);
      this.optionIndexList = newValue;
    },
    outPutList(newValue, oldValue) {
      this.optionList = newValue;
      // console.log(this.optionList)
    },
    toolType(newVal, oldVal) {
      this.toolTypeStatus = newVal;
    }
  },
  data() {
    return {
      fileNameList: [
        // {
        //   fieldDesc: "",
        //   fieldName: "",
        //   isIndex: "0",
        //   formula: ""
        // }
      ],
      fileNameList1: [
        // {
        //   fieldDesc: "",
        //   fieldName: "",
        //   isIndex: "0"
        // }
      ],
      showAdd: false,
      addForm: {
        name: "",
        value: ""
      },
      optionIndexList: [],
      optionAndList: [], //关联项optionList
      optionList: [],
      optionList2: [
        {
          name: "保单号",
          id: "0"
        },
        {
          name: "产品ID",
          id: "1"
        },
        {
          name: "产品名称",
          id: "2"
        },
        {
          name: "佣金",
          id: "3"
        },
        {
          name: "保费",
          id: "4"
        }
      ],
      optionList3: [
        {
          name: "保单号",
          id: "0"
        },
        {
          name: "产品ID",
          id: "1"
        },
        {
          name: "产品名称",
          id: "2"
        },
        {
          name: "佣金",
          id: "3"
        },
        {
          name: "保费",
          id: "4"
        }
      ],
      optionList5: [
        {
          name: "否",
          id: "0"
        },
        {
          name: "是",
          id: "1"
        }
      ],
      editForm: {
        channel_name: "",
        channel_name1: ""
      },
      toolTypeStatus: "0"
    };
  },
  created() {
    this.nodeObjList = this.dataItem;
    this.optionIndexList = this.childList;
    console.log(this.childList, this.toolType);
    if (this.nodeObjList.output.fields.default) {
      this.fileNameList1 = this.nodeObjList.output.fields.default;
    }
    if (this.nodeObjList.output.fields.custom) {
      this.fileNameList = this.nodeObjList.output.fields.custom;
    }
    this.optionList = this.outPutList;
    console.log(this.optionList);
    this.toolTypeStatus = this.toolType;
    console.log("toolTypeStatus", this.toolTypeStatus);
  },
  methods: {
    changeName() {
      if (this.nodeObjList.output.fields.default) {
        let arr = Array.from(this.nodeObjList.output.fields.default);
        console.log(arr);
        let data = [];
        for (let i = 0; i < arr.length; i++) {
          data.push({
            fieldDesc: arr[i].fieldDesc,
            fieldName: arr[i].fieldName,
            isIndex: arr[i].isIndex ? arr[i].isIndex : "0",
            fieldDesc1: arr[i].fieldDesc,
            fieldNodeId: this.nodeObjList.nodeId
          });
        }
      }
    },
    //子节点同步更新
    childrenName() {
      console.log(this.nodeObjList.children, this.nodeObjList.children.length);
      if (this.nodeObjList.children && this.nodeObjList.children.length > 0) {
        let arr = this.nodeObjList.children;
        let data = [];
        let data1 = [];
        let data2 = [];
        // 默认字段拼接下拉option
        for (let i = 0; i < arr.length; i++) {
          if (
            arr[i].output.fields.default &&
            arr[i].output.fields.default.length
          ) {
            for (let k = 0; k < arr[i].output.fields.default.length; k++) {
              // 循环展示字段
              data.push({
                fieldDesc:
                  arr[i].nodeName +
                  "-" +
                  arr[i].output.fields.default[k].fieldDesc,
                fieldName: arr[i].output.fields.default[k].fieldName,
                isIndex: arr[i].output.fields.default[k].isIndex
                  ? arr[i].output.fields.default[k].isIndex
                  : "0",
                formula: arr[i].output.fields.default[k].fieldDesc
              });
              // 下拉option
              data2.push({
                fieldDesc:
                  arr[i].nodeName +
                  "-" +
                  arr[i].output.fields.default[k].fieldDesc,
                fieldNodeId: arr[i].nodeId,
                fieldName: arr[i].output.fields.default[k].fieldName
              });
            }
          }

          if (
            arr[i].output.fields.custom &&
            arr[i].output.fields.custom.length
          ) {
            for (let j = 0; j < arr[i].output.fields.custom.length; j++) {
              data1.push({
                fieldDesc: arr[i].output.fields.custom[j].fieldDesc,
                fieldName: arr[i].output.fields.custom[j].fieldName,
                isIndex: arr[i].output.fields.custom[j].isIndex
                  ? arr[i].output.fields.custom[j].isIndex
                  : "0"
              });
            }
          }
        }
        this.fileNameList1 = data;
        this.optionList = data2.concat(data1); //自定义字段包含前面字段输出
        console.log(this.optionList);
        // this.fileNameList = data1;
      }
    },
    querySearch(queryString, cb) {
      console.log(this.optionList);
      var restaurants = this.optionList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      console.log(results);
      let types = [];
      for (let item of results) {
        types.push({
          value: item.fieldName
        });
      }
      cb(types);
    },
    createFilter(queryString) {
      console.log(queryString);
      return restaurant => {
        // console.log(queryString.toLowerCase().indexOf('$'));
        // if(queryString.toLowerCase().indexOf('$')==0){
        //   return (-1)
        // }
        return (
          restaurant.fieldName
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    showAnchor() {
      this.mouseEnter = true;
    },
    hideAnchor() {
      this.mouseEnter = false;
    },
    // 设置disabled
    getDisabledList(val) {
      for (let i = 0; i < this.optionList.length; i++) {
        this.optionList[i].disabled = true;
      }
    },
    //原始字段编辑存储
    blur(val, index) {
      console.log(val, this.optionList);
      let data = [];
      for (let i = 0; i < this.optionList.length; i++) {
        if (this.optionList[i].fieldNodeId == val) {
          data.push({
            fieldName: this.optionList[i].fieldName,
            fieldDesc: this.optionList[i].fieldDesc,
            fieldNodeId: this.optionList[i].fieldNodeId
          });
        }
      }
      this.optionAndList = data;
      this.fileNameList1[index].fieldDesc = "";
    },
    //原始字段编辑存储
    blur1(val, index) {
      let fieldName = "";
      let fieldNodeId = "";
      let fieldDesc = "";
      let fieldDesc1 = "";
      for (let i = 0; i < this.optionList.length; i++) {
        if (this.optionList[i].fieldName == val) {
          fieldName = this.optionList[i].fieldName;
          fieldDesc = this.optionList[i].fieldDesc;
          fieldDesc1 = this.optionList[i].fieldDesc1
            ? this.optionList[i].fieldDesc1
            : this.optionList[i].fieldDesc;
          fieldNodeId = this.optionList[i].fieldNodeId
            ? this.optionList[i].fieldNodeId
            : "";
        }
      }
      //基本信息输出
      if (this.toolTypeStatus == "0") {
        // console.log('111')
        this.fileNameList1[index].fieldName = val;
        this.fileNameList1[index].fieldDesc = fieldDesc;
        let data = {
          name: "fieldName",
          key: this.idKey,
          value: this.fileNameList1[index],
          index: index,
          type: 0 //原始字段输出
        };
        this.$emit("outPutChange", data);
      } else {
        this.fileNameList1[index].fieldName = val;
        this.fileNameList1[index].fieldDesc = fieldDesc1;
        // this.fileNameList1[index].fieldDesc1 = fieldDesc1;
        this.fileNameList1[index].fieldNodeId = fieldNodeId;
        let data = {
          name: "fieldNodeId",
          key: this.idKey,
          value: this.fileNameList1[index],
          index: index,
          type: 0 //原始字段输出
        };
        console.log(data);
        this.$emit("outPutChange", data);
      }
    },
    blur3(val, index) {
      let data = {
        name: "isIndex",
        key: this.idKey,
        value: val,
        index: index,
        type: 0 //原始字段输出
      };
      this.$emit("outPutChange", data);
    },
    // 自定义字段编辑存储
    blurSelf1(val, index) {
      console.log(val, index);
      let data = {
        name: "fieldDesc",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf2(val, index) {
      console.log(val, index);
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf3(val, index) {
      console.log(val, index);
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf4(val, index) {
      let data = {
        name: "isIndex",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    handleAdd1() {
      this.fileNameList1.push({
        fieldDesc: "",
        fieldName: "",
        isIndex: "0"
      });
      let data = {
        value: this.fileNameList1,
        type: 0,
        key: this.idKey
      };
      console.log(data);
      this.$emit("outPutAdd", data);
    },
    handleAdd() {
      this.fileNameList.push({
        fieldDesc: "",
        fieldName: "",
        isIndex: "0",
        formula: ""
      });
      let data = {
        value: this.fileNameList,
        type: 1,
        key: this.idKey
      };
      this.$emit("outPutAdd", data);
    },
    // 删除行
    deleteRow(index) {
      console.log("index", index);
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index === i) {
          this.fileNameList.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 1,
            key: this.idKey,
            value: this.fileNameList
          };
          this.$emit("outPutDel", data);
        }
      }
      console.log(this.fileNameList);
      // });
    },
    // 删除行
    deleteRow1(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList1.length; i++) {
        if (index === i) {
          this.fileNameList1.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 0,
            key: this.idKey,
            value: this.fileNameList1
          };

          this.$emit("outPutDel", data);
        }
      }
      // });
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    // 新增切片
    async toAddForm() {
      const data = {
        ...this.addForm
      };
      //   await api.bsChannelAdd(data);
      this.showAdd = false;
      //   this.$refs["addUserForm"].resetFields();
    },
    //关闭新增弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
      this.$nextTick(() => {
        this.$refs.addUserForm.clearValidate();
      });
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
.output-container {
  .table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f7ecdd;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.custom {
            i {
              color: #D7A256;
            }
          }

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }

      .header-actions {
        .add-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          background: #D7A256;
          color: white;
          border: none;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
            background: #E6B366;
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .table-wrapper {
      .modern-table {
        border-radius: 0;
        overflow: hidden;
        border: none;
        font-size: 14px;

        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #fbf6ee;
              font-weight: 600;
              font-size: 13px;
              color: #2c3e50;
              border-bottom: 2px solid #f7ecdd;
              padding: 12px 0;

              .cell {
                padding: 0 16px;
              }
            }
          }
        }

        /deep/ .el-table__body-wrapper {
          .el-table__body {
            tr {
              transition: all 0.2s ease;

              &:hover {
                background: #f0f9ff !important;
              }

              td {
                padding: 12px 0;
                border-bottom: 1px solid #f5f7fa;

                .cell {
                  padding: 0 16px;
                }

                .el-select {
                  .el-input__inner {
                    border-radius: 6px;
                    border-color: #dcdfe6;
                    height: 32px;
                    line-height: 32px;
                    transition: all 0.3s ease;

                    &:focus {
                      border-color: #D7A256;
                      box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                    }

                    &:hover {
                      border-color: #c0c4cc;
                    }
                  }
                }

                .el-input {
                  .el-input__inner {
                    border-radius: 6px;
                    border-color: #dcdfe6;
                    height: 32px;
                    line-height: 32px;
                    transition: all 0.3s ease;

                    &:focus {
                      border-color: #D7A256;
                      box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                    }

                    &:hover {
                      border-color: #c0c4cc;
                    }

                    &:disabled {
                      background: #f5f7fa;
                      color: #909399;
                    }
                  }
                }

                .delete-btn {
                  color: #f56c6c;
                  font-size: 14px;
                  padding: 4px 8px;
                  border-radius: 4px;
                  transition: all 0.3s ease;
                  background: transparent;
                  border: none;

                  &:hover {
                    background: #fef0f0;
                    color: #f56c6c;
                    transform: scale(1.05);
                  }
                }
              }
            }
          }
        }

        /deep/ .el-table__empty-block {
          background: #fafbfc;
          
          .el-table__empty-text {
            color: #909399;
            font-size: 14px;
            padding: 40px 0;
          }
        }
      }
    }
  }
}
</style>
