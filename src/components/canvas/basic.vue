<template>
  <div class="basic-config-container">
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置节点基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
          label-width="80px"
          label-position="right"
          :model="editForm"
          class="editUserForm"
          ref="editUserForm"
          :inline="false"
        >
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="editForm.name"
              auto-complete="off"
              @change="blur1"
              placeholder="请输入节点名称"
              class="modern-input"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="名称" prop="strName">
            <el-input
              style="width: 150px;"
              v-model="editForm.strName"
              auto-complete="off"
              @change="blur1"
              placeholder="请输入指标名称"
            ></el-input>
          </el-form-item> -->
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="editForm.remark"
              auto-complete="off"
              @change="blur2"
              placeholder="请输入备注信息"
              type="textarea"
              :rows="3"
              class="modern-textarea"
            ></el-input>
          </el-form-item>
        </el-form>
        <!-- <div>{{dataItem}}</div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "basic",
  props: {
    idKey: String,
    idName: String,
    dataItem: Object
  },
  watch: {
    dataItem(newValue, oldValue) {
      this.nodeObjList = newValue;
    },
    idName(newVal, oldVal) {
      this.editForm.name = newVal;
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        this.changeName();
      }
    }
  },
  data() {
    return {
      mouseEnter: false,
      isActive: false,
      isSelected: false,
      editForm: {
        strName: "",
        remark: "",
        name: ""
      },
      nodeObjList: []
    };
  },
  created() {
    this.editForm.name = this.idName;
    this.nodeObjList = this.$store.state.templateEngine.nodeItemList;
    if (this.nodeObjList.length > 0) {
      this.changeName();
    }
    // console.log(this.nodeObjList);
  },
  methods: {
    changeName() {
      for (let i = 0; i < this.nodeObjList.length; i++) {
        if (this.nodeObjList[i].nodeId == this.idKey) {
          this.editForm.remark = this.nodeObjList[i].remark;
          this.editForm.strName = this.nodeObjList[i].nodeName;
        }
      }
    },
    showAnchor() {
      this.mouseEnter = true;
    },
    hideAnchor() {
      this.mouseEnter = false;
    },
    blur1(val) {
      console.log(val)
      let data = {
        name: "strName",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    blur2(val) {
      console.log(val);
      let data = {
        name: "remark",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    setActive() {
      if (window.event.ctrlKey) {
        this.isSelected = !this.isSelected;
        return false;
      }
      this.isActive = true;
      this.isSelected = false;
      setTimeout(() => {
        this.$emit("changeLineState", this.node, true);
      }, 0);
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
.basic-config-container {
  .config-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f7ecdd;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }
    }

    .section-content {
      padding: 20px;

      .editUserForm {
        /deep/ .el-form-item {
          .el-form-item__label {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            line-height: 36px;
          }

          .el-form-item__content {
            .modern-input, .modern-textarea {
              .el-input__inner {
                border-radius: 8px;
                border-color: #dcdfe6;
                font-size: 14px;
                padding: 0 12px;
                height: 36px;
                line-height: 36px;
                transition: all 0.3s ease;

                &:focus {
                  border-color: #D7A256;
                  box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                }

                &:hover {
                  border-color: #c0c4cc;
                }
              }
            }

            .modern-textarea {
              .el-textarea__inner {
                border-radius: 8px;
                border-color: #dcdfe6;
                font-size: 14px;
                padding: 8px 12px;
                transition: all 0.3s ease;
                resize: vertical;

                &:focus {
                  border-color: #D7A256;
                  box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                }

                &:hover {
                  border-color: #c0c4cc;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
