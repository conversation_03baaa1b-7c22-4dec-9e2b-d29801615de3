<template>
  <div class="connection-config-container">
    <!-- 基本信息 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置节点基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
          label-width="80px"
          label-position="right"
          :model="editForm"
          class="editUserForm"
          ref="editUserForm"
          :inline="false"
        >
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="editForm.name"
              auto-complete="off"
              @change="blurName"
              placeholder="请输入节点名称"
              class="modern-input"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 默认数据连接 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon default">
            <i class="el-icon-link"></i>
          </div>
          <div class="section-title">
            <h4>默认数据连接</h4>
            <span class="section-subtitle">配置默认数据连接规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增默认连接
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          stripe
          v-hover
          class="modern-table"
          empty-text="暂无默认连接数据"
        >
          <el-table-column align="center" prop="fromNodeId" label="源节点">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fromNodeId"
                clearable
                placeholder="请选择源节点"
                filterable
                size="small"
                @change="blurGroup1(scope.row.fromNodeId, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.nodeId"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.fromNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fromFieldName" label="源字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fromFieldName"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurGroup2(scope.row.fromFieldName, scope.$index)"
                :disabled="scope.row.disabled1"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionListFiled[scope.$index].optionListFiled1"
                  :key="option.fieldDesc"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" width="80" prop="toFieldType" label="关系">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.toFieldType"
                placeholder="等于"
                size="small"
                disabled
                class="modern-input"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="toNodeId" label="目标节点">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.toNodeId"
                clearable
                placeholder="请选择目标节点"
                filterable
                size="small"
                @change="blurGroup3(scope.row.toNodeId, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.nodeId"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.toNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="toFieldName" label="目标字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.toFieldName"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurGroup4(scope.row.toFieldName, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionListFiled[scope.$index].optionListFiled2"
                  :key="option.fieldDesc"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="connectType" label="连接方式">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.connectType"
                clearable
                placeholder="请选择连接方式"
                filterable
                size="small"
                @change="blurGroup5(scope.row.connectType, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList3"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 自定义数据连接 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon custom">
            <i class="el-icon-edit"></i>
          </div>
          <div class="section-title">
            <h4>自定义数据连接</h4>
            <span class="section-subtitle">配置自定义数据连接规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd1()"
            class="add-btn"
          >
            新增自定义连接
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList1"
          stripe
          v-hover
          class="modern-table"
          empty-text="暂无自定义连接数据"
        >
          <el-table-column align="center" prop="fromNodeId" label="源节点">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fromNodeId"
                clearable
                placeholder="请选择源节点"
                filterable
                size="small"
                @change="blurcust1(scope.row.fromNodeId, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.nodeId"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.fromNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fromFieldName" label="源字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fromFieldName"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurcust2(scope.row.fromFieldName, scope.$index)"
                :disabled="scope.row.disabled1"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionListFiled[scope.$index].optionListFiled1"
                  :key="option.fieldDesc"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" width="80" prop="toFieldType" label="关系">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.toFieldType"
                placeholder="等于"
                size="small"
                disabled
                class="modern-input"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="toNodeId" label="目标节点">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.toNodeId"
                clearable
                placeholder="请选择目标节点"
                filterable
                size="small"
                @change="blurcust3(scope.row.toNodeId, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.nodeId"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.toNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="toFieldName" label="目标字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.toFieldName"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurcust4(scope.row.toFieldName, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionListFiled[scope.$index].optionListFiled2"
                  :key="option.fieldDesc"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="connectType" label="连接方式">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.connectType"
                clearable
                placeholder="请选择连接方式"
                filterable
                size="small"
                @change="blurcust5(scope.row.connectType, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList3"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="formula" label="自定义公式">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.formula"
                placeholder="请输入自定义公式"
                size="small"
                @change="blurcust6(scope.row.formula, scope.$index)"
                class="modern-input"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow1(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import autoInput from "./autoInput";
export default {
  name: "connection",
  components: {
    autoInput
  },
  props: {
    typeTitle: String,
    idKey: String,
    idName: String,
    dataItem: Object,
    connectOutPutList: Array,
    childList: Array,
    filedList: Array
  },
  watch: {
    filedList(newValue, oldValue) {
      // console.log(newValue, oldValue);
      // this.optionList = newValue;
    },
    dataItem(newValue, oldValue) {
      // console.log(newValue, oldValue);
      if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
        this.nodeObjList = newValue;
        setTimeout(function() {
          this.changeName(); // 3 秒后，打印 1
        }, 1000);
      }
    },
    childList(newValue, oldValue) {
      this.optionList = newValue;
    },
    idKey(newVal, oldVal) {
      // if (newVal != oldVal) {
      //   this.changeName();
      // }
    },
    idName(newVal, oldVal) {
      console.log(newVal);
      this.editForm.name = newVal;
    }
  },
  data() {
    return {
      editForm: {
        name: ""
      },
      fileNameList: [
        // {
        //   fromFieldName: "",
        //   fromNodeId: "",
        //   toFieldName: "",
        //   toNodeId: "",
        //   connectType: "",
        //   toFieldType: "等于",
        //   optionListFiled1: [],
        //   disabled1: true,
        //   optionListFiled2: [],
        //   disabled2: true
        // }
      ],
      visible: false,
      fileNameList1: [],
      optionListFiled: [],
      optionListFiled1: [],
      optionListFiled2: [],
      optionListFiledCust: [],
      optionListFiledCust1: [],
      optionListFiledCust2: [],
      fileNameListSame: [],
      showAdd: false,
      addForm: {
        name: "",
        value: ""
      },
      optionList: [],
      optionList2: [],
      optionList3: [
        {
          name: "左连接",
          id: "left join"
        },
        {
          name: "内连接",
          id: "join"
        }
      ],
      optionList4: [
        {
          name: "等于",
          id: "0"
        }
      ],
      lastChar: "",
      isNewInput: false,
      selectedPart: ""
    };
  },
  created() {
    this.editForm.name = this.idName;
    this.nodeObjList = this.dataItem;
    this.optionList = this.childList;
    let that = this;
    setTimeout(function() {
      that.changeName(); // 3 秒后，打印 1
    }, 1000);
  },
  methods: {
    blurName(val) {
      let data = {
        name: "strName",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    //回显初始化列表
    changeName() {
      // 设置输出字段默认数据连接
      // console.log('this.nodeObjList',this.nodeObjList)
      if (
        this.nodeObjList.nodeConfig.connectCondition &&
        this.nodeObjList.nodeConfig.connectCondition.length > 0
      ) {
        let arr = this.nodeObjList.nodeConfig.connectCondition;
        let data = [];
        let data1 = [];
        for (let i = 0; i < arr.length; i++) {
          let id1 = arr[i].fromNodeId ? this.getList(arr[i].fromNodeId) : [];
          let id2 = arr[i].toNodeId ? this.getList(arr[i].toNodeId) : [];
          data.push({
            fromFieldName: arr[i].fromFieldName ? arr[i].fromFieldName : "",
            fromNodeId: arr[i].fromNodeId ? arr[i].fromNodeId : "",
            toFieldName: arr[i].toFieldName ? arr[i].toFieldName : "",
            toNodeId: arr[i].toNodeId ? arr[i].toNodeId : "",
            connectType: arr[i].connectType,
            toFieldType: "等于"
          });
          data1.push({
            optionListFiled1: id1,
            optionListFiled2: id2
          });
          // this.optionListFiled1 = id1;
          // this.optionListFiled2 = id2;
        }
        this.optionListFiled = data1;
        this.fileNameList = data;
      }
      // 设置输出字段默认数据连接
      if (
        this.nodeObjList.nodeConfig.custom &&
        this.nodeObjList.nodeConfig.custom.length > 0
      ) {
        let arr = this.nodeObjList.nodeConfig.custom;
        let data = [];
        let data1 = [];
        for (let i = 0; i < arr.length; i++) {
          let id1 = arr[i].fromNodeId ? this.getList(arr[i].fromNodeId) : [];
          let id2 = arr[i].toNodeId ? this.getList(arr[i].toNodeId) : [];
          data.push({
            formula: arr[i].formula ? arr[i].formula : "",
            fromNodeId: arr[i].fromNodeId ? arr[i].fromNodeId : "",
            toNodeId: arr[i].toNodeId ? arr[i].toNodeId : "",
            connectType: arr[i].connectType,
          });
          data1.push({
            optionListFiledCust1: id1,
            optionListFiledCust2: id2
          });
          // this.optionListFiled1 = id1;
          // this.optionListFiled2 = id2;
        }
        this.optionListFiledCust = data1;
        this.fileNameList1 = data;
      }
    },
    // 关联输出字段回显
    getList(val) {
      let arr = this.optionList;
      let data = [];
      for (let i = 0; i < arr.length; i++) {
        if (val == arr[i].nodeId) {
          data = arr[i].output.fields.default;
        }
      }
      return data;
    },
    showTooltip() {
      this.visible = true;
    },
    hideTooltip() {
      this.visible = false;
    },
    querySearch(queryString, cb) {
      console.log(queryString);
      var restaurants = this.connectOutPutList;
      this.lastChar = queryString.slice(-1);
      var results = queryString
        ? restaurants.filter(this.createFilter(this.lastChar))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      console.log(results);
      let types = [];
      for (let item of results) {
        types.push({
          value: item.fieldName
        });
      }
      cb(types);
    },
    createFilter(queryString) {
      console.log(queryString);
      return restaurant => {
        console.log(queryString.toLowerCase().indexOf("$"));
        if (queryString.toLowerCase().indexOf("$") == 0) {
          return -1;
        }
        return (
          restaurant.fieldName
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleAdd() {
      this.fileNameList.push({
        fromFieldName: "",
        fromNodeId: "",
        toFieldName: "",
        toNodeId: "",
        connectType: "",
        toFieldType: "等于"
      });
      this.optionListFiled.push({
        optionListFiled1: [],
        optionListFiled2: []
      });
      let data = {
        value: this.fileNameList,
        type: 0,
        key: this.idKey
      };
      this.$emit("outConnectAdd", data);
    },
    // 删除行
    deleteRow(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index === i) {
          this.fileNameList.splice(i, 1);
          this.optionListFiled.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 0,
            key: this.idKey,
            value: this.fileNameList
          };
          this.$emit("outConnectDel", data);
        }
      }
      // });
    },
    // 组合函数应用
    blurGroup1(val, index) {
      // console.log(val);
      let list = this.getList(val);
      console.log("list", list);
      // this.fileNameList[index].disabled1 = false;
      this.optionListFiled[index].optionListFiled1 = list;
      console.log(this.optionListFiled[index]);
      let data = {
        name: "fromNodeId",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].fromNodeId = val;
      this.$emit("outConnectChange", data);
    },
    blurGroup2(val, index) {
      let data = {
        name: "fromFieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].fromFieldName = val;
      this.$emit("outConnectChange", data);
    },
    blurGroup3(val, index) {
      let list = this.getList(val);
      // this.fileNameList[index].disabled2 = false;
      this.optionListFiled[index].optionListFiled2 = list;
      // this.optionListFiled2 = list;
      let data = {
        name: "toNodeId",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].toNodeId = val;
      this.$emit("outConnectChange", data);
    },
    blurGroup4(val, index) {
      let data = {
        name: "toFieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].toFieldName = val;
      this.$emit("outConnectChange", data);
    },
    blurGroup5(val, index) {
      let data = {
        name: "connectType",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].connectType = val;
      this.$emit("outConnectChange", data);
    },
    handleAdd1() {
      this.fileNameList1.push({
        // fromFieldName: "",
        fromNodeId: "",
        // toFieldName: "",
        toNodeId: "",
        connectType: "",
        toFieldType: "等于",
        formula: ""
      });
      this.optionListFiledCust.push({
        optionListFiledCust1: [],
        optionListFiledCust2: []
      });
      let data = {
        value: this.fileNameList1,
        type: 1,
        key: this.idKey
      };
      this.$emit("outConnectAdd", data);
    },
    // 删除行
    deleteRow1(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList1.length; i++) {
        if (index === i) {
          this.fileNameList1.splice(i, 1);
          this.optionListFiledCust.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 1,
            key: this.idKey,
            value: this.fileNameList1
          };
          this.$emit("outConnectDel", data);
        }
      }
      // });
    },
    // 组合函数应用
    blurcust1(val, index) {
      // console.log(val);
      let list = this.getList(val);
      // this.fileNameList[index].disabled1 = false;
      this.optionListFiledCust[index].optionListFiled1 = list;
      let data = {
        name: "fromNodeId",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].fromNodeId = val;
      this.$emit("outConnectChange", data);
    },
    blurcust2(val, index) {
      let data = {
        name: "fromFieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].fromFieldName = val;
      this.$emit("outConnectChange", data);
    },
    blurcust3(val, index) {
      let list = this.getList(val);
      // this.fileNameList[index].disabled2 = false;
      this.optionListFiledCust[index].optionListFiled2 = list;
      // this.optionListFiled2 = list;
      let data = {
        name: "toNodeId",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].toNodeId = val;
      this.$emit("outConnectChange", data);
    },
    blurcust4(val, index) {
      let data = {
        name: "toFieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].toFieldName = val;
      this.$emit("outConnectChange", data);
    },
    blurcust5(val, index) {
      let data = {
        name: "connectType",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].connectType = val;
      this.$emit("outConnectChange", data);
    },
    blurcust6(val, index) {
      console.log(val);
      // this.lastChar=val
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].formula = val;
      this.$emit("outConnectChange", data);
    },
    blurcustList(val) {
      if (!this.selectedPart || this.isNewInput) {
        return;
      }
      // 当检测到在已选项后输入新字符时
      if (
        val.startsWith(this.selectedPart) &&
        val.length > this.selectedPart.length
      ) {
        this.combinedValue =
          this.selectedPart + val.slice(this.selectedPart.length);
        this.isNewInput = true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';

.connection-config-container {
  .config-section {
    .config-section();
    
    .section-icon {
      &.custom {
        i {
          color: @primary-color;
        }
      }
    }
  }
}
</style>
