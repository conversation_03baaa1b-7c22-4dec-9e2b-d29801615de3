<template>
  <!-- 底部配置抽屉 -->
  <div class="config-drawer" :class="{ 'drawer-open': visible }">
    <!-- 抽屉头部 -->
    <div class="drawer-header">
      <div class="drawer-title">
        <i :class="getNodeIcon(selectedNode)"></i>
        <span>{{ selectedNode ? selectedNode.nodeName : '节点配置' }}</span>
      </div>
      <div class="drawer-actions">
        <el-button
          type="text"
          size="small"
          @click="handleClose"
          class="close-btn"
        >
          <i class="el-icon-close"></i>
          确定
        </el-button>
      </div>
    </div>

    <!-- 抽屉内容 -->
    <div class="drawer-content" v-if="selectedNode">
      <!-- 数据指标配置 -->
      <div v-if="typeGet && typeGet >= '0'" class="config-section">
        <!-- 配置标签页 -->
        <div class="config-tabs">
          <div
            :class="['tab-item', { active: activeTab == '1' }]"
            @click="handleTabClick('1')"
          >
            <i :class="getNodeIcon(selectedNode)"></i>
            <span>{{ typeTitle }}</span>
            <div class="tab-indicator" v-if="activeTab == '1'"></div>
          </div>
          <div
            :class="['tab-item', { active: activeTab == '2' }]"
            @click="handleTabClick('2')"
          >
            <i class="el-icon-upload2"></i>
            <span>输出</span>
            <div class="tab-indicator" v-if="activeTab == '2'"></div>
          </div>
        </div>

        <!-- 配置内容容器 -->
        <div class="config-content">
          <div class="content-wrapper">
            <!-- 基本信息 -->
            <div class="config-panel" v-if="typeGet == '0'">
              <basic
                :idKey="idKey"
                :idName="idName"
                :dataItem="dataItem"
                @changefiled="$emit('changefiled', $event)"
                v-if="activeTab == '1'"
              />
              <outPut
                :idKey="idKey"
                :toolType="toolType"
                :dataItem="dataItem"
                :outPutList="outPutList"
                @outPutChange="$emit('outPutChange', $event)"
                @outPutAdd="$emit('outPutAdd', $event)"
                @outPutDel="$emit('outPutDel', $event)"
                v-if="activeTab == '2'"
              />
            </div>

            <!-- 分组聚合 -->
            <div class="config-panel" v-if="typeGet == '1'">
              <groupAg
                v-if="activeTab == '1'"
                :idKey="idKey"
                :dataItem="dataItem"
                :idName="idName"
                :groupOutPutList="filedOutPutList"
                @outGroupChange="$emit('outGroupChange', $event)"
                @outGroupAdd="$emit('outGroupAdd', $event)"
                @outGroupDel="$emit('outGroupDel', $event)"
                @changefiled="$emit('changefiled', $event)"
              />
              <outPut
                v-if="activeTab == '2'"
                :idKey="idKey"
                :toolType="toolType"
                :dataItem="dataItem"
                :outPutList="filedOutPutList"
                :childList="childList"
                @outPutChange="$emit('outPutChange', $event)"
                @outPutAdd="$emit('outPutAdd', $event)"
                @outPutDel="$emit('outPutDel', $event)"
              />
            </div>

            <!-- 数据连接 -->
            <div class="config-panel" v-if="typeGet == '2'">
              <connec
                v-if="activeTab == '1'"
                :idKey="idKey"
                :idName="idName"
                :dataItem="dataItem"
                :filedList="filedList"
                :connectOutPutList="filedOutPutList"
                :childList="childList"
                @outConnectChange="$emit('outConnectChange', $event)"
                @outConnectAdd="$emit('outConnectAdd', $event)"
                @outConnectDel="$emit('outConnectDel', $event)"
                @changefiled="$emit('changefiled', $event)"
              />
              <outPut
                v-if="activeTab == '2'"
                :idKey="idKey"
                :toolType="toolType"
                :dataItem="dataItem"
                :outPutList="filedOutPutList"
                :childList="childList"
                @outPutChange="$emit('outPutChange', $event)"
                @outPutAdd="$emit('outPutAdd', $event)"
                @outPutDel="$emit('outPutDel', $event)"
              />
            </div>

            <!-- 条件函数 -->
            <div class="config-panel" v-if="typeGet == '3'">
              <coFn
                v-if="activeTab == '1'"
                :coFnOutPutList="filedOutPutList"
                :dataItem="dataItem"
                :idName="idName"
                :idKey="idKey"
                @outCoFnChange="$emit('outCoFnChange', $event)"
                @outCoFnAdd="$emit('outCoFnAdd', $event)"
                @changefiled="$emit('changefiled', $event)"
                @outCoFnDel="$emit('outCoFnDel', $event)"
              />
              <outPut
                v-if="activeTab == '2'"
                :idKey="idKey"
                :toolType="toolType"
                :dataItem="dataItem"
                :outPutList="filedOutPutList"
                :childList="childList"
                @outPutChange="$emit('outPutChange', $event)"
                @outPutAdd="$emit('outPutAdd', $event)"
                @outPutDel="$emit('outPutDel', $event)"
              />
            </div>

            <!-- 数据过滤 -->
            <div class="config-panel" v-if="typeGet == '4'">
              <filterCo
                v-if="activeTab == '1'"
                :idKey="idKey"
                :idName="idName"
                :dataItem="dataItem"
                :filterOutPutList="filedOutPutList"
                @outFilterChange="$emit('outFilterChange', $event)"
                @outFilterAdd="$emit('outFilterAdd', $event)"
                @changefiled="$emit('changefiled', $event)"
                @outFilterDel="$emit('outFilterDel', $event)"
              />
              <outPut
                v-if="activeTab == '2'"
                :idKey="idKey"
                :toolType="toolType"
                :dataItem="dataItem"
                :outPutList="filedOutPutList"
                :childList="childList"
                @outPutChange="$emit('outPutChange', $event)"
                @outPutAdd="$emit('outPutAdd', $event)"
                @outPutDel="$emit('outPutDel', $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 未选择节点时的提示 -->
    <div class="drawer-content" v-if="!selectedNode">
      <div class="no-selection">
        <div class="empty-state">
          <div class="empty-icon">
            <i class="el-icon-mouse"></i>
          </div>
          <h4>选择节点开始配置</h4>
          <p>点击画布上的任意节点来查看和编辑其配置</p>
          <div class="help-tips">
            <div class="tip-item">
              <i class="el-icon-info"></i>
              <span>双击节点可快速编辑名称</span>
            </div>
            <div class="tip-item">
              <i class="el-icon-link"></i>
              <span>拖拽连接点可连接节点</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import basic from "./basic";
import groupAg from "./groupAggregation";
import connec from "./connection";
import coFn from "./coFn";
import filterCo from "./filterCo";
import outPut from "./outPut";

export default {
  name: "ConfigDrawer",
  components: {
    basic,
    groupAg,
    connec,
    coFn,
    filterCo,
    outPut
  },
  props: {
    // 是否显示抽屉
    visible: {
      type: Boolean,
      default: false
    },
    // 选中的节点
    selectedNode: {
      type: Object,
      default: null
    },
    // 当前活动标签页
    activeTab: {
      type: String,
      default: '1'
    },
    // 节点类型
    typeGet: {
      type: String,
      default: null
    },
    // 类型标题
    typeTitle: {
      type: String,
      default: '基本信息'
    },
    // 配置相关数据
    idKey: {
      type: String,
      default: ''
    },
    idName: {
      type: String,
      default: ''
    },
    dataItem: {
      type: Object,
      default: () => ({})
    },
    toolType: {
      type: String,
      default: ''
    },
    outPutList: {
      type: Array,
      default: () => []
    },
    filedOutPutList: {
      type: Array,
      default: () => []
    },
    childList: {
      type: Array,
      default: () => []
    },
    filedList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 关闭抽屉
    handleClose() {
      this.$emit('close');
    },
    
    // 切换标签页
    handleTabClick(tab) {
      this.$emit('tab-change', tab);
    },
    
    // 获取节点图标
    getNodeIcon(node) {
      if (!node) return 'el-icon-setting';
      
      // 如果节点已经有logImg属性，直接使用
      if (node.logImg) {
        return node.logImg;
      }
      
      // 根据节点类型或nodeTypeSub获取图标
      const iconMap = {
        // 按nodeTypeSub分类
        'source': 'el-icon-coin',
        'indicator': 'el-icon-data-line',
        'group': 'el-icon-files',
        'connect': 'el-icon-link', 
        'case': 'el-icon-switch-button',
        'filter': 'el-icon-search',
        // 按type分类（备用）
        '0': 'el-icon-coin',
        '1': 'el-icon-files',
        '2': 'el-icon-link',
        '3': 'el-icon-switch-button',
        '4': 'el-icon-search'
      };
      
      return iconMap[node.nodeTypeSub] || iconMap[node.type] || 'el-icon-setting';
    }
  }
};
</script>

<style lang="less" scoped>
.config-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1300;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  resize: vertical;
  overflow: hidden;

  &.drawer-open {
    transform: translateY(0);
  }

  .drawer-header {
    background-color: #D7A256;
    color: white;
    padding: 8px 16px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: row-resize;
    
    .drawer-title {
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      
      i {
        font-size: 16px;
        color: white;
        font-weight: 600;
      }
      
      span {
        font-weight: 500;
      }
    }
    
    .drawer-actions .close-btn {
      font-size: 12px;
      height: 28px;
      padding: 0 8px;
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .drawer-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 12px;
    background: #fbf6ee;
    
    .config-tabs {
      display: flex;
      background: #fbf6ee;
      border-radius: 0;
      padding: 0;
      margin: 0;
      border-bottom: 1px solid #f7ecdd;

      .tab-item {
        flex: 1;
        padding: 16px 20px;
        text-align: center;
        cursor: pointer;
        border-radius: 0;
        font-size: 15px;
        font-weight: 500;
        color: #606266;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: relative;

        &:hover {
          background: #fbf6ee;
          color: #D7A256;
          
          i {
            color: #D7A256;
          }
        }

        &.active {
          background: white;
          color: #D7A256;
          border-bottom: 2px solid #D7A256;
          
          i {
            color: #D7A256;
          }
        }

        .tab-indicator {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: #D7A256;
          border-radius: 1px 1px 0 0;
        }

        i {
          font-size: 16px;
          color: #606266;
          font-weight: 600;
        }

        span {
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
    
    .config-content {
      .content-wrapper {
        padding: 20px 20px 0px 20px;
        background: white;
        min-height: 300px;
      }

      .config-panel {
        // 表单样式优化
        /deep/ .el-form {
          .el-form-item {
            .el-form-item__label {
              font-weight: 500;
              color: #2c3e50;
              font-size: 14px;
              line-height: 36px;
            }

            .el-form-item__content {
              .el-input, .el-select {
                .el-input__inner {
                  border-radius: 8px;
                  border-color: #dcdfe6;
                  font-size: 14px;
                  padding: 0 12px;
                  height: 36px;
                  line-height: 36px;
                  transition: all 0.3s ease;

                  &:focus {
                    border-color: #D7A256;
                    box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
                  }

                  &:hover {
                    border-color: #c0c4cc;
                  }
                }
              }

              .el-button {
                border-radius: 6px;
                font-weight: 500;
                font-size: 14px;
                transition: all 0.3s ease;

                &:hover {
                  transform: translateY(-1px);
                }
              }

              .el-table {
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid #f7ecdd;
                font-size: 14px;

                .el-table__header {
                  background: #fbf6ee;
                  
                  th {
                    background: #fbf6ee !important;
                    color: #2c3e50;
                    font-weight: 600;
                    border-bottom: 1px solid #f7ecdd;
                  }
                }

                .el-table__body {
                  tr {
                    &:hover {
                      background: rgba(215, 162, 86, 0.05) !important;
                    }
                  }
                  
                  td {
                    border-bottom: 1px solid #f7ecdd;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    // 未选择节点时的提示
    .no-selection {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
      
      .empty-state {
        text-align: center;
        color: #909399;
        
        .empty-icon {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          background: rgba(144, 147, 153, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16px;
          
          i {
            font-size: 28px;
            color: #c0c4cc;
          }
        }
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #606266;
        }
        
        p {
          margin: 0 0 20px 0;
          font-size: 14px;
          color: #909399;
        }
        
        .help-tips {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .tip-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 13px;
            color: #c0c4cc;
            
            i {
              font-size: 14px;
              color: #D7A256;
            }
          }
        }
      }
    }
  }
}

// 滚动条美化
.config-drawer .drawer-content {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.3s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}
</style> 