<template>
  <div class="cofn-config-container">
    <!-- 基本信息 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置节点基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
          label-width="80px"
          label-position="right"
          :model="editForm"
          class="editUserForm"
          ref="editUserForm"
          :inline="false"
        >
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="editForm.name"
              auto-complete="off"
              @change="blurName"
              placeholder="请输入节点名称"
              class="modern-input"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 条件函数配置 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon function">
            <i class="el-icon-switch-button"></i>
          </div>
          <div class="section-title">
            <h4>条件函数配置</h4>
            <span class="section-subtitle">配置条件函数规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增条件字段
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          stripe
          v-hover
          class="modern-table"
          empty-text="暂无条件函数数据"
        >
          <el-table-column
            align="center"
            width="150"
            prop="fieldDesc"
            label="字段名称"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldDesc"
                placeholder="请输入字段名称"
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
                class="modern-input"
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="150"
            prop="fieldName"
            label="字段名"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                placeholder="请输入字段名"
                size="small"
                @change="blur2(scope.row.fieldName, scope.$index)"
                class="modern-input"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="default" width="350px" label="条件">
            <template slot-scope="scope">
              <el-row v-for="(e, index) in scope.row.default" :key="index" class="condition-row">
                <el-col :span="8">
                  <el-select
                    v-model="e.left"
                    clearable
                    placeholder="选择字段"
                    filterable
                    size="small"
                    @change="blurGroup1(e.left, index, scope.$index)"
                    class="modern-select"
                  >
                    <el-option
                      v-for="option in optionList1"
                      :key="option.fieldName"
                      :label="option.fieldDesc"
                      :value="option.fieldName"
                    />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-select
                    v-model="e.operator"
                    clearable
                    placeholder="选择操作符"
                    filterable
                    size="small"
                    @change="blurGroup2(e.operator, index, scope.$index)"
                    class="modern-select"
                  >
                    <el-option
                      v-for="option in optionListAvg"
                      :key="option.id"
                      :label="option.name"
                      :value="option.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="7">
                  <el-input
                    v-model="e.right"
                    placeholder="输入值"
                    size="small"
                    @change="blurGroup3(e.right, index, scope.$index)"
                    class="modern-input"
                  />
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column align="center" width="180" prop="default" label="值">
            <template slot-scope="scope">
              <el-row v-for="(e, index) in scope.row.default" :key="index" class="value-row">
                <el-col :span="14">
                  <el-input
                    v-model="e.caseValue"
                    placeholder="请输入值"
                    size="small"
                    @change="blurGroup4(e.caseValue, index, scope.$index)"
                    class="modern-input"
                  />
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="custom"
            width="300"
            label="自定义条件"
          >
            <template slot-scope="scope">
              <div v-for="(e, index) in scope.row.custom" :key="index" class="custom-condition">
                <el-input
                  v-model="e.formula"
                  placeholder="请输入自定义条件"
                  size="small"
                  @change="blurcustom1(e.formula, index, scope.$index)"
                  class="modern-input"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" width="180" prop="custom" label="值">
            <template slot-scope="scope">
              <el-row v-for="(e, index) in scope.row.custom" :key="index" class="value-row">
                <el-col :span="14">
                  <el-input
                    v-model="e.caseValue"
                    placeholder="请输入值"
                    size="small"
                    @change="blurcustom2(e.caseValue, index, scope.$index)"
                    class="modern-input"
                  />
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "groupAggregation",
  props: {
    typeTitle: String,
    coFnOutPutList: Array,
    idKey: String,
    dataItem: Object,
    idName: String
  },
  watch: {
    coFnOutPutList(newValue, oldValue) {
      console.log(newValue, oldValue);
      this.optionList1 = newValue;
    },
    dataItem(newValue, oldValue) {
      // if (newValue != oldValue) {
      this.nodeObjList = newValue;
      // this.changeName();
      // }
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        // this.changeName();
      }
    },
    idName(newVal, oldVal) {
      console.log(newVal);
      this.editForm.name = newVal;
    }
  },
  data() {
    return {
      editForm: {
        name: ""
      },
      fileNameList: [
        // {
        //   fieldName: "",
        //   default: [
        //     // {
        //     //   operator: ">",
        //     //   left: "",
        //     //   right: "",
        //     //   caseValue: ""
        //     // }
        //   ],
        //   custom: [
        //     // {
        //     //   formula: "",
        //     //   caseValue: ""
        //     // }
        //   ]
        // }
      ],
      showAdd: false,
      optionList1: [],
      optionList2: [],
      optionList3: [],
      optionListAvg: [
        {
          name: ">=",
          id: ">="
        },
        {
          name: "<=",
          id: "<="
        },
        {
          name: "!=",
          id: "!="
        },
        {
          name: ">",
          id: ">"
        },
        {
          name: "<",
          id: "<"
        },
        {
          name: "%",
          id: "%"
        },
        {
          name: "=",
          id: "="
        }
      ]
    };
  },
  computed: {},
  created() {
    this.editForm.name = this.idName;
    this.nodeObjList = this.dataItem;
    console.log(this.nodeObjList);
    if (this.nodeObjList.nodeConfig.handle) {
      this.fileNameList = this.nodeObjList.nodeConfig.handle;
    }
    console.log(this.coFnOutPutList)
    this.optionList1 = this.coFnOutPutList;
  },
  methods: {
    querySearch(queryString, cb) {
      console.log(this.optionList);
      var restaurants = this.coFnOutPutList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      console.log(results);
      let types = [];
      for (let item of results) {
        types.push({
          value: item.fieldName
        });
      }
      cb(types);
    },
    createFilter(queryString) {
      console.log(queryString);
      return restaurant => {
        // console.log(queryString.toLowerCase().indexOf('$'));
        // if(queryString.toLowerCase().indexOf('$')==0){
        //   return (-1)
        // }
        return (
          restaurant.fieldName
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    blurName(val) {
      console.log(val);
      let data = {
        name: "strName",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    //聚合字段
    blur1(val, index) {
      // let fieldName = "";
      // for (let i = 0; i < this.optionList1.length; i++) {
      //   if (this.optionList1[i].fieldDesc == val) {
      //     fieldName = this.optionList1[i].fieldDesc;
      //   }
      // }
      // this.getDisabledList(val);
      let data = {
        name: "fieldDesc",
        key: this.idKey,
        value: val,
        index: index,
        type: 0 //聚合字段
      };
      this.fileNameList[index].fieldDesc = val;
      this.$emit("outCoFnChange", data);
    },
    blur2(val, index) {
      // let fieldName = "";
      // for (let i = 0; i < this.optionList1.length; i++) {
      //   if (this.optionList1[i].fieldDesc == val) {
      //     fieldName = this.optionList1[i].fieldDesc;
      //   }
      // }
      // this.getDisabledList(val);
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 0 //聚合字段
      };
      this.fileNameList[index].fieldName = val;
      this.$emit("outCoFnChange", data);
    },
    // 组合函数应用
    blurGroup1(val, index, defaultIndex) {
      let data = {
        name: "left",
        key: this.idKey,
        value: val,
        index: index,
        defaultIndex: defaultIndex,
        type: 1 //组合函数应用
      };
      this.fileNameList[defaultIndex].default[index].left = val;
      this.$emit("outCoFnChange", data);
    },
    blurGroup2(val, index, defaultIndex) {
      // this.getDisabledList(val);
      let data = {
        name: "operator",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList[defaultIndex].default[index].operator = val;
      this.$emit("outCoFnChange", data);
    },
    blurGroup3(val, index, defaultIndex) {
      let data = {
        name: "right",
        key: this.idKey,
        value: val,
        index: index,
        defaultIndex: defaultIndex,
        type: 1
      };
      this.fileNameList[defaultIndex].default[index].right = val;
      this.$emit("outCoFnChange", data);
    },
    blurGroup4(val, index, defaultIndex) {
      let data = {
        name: "caseValue",
        key: this.idKey,
        value: val,
        index: index,
        defaultIndex: defaultIndex,
        type: 1 //原始字段输出
      };
      this.fileNameList[defaultIndex].default[index].caseValue = val;
      this.$emit("outCoFnChange", data);
    },
    blurcustom1(val, index, defaultIndex) {
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        defaultIndex: defaultIndex,
        type: 2 //原始字段输出
      };
      this.fileNameList[defaultIndex].custom[index].formula = val;
      this.$emit("outCoFnChange", data);
    },
    blurcustom2(val, index, defaultIndex) {
      let data = {
        name: "caseValue",
        key: this.idKey,
        value: val,
        index: index,
        defaultIndex: defaultIndex,
        type: 2 //原始字段输出
      };
      this.fileNameList[defaultIndex].custom[index].caseValue = val;
      this.$emit("outCoFnChange", data);
    },
    handleAdd() {
      this.fileNameList.push({
        default: [
          {
            operator: ">",
            left: "",
            right: "",
            caseValue: ""
          }
        ],
        custom: [
          {
            formula: "",
            caseValue: ""
          }
        ],
        fieldName: "", //# 字段别名
        fieldDesc: "" //# 字段中文名
      });
      let data = {
        value: this.fileNameList,
        type: 0,
        key: this.idKey
      };
      console.log(data);
      this.$emit("outCoFnAdd", data);
    },
    handleAdd1(index) {
      console.log(index);
      // this.fileNameList.default=[]
      this.fileNameList[index].default.push({
        operator: ">",
        left: "",
        right: "",
        caseValue: ""
      });
      let data = {
        value: this.fileNameList[index].default,
        type: 1,
        key: this.idKey,
        index: index
      };
      this.$emit("outCoFnAdd", data);
    },
    handleAdd2(index) {
      console.log(index);
      // this.fileNameList.custom = []
      this.fileNameList[index].custom.push({
        formula: "",
        caseValue: ""
      });
      let data = {
        value: this.fileNameList[index].custom,
        type: 2,
        key: this.idKey,
        index: index
      };
      this.$emit("outCoFnAdd", data);
    },
    // 删除行
    deleteRow(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index === i) {
          this.fileNameList.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 0,
            value: this.fileNameList,
            key: this.idKey
          };
          this.$emit("outCoFnDel", data);
        }
      }
      // });
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    getGoalName(val, index, num) {
      console.log(val, index, num);
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';

.cofn-config-container {
  .config-section {
    .config-section();
    
    .section-icon {
      &.function {
        i {
          color: @primary-color;
        }
      }
    }
  }

  // 特殊样式：条件行和值行
  .condition-row, .value-row {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .custom-condition {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
