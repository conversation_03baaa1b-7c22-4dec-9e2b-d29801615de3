<template>
    <div class="tree-transfer">
        <el-container class="wh100 dt-menuList-wrap">
            <!-- 左侧选择栏 -->
            <el-aside :width="sideWidth" class="tree-search-custom" v-show="auth=='edit'">
                <div class="search-view">
                    <el-input
                        placeholder="请输入关键字"
                        clearable
                        v-model="filterText">
                    </el-input>
                </div>
                <div class="tree-view">
                    <el-tree 
                        ref="tree"
                        show-checkbox
                        node-key="directId"
                        :data="treeList" 
                        :props="treeDefaultProps" 
                        :filter-node-method="searchKey"
                        @check-change="checkChange"
                        @node-expand="treeExpand"
                        @node-collapse="treeExpand"
                    >
                    </el-tree>
                </div>
            </el-aside>
            <!--右侧配置栏 -->
            <div class="wh100 tree-selected-main" :class="{'only-look':auth == 'look'}" :style="{'background-color':themeObj.navTagUnselectedColor}">
                <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" v-if="auth == 'edit'"></TableToolTemp>
                <div v-if="title" class="title-view" :style="{'color':themeObj.color}">{{title}}</div>
                <div class="selected-container" :class="{'no-title':!title,'only-look':auth == 'look'}">
                    <el-tree 
                        class="select-tree"
                        ref="tree1"
                        node-key="directId"
                        :data="treeList" 
                        :props="treeDefaultProps" 
                        :filter-node-method="filterNode"
                        @node-expand="tree1Expand"
                        @node-collapse="tree1Expand"
                    >
                    </el-tree>
                </div>
                <div class="btn-container" v-if="auth=='edit'">
                    <el-button type="primary" @click="confirm" class="dt-btn">保存</el-button>
                    <el-button
                        @click="cancel"
                        class="dt-btn"
                        type="primary"
                        plain
                        :style="{color:$store.state.layoutStore.themeObj.color}"
                    >取消</el-button>
                </div>
            </div>
        </el-container>
    </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
export default {
    name:'treeTransfer',
    props:{
       value:{//为数选中的id数组
           type:Array,
           default:()=>{
               return [] 
           }
       },
       treeList:{//为树列表
           type:Array,
           default:()=>{
               return []
           }
       },
       treeDefaultProps:{
           type:Object,
           default:()=>{
               return {
                    children: 'childs',
                    label: 'directName'
                }
           }
       },
       title:{
           type:String,
           default:''
       },
       toolTitle:{
           type:String,
           default:''
       },
       sideWidth:{
           type:String,
           default:'428px'
       },
       auth:{
            type:String,
            default:'edit',
            validator(value){
               return ['edit','look'].indexOf(value)>=0
            }
       }
    },
    data(){
        return{
            filterText:'',
            selectKey:[],//选中的id值数组
            selectNodes:[],//选中的nodesMap的item数组值
            toolListProps: {
                toolTitle: "",
                toolTitleIcon:"iconfont icondt-218",
                toolList: [
                    {
                        name: "清空",
                        icon: "iconfont icondt-217",
                        btnCode: "dics:add"
                    }
                ]
            },
            initValue:[],//初始化临时存储
        }
    },
    components: {
        TableToolTemp
    },
    mounted () {
        this.initPage()
    },
    computed: {
        themeObj() {
            return this.$store.getters["layoutStore/getThemeObj"];
        }
    },
    methods: {
        initPage(){//初始化页面
            this.toolListProps.toolTitle = this.toolTitle;
            this.setTree(this.value)
        },
        handleTool(val){
            if(val.name == '清空'){
                this.setTree([]);//清空原始树和配置树
                this.packUpNode(this.$refs.tree.store.nodesMap,false);//收起所有的树节点
                this.$emit('clear',this.selectKey,this.selectNodes)
            }
        },
        setTree(arr){//给树和配置树设置值
            this.$refs.tree && this.$refs.tree.setCheckedKeys(arr);//勾选tree选中的值
            this.$nextTick(()=>{
                let checkedkeys = this.$refs.tree.getCheckedKeys();//获取当前数勾选的所有层级
                this.$refs.tree1.filter(checkedkeys);//过滤tree1中多余的节点
                this.$nextTick(()=>{
                    this.packUpNode(this.$refs.tree1.store.nodesMap,false);//收起所有的树节点
                })
            })
            this.checkChange();//更新数据
        },
        packUpNode(nodesMap,status){//收起树的所有节点
            for(let key in nodesMap){
                nodesMap[key].expanded = status;
            }
        },
        searchKey(value, data) {//tree的搜索过滤
            if (!value) return true;
            return data.directName.indexOf(value) !== -1;
        },
        treeExpand(e){
            this.$nextTick(()=>{
                this.asyncExpanded(e.directId,this.$refs.tree,this.$refs.tree1);
            })
        },
        tree1Expand(e){
            this.$nextTick(()=>{
                this.asyncExpanded(e.directId,this.$refs.tree1,this.$refs.tree);
            })
        },
        checkChange(){//tree勾选checkbox时触发
            this.selectKey = this.$refs.tree ? this.$refs.tree.getCheckedKeys() : [];//获取所有选中的key值
            this.$refs.tree1.filter(this.selectKey);//触发树tree1的过滤函数
            this.$emit('input',this.selectKey)
            this.selectNodes = this.selectKey.map(id=>{
                this.asyncExpanded(id,this.$refs.tree,this.$refs.tree1);
                return this.$refs.tree.store.nodesMap[id]
            })
        },
        asyncExpanded(id,tree,tree1){//同步两棵树已勾选节点的展开
            tree1.store.nodesMap[id].expanded = tree.store.nodesMap[id].expanded;
            this.asyncParent(id,tree,tree1)
        },
        asyncParent(id,tree,tree1){//同步已勾选节点父级展开
            let parent = tree.store.nodesMap[id] && tree.store.nodesMap[id].parent;
            if(parent&&parent.key){
                tree1.store.nodesMap[parent.key].expanded = parent.expanded;
                this.asyncParent(parent.key,tree,tree1)
            }
            this.$forceUpdate()
        },
        filterNode(value=[],data){//tree1的搜索过滤
            return value.indexOf(data.directId) !== -1;
        },
        cancel(){//取消
            this.$emit('cancel')
        },
        confirm(){//确认
            this.checkChange();//更新数据
            this.$emit('confirm',this.selectKey,this.selectNodes)
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
        treeList:{//用于初始化回显树和回显数据异步
            handler(_new,_old){
                if(!_old||_old.length==0){//只在初始化的时候触发一次
                    this.setTree(this.initValue);//该方法为初始化试调用
                }
            },
            immediate:false
        },
        value:{
            handler(_new,_old){
                if(_new && _new.length>0 && (!_old||_old.length==0)){//只在初始化的时候触发一次
                    this.setTree(_new);//该方法为初始化试调用
                    this.initValue=_new
                }
            },
            immediate:false
        }
    },
}
</script>
<style lang="less">
    .tree-transfer{
        .dt-menuList-wrap {
            padding: 0px 20px 0;
            height: calc(100vh - 240px);
            .tree-search-custom{
                width:518px;
                height:auto;
                background:#f6f6f6;
                margin-right:20px;
                .search-view{
                    padding:20px;
                }
                .tree-view{
                    // max-height:800px;
                    // min-height:50vh;
                    height: calc(100% - 76px);
                    overflow-y:auto;
                }
            }
            .el-tree {
                background: #f6f6f6;
                overflow-y: auto;
                &.allTree {height: 100%;}
                .el-tree-node__content {
                    background:#ebebeb;
                    height: 52px;
                    border-bottom:1px solid #F6F6F6;
                }
                .el-tree-node__children {
                    .el-tree-node__content { 
                        background:#f6f6f6;
                        border:none;
                    }   
                }
                .el-tree-node__expand-icon{margin-left:15px;}
                .el-tree__empty-block{margin-top:10vh}
            }
            .select-tree{
                background:transparent;
                .el-tree-node__content{
                    background:rgba(234,240,247,.6);
                }
                .el-tree-node__children .el-tree-node__content{
                    background:transparent;
                }
            }
            .tree-selected-main{
                position: relative;
                &.only-look{
                    width:428px;
                }
                .title-view{
                    height:52px;
                    line-height:52px;
                    padding-left:20px;
                }
                .tab-tool{
                    padding-bottom:10px;
                    margin-bottom:0px;
                }
                .selected-container{
                    height:calc(100% - 180px);
                    &.no-title{
                        height:calc(100% - 128px);
                    }
                    &.only-look{
                        height:calc(100% - 0px);
                    }
                    overflow: auto;
                }
                .btn-container{
                    line-height:70px;
                    padding-left:20px;
                }
            }
        }
    }
</style>