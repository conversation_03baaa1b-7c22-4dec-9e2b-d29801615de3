<template>
  <div class="tab-tool">
    <div class="tab-title dt-fz18" v-if="toolListProps.toolTitle">
      <i v-if="toolListProps.toolTitleIcon" :class="toolListProps.toolTitleIcon" :style="{color:themeObj.color}"></i>
      <i v-else :style="{background:themeObj.color}" :class="toolListProps.totleLeftIcon"></i>
      {{ toolListProps.toolTitle }}
    </div>
    <slot name="btn-left"></slot>
    <div v-for="(item, index) in toolList" :key="index" class="tab-wrap">
      <el-button v-if="!isInclude(item.name)" type="text" :icon="item.icon" @click="handleTool(item)">
        <span v-if="!isIncludeDownload(item.name)">{{ item.name }}</span>
        <span v-else>
          <a :href="item.downloadURL" :download="item.downloadURL" class="download-btn" :style="{color:themeObj.color}">{{item.name}}</a>
        </span>
      </el-button>
      <el-button v-else type="text" :icon="item.icon" @click="fileClick(item,index)">
        {{ item.name }}
      </el-button>
      <input :id="'file'+index" ref="file" type="file" style="display:none" :multiple="true" name="termFile" @change="fileChange($event,item,index)">
    </div>
    <div style="display:inline-block">
      <slot name="btn-right"></slot>
    </div>
    <div style="display:inline-block;float:right;margin-top:2px;">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
import { hasRights } from "@/config/tool";
import { rootPath } from "@/utils/globalParam";
import http from "@/utils/httpService";
export default {
  name: "",
  props: {
    toolListProps: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      mapToolIcon: {
        新增: { icon: "iconfont icondt8" },
        添加: { icon: "iconfont icondt8" },
        批量添加: { icon: "iconfont icondt8" },
        新增租户: { icon: "iconfont icondt8" },
        新增用户: { icon: "iconfont icondt8" },
        新增公司: { icon: "iconfont icondt8" },
        新增角色: { icon: "iconfont icondt8" },
        新增保险公司: { icon: "iconfont icondt8" },
        新增医院: { icon: "iconfont icondt8" },
        新增服务商: { icon: "iconfont icondt8" },
        添加应用: { icon: "iconfont icondt8" },
        新增主菜单: { icon: "iconfont icondt8" },
        指定群成员: { icon: "iconfont icondt8" },
        新增子菜单: { icon: "iconfont icondt8" },
        新增一级机构: { icon: "iconfont icondt8" },
        新增子机构: { icon: "iconfont icondt8" },
        新增字典项: { icon: "iconfont icondt8" },
        新增参数: { icon: "iconfont icondt8" },
        新增一级部门: { icon: "iconfont icondt8" },
        新增子部门: { icon: "iconfont icondt8" },
        导出: { icon: "iconfont icondt10" },
        导出机构: { icon: "iconfont icondt10" },
        导出部门: { icon: "iconfont icondt10" },
        导出医院: { icon: "iconfont icondt10" },
        下载模板: { icon: "iconfont icondt10" },
        下载导入模板: { icon: "iconfont icondt10" },
        导入: { icon: "iconfont icondt21" },
        导入机构: { icon: "iconfont icondt21" },
        导入部门: { icon: "iconfont icondt21" },
        导入医院: { icon: "iconfont icondt21" },
        清空: { icon: "iconfont icondt24" },
        删除:{ icon: "iconfont icondt24" },
        删除已选字典: { icon: "iconfont icondt24" },
        删除参数: { icon: "iconfont icondt24" },
        关联租户: { icon: "iconfont icondt20" },
        关联保险公司: { icon: "iconfont icondt20" },
        新增字典: { icon: "iconfont icondt8" },
        新增文件路径: { icon: "iconfont icondt8" },
        删除已选类型: { icon: "iconfont icondt24" },
        同步核心数据: { icon: "iconfont icondt-138" },
        推送消息: { icon: "el-icon-position" },
        推送历史: { icon: "el-icon-position" }
      }
    };
  },
  computed: {
    toolList() {
      let toolListProps = this._.cloneDeep(this.toolListProps);
      this._.each(this.mapToolIcon, (val, key) => {
        let item = this._.find(toolListProps.toolList, el => {
          return el.name.indexOf(key) > -1
        });
        if (item) {
          item.icon = val.icon;
        }
      });
      toolListProps.toolList = this._.filter(toolListProps.toolList, item => {
        return hasRights(item.btnCode);
      });
      return toolListProps.toolList;
    },

    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  watch: {

  },
  created() { },
  methods: {
    fileClick(item, index) {
      document.getElementById("file" + index).click()
    },
    fileChange(e, item, index) {
      let files = this.$refs.file[index].files[0]
      if (!/\.(xls|xlsx)$/.test(files.name.toLowerCase())) {
        this.$message({
          type: "error",
          message: "上传格式不正确，请上传xls或者xlsx格式"
        });
        return false;
      }
      http.Axios.post(item.action, {file:files}, { isImport: true, noError: true }).then(res => {
        if (res.resp_code == 1) {
          this.$message({
            showClose: true,
            message: res.resp_msg,
            type: 'error'
          });
          e.target.value = ""
          return
        }
        e.target.value = ""

        this.$emit("fileUpload", true, item);
      })
    },
    isInclude(item) {
      let arr = ["导入", "机构导入", "用户导入"];
      return this._.includes(arr, item);
    },
    isIncludeDownload(item) {
      let arr = ["下载模板"];
      return this._.includes(arr, item);
    },
    handleTool(item) {
      this.$emit("handleTool", item);
    }
  }
};
</script>

<style lang="less">
.tab-tool {
  background-color: #fff; // padding-left: 10px;
  padding-top: 10px;
  margin-bottom: 20px;
  .tab-title {
    display: inline-block;
    font-weight: 600;
    margin-right: 10px;
    color: #333;
    font-family: PingFang SC;
    i {
      display: inline-block;
      width: 4px;
      height: 21px;
      margin-right: 10px;
      position: relative;
      top: 3px;
      &.rhombus {
        top: 0px;
        width: 10px;
        height: 10px;
        margin-right: 6px;
        display: inline-block;
        transform: rotate(45deg);
      }
    }
    .iconfont {
      margin-right: 20px;
    }
  }

  .tab-wrap {
    display: inline-block;
    margin-right: 15px;
    .el-button--medium {
      padding-bottom: 12px;
      line-height: 1em;
    }
  }
  .iconfont {
    position: relative;
    top: 2px;
    font-size: 22px;
    margin-right: 5px;
  }
  a {
    text-decoration: none;
  }
}
</style>
