<template>
  <div class="info-card">
    <div class="card-header">
      <div class="header-left">
        <h3>
          <i :class="icon" v-if="icon"></i> 
          {{ title }}
        </h3>
        <el-tag v-if="tagText" :type="tagType" size="small">{{ tagText }}</el-tag>
      </div>
      <div class="header-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InfoCard',
  props: {
    // 卡片标题
    title: {
      type: String,
      required: true
    },
    // 标题图标
    icon: {
      type: String,
      default: ''
    },
    // 标签文本
    tagText: {
      type: String,
      default: ''
    },
    // 标签类型
    tagType: {
      type: String,
      default: 'info'
    }
  }
}
</script>

<style lang="less" scoped>
.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
  border: 1px solid #eaedf2; // 更改为更明显的边框
  
  .card-header {
    background: #f7ecdd;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0e6d0;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #D7A256;
        }
      }
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
      
      .el-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .card-content {
    padding: 24px;
  }
}
</style> 