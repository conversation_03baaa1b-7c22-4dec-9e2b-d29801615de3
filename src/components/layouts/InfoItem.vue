<template>
  <div class="info-item" :class="{ 'full-width': fullWidth }">
    <span class="label" :style="{ width: labelWidth }">{{ label }}：</span>
    <span class="value" v-if="!$slots.default">{{ value }}</span>
    <slot v-else></slot>
  </div>
</template>

<script>
export default {
  name: 'InfoItem',
  props: {
    // 标签文本
    label: {
      type: String,
      required: true
    },
    // 值
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    // 是否占满整行
    fullWidth: {
      type: Boolean,
      default: false
    },
    // 标签宽度
    labelWidth: {
      type: String,
      default: 'auto'
    }
  }
}
</script>

<style lang="less" scoped>
.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: baseline;
  
  &.full-width {
    width: 100%;
  }
  
  .label {
    font-weight: 600;
    color: #606266;
    margin-right: 8px;
    flex-shrink: 0;
  }
  
  .value {
    color: #2c3e50;
    word-break: break-all;
  }
}
</style> 