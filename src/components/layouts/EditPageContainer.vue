<template>
  <div class="edit-page-container" :class="{ 'view-mode': isView }">
    <SecondaryPageHeader
      :title="title"
      :icon="icon"
      :breadcrumb-items="breadcrumbItems"
      @back="handleBack"
      @breadcrumb-click="handleBreadcrumbClick"
    >
      <template v-slot:actions>
        <el-button v-if="!isView" type="primary" icon="el-icon-check" @click="handleSave" class="save-btn" :loading="loading">保存</el-button>
        <el-button @click="handleBack" class="back-btn">返回</el-button>
      </template>
    </SecondaryPageHeader>

    <div class="form-section">
      <slot></slot>
    </div>

    <div class="page-footer" v-if="!isView">
      <el-button @click="handleBack" class="back-btn">取消</el-button>
      <el-button type="primary" icon="el-icon-check" @click="handleSave" class="save-btn" :loading="loading">保存</el-button>
    </div>
  </div>
</template>

<script>
import SecondaryPageHeader from './SecondaryPageHeader.vue'

export default {
  name: 'EditPageContainer',
  components: { SecondaryPageHeader },
  props: {
    title: { type: String, required: true },
    icon: { type: String, required: true },
    breadcrumbItems: { type: Array, required: true },
    isView: { type: Boolean, default: false },
    loading: { type: Boolean, default: false }
  },
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleSave() {
      this.$emit('save')
    },
    handleBreadcrumbClick(item) {
      this.$emit('breadcrumb-click', item)
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';
.edit-page-container {
  height: 100vh;
  background: #fbf6ee;
  position: relative;
  padding-bottom: 60px;
  overflow: hidden;
  
  .form-section {
    background: white;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: calc(100vh - 60px);
    overflow-y: auto;
    padding-top: 140px;
  }
  
  // 查看模式下不需要底部操作区，容器高度可以占满
  &.view-mode {
    padding-bottom: 0;
    
    .form-section {
      height: calc(100vh - 0px);
    }
  }
  
  .page-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 12px 24px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    z-index: 10;
    
    .save-btn {
      background-color: #D7A256;
      border-color: #D7A256;
      &:hover {
        background-color: #E6B366;
        border-color: #E6B366;
      }
    }
  }
}
</style> 