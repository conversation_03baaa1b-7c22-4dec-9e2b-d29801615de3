<template>
    <div class="dt-message">
        <div class="dt-message-box">
            <span class='close iconfont icondt25' @click="closePopup"></span>
            <div class="head"><div v-if="title">{{title}}</div></div>
            <div class="main" :style="{'text-align':align}" v-html="content"></div>
            <div class="foot" v-if="foot">
                <el-button class="dt-btn" type="primary" plain :style="{color:themeObj.color}" @click="closePopup">取 消</el-button>
				<el-button type="primary" class="dt-btn" @click="confirm">确 认</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import store from "@/store/layoutStore.js";
export default {
    name:'dtMessage',
    props:{
        title: {
            type: String,
            default: ""
        },
        content: {
            type: String,
            default: ""
        },
        themeObj:{
            type: Object,
            default:()=>{
                return store.state.themeObj
            }
        },
        foot:{
            type:Boolean,
            default:true
        },
        align:{
            type:String,
            default:'left'
        }
    },
    methods: {
        closePopup() {
            this.$emit("close");
        },
        confirm() {
            this.$emit("confirm");
        }
    }
}
</script>
<style lang="less">
    .dt-message{
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index:9999;
        overflow: auto;
        margin: 0;
        background:rgba(0,0,0,.5);
        .dt-message-box{
            position: relative;
            z-index:999;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding:0 40px;
            border-radius: 10px;
            display: inline-block;
            min-width:670px;
            min-height:100px;
            box-shadow: 0 1px 3px rgba(0,0,0,.3);
            background:#fff;
            padding-bottom:20px;
            box-sizing: border-box;
            animation:slide-up 300ms;
            &>.close{
                position: absolute;
                font-size: 65px;
                top: -12px;
                right: -11px;
                color: #B8B8B8;
                cursor: pointer;
            }
            &>.head{
                width:100%;
                box-sizing: border-box;
                padding:20px;
                height:60px;
                line-height: 24px;
                font-size: 18px;
                color: #303133;
                text-align: center;
                font-weight: bolder;
            }
            &>.main{
                width: 590px;
                max-height:65vh;
                overflow: auto;
                font-size: 16px;
                color: #606266;
                min-height:80px;
                margin-top:20px;
                word-wrap:break-word;
                &.align{
                    text-align: center;
                }
            }
            &>.foot{
                text-align: center;
                margin-bottom:30px;
            }
        }
        .dt-btn {
            padding: 14px 66px;
            font-size: 18px;
            cursor: pointer;
        }
    }
    @keyframes slide-up{
        0%{opacity:0}
        100%{opacity:1}
    }
</style>