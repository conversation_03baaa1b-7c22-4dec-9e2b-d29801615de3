<template>
    <div class="upload-file">
        <div class="exhibition">
            <img v-if="loading" class="loading" src="../../assets/imgs/loading.gif" alt="">
            <span v-else-if="fileData.name">{{fileData.name}}</span>
        </div>
        <el-button v-show="!disable&&!loading" class="upload-btn" type="text">
            <span v-if="!fileData.name">点击上传</span>
            <span v-else >重新上传</span>
            <form ref='form' class="form-view">
                <input type="file" value="" ref='file'>
            </form>
        </el-button>
    </div>
</template>
<script>
export default {
    name:'uploadFile',
    props:{
        value: {
            type: Object,
            default:()=>{
                return {}
            },
        },
        disable:{
            type:Boolean,
            default:false
        },
        uploadAPI:{
            type:Function
        }
    },
    data(){
        return{
            fileData:{//上传成功的回调数据
                url:'',
                name:'',
                size:''
            }, 
            loading:false,//文件处于上传中
        }
    },
    created(){
        this.echo(this.value);
    },
    mounted () {
        let _this=this;
        this.$refs.file.addEventListener('change',(e)=>{
            e.preventDefault();
            let file = e.target.files[0];
            this.$refs.file.value=""//清空重复上传的缓存
            _this.upload(file);
        })
    },
    methods: {
        async upload(file){//上传文件
            // if (!/\.(pdf)$/.test(file.name.toLowerCase())) {
            //     this.$message({
            //         type: "error",
            //         message: "上传格式不正确，请上传pdf格式"
            //     });
            //     return false;
            // }
            console.log('dsaf');
            this.loading = true;
            let id = this.value.id === undefined?'':this.value.id;
            let res = await this.uploadAPI(file,id);
            if(res){
                this.fileData = {
                    url:res.url,
                    name: file.name,
                    size: file.size,
                    id:res.id,
                    data:res
                }
                let data = this.transformObj(this.fileData,'1');
                this.$emit('fileSucceed',data)
                this.$emit('input',data)
            }
            this.loading = false;
        },
        echo(value){//回显
            if(value.enclosureName){
                this.fileData=this.transformObj(value,'0');
            }else{
                let data=_.cloneDeep(this.$options.data().fileData);
                this.fileData = data;
            }
        },
        transformObj(obj,type){//type为0简化的key,type为1转换为复杂的key
            if(type=="0"){
                return{
                    name:obj.enclosureName,
                    size:obj.enclosureSize,
                    url:obj.enclosureUrl,
                    id:obj.id,
                    data:obj.data
                }
            }else{
                return{
                    enclosureName:obj.name,
                    enclosureSize:obj.size,
                    enclosureUrl:obj.url,
                    id:obj.id,
                    data:obj.data
                }
            }
        }
    }
}
</script>
<style lang="less">
    .upload-file{
        .exhibition{
            // height:1em;
            display:inline-flex;
            align-items:center;
            margin-right:10px;
            .loading{
                width:30px;
                margin-right:10px;
            }
            
        }
        .upload-btn{
            position: relative;
            overflow: hidden;
            .form-view{
                position:absolute;
                top:0;
                right:0;
                bottom:0;
                left:0;
                opacity:0;
                &>input{
                    cursor: pointer;
                    font-size:0px;
                    width:60px;
                    height:20px;
                }
            }
        }
    }
</style>
