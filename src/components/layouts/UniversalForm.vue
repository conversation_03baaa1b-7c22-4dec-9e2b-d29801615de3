<template>
  <div class="universal-form-container">
    <el-form
      ref="form"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      class="universal-form scoped-universal-form"
      :disabled="isView"
      :hide-required-asterisk="isView"
    >
      <div v-for="(group, gIdx) in formGroups" :key="gIdx" class="form-group">
        <div class="group-title" v-if="group.title && !group.fields.some(row => row.some(field => field.type === 'list'))">
          <div class="title-left">
            <i v-if="group.icon" :class="group.icon"></i>{{ group.title }}
          </div>
        </div>
        <div v-for="(row, rIdx) in group.fields" :key="rIdx">
          <el-row :gutter="rowGutter" v-if="!row.some(field => field.type === 'list')">
            <el-col v-for="field in row" :key="field.prop" :span="field.type === 'textarea' ? 16 : (field.span || 8)">
              <el-form-item :label="field.label" :prop="field.prop" :class="[{ 'view-mode': isView }, { 'textarea-full-row': field.type === 'textarea' }]">
                <div v-if="!isView && field.unit" class="input-with-unit">
                  <component
                    :is="getComponentType(field.type)"
                    v-model="formData[field.prop]"
                    v-bind="getComponentProps(field)"
                    class="input-field"
                  />
                  <span class="unit-text">{{ field.unit }}</span>
                </div>
                <component
                  v-else-if="!isView && field.type !== 'radio'"
                  :is="getComponentType(field.type)"
                  v-model="formData[field.prop]"
                  v-bind="getComponentProps(field)"
                  class="input-field"
                />
                <el-radio-group
                  v-else-if="!isView && field.type === 'radio'"
                  v-model="formData[field.prop]"
                  v-bind="getComponentProps(field)"
                  class="input-field"
                >
                  <el-radio
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
                <div v-else class="view-value">
                  {{ formatFieldValue(field.prop, formData[field.prop]) }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- list型表格渲染 -->
          <div v-if="row.some(field => field.type === 'list')" v-for="field in row" :key="field.prop" class="list-form-section">
            <div class="group-title">
              <div class="title-left">
                <div class="title-text">
                  <i v-if="field.icon" :class="field.icon"></i>{{ field.label }}
                </div>
                <div class="title-buttons">
                  <!-- 添加按钮 -->
                  <el-button v-if="!isView && field.addText" size="mini" @click="addListRow(field)" class="action-btn add-btn" :title="field.addText">
                    <i class="el-icon-plus"></i>
                  </el-button>
                  <!-- 导入导出按钮 -->
                  <template v-if="!isView && field.showImportExport">
                    <el-button size="mini" @click="handleExportList(field)" class="action-btn export-btn" title="导出字段">
                      <i class="el-icon-download"></i>
                    </el-button>
                    <el-button size="mini" @click="handleImportList(field)" class="action-btn import-btn" title="导入字段">
                      <i class="el-icon-upload2"></i>
                    </el-button>
                  </template>
                </div>
              </div>
            </div>
            <el-table :data="formData[field.prop]" stripe class="modern-table" :class="{ 'view-mode': isView }" :empty-text="field.emptyText || '暂无数据'">
              <el-table-column v-for="col in field.columns" :key="col.prop" :label="col.label" :width="col.width" :min-width="col.minWidth" :align="col.align || 'center'">
                <template slot-scope="scope">
                  <template v-if="!isView && col.type === 'select'">
                    <el-select v-model="scope.row[col.prop]"
                      :placeholder="typeof col.placeholder === 'function' ? col.placeholder(scope.row) : col.placeholder"
                      :disabled="typeof col.disabled === 'function' ? col.disabled(scope.row) : col.disabled"
                      @change="handleFieldChange(field, scope.row, scope.$index, col)"
                      style="width: 100%;"
                      :multiple="col.multiple">
                      <el-option v-for="opt in col.options" :key="opt.value" :label="opt.label" :value="opt.value" />
                    </el-select>
                  </template>
                  <template v-else-if="!isView && col.type === 'input'">
                    <el-input v-model="scope.row[col.prop]"
                      :type="col.inputType || 'text'"
                      :placeholder="typeof col.placeholder === 'function' ? col.placeholder(scope.row) : col.placeholder"
                      :disabled="typeof col.disabled === 'function' ? col.disabled(scope.row) : col.disabled"
                    />
                  </template>
                  <template v-else-if="!isView && col.type === 'number'">
                    <el-input v-model="scope.row[col.prop]" type="number" :placeholder="col.placeholder" :disabled="typeof col.disabled === 'function' ? col.disabled(scope.row) : col.disabled" />
                  </template>
                  <template v-else-if="!isView && col.type === 'radio'">
                    <el-radio-group v-model="scope.row[col.prop]" :disabled="typeof col.disabled === 'function' ? col.disabled(scope.row) : col.disabled">
                      <el-radio
                        v-for="option in col.options"
                        :key="option.value"
                        :label="option.value"
                      >
                        {{ option.label }}
                      </el-radio>
                    </el-radio-group>
                  </template>
                  <template v-else>
                    <div class="view-value">{{ formatListCell(scope.row, col) }}</div>
                  </template>
                </template>
              </el-table-column>
              <!-- 排序列 -->
              <el-table-column v-if="!isView && field.sortable" label="排序" width="80" align="center">
                <template slot-scope="scope">
                  <div class="sort-buttons">
                    <el-button
                      type="text"
                      icon="el-icon-arrow-up"
                      size="mini"
                      :disabled="scope.$index === 0"
                      @click="moveListRow(field, scope.$index, 'up')"
                      title="上移"
                    />
                    <el-button
                      type="text"
                      icon="el-icon-arrow-down"
                      size="mini"
                      :disabled="scope.$index === formData[field.prop].length - 1"
                      @click="moveListRow(field, scope.$index, 'down')"
                      title="下移"
                    />
                  </div>
                </template>
              </el-table-column>
              <!-- 操作列 -->
              <el-table-column v-if="!isView && field.actions && field.actions.length" :label="'操作'" :width="field.sortable ? '140' : '120'" align="center">
                <template slot-scope="scope">
                  <div v-for="(action, aIdx) in field.actions" :key="aIdx" :style="{ display: field.wrapActions ? 'block' : 'inline-block', marginBottom: field.wrapActions ? '4px' : '0' }">
                    <el-button
                      v-if="!action.show || action.show(scope.row, scope.$index, isView)"
                      :type="action.type || 'text'"
                      :icon="action.icon"
                      size="mini"
                      @click="handleListAction(action, field, scope.row, scope.$index)"
                      :style="{ marginRight: field.wrapActions ? '0' : '8px' }"
                    >
                      {{ action.label }}
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'UniversalForm',
  props: {
    formData: { type: Object, required: true },
    formRules: { type: Object, default: () => ({}) },
    formGroups: { type: Array, required: true },
    labelWidth: { type: String, default: '100px' },
    rowGutter: { type: Number, default: 20 },
    isView: { type: Boolean, default: false }
  },
  methods: {
    getComponentType(type) {
      const typeMap = {
        input: 'el-input',
        select: 'el-select',
        date: 'el-date-picker',
        number: 'el-input-number',
        textarea: 'el-input',
        radio: 'el-radio-group'
      }
      return typeMap[type] || 'el-input'
    },
    getComponentProps(field) {
      const commonProps = {
        placeholder: field.placeholder,
        disabled: field.disabled,
        clearable: true
      }
      if (field.type === 'input') {
        return {
          ...commonProps,
          maxlength: field.maxlength,
          'show-word-limit': field.showWordLimit
        }
      } else if (field.type === 'select') {
        return {
          ...commonProps,
          // style: field.unit ? {} : { width: '100%' }, // 移除或改为 auto
          style: field.unit ? {} : { width: 'auto' },
          filterable: field.filterable,
          options: field.options
        }
      } else if (field.type === 'date') {
        return {
          ...commonProps,
          type: 'date',
          style: field.unit ? {} : { width: '100%' }
        }
      } else if (field.type === 'number') {
        return {
          ...commonProps,
          min: field.min,
          max: field.max,
          precision: field.precision,
          step: field.step,
          style: field.unit ? {} : { width: '100%' }
        }
      } else if (field.type === 'textarea') {
        return {
          ...commonProps,
          type: 'textarea',
          rows: field.rows || 3,
          maxlength: field.maxlength,
          'show-word-limit': field.showWordLimit
        }
      } else if (field.type === 'radio') {
        return {
          ...commonProps,
          style: field.unit ? {} : { width: '100%' }
        }
      }
      return commonProps
    },
    formatFieldValue(prop, value) {
      // 查找字段配置
      let field;
      for (const group of this.formGroups) {
        for (const row of group.fields) {
          for (const f of row) {
            if (f.prop === prop) {
              field = f;
              break;
            }
          }
          if (field) break;
        }
        if (field) break;
      }
      // 字典翻译
      if (field && field.options) {
        if (Array.isArray(value)) {
          // 多选
          return value.map(v => {
            const opt = field.options.find(o => String(o.value) === String(v));
            return opt ? opt.label : v;
          }).join('，') || '暂无';
        } else {
          // 单选
          const opt = field.options.find(o => String(o.value) === String(value));
          return opt ? opt.label : value || '暂无';
        }
      }
      // 默认
      return value || '暂无';
    },
    formatListCell(row, col) {
      if (col.options) {
        const value = row[col.prop];
        if (Array.isArray(value)) {
          return value.map(v => {
            const opt = col.options.find(o => String(o.value) === String(v));
            return opt ? opt.label : v;
          }).join('，') || '暂无';
        } else {
          const opt = col.options.find(o => String(o.value) === String(value));
          return opt ? opt.label : value || '暂无';
        }
      }
      return row[col.prop] || '-';
    },
    addListRow(field) {
      if (!this.formData[field.prop]) this.$set(this.formData, field.prop, [])
      const row = field.defaultRow ? (typeof field.defaultRow === 'function' ? field.defaultRow() : { ...field.defaultRow }) : {}
      this.formData[field.prop].push(row)
    },
    handleListAction(action, field, row, index) {
      if (typeof action.onClick === 'function') {
        action.onClick(row, index, this.formData)
      } else if (action.type === 'danger' || action.icon === 'el-icon-delete') {
        // 默认删除
        this.formData[field.prop].splice(index, 1)
      }
    },
    handleFieldChange(field, row, index, col) {
      if (col.onChange) {
        col.onChange(row[col.prop], row, index, this.formData)
      }
    },
    moveListRow(field, index, direction) {
      const list = this.formData[field.prop]
      if (!list || list.length <= 1) return

      let targetIndex
      if (direction === 'up' && index > 0) {
        targetIndex = index - 1
      } else if (direction === 'down' && index < list.length - 1) {
        targetIndex = index + 1
      } else {
        return
      }

      // 交换位置
      const temp = list[index]
      this.$set(list, index, list[targetIndex])
      this.$set(list, targetIndex, temp)
    },
    handleExportList(field) {
      // 触发父组件的导出事件
      this.$emit('export-list', field)
    },
    handleImportList(field) {
      // 触发父组件的导入事件
      this.$emit('import-list', field)
    },
    validate() {
      return this.$refs.form.validate()
    },
    resetFields() {
      this.$refs.form.resetFields()
    },
    clearValidate(props) {
      this.$refs.form.clearValidate(props)
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';

.universal-form-container {
  padding:20px;
  .universal-form {
    .form-group {
      margin-bottom: 32px;
    }
    .group-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-left {
        display: flex;
        align-items: center;
        i {
          margin-right: 8px;
          color: #D7A256;
        }
      }
      .title-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 12px;
      }
      .title-text {
        display: flex;
        align-items: center;
      }
      .action-btn {
        height: 20px;
        width: 20px;
        padding: 0;
        border-radius: 50%;
        font-size: 16px;
        background: #D7A256;
        border: 1.5px solid #D7A256;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: none;
        &:hover {
          background: #E6B366;
          color: #fff;
          border-color: #E6B366;
        }
        i {
          color: inherit;
          font-size: 12px;
          margin-right: 0;
          line-height: 1;
          vertical-align: middle;
        }
      }
    }
    .el-form-item {
      margin-bottom: 20px;
      .el-input,
      .el-select,
      .el-date-picker,
      .el-input-number {
        max-width: 300px;
        width: auto;
        .el-input {
          width: auto;
        }
      }
      .view-value {
        padding: 8px 12px;
        background: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        font-size: 14px;
        line-height: 1.4;
        min-height: 20px;
        border: 1px solid #dcdfe6;
        &.description-view {
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
      .input-with-unit {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 300px;
        .input-field {
          flex: 1;
          min-width: 0;
          .el-input {
            width: auto !important;
          }
        }
        .unit-text {
          flex-shrink: 0;
          color: #909399;
          font-size: 14px;
          white-space: nowrap;
          min-width: 40px;
          margin-left: 8px;
        }
      }
      // 查看模式样式
      &.view-mode {
        .view-value {
          background: transparent;
          border: none;
          padding: 8px 0;
          color: #303133;
          font-weight: 500;
        }
        .el-form-item__label {
          color: #606266;
          font-weight: 500;
        }
      }
    }
    .list-form-section {
      margin-bottom: 32px;
      .modern-table {
        margin-top: 0;
        // 查看模式下的表格样式
        &.view-mode {
          .el-table__body-wrapper {
            .el-table__row {
              .el-table__cell {
                .view-value {
                  background: transparent;
                  border: none;
                  padding: 4px 0;
                  color: #303133;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
      // 排序按钮样式
      .sort-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
        .el-button {
          padding: 2px 4px;
          margin: 0;
          &:disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
          &:not(:disabled):hover {
            color: #409eff;
          }
        }
      }
    }
  }
}

// 强力覆盖全局宽度，使用 /deep/ 穿透 ElementUI 组件内部
.scoped-universal-form /deep/ .el-input,
.scoped-universal-form /deep/ .el-input__inner,
.scoped-universal-form /deep/ .el-select,
.scoped-universal-form /deep/ .el-date-picker,
.scoped-universal-form /deep/ .el-input-number {
  width: auto !important;
}

// 统一所有输入类型的宽度
.scoped-universal-form /deep/ .el-input,
.scoped-universal-form /deep/ .el-select,
.scoped-universal-form /deep/ .el-date-picker,
.scoped-universal-form /deep/ .el-input-number,
.scoped-universal-form /deep/ .el-input__inner {
  width: 240px !important;
  min-width: 0 !important;
  max-width: 100% !important;
  box-sizing: border-box;
}

.scoped-universal-form /deep/ .el-textarea {
  width: 240px !important;
  min-width: 0 !important;
  max-width: 100% !important;
  box-sizing: border-box;
}

// 针对 input-with-unit 下的输入框
.scoped-universal-form /deep/ .input-with-unit .input-field {
  max-width: 240px;
}

// 针对 input-with-unit 下的数字输入框做专门样式覆盖
.scoped-universal-form /deep/ .input-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 300px;
}
.scoped-universal-form /deep/ .input-with-unit .input-field {
  flex: 1 1 0%;
  min-width: 0;
  max-width: 200px;
}
.scoped-universal-form /deep/ .input-with-unit .el-input-number,
.scoped-universal-form /deep/ .input-with-unit .el-input,
.scoped-universal-form /deep/ .input-with-unit .el-input__inner {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  box-sizing: border-box;
}
.scoped-universal-form /deep/ .el-form-item.textarea-full-row {
  width: 100%;
}
.scoped-universal-form /deep/ .el-form-item.textarea-full-row .el-textarea {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}
</style>
