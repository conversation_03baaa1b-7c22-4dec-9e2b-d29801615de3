<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="confirm-dialog"
    @close="handleClose"
  >
    <div class="confirm-content">
      <div class="confirm-icon" v-if="icon">
        <i :class="icon"></i>
      </div>
      <div class="confirm-text">
        <p>{{ message }}</p>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ cancelText }}</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        {{ confirmText }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
/**
 * 通用确认弹窗组件
 * 
 * 使用示例：
 * 
 * <template>
 *   <div>
 *     <el-button @click="showDeleteConfirm">删除</el-button>
 *     
 *     <ConfirmDialog
 *       ref="confirmDialog"
 *       title="确认删除"
 *       message="删除后无法恢复，请确认是否删除？"
 *       icon="el-icon-warning"
 *       confirm-text="删除"
 *       cancel-text="取消"
 *       @confirm="handleDelete"
 *     />
 *   </div>
 * </template>
 * 
 * ```javascript
 * import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'
 * 
 * export default {
 *   components: { ConfirmDialog },
 *   methods: {
 *     showDeleteConfirm() {
 *       this.$refs.confirmDialog.show()
 *     },
 *     async handleDelete() {
 *       // 执行删除操作
 *       await this.deleteData()
 *       this.$refs.confirmDialog.hide()
 *     }
 *   }
 * }
 * ```
 */
export default {
  name: "ConfirmDialog",
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: "确认操作"
    },
    // 弹窗内容
    message: {
      type: String,
      default: "请确认是否执行此操作？"
    },
    // 图标类名
    icon: {
      type: String,
      default: "el-icon-warning"
    },
    // 弹窗宽度
    width: {
      type: String,
      default: "400px"
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: "取消"
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: "确认"
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false
    };
  },
  methods: {
    // 显示弹窗
    show() {
      this.visible = true;
    },
    // 隐藏弹窗
    hide() {
      this.visible = false;
    },
    // 处理关闭
    handleClose() {
      this.hide();
      this.$emit("close");
    },
    // 处理取消
    handleCancel() {
      this.hide();
      this.$emit("cancel");
    },
    // 处理确认
    handleConfirm() {
      this.$emit("confirm");
    }
  }
};
</script>

<style lang="less" scoped>
.confirm-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(215, 162, 86, 0.15);
    background: white;
    
    .el-dialog__header {
      background: #D7A256;
      padding: 16px 28px;
      border-bottom: 1px solid #C4933C;
      position: relative;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: white;
        display: flex;
        align-items: center;
        gap: 10px;

        &::before {
          content: '';
          width: 24px;
          height: 24px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5z'/%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: center;
          background-size: 14px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }

    .el-dialog__body {
      padding: 28px 32px;
      background: #fcfaf7;
    }

    .el-dialog__footer {
      padding: 20px 28px 20px;
      background: #f8f4ec;
      border-top: 1px solid #e8dcc0;
      text-align: right;

      .el-button {
        height: 36px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--default {
          background: white;
          border: 1px solid #e8dcc0;
          color: #8b7355;

          &:hover {
            background: #fcfaf7;
            border-color: #D7A256;
            color: #D7A256;
            transform: translateY(-1px);
          }
        }

        &.el-button--primary {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          color: white;
          box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
          }

          &.is-loading {
            transform: none;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.2);
          }
        }
      }
    }
  }

  .confirm-content {
    display: flex;
    align-items: center;
    gap: 20px;

    .confirm-icon {
      width: 40px;
      height: 40px;
      background: rgba(230, 162, 60, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 24px;
        color: #e6a23c;
      }
    }

    .confirm-text {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

// 预设样式类
.confirm-dialog {
  &.warning {
    /deep/ .el-dialog__header {
      background: #e6a23c;
      border-bottom-color: #d19b3a;
    }
    
    .confirm-icon {
      background: rgba(230, 162, 60, 0.1);
      
      i {
        color: #e6a23c;
      }
    }
  }

  &.danger {
    /deep/ .el-dialog__header {
      background: #f56c6c;
      border-bottom-color: #e55a5a;
    }
    
    .confirm-icon {
      background: rgba(245, 108, 108, 0.1);
      
      i {
        color: #f56c6c;
      }
    }
  }

  &.success {
    /deep/ .el-dialog__header {
      background: #67c23a;
      border-bottom-color: #5daf34;
    }
    
    .confirm-icon {
      background: rgba(103, 194, 58, 0.1);
      
      i {
        color: #67c23a;
      }
    }
  }

  &.info {
    /deep/ .el-dialog__header {
      background: #409eff;
      border-bottom-color: #3a8ee6;
    }
    
    .confirm-icon {
      background: rgba(64, 158, 255, 0.1);
      
      i {
        color: #409eff;
      }
    }
  }
}
</style> 