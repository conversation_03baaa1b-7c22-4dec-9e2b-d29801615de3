<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    :width="width"
    :close-on-click-modal="closeOnClickModal"
    @close="handleClose"
    :class="['universal-form-dialog', dialogClass]"
  >
    <el-form
      :model="formData"
      :rules="formRules"
      ref="formRef"
      :label-width="labelWidth"
      class="universal-form"
    >
      <template v-for="field in formFields">
        <!-- 输入框 -->
        <el-form-item
          v-if="field.type === 'input'"
          :key="`input-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled || (field.disabledOnEdit && isEdit)"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            :clearable="field.clearable !== false"
          />
        </el-form-item>

        <!-- 文本域 -->
        <el-form-item
          v-else-if="field.type === 'textarea'"
          :key="`textarea-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input
            v-model="formData[field.prop]"
            type="textarea"
            :rows="field.rows || 3"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
          />
        </el-form-item>

        <!-- 选择器 -->
        <el-form-item
          v-else-if="field.type === 'select'"
          :key="`select-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-select
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            style="width: 100%"
          >
            <el-option
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionLabel || 'label']"
              :value="option[field.optionValue || 'value']"
            />
          </el-select>
        </el-form-item>

        <!-- 数字输入框 -->
        <el-form-item
          v-else-if="field.type === 'number'"
          :key="`number-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input-number
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :precision="field.precision"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 日期选择器 -->
        <el-form-item
          v-else-if="field.type === 'date'"
          :key="`date-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-date-picker
            v-model="formData[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :format="field.format"
            :value-format="field.valueFormat"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 开关 -->
        <el-form-item
          v-else-if="field.type === 'switch'"
          :key="`switch-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-switch
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue"
            :inactive-value="field.inactiveValue"
          />
        </el-form-item>

        <!-- 单选框组 -->
        <el-form-item
          v-else-if="field.type === 'radio'"
          :key="`radio-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-radio-group v-model="formData[field.prop]" :disabled="field.disabled">
            <el-radio
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionValue || 'value']"
            >
              {{ option[field.optionLabel || 'label'] }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 复选框组 -->
        <el-form-item
          v-else-if="field.type === 'checkbox'"
          :key="`checkbox-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-checkbox-group v-model="formData[field.prop]" :disabled="field.disabled">
            <el-checkbox
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionValue || 'value']"
            >
              {{ option[field.optionLabel || 'label'] }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 自定义插槽 -->
        <el-form-item
          v-else-if="field.type === 'slot'"
          :key="`slot-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <slot :name="field.slotName || field.prop" :field="field" :formData="formData" />
        </el-form-item>
      </template>

      <!-- 额外的插槽内容 -->
      <slot name="form-extra" :formData="formData" />
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" :disabled="loading">
        {{ cancelText }}
      </el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="loading"
        class="confirm-btn"
      >
        {{ computedConfirmText }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'UniversalFormDialog',
  props: {
    // 弹窗显示控制
    value: {
      type: Boolean,
      default: false
    },
    
    // 弹窗基本配置
    title: {
      type: String,
      default: ''
    },
    editTitle: {
      type: String,
      default: ''
    },
    addTitle: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '600px'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    dialogClass: {
      type: String,
      default: ''
    },
    
    // 表单配置
    formFields: {
      type: Array,
      required: true
    },
    formRules: {
      type: Object,
      default: () => ({})
    },
    labelWidth: {
      type: String,
      default: '100px'
    },
    
    // 数据
    formData: {
      type: Object,
      required: true
    },
    
    // 模式
    isEdit: {
      type: Boolean,
      default: false
    },
    
    // 按钮文本
    confirmText: {
      type: String,
      default: ''
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    
    dialogTitle() {
      if (this.title) {
        return this.title;
      }
      if (this.isEdit && this.editTitle) {
        return this.editTitle;
      }
      if (!this.isEdit && this.addTitle) {
        return this.addTitle;
      }
      return this.isEdit ? '编辑' : '新增';
    },
    
    computedConfirmText() {
      if (this.confirmText) {
        return this.confirmText;
      }
      return this.isEdit ? '保存' : '新增';
    }
  },
  
  methods: {
    // 确认操作
    async handleConfirm() {
      if (!this.$refs.formRef) return;
      
      try {
        await this.$refs.formRef.validate();
        this.$emit('confirm', {
          formData: this.formData,
          isEdit: this.isEdit
        });
      } catch (error) {
        console.warn('表单验证失败:', error);
      }
    },
    
    // 取消操作
    handleCancel() {
      this.$emit('cancel');
      this.visible = false;
    },
    
    // 关闭操作
    handleClose() {
      this.$emit('close');
      this.clearValidation();
    },
    
    // 清除验证状态
    clearValidation() {
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
    },
    
    // 手动验证
    validate() {
      return this.$refs.formRef ? this.$refs.formRef.validate() : Promise.resolve(true);
    },
    
    // 验证指定字段
    validateField(prop) {
      if (this.$refs.formRef) {
        this.$refs.formRef.validateField(prop);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.universal-form-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(215, 162, 86, 0.15);
  }

  /deep/ .el-dialog__header {
    background: #D7A256;
    padding: 16px 28px;
    border-bottom: 1px solid #C4933C;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: white;
      display: flex;
      align-items: center;
      gap: 10px;

      &::before {
        content: '';
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 14px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 18px;
        transition: all 0.3s ease;

        &:hover {
          color: white;
          transform: rotate(90deg);
        }
      }
    }
  }

  /deep/ .el-dialog__body {
    padding: 28px 32px;
    background: #fcfaf7;

    .universal-form {
      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          font-weight: 600;
          color: #2c3e50;
          font-size: 14px;
          line-height: 44px;
        }

        .el-form-item__content {
          .el-input, .el-select, .el-input-number, .el-date-editor {
            .el-input__inner {
              border: 1px solid #e8dcc0;
              border-radius: 8px;
              padding: 12px 16px;
              height: 44px;
              font-size: 14px;
              transition: all 0.3s ease;
              background: white;

              &:hover {
                border-color: rgba(215, 162, 86, 0.6);
              }
              
              &:focus {
                border-color: #D7A256;
                box-shadow: 0 0 0 3px rgba(215, 162, 86, 0.15);
              }

              &::placeholder {
                color: #c0c4cc;
                font-size: 13px;
              }
            }
          }

          .el-textarea {
            .el-textarea__inner {
              border: 1px solid #e8dcc0;
              border-radius: 8px;
              padding: 12px 16px;
              font-size: 14px;
              transition: all 0.3s ease;
              background: white;
              resize: vertical;
              min-height: 88px;

              &:hover {
                border-color: rgba(215, 162, 86, 0.6);
              }
              
              &:focus {
                border-color: #D7A256;
                box-shadow: 0 0 0 3px rgba(215, 162, 86, 0.15);
              }

              &::placeholder {
                color: #c0c4cc;
                font-size: 13px;
              }
            }
          }

          .el-input.is-disabled .el-input__inner,
          .el-textarea.is-disabled .el-textarea__inner {
            background: #f5f7fa;
            color: #c0c4cc;
            border-color: #e4e7ed;
          }

          .el-switch {
            .el-switch__core {
              border-color: #e8dcc0;
              background: #e8dcc0;

              &.is-checked {
                border-color: #D7A256;
                background: #D7A256;
              }
            }
          }

          .el-radio-group, .el-checkbox-group {
            .el-radio, .el-checkbox {
              margin-right: 20px;
              margin-bottom: 8px;

              .el-radio__input.is-checked .el-radio__inner,
              .el-checkbox__input.is-checked .el-checkbox__inner {
                border-color: #D7A256;
                background: #D7A256;
              }
            }
          }
        }

        &.is-error {
          .el-input__inner, .el-textarea__inner {
            border-color: #f56c6c !important;
            box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.1) !important;
          }
        }
      }
    }
  }

  /deep/ .el-dialog__footer {
    padding: 20px 28px;
    background: #f8f4ec;
    border-top: 1px solid #e8dcc0;
    text-align: right;

    .el-button {
      height: 40px;
      padding: 0 24px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      margin-left: 12px;

      &:first-child {
        margin-left: 0;
      }

      &.el-button--default {
        background: white;
        border: 1px solid #e8dcc0;
        color: #8b7355;

        &:hover:not(:disabled) {
          background: #fcfaf7;
          border-color: #D7A256;
          color: #D7A256;
          transform: translateY(-1px);
        }
      }

      &.confirm-btn {
        background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
        border: none;
        color: white;
        box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
        }

        &.is-loading {
          transform: none;
          box-shadow: 0 2px 8px rgba(215, 162, 86, 0.2);
        }
      }
    }
  }
}
</style> 