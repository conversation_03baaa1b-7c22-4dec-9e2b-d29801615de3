<template>
  <div class="info-page-layout">
    <!-- 页面头部 -->
    <SecondaryPageHeader
      :title="title"
      :subtitle="subtitle"
      :icon="icon"
      :breadcrumbItems="breadcrumbItems"
      @back="handleBack"
    />
    
    <!-- 页面内容区域 -->
    <div class="info-content" :class="{ 'with-sidebar': showSidebar }">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <slot></slot>
      </div>
      
      <!-- 侧边栏 -->
      <div class="sidebar" v-if="showSidebar">
        <slot name="sidebar"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import SecondaryPageHeader from '@/components/layouts/SecondaryPageHeader'

export default {
  name: 'InfoPageLayout',
  components: {
    SecondaryPageHeader
  },
  props: {
    // 页面标题
    title: {
      type: String,
      required: true
    },
    // 页面副标题
    subtitle: {
      type: String,
      default: ""
    },
    // 标题图标
    icon: {
      type: String,
      default: ""
    },
    // 面包屑数据
    breadcrumbItems: {
      type: Array,
      default: () => []
    },
    // 是否显示侧边栏
    showSidebar: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style lang="less" scoped>
.info-page-layout {
  margin-top: 145px;
  min-height: 100vh;
  background: #ffffff; // 改为纯白色背景
  
  .info-content {
    padding: 24px;
    display: flex;
    
    &.with-sidebar {
      .main-content {
        width: 75%;
        padding-right: 24px;
      }
      
      .sidebar {
        width: 25%;
      }
    }
    
    .main-content {
      width: 100%;
    }
    
    .sidebar {
      .info-card {
        position: sticky;
        top: 24px;
      }
    }
  }
}
</style> 