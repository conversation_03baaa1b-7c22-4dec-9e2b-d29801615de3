<template>
    <div class="dt-upload">
        <div class="upload-card">
            <div class="thumbnail-wrap">
                <img v-if="imageUrl" :src="imageUrl" @click="dialogVisible=true">
                <div class="tip-text" v-if="!imageUrl&&manner=='upload'">
                    {{disc}}
                    <br />图片大小不超过{{convertUnit(size)}}
                </div>
            </div>
            <div class="upload-card-btn">        
                <el-upload
                    v-if="manner=='upload'"
                    :action="action"
                    :on-success="(res, file) => handleAvatarSuccess(res)"
                    :headers="headers"
                    :show-file-list="false"
                    :before-upload="file => checkSize(file, size)"
                >
                    <el-button slot="trigger" type="text">{{imageUrl?'点击重新上传':'点击上传'}}</el-button>
                </el-upload>
                <div class="look" v-if="imageUrl">
                    <i v-if="manner=='upload'" :style="{background:themeObj.color}"></i>
                    <el-button slot="trigger" type="text" @click="dialogVisible=true">查看原图</el-button>
                </div>
            </div>
        </div>
        <el-dialog :visible.sync="dialogVisible" :modal="imgPreviewModal">
            <img width="100%" :src="imageUrl" alt="">
        </el-dialog>
    </div>
</template>
<script>
export default {
    model:{
        prop:'imageUrl',
        event:'change'
    },
    props: {
        imageUrl:{
            type:String,
            default: ""
        },
        action: {
            type: String,
            default: ""
        },
        size: {
            type: String,
            default: "2048"
        },
        disc:{
            type: String,
            default: "尺寸750x1334px"
        },
        manner:{
            type:String,
            default:'upload',//默认为上传模式，look为查看模式
        },
        imgPreviewModal:{
            type:Boolean,
            default:true
        },
        formatList: {
            type: Array,
            default: () => {
                return ["png", "jpg", "jpeg","gif"];
            }
        }
    },
    data(){
        return {
            headers: {
                access_token: sessionStorage.getItem("LoginAccessToken"),
                funcId:this.$store.getters["layoutStore/getCurrentLoginUser"].funcId,
                tenantId:this.$store.getters["layoutStore/getCurrentLoginUser"].tenantId
            },
            dialogVisible:false
        }
    },
    computed: {
        themeObj() {
            return this.$store.getters["layoutStore/getThemeObj"];
        }
    },
    // mounted(){
    //     setTimeout(() => {
    //         console.log('asdf',this);
    //         let _this = this.$parent;
    //         console.log('df',_this);
    //         let validate = _this.$refs["userForm"].validate;
    //          const e = new Event('a');
    //         validate = validate.apply(_this,arguments);//绑定并出发globalEvent事件
    //         window.dispatchEvent(e);//处罚eventName名称的监听事件
    //         window.addEventListener('a',e=>{
    //             console.log('dsf',e);
    //         })
    //     }, 3000);
    // },
    methods: {
        handleAvatarSuccess(res, data) {
            if (res.resp_code == 0) {
                this.$emit('change',res.datas);
            } else {
                this.$message({
                    type: "error",
                    message: res.resp_msg || "图片上传失败"
                });
            }
        },
        checkSize(file, data) {
            const _this = this;
            const containFormat = this.formatList.some(item =>
                _this._.endsWith(file.name, item)
            );
            if (!containFormat) {
                this.$message({
                    type: "error",
                    message: `上传图片只支持${_this.formatList.join("，")}格式`
                });
                return false;
            }
            let size = file.size / 1024;
            if (data&&size > data) {
                this.$message({
                    type: "error",
                    message: `图片大小超过${this.convertUnit(data)}`
                });
            }
            return size < data;
        },
        convertUnit(data){
            data = parseInt(data);
            if(data>1024){
                return data = data/1024 + 'M'
            }else{
                return data + 'KB';
            }
        }
    }
}
</script>
<style lang="less">
    .dt-upload{
        .upload-card{
            width:249px;
            height:146px;
            overflow: hidden;
            border-radius:6px;
            border: 1px solid #EAEAEA;
            .thumbnail-wrap{
                display: flex;
                justify-content:center;
                width:100%;
                height:108px;
                img{
                    width:auto;
                    height:100%;
                    padding:5px 0;
                    box-sizing: border-box;
                    cursor: pointer;
                }
                .tip-text{
                    width:100%;
                    height:100%;
                    display: flex;
                    justify-content:center;
                    align-items: center;
                    font-size:14px;
                    line-height: 18px;
                    color:#9A9A9A;
                }
            }
        }
        .upload-card-btn{
            display: flex;
            justify-content:center;
            height:38px; 
            background: #FAFAFA;
            &>div{
                width:50%;
                display: flex;
                justify-content:center;
            }
            .look{
                position: relative;
                    &>i{
                        position: absolute;
                        top:50%;
                        left:0px;
                        transform: translateY(-50%);
                        display: inline-block;
                        width:1px;
                        height:16px;
                    }
            }
        }
    }
</style>