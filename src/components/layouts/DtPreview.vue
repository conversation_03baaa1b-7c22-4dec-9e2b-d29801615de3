<template>
    <div class="dt-preview">
        <el-image :src="src" :preview-src-list="srcList"></el-image>
        <slot class="slot"></slot>
    </div>
</template>
<script>
export default {
    name:'DtPreview',
    props:{
        src:{
            type:String,
            default:''
        }
    },
    data(){
        return{
            srcList:[] 
        }
    },
    created(){
        this.srcList=[this.src]
    }
}
</script>
<style lang="less">
    .dt-preview{
        display: inline-block;
        position: relative;
        width:auto;
        height:auto;
        &>.el-image{
            position:absolute;
            width:100%;
            height:100%;
            top:0;
            left:0;
            &>img{
                opacity: 0;
            }
        }
    }
</style>