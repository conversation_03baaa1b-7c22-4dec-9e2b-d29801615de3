<template>
  <div class="form-block" :class="{ expand: foldType }">
    <el-container class="wh100 dt-menusList-wrap">
      <div>
        <el-form
          ref="form"
          :model="searchForm"
          :label-width="labelWidth"
          :inline="true"
          :label-position="position"
          v-if="hasParam"
        >
          <el-form-item>
            <div
              style="margin-top: 15px;"
              v-for="(item, index) in searchFormTemp"
              :key="index"
            >
              <!-- 字段选择 -->
              <el-form-item
                v-if="item.field.name == 'field'"
                :label="item.field.label"
              >
                <el-select
                  v-model="item.field.value"
                  :multiple="item.field.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="
                    handleChange(item.field.name, ...arguments, index)
                  "
                  :disabled="item.field.disabled"
                  :placeholder="item.field.placeholder || '请选择字段'"
                >
                  <el-option
                    v-for="(el, idx) in item.field.list"
                    :key="idx"
                    :label="el.label"
                    :value="el.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- 匹配符选择 -->
              <el-form-item
                v-if="item.operator.name == 'operator'"
                :label="item.operator.label"
              >
                <el-select
                  v-model="item.operator.value"
                  :multiple="item.operator.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="handleChange(item.operator.name, ...arguments, index)"
                  :disabled="item.operator.disabled"
                  :placeholder="item.operator.placeholder || '请选择匹配符'"
                >
                  <el-option
                    v-for="(el, idx) in item.operator.list"
                    :key="idx"
                    :label="el.label"
                    :value="el.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- 值输入 -->
              <el-form-item
                v-if="item.value.name == 'value'"
                :label="item.value.label"
              >
                <el-input
                  v-model="item.value.value"
                  :placeholder="item.value.placeholder || '请输入值'"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="item.btn.name == 'btn'">
                <el-button
                  type="primary"
                  plain
                  :style="{
                    color: themeObj.color,
                    'border-color': themeObj.color
                  }"
                  @click="del(index)"
                >
                  {{ item.btn.label }}
                </el-button>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item style="margin-top: 15px;" v-show="!foldType">
            <el-button
              type="primary"
              plain
              :style="{ color: themeObj.color, 'border-color': themeObj.color }"
              @click="addTemp()"
            >
              新增条件
            </el-button>
            <el-button
              type="primary"
              :style="{
                'background-color': themeObj.color,
                'border-color': themeObj.color
              }"
              @click="normalSearch()"
            >
              搜索
            </el-button>
            <el-button
              type="primary"
              plain
              :style="{ color: themeObj.color, 'border-color': themeObj.color }"
              @click="normalResetQuery()"
            >
              重置
            </el-button>
            <el-button
              type="primary"
              icon="el-icon-download"
              @click="$emit('export')"
            >
              导出
            </el-button>
          </el-form-item>
          <!-- <el-form-item
            v-for="(item, index) in searchFormTemp"
            :key="index"
            :label="item.label"
          >
            <el-row>
              <el-col span="8">
                <el-input
                  v-model="searchForm.param[item.name]"
                  :placeholder="item.placeholder || '请输入'"
                ></el-input>
              </el-col>
              <el-col span="8">
                <el-select
                  v-model="searchForm.param[item.name]"
                  :multiple="item.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="handleChange(item.name, ...arguments)"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder || '请选择'"
                >
                  <el-option
                    v-for="(el, idx) in item.list"
                    :key="idx"
                    :label="el.dicItemName"
                    :value="el.dicItemCode"
                  ></el-option>
                </el-select>
              </el-col>
              <el-col span="8">
                <el-button
                  v-if="item.type='btn'"
                  type="primary"
                  plain
                  :style="{ color: themeObj.color, 'border-color': themeObj.color }"
                  @click="normalResetQuery()"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>
          </el-form-item> -->
        </el-form>
      </div>
      <el-aside :width="sideWidth" class="dt-menusList-tree" v-show="foldType">
        <div
          style="align-items: center;justify-content: center;display: flex;height: 95%;margin-top:0%;border-left: 1px dotted #ccc;"
        >
          <el-button
            type="primary"
            :style="{
              'background-color': themeObj.color,
              'border-color': themeObj.color
            }"
            @click="normalSearch()"
          >
            搜索
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="normalResetQuery()"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="addTemp()"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="changeExpand()"
          >
            {{ expand ? "精确搜索" : "精简搜索" }}
          </el-button>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>
<script>
export default {
  name: "search",
  props: {
    searchForm: {
      type: Object,
      default: function() {
        return {};
      }
    },
    searchFormTemp: {
      type: Array,
      default: function() {
        return [];
      }
    },
    labelWidth: {
      type: String,
      default: "auto"
    },
    foldType: {
      type: Boolean,
      default: false
    },
    isSearch: {
      type: Boolean,
      default: true
    },
    isReset: {
      type: Boolean,
      default: true
    },
    position: {
      type: String,
      default: "left"
    }
  },
  data() {
    return {
      sideWidth: "300px",
      expand: true,
      hasParam: false //判断当前传入的是两层对象
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  created() {
    this.hasParam = this.searchForm.hasOwnProperty("param");
    console.log(this.hasParam);
  },
  mounted() {},
  methods: {
    normalSearch() {
      // this.handleSearchForm();
      this.$emit("normalSearch", this.searchFormTemp);
    },
    normalResetQuery() {
      // this.$refs.form.resetFields();
      for (let item of this.searchFormTemp) {
        item.field.value = "";
        item.operator.value = "";
        item.value.value = "";
      }
      this.$emit("normalResetQuery", this.searchFormTemp);
    },
    del(val) {
      this.$emit("del", val);
    },
    addTemp() {
      this.$emit("addTemp", 1);
    },
    handleSearchForm() {
      let tempObj = {};
      // for (let item of this.searchFormTemp) {
      //   if (item.type == "doubleDate") {
      //     for (let li of item.options) {
      //       tempObj[li.name] = li.value;
      //     }
      //     break;
      //   }
      // }
      if (this.hasParam) {
        Object.assign(this.searchForm.param, tempObj);
      }
    },
    changeExpand() {
      this.searchFormTemp.forEach(item => {
        item.tempShow = this.expand;
      });
      this.expand = !this.expand;
    },
    handleChange(name, val, index) {
      this.$emit("handleChange", name, true, val, index);
    }
  }
};
</script>
<style lang="less">
.form-block {
  background: #fff;
  padding-left: 11px;

  .el-input--medium {
    width: 215px !important;
  }

  .el-input__inner,
  .el-button--medium {
    border-radius: 6px;
  }

  .el-form-item__label {
    color: #333;
    width: 100px !important;
    text-align: right !important;
    justify-content: flex-end !important;
    margin-right: 10px;
  }

  .el-form-item {
    margin-bottom: 13px;
  }

  .dt-cascader {
    .el-input--medium {
      width: 600px !important;
    }
  }
  .ml0 {
    .el-form-item__label-wrap {
      margin-left: 0 !important;
    }
  }
}
</style>
