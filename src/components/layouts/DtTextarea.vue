<template>
  <div class="dt-textarea" ref="asd">
    <div 
      ref="textarea" 
      contenteditable="true"
      user-modify="read-write" 
      class="edit needsclick" 
      v-html="value" 
      @input="input"
      @click="saveLastRange" 
      @keyup="saveLastRange"
      @mouseout="saveLastRange"
      @focus="$emit('focus',$event)"
      @blur="$emit('blur',$event)"
    ></div>
    <span v-if="content==''" class="placeholder">{{placeholder}}</span>
  </div>
</template>
<script>
import _ from 'lodash';
import { setTimeout, clearTimeout, setInterval, clearInterval } from 'timers';
import { log } from 'util';
export default {
  name:'dt-textarea',
  props:{
    value:{
      type:String,
      default:''
    },
    placeholder:{
      type:String,
      default:'sadf'
    },
    autosize:{//输入框的最小行数和最大行数
      type:Object,
      default:e=>{
        if(!e){
          return {
            minRow:3,
            maxRow:5
          }
        }
      }
    }
  },
  data(){
    return{
      lastRange:null,
      content:'',

      timer:null,
      timerLoadNum:0,
      keyboardHeight:0,
      screenHeight:window.innerHeight

    }
  },
  mounted(){
    let {minRow,maxRow,row} = this.autosize;
    this.$refs.textarea.style.minHeight=`${minRow*1.25}em`;
    this.$refs.textarea.style.maxHeight=`${maxRow*1.25}em`;
    if(row){
      this.$refs.textarea.style.height=`${row*1.25}em`;
      this.$refs.textarea.style.overflow="hidden";
      this.$refs.textarea.style.whiteSpace="pre";
    }
  },
  methods:{
    input(e){
      this.content = e.target.innerHTML
      this.$emit('input')
    },
    saveLastRange(e){//保存最后的光标（时机为click,keyup）
      try {//防止selection.getRangeAt(0)报获取不到0的索引，待研究。但不影响程序运行
        let selection = window.getSelection();//获取当前光标对象
        let containerClass = selection.getRangeAt(0).startContainer.parentElement._prevClass;
        if(!(containerClass == 'dt-textarea'||containerClass =='edit needsclick'))return;//防止添加到其它位置
        this.lastRange = selection.getRangeAt(0);//保存最后光标对象
      } catch (error) {}
    },
    setLastRange(){//将光标设置到最后的保存的位置
      try {//防止selection.getRangeAt(0)报获取不到0的索引，待研究。但不影响程序运行
        let selection = window.getSelection();//获取
        let containerClass = selection.getRangeAt(0).startContainer.parentElement._prevClass;
        if(!(containerClass == 'dt-textarea'||containerClass =='edit needsclick'))return;//防止添加到其它位置
        selection.removeAllRanges()
        this.lastRange.collapse(true)
        selection.addRange(this.lastRange)
      }catch(error){}
    },
    keepLastIndex(){//将光标移动到输入框最后位置
      this.$refs.textarea.selectionStart = this.content.length-2;
      this.$refs.textarea.selectionEnd = this.content.length-2;
      this.$refs.textarea.focus();
    },
    addNode(type='text',value){//给输入框光标位置添加内容
      if(!(this.lastRange&&this.lastRange.startContainer)){//没有获取过光标的情况
        this.keepLastIndex();
      }
      //将字符value按类型转为虚拟dom
      let tempNode;
      switch (type) {
        case "html":
          tempNode = document.createElement('span');
          tempNode.innerHTML = value;
          tempNode = tempNode.children[0];
          break;
        case "text":
          tempNode = document.createTextNode(value)
          break;
      }
      this.insertHtmlAtCaret(tempNode);
      this.content = this.getNode();
      document.activeElement.blur();//最外层拦截软键盘弹出
    },
    getNode(type='html'){//获取输入框中的内容
      let content;
      switch (type) {
        case "html":
          content = this.$refs.textarea.innerHTML;
          break;
        case "text":
          content = this.$refs.textarea.innerText;
          break;
      }
      return content;
    },
    //将虚拟dom节点插入光标指定位置
    insertHtmlAtCaret(childElement) {
        var sel, range;
        if (window.getSelection) {
            // IE9 and non-IE
            sel = window.getSelection();
            if (sel.getRangeAt && sel.rangeCount) {
                range = sel.getRangeAt(0);
                if(this.lastRange){
                  range = this.lastRange;
                }
                let containerClass = range.startContainer.parentElement._prevClass;
                if(!(containerClass == 'dt-textarea'||containerClass =='edit needsclick'))return;//防止添加到其它位置
                range.deleteContents();

                var el = document.createElement("div");
                el.appendChild(childElement);
                var frag = document.createDocumentFragment(), node, lastNode;
                while ((node = el.firstChild)) {
                    lastNode = frag.appendChild(node);
                }
                range.insertNode(frag);
                if (lastNode) {
                    range = range.cloneRange();
                    range.setStartAfter(lastNode);
                    range.collapse(true);
                    sel.removeAllRanges();
                    sel.addRange(range);
                }
            }
        }
        else if (document.selection && document.selection.type != "Control") {
            // IE < 9
            //document.selection.createRange().pasteHTML(html);
        }
        this.saveLastRange();
    },
    getKeyboardHeight(callback){//动态获取键盘高度
      if(!this.keyboardHeight){
        this.timer = setInterval(()=>{
          if(this.screenHeight !== window.innerHeight){
            this.keyboardHeight = this.screenHeight - window.innerHeight;
          }
          this.timerLoadNum++;//记录定时器其次，防止某种原因导致无效值加载
          if(this.keyboardHeight||this.timerLoadNum>10){//获取到了键盘高度或者多次加载无果
            clearInterval(this.timer);
            callback(this.keyboardHeight);
          }
        },50)
      }
    },
    touchFocus(){//获取焦点
      this.$refs.textarea.focus();
      this.$emit('focus');
    },
    touchBlur(){//失去焦点
      this.$refs.textarea.blur();
      this.$emit('blur');
    },
    touchReset(){
      this.lastRange = this.$options.data().lastRange;
      this.content = this.$options.data().content;
      this.$refs.textarea.innerHTML = "";
    }
  }
}
</script>
<style lang="less" scoped>
.dt-textarea{
  position: relative;
  padding:0.25em !important;
  .edit{
    padding:1px 0;
    line-height: 1.25em;
    overflow: auto;
    text-align: left;
    &::-webkit-scrollbar-track{
      background:transparent;
    }
    &::-webkit-scrollbar{
      width:4px;
      background:transparent;
    }
    &::-webkit-scrollbar-thumb{
      background:rgba(69, 90, 100, 0.2);
      border-radius: 4px;
    }
  }
  .placeholder{
    position: absolute;
    left:0.25em;
    top:0.25em;
    color:#999999;
  }
}
</style>
<style lang="less" scoped>
.dt-textarea{
  padding:0.5em !important;
  .edit{
    padding:1px 0;
    line-height: 1.25em;
    overflow: auto;
    &::-webkit-scrollbar{
      width:10px;
    }
  }
  .placeholder{
    position: absolute;
    left:0.5em;
    top:0.5em;
    color:#999999;
  }
}
</style>
