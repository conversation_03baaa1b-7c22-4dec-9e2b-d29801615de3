<template>
  <div class="stat-card" :class="[`bg-${bgColor}`]">
    <div class="stat-icon" v-if="icon">
      <i :class="icon"></i>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ value }}</div>
      <div class="stat-label">{{ label }}</div>
      <div class="stat-tag" v-if="tagText">
        <el-tag :type="tagType" size="mini">{{ tagText }}</el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    // 统计值
    value: {
      type: [String, Number],
      required: true
    },
    // 统计标签
    label: {
      type: String,
      required: true
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: 'default' // default, primary, success, warning, danger, info
    },
    // 标签文本
    tagText: {
      type: String,
      default: ''
    },
    // 标签类型
    tagType: {
      type: String,
      default: 'info'
    }
  }
}
</script>

<style lang="less" scoped>
.stat-card {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #eaedf2;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  }
  
  &.bg-default {
    background: #f9f9f9;
  }
  
  &.bg-primary {
    background: rgba(64, 158, 255, 0.1);
    .stat-value, .stat-icon i {
      color: #409EFF;
    }
  }
  
  &.bg-success {
    background: rgba(103, 194, 58, 0.1);
    .stat-value, .stat-icon i {
      color: #67C23A;
    }
  }
  
  &.bg-warning {
    background: rgba(230, 162, 60, 0.1);
    .stat-value, .stat-icon i {
      color: #E6A23C;
    }
  }
  
  &.bg-danger {
    background: rgba(245, 108, 108, 0.1);
    .stat-value, .stat-icon i {
      color: #F56C6C;
    }
  }
  
  &.bg-info {
    background: rgba(144, 147, 153, 0.1);
    .stat-value, .stat-icon i {
      color: #909399;
    }
  }
  
  &.bg-gold {
    background: rgba(215, 162, 86, 0.1);
    .stat-value, .stat-icon i {
      color: #D7A256;
    }
  }
  
  .stat-icon {
    margin-bottom: 12px;
    
    i {
      font-size: 32px;
      color: #D7A256;
    }
  }
  
  .stat-content {
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #D7A256;
      margin-bottom: 8px;
    }
    
    .stat-label {
      color: #606266;
      font-size: 14px;
      margin-bottom: 8px;
    }
  }
}
</style> 