<template>
    <div class="search-head" >    
        <el-input 
            clearable
            v-model="keyword"
            placeholder="请输入关键字"
            @input='input'
            @clear='clear'
            @keyup.enter.native="enter"
        > 
            <div 
                class="search-button"
                slot="append" 
                :style="{background:themeObj.color,border:`1px solid ${themeObj.color}`}"
                @click="clickSearch" 
            >
                搜索知识
            </div>
        </el-input>
    </div>
</template>
<script>
export default {
    props:{
        value:{
            type:String,
            default:""
        }
    },
    created() {

    },
    data(){
        return{
            keyword:'',
            titles:[]
        }
    },
    computed: {
        themeObj() {
            return this.$store.getters["layoutStore/getThemeObj"];
        }
    },
    methods: {
        input(){
            if(this.keyword){
                this.$emit('input',this.keyword)
            }else {
                this.clear();
            }
        },
        clear(){
            this.$emit('clear')
        },
        enter(){
            if(this.keyword){
                this.$emit('enter',this.keyword)
            }
        },
        clickSearch(){
            this.$emit('click-search',this.keyword)
        }
    },
    watch: {
        keyword:{
            handler(_new,_old){
                this.$emit('input',_new) 
            },
            immediate:true,
            deep:true 
        },
        value:{
            handler(_new,_old){
                this.keyword=_new; 
            },
            immediate:true,
            deep:true 
        },
    },
}
</script>
<style lang="less">
    .search-head{
        border-radius:10px;
        overflow: hidden;
        .el-input--medium .el-input__inner{
            height:45px;
            line-height: 45px;
            border-radius:10px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0
        }
        .el-input-group__append{
            cursor:pointer;
            color:#fff;
            padding:0px;
            border:none;
            .search-button{
                height:43px;
                line-height: 43px;
                padding:0 20px;
                &:active{opacity:.8}
            }
        }
    }
</style>