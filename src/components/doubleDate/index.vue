<!--
/*
 * @Author: thomas
 * @Date: 2019-05-27 13:40:47
 * @Description: 封装table搜索条件(双输入框)
 -->
<template>
	<div>
		<el-date-picker
			v-if="elType=='DateTimePicker'"
			v-model="value"
			:clearable="clearable"
			:popper-class="!clearable?'hidenclearable':''"
			type="datetimerange"
			range-separator="至"
			start-placeholder="开始时间"
			end-placeholder="结束时间"
			value-format="yyyy-MM-dd HH:mm:ss"
			:picker-options="pickerOptions"
			@change="getValue()"
		></el-date-picker>
		<el-date-picker
			v-if="elType=='DatePicker'"
			v-model="value"
			:clearable="clearable"
			:popper-class="!clearable?'hidenclearable':''"
			type="daterange"
			range-separator="至"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
			value-format="yyyy-MM-dd"
			:picker-options="pickerOptions"
			@change="getValue()"
		></el-date-picker>
		<el-time-picker
			v-if="elType=='TimePicker'"
			v-model="value"
			:clearable="clearable"
			:popper-class="!clearable?'hidenclearable':''"
			is-range
			range-separator="至"
			start-placeholder="开始时间"
			end-placeholder="结束时间"
			value-format="HH:mm:ss"
			:picker-options="pickerOptions"
			@change="getValue()"
		></el-time-picker>
	</div>
</template>

<script>
export default {
  name: "ElType",
	props: {
		elType: {
			type: String,
			default: "DateTimePicker"
		},
		pickerOptions:{
			type:Object,
			default:()=>{
				return {}
			}
		},
		options: {
			type: Array,
			default: () => []
		},
		name: {
			type: String,
			default: ""
		},
		clearable:{
			type: Boolean,
			default:true
		}
  },
	data() {
		return {
			value: ""
		};
	},
	watch: {
		options: {
			immediate: true,
			deep: true,
			handler: function(val, oldVal) {
				if (val[0].value == "") {
				this.value = "";
				} else if (val.length == 2 && val[0].value != "") {
				this.value = [];
				this.value[0] = val[0].value;
				this.value[1] = val[1].value;
				}
			}
		}
	},
	created() {
		console.log('d',this.pickerOptions)
	},
	methods: {
		getValue() {
			this.$emit("editParams", this.value, this.name);
		}
	}
};
</script>

<style scoped lang="less">
.query-conditions-container {
  background: #eee;
  padding: 18px 0 0;
  border: 1px solid #d3dce6;

  .el-input,
  .el-select {
    width: 190px;
  }

  .search-btn {
    margin-left: 20px;
  }
}
</style>
<style lang="less">
.hidenclearable .el-picker-panel__link-btn.el-button--text{
  display: none;
}
</style>