<template>
  <div class="data-template-view-container">
    <UniversalTable
      :title="pageTitle"
      :subtitle="pageSubtitle"
      title-icon="el-icon-view"
      :table-data="tableData"
      :loading="loading"
      :columns="dynamicColumns"
      search-type="custom"
      :custom-search-config="customSearchConfig"
      :search-params="searchParams"
      :pagination-data="pagination"
      :total="pagination.total"
      :show-breadcrumb="showBreadcrumb"
      :breadcrumb-items="breadcrumbItems"
      :show-add-button="showAddButton"
      :actions="actions"
      :action-column-width="actionColumnWidth"
      :show-selection="showSelection"
      empty-title="暂无数据"
      empty-description="该模板暂时没有数据"
      v-bind="$attrs"
      v-on="$listeners"
      @custom-search="handleCustomSearch"
      @custom-reset="handleCustomReset"
      @custom-search-del="handleCustomSearchDel"
      @custom-search-add="handleCustomSearchAdd"
      @custom-search-change="handleCustomSearchChange"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- 透传所有插槽 -->
      <template v-for="(_, slot) of $scopedSlots" v-slot:[slot]="scope">
        <slot :name="slot" v-bind="scope" />
      </template>
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import { getTemplateFields, getTemplateData } from '@/api/basicConfig'

export default {
  name: 'DataTemplateView',
  components: { UniversalTable },
  props: {
    // 模板编码
    templateCode: {
      type: String,
      required: true
    },
    // 模板名称
    templateName: {
      type: String,
      default: ''
    },
    // 自定义标题
    customTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      dynamicColumns: [],
      customSearchConfig: [],
      searchParams: {
        param: {}
      },
      searchForm: {},
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.customTitle) {
        return this.customTitle
      }
      return `数据模板查看 - ${this.templateName || '加载中...'}`
    },
    pageSubtitle() {
      return `查看模板数据，共 ${this.pagination.total} 条记录`
    },
    // 从$attrs中获取配置，提供默认值
    showBreadcrumb() {
      return this.$attrs['show-breadcrumb'] !== false
    },
    breadcrumbItems() {
      return this.$attrs['breadcrumb-items'] || []
    },
    showAddButton() {
      return this.$attrs['show-add-button'] || false
    },
    actions() {
      return this.$attrs.actions || []
    },
    actionColumnWidth() {
      return this.$attrs['action-column-width'] || 200
    },
    showSelection() {
      return this.$attrs['show-selection'] || false
    }
  },
  watch: {
    templateCode: {
      handler(newVal) {
        if (newVal) {
          this.loadTemplateFields(newVal)
        }
      },
      immediate: true
    }
  },
  methods: {
    async loadTemplateFields(templateCode) {
      try {
        const response = await getTemplateFields(templateCode)
        if (response && response.length > 0) {
          // 构建动态列配置
          this.dynamicColumns = response
            .filter(field => field.showType !== '0') // 过滤不可见字段
            .map(field => ({
              prop: field.fieldCode,
              label: field.fieldName,
              minWidth: 120,
              align: 'center',
              showOverflowTooltip: true
            }))

          // 构建CustomSearch配置
          this.customSearchConfig = this.buildCustomSearchConfig(response)

          // 加载数据
          this.loadTableData()
        }
      } catch (error) {
        this.$message.error('加载模板字段失败')
        console.error('加载模板字段失败:', error)
      }
    },
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          param: {
            templateCode: this.templateCode,
            ...this.searchParams.param
          },
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        const response = await getTemplateData(params)
        if (response) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    buildCustomSearchConfig(fields) {
      // 过滤可搜索的字段
      const searchableFields = fields.filter(field =>
        field.showType !== '0' && ['1', '3'].includes(field.showType)
      )

      if (searchableFields.length === 0) {
        return []
      }

      // 构建CustomSearch需要的配置格式
      return [{
        field: {
          name: 'field',
          label: '字段',
          value: '',
          multiple: false,
          disabled: false,
          placeholder: '请选择字段',
          list: searchableFields.map(field => ({
            label: field.fieldName,
            value: field.fieldCode
          }))
        },
        operator: {
          name: 'operator',
          label: '匹配符',
          value: '=',
          multiple: false,
          disabled: false,
          placeholder: '请选择匹配符',
          list: [
            { label: '=', value: '=' },
            { label: '>', value: '>' },
            { label: '<', value: '<' },
            { label: '>=', value: '>=' },
            { label: '<=', value: '<=' }
          ]
        },
        value: {
          name: 'value',
          label: '值',
          value: '',
          placeholder: '请输入值'
        },
        btn: {
          name: 'btn',
          label: '删除'
        }
      }]
    },
    handleCustomSearch(searchFormTemp) {
      // 将CustomSearch的数据转换为指定格式的查询参数
      const searchParamList = []

      searchFormTemp.forEach(item => {
        if (item.field.value && item.operator.value && item.value.value) {
          searchParamList.push({
            fieldName: item.field.value,
            operator: item.operator.value,
            value: item.value.value
          })
        }
      })

      // 更新searchParams为指定格式
      this.searchParams = {
        param: {
          searchParamList: searchParamList
        }
      }

      this.pagination.pageNum = 1
      this.loadTableData()
    },
    handleCustomReset(searchFormTemp) {
      // 重置搜索条件
      searchFormTemp.forEach(item => {
        item.field.value = ''
        item.operator.value = ''
        item.value.value = ''
      })

      // 重置searchParams为初始格式
      this.searchParams = {
        param: {}
      }

      this.pagination.pageNum = 1
      this.loadTableData()
    },
    handleCustomSearchDel(index) {
      // 删除指定索引的搜索条件
      if (this.customSearchConfig.length > 1) {
        this.customSearchConfig.splice(index, 1)
      } else {
        // 如果只有一个条件，重置它
        const item = this.customSearchConfig[0]
        item.field.value = ''
        item.operator.value = ''
        item.value.value = ''
      }
    },
    handleCustomSearchAdd() {
      // 添加新的搜索条件
      const newCondition = JSON.parse(JSON.stringify(this.customSearchConfig[0]))
      newCondition.field.value = ''
      newCondition.operator.value = ''
      newCondition.value.value = ''
      this.customSearchConfig.push(newCondition)
    },
    handleCustomSearchChange({ name, isValid, value, index }) {
      // 处理字段变化
      if (this.customSearchConfig[index] && this.customSearchConfig[index][name]) {
        this.customSearchConfig[index][name].value = value
      }
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadTableData()
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadTableData()
    },
    // 公开方法：刷新数据
    refresh() {
      this.loadTableData()
    },
    // 公开方法：重置搜索
    resetSearch() {
      this.searchParams = { param: {} }
      this.pagination.pageNum = 1
      this.loadTableData()
    }
  }
}
</script>

<style lang="less" scoped>
.data-template-view-container {
  height: 100vh;
  overflow: hidden;

  .template-info-card {
    background: rgba(215, 162, 86, 0.1);
    border: 1px solid rgba(215, 162, 86, 0.2);
    border-radius: 6px;
    padding: 12px 16px;

    .info-content {
      display: flex;
      align-items: center;
      gap: 24px;
      flex-wrap: wrap;

      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .label {
          font-size: 12px;
          color: #8b7355;
          font-weight: 500;
        }

        .value {
          font-size: 13px;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
