import Vue from "vue";
import dtMessage from "@/components/layouts/dtMessage"

export const message = ()=>{
    Vue.prototype.$dtMessage=({title,content,themeObj,foot,align,confirm,cancel,closeText,confirmText})=>{
        let Constructor = Vue.extend(dtMessage)
        let dt_message = new Constructor({
            propsData:{
                title,//标题
                content,//内容
                themeObj,//主题
                foot,//是否显示foot,boolean
                align,//是否居中
                confirmText,
                closeText
            }
        })
        dt_message.$mount();
        let remove=()=>{//移除
            document.body.removeChild(dt_message.$el)
            dt_message=null;
        }
        dt_message.$on('close',async ()=>{
            if(cancel){
                await cancel()
            }
            remove();
        })
        dt_message.$on('confirm',async ()=>{
            if(confirm){
                await confirm()
            }
            remove();
        })
        document.body.appendChild(dt_message.$el);//将内存中的片段渲染到dom中
    }
}