let mesUrl = "";
let kbcUrl = "";       //快保云服基础框架访问URL

let cryptoConfig = {
  key:'0cc4cbd00a5540c0a1f0ae2ffe13c7cb',//crypto加密密钥
  offset:'444c4b45355145314454494e53555245',//crypto加密偏移量
}

/**
 * 运行环境定义
 * DEV  - 本地开发环境
 * STA  - 对应STA部署环境
 * UAT  - 对应UAT部署环境
 * PROD - 对应生产部署环境
 */
if (process.env.NODE_ENV == "dev") {
  mesUrl = "mock/";
  kbcUrl    = "https://kbc-sta.kbao123.com/";
} else if (process.env.NODE_ENV == "sta") {
  mesUrl = "https://kbc-sta.kbao123.com";
  kbcUrl    = "https://kbc-sta.kbao123.com/";
} else if (process.env.NODE_ENV == "prod") {
  mesUrl = "https://kbc.dtinsure.com";
  kbcUrl    = "https://kbc.dtinsure.com/";
}

export const project = 'kbc-elms'
export const rootPath = process.env.NODE_ENV == 'dev' ? '/mock' : location.origin + '/gateway/kbc-elms';
export const baseUrl = mesUrl;
export const kbcPath = kbcUrl;
export {cryptoConfig};

















