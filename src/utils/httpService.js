import axios from "axios";
import {
  Loading
} from "element-ui";
import router from "../router/index";
import store from "../store/index";
import Vue from "vue";
let _this = new Vue();

let axiosConfig = {
  // `baseURL` 将自动加在 `url` 前面，除非 `url` 是一个绝对 URL。
  // 它可以通过设置一个 `baseURL` 便于为 axios 实例的方法传递相对 URL
  baseURL: "",
  timeout: 15000,
  responseType: "json",
  withCredentials: true, // 是否允许带cookie这些
  headers: {
    "Content-Type": "application/json;charset=utf-8"
  }
}
let loadingInstance = null; //自定义不需要loading
const Axios = axios.create(axiosConfig);
//Axios.defaults.headers.common['Authorization'] = token;
//添加请求拦截器
Axios.interceptors.request.use(
  config => {
    if (!config.noLoading) {
      loadingInstance = Loading.service({
        fullscreen: true,
        lock: false,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0]
      });
    }

    if (sessionStorage.getItem("LoginAccessToken") && sessionStorage.getItem("LoginAccessToken") != "null" && sessionStorage.getItem("LoginAccessToken") != "undefined") {
			config.headers["access_token"] = sessionStorage.getItem("LoginAccessToken")

		}

    if (store.state.layoutStore.currentLoginUser) {
      config.headers["tenantId"] = store.state.layoutStore.currentLoginUser.tenantId;
      config.headers["funcId"] = store.state.layoutStore.currentLoginUser.funcId;
    }
    if (config.ContentType) {
      config.headers["Content-Type"] = config.ContentType;
    }
    // 导入文件 需要转换
    if (config.isImport) {
      config.headers["Content-Type"] = "multipart/form-data";
      config.timeout = Infinity;//上传文件时，时间设置位无线
      //处理文件上传 使用FormData处理 Blob, File，string类型
      let fd = new FormData();
      for (let key in config.data) {
        if (config.data.hasOwnProperty(key)) {
          fd.append(key, config.data[key]);
        }
      }
      config.data = fd;

    }

    // 在发送请求之前做些什么
    return config;
  },
  error => {
    // error 的回调信息
    console.log("request：error", error);
    return Promise.reject(error.data.error.message);
  }
);

//返回状态判断(添加响应拦截器)
Axios.interceptors.response.use(
  async res => {
    if (loadingInstance) {
      loadingInstance.close();
    };

    //resp_code == 0 接口正常返回
    if (res.data.resp_code == 0) {
      //如果有些接口 成功后是不返还任何值的（例如登出接口）前端赋值，以免没有datas
      if (!res.data.datas&&!['',0,false].some(v=>v==res.data.datas)) {
        res.data.datas = res.data.resp_msg ? {
          resp_msg: res.data.resp_msg
        } : 'ok';
      };
      //如果设置接口(noError)不拦截error 返回参数
      if (res.config.noError) {
        return res.data;
      };
      return res.data.datas;
    }
    //resp_code == 1 接口有返回 但是有相应的错误resp_msg
    if (res.data.resp_code == 1) {
      if (!res.config.noError) {
        _this.$message({
          showClose: true,
          message: res.data.resp_msg,
          type: 'error'
        });
        return "";
      } else {
        return res.data;
      };
    }
    // resp_code == 401 登录信息过期 重新获取accessToken
    else if (res.data.resp_code == 401) {
      // 当子系统accessToken过期时将过期的accessToken通过postMessage方法传给父级
      const currentLoginUser = store.state.layoutStore.currentLoginUser;
      // window.parent.postMessage({
      //   msg: currentLoginUser.access_token
      // }, '*');
      //父级会重新获取新的AccessToken回传
      // window.addEventListener('message', async function (e) {
      //   if (e.data.data) {
      //     let lastApiUrl = res.config.url;
      //     let lastApiData = res.config.data;
      //     const access_token = e.data.data;
      //     const tenantId = currentLoginUser.tenantId;
      //     const funcId = currentLoginUser.funcId;
      //     store.commit("layoutStore/setCurrentLoginUser", { access_token, tenantId, funcId })
      //     const lastData = await Axios.post(lastApiUrl, lastApiData);
      //     if (lastData) {
      //        return lastData;
      //     };
      //   }
      // })
      if (window.parent && window.parent.kbcChangeToken) {
          await window.parent.kbcChangeToken(currentLoginUser.access_token)
          let lastApiUrl = res.config.url;
          let lastApiData = res.config.data;
          const access_token = sessionStorage.getItem("LoginAccessToken");
          const tenantId = currentLoginUser.tenantId;
          const funcId = currentLoginUser.funcId;
          store.commit("layoutStore/setCurrentLoginUser", {access_token,tenantId,funcId})
          const lastData = await Axios.post(lastApiUrl, lastApiData);
          if (lastData) {
            return lastData;
          };
      }
    }
  },
  error => {
    //服务器状态码不是200的情况
    //处理404 500之类
    if (loadingInstance) {
      loadingInstance.close();
    };
    if (error.response.status) {
      _this.$message({
        showClose: true,
        message: "请求报错或请求超时！",
        type: "error"
      });
    };
    // 返回 response 里的错误信息
    return Promise.reject(error.response.data);
  }
);

// 对axios的实例重新封装成一个plugin ,方便 Vue.use(xxxx)
/* eslint-disable */
export default {
  Axios,
  install: function (Vue, Option) {
    Object.defineProperty(Vue.prototype, "$http", {
      value: Axios
    });
  }
};