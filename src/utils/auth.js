import CryptoJS from "crypto-js";
import { cryptoConfig } from "@/utils/globalParam";
//检查按钮的权限
export function checkButton(auth) {
  return true;
}
//CBC模式加密
export function encryption(src){
  let key = CryptoJS.enc.Hex.parse(cryptoConfig.key)
  let iv = CryptoJS.enc.Hex.parse(cryptoConfig.offset)
  let enc = CryptoJS.AES.encrypt(src ,key,{
      iv:iv,
      mode: CryptoJS.mode.CBC,  
      padding: CryptoJS.pad.Pkcs7
  })
  let enced = enc.ciphertext.toString()
  return enced;
}

//CBC模式解密
export function decryption(enced){
  let key = CryptoJS.enc.Hex.parse(cryptoConfig.key)
  let iv = CryptoJS.enc.Hex.parse(cryptoConfig.offset)
  let dec = CryptoJS.AES.decrypt(CryptoJS.format.Hex.parse(enced), key,{
      iv:iv,
      mode: CryptoJS.mode.CBC,  
      padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(dec);
}