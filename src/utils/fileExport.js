import axios from "axios";
import store from '../store/index';
import { rootPath } from "@/utils/globalParam";
import { Message } from "element-ui";

/**
 * 导出excel文件
 * fileName 文件的名称 请自带xlsx文件后缀
 * url 请求全路径
 * param 参数
 */
export default async (fileName, param = {}, url) => {

  let currentLoginUser = store.state.layoutStore.currentLoginUser
  return axios
    .post(url, param, {
      responseType: "blob",
      baseURL: rootPath,
      timeout: 10000,
      withCredentials: true, // 是否允许带cookie这些
      headers: {
        // "content-Type": "multipart/form-data",
        access_token: currentLoginUser.accessToken,
        tenantId: currentLoginUser.tenantId,
        funcId: currentLoginUser.funcId,
      }
    })
    .then(res => {
      // console.log(res);
      //返回结果为空
      if (!res) {
        Message.error("发生错误！请重新操作");
        return;
      }
      //返回结果不是200，抛出错误提示
      if (res.status != "200") {
        Message.error("请求发生错误，请联系管理员");
        return;
      }
      // console.log(res);
      const content = res.data;
      const blob = new Blob([content]);
      if ("download" in document.createElement("a")) {
        // 非IE下载
        const elink = document.createElement("a");
        elink.download = fileName;
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName);
      }
    });
};
