import { baseUrl,project} from "@/utils/globalParam";
export const dateFormat = (fmt, date) => {
  let ret;
  date = new Date(date)
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString() // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    };
  };
  return fmt;
}

export const getParamString = (param) => {
  let strHref = window.location.href;     //获取Url字串
  let intPos = strHref.indexOf("?");      // 参数开始位置
  let strRight = strHref.substr(intPos + 1);
  let arrTmp = strRight.split("&"); //参数分割符
  for (let i = 0; i < arrTmp.length; i++) {
    let arrTemp = arrTmp[i].split("=");
    if (arrTemp[0].toUpperCase() == param.toUpperCase()) {
      return arrTemp[1];
    }
  }
  return "";
}

export const stringToObject = (param) =>{
    let arr = param.split("&");
    let objParam={};
    arr.map(item=>{
      let tempArr = item.split("=");
      if(tempArr[1] != undefined && tempArr[1] != null && tempArr[1] != 'undefined' && tempArr[1] != 'null' ){
        objParam[tempArr[0]]=tempArr[1];
      }
    })
    return objParam;
}

export const objectToString = args => {
  let keys = Object.keys(args)
  keys = keys.sort()
  let newArgs = {}
  keys.forEach(function (key) {
    newArgs[key] = args[key]
  })
  let string = ''
  for (let k in newArgs) {
    string += '&' + k + '=' + newArgs[k]
  }
  string = string.substr(1)
  return string
}

/**
 * 根据参数生成url参数字符串
 */
export const raw = args => {
  let keys = Object.keys(args)
  keys = keys.sort()
  let newArgs = {}
  keys.forEach(function (key) {
    newArgs[key] = args[key]
  })
  let string = ''
  for (let k in newArgs) {
    string += '&' + k + '=' + newArgs[k]
  }
  string = string.substr(1)
  return string
}

/** 生成uuid */
export const guid = () => {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  return (s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4());
}

/*替换数组中中两个任意item的位置*/
export const swap = (array, firstIndex, secondIndex) => {
    var tmp = array[secondIndex];
    array[secondIndex] = array[firstIndex];
    array[firstIndex] = tmp;
    return array; 
}

/*动态添加js*/
export const appendJs = function (url, callback) {
  var head = document.getElementsByTagName('head')[0];
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = url;
  if (typeof (callback) == 'function') {
    script.onload = script.onreadystatechange = function () {
      if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
        callback();
        script.onload = script.onreadystatechange = null;
      }
    };
  }
  head.appendChild(script);
}

/**
* 子系统调用父系统的方法 ,用于路由跳转，主要针对子系统之间的跳转
* 跳转对象包含以下参数
* @param obj 调用的全部参数
* @param obj/routePath 必传页面路由
* @param obj/param (如果需要跳转子页面或传参。选填)
openView({
    routePath:"knowledgeList", // 打开标签的路由地址 
    param:{//打开页面的参数，没有可不传
      path:'addKnowledge',//打开父系统标签后，需重新定向的路由地址
      knowledgeId:this.$route.query.id //跳转页面的query参数,该参数为path或routePath的参数
    }
})
*/
export const openView = (obj,_this)=>{
    let {routePath,param} = obj;
    if (window.parent && window.parent.kbcHopRouting) {
        let routeObj = {
            rootPath:project, // 项目跟路径 
            routePath, // 项目路由地址 
        }
        if(param){
            //如果当前页面只是纯粹为了跳转不涉及相关参数可不传
            //如果需要带上某个来源路由的参数需要拼接成下面这种格式
            // !! 注意 https://kbc-sta.kbao123.com 域名地址做成环境变量的形式 区分 sta uat prod环境
            param = encodeURIComponent(raw(param))
            routeObj.postChildUrl=`iframe?iframeUrl=${baseUrl}/kbc-ews/${routePath}?from=iframe&param=${param}`
        }
        window.parent.kbcHopRouting(routeObj)
    }else{
        let routeObj = {
            path:obj.param&&obj.param.path?obj.param.path:obj.routePath,
        }
        if(obj.param&&obj.param.path){
          delete obj.param['path']
        }
        routeObj.query = obj.param;
        _this.$router.push(routeObj)
    }
}
//复制功能
export const copyText = (text,_this) => {
  // 数字没有 .length 不能执行selectText 需要转化成字符串
  const textString = text.toString();
  localStorage.setItem('copyObj',textString)
  let input = document.querySelector('#copy-input');
  if (!input) {
    input = document.createElement('input');
    input.id = "copy-input";
    input.readOnly = "readOnly";        // 防止ios聚焦触发键盘事件
    input.style.position = "absolute";
    input.style.left = "-1000px";
    input.style.zIndex = "-1000";
    document.body.appendChild(input)
  }

  input.value = textString;
  // ios必须先选中文字且不支持 input.select();
  selectText(input, 0, textString.length);
  if (document.execCommand('copy')) {
    document.execCommand('copy');
    if(_this){
      _this.$message('复制成功');
    }
  }
  input.blur();

  // input自带的select()方法在苹果端无法进行选择，所以需要自己去写一个类似的方法
  // 选择文本。createTextRange(setSelectionRange)是input方法
  function selectText(textbox, startIndex, stopIndex) {
    if (textbox.createTextRange) {//ie
      const range = textbox.createTextRange();
      range.collapse(true);
      range.moveStart('character', startIndex);//起始光标
      range.moveEnd('character', stopIndex - startIndex);//结束光标
      range.select();//不兼容苹果
    } else {//firefox/chrome
      textbox.setSelectionRange(startIndex, stopIndex);
      textbox.focus();
    }
  }
};
//获取复制的内容
export const getCopyText = () => {
  return localStorage.getItem('copyObj')
  // let input = document.querySelector('#copy-input');
  // return input?document.getElementById('copy-input').value:'';
}
//将颜色转换为rgba格式
// colorToRgba('#fff',0.8);//输出rgba(255,255,255,0.8);
export const colorToRgba = (color,opacity) =>{
  let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;  
  color = String(color);//
  if(color && reg.test(color)){  
      if(color.length === 4){  
          var colorNew = "#";  
          for(var i=1; i<4; i+=1){  
              colorNew += color.slice(i,i+1).concat(color.slice(i,i+1));     
          }  
          color = colorNew;  
      }  
      //处理六位的颜色值  
      var colorChange = [];  
      for(var i=1; i<7; i+=2){  
          colorChange.push(parseInt("0x"+color.slice(i,i+2)));    
      }  
      return "rgba(" + colorChange.join(",") + ","+opacity+")"; 
  }else if(color.indexOf('rgb(')==0||color.indexOf('RGB(')==0){
    return color.replace(')',`,${opacity})`).replace(/rgb|RGB/g,"rgba")
  }else{  
      return color;    
  }  
}
export const formatDate = (t, str) => {//格式化时间
  t = t||new Date();
  let  obj = {
      YYYY: t.getFullYear(),
      YY: ("" + t.getFullYear()).slice(-2),
      M: t.getMonth() + 1,
      MM: ("0" + (t.getMonth() + 1)).slice(-2),
      D: t.getDate(),
      DD: ("0" + t.getDate()).slice(-2),
      H: t.getHours(),
      HH: ("0" + t.getHours()).slice(-2),
      h: t.getHours() % 12,
      hh: ("0" + t.getHours() % 12).slice(-2),
      m: t.getMinutes(),
      mm: ("0" + t.getMinutes()).slice(-2),
      s: t.getSeconds(),
      ss: ("0" + t.getSeconds()).slice(-2),
      w: ['日', '一', '二', '三', '四', '五', '六'][t.getDay()]
  };
  return str.replace(/([a-z]+)/ig, function ($1) {
      return obj[$1]
  });
}

//计算两个日期之间经过了多少天：
export const getDaysBetweenDates=(date1, date2) =>{
  const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数

  // 将日期字符串转换为日期对象
  const startDate = new Date(date1);
  const endDate = new Date(date2);

  // 计算日期之间的天数差
  const diffDays = Math.round(Math.abs((startDate - endDate) / oneDay));

  return diffDays;
}
