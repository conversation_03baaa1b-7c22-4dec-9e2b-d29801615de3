import _ from "lodash";
const state = { 
  themeObj: {
    color: "#D7A256",
    tableBtnActiveColor: "#D7A256",
    navTagUnselectedColor: "#FFF6E8",
    text: ""
  },
 
  // 当前所选的租户对象
  activeTenantObj: {},
  //待缓存的页面路由名称
  cacheArrName: [],
  //不需要缓存的页面路由名称
  notCacheArrName: [],
  //保存缓存页面的scroll
  cacheArrScrollTop:{},
  //权限控制
  authSet: [],
  //权限控制对象集合
  authSetObj: [],
  //获取主系统的参数
  currentLoginUser:{}
};
const getters = {
  getThemeObj: state => {
    return state.themeObj;
  },
  getAuthSet: state => {
    return state.authSet;
  },
  getAuthSetObj: state => {
    return state.authSetObj;
  },
  getCurrentLoginUser: state => {
    return state.currentLoginUser;
  },
};

const mutations = {
  setAuthSet(state, data) {
    state.authSet = data
  },
  setAuthSetObj(state, data) {
    state.authSetObj = data
  },
  setThemeObj(state, data) {
    state.themeObj = data;
  },
  setCurrentLoginUser(state,data){
    state.currentLoginUser = data
  },
  //给currentLoginUser对象添加参数
  addCurrentLoginUser(state,data){
    state.currentLoginUser = {...state.currentLoginUser,...data}
  },
  //动态设置需要缓存的页面
  setCacheArr(state, data) {
    // data包含两个字段  statue add为添加 del为删除 clear清空缓存列表(主要针对切换用户)
    if (data.status == "add") {
      if (!_.find(state.cacheArrName, ["name", data.routeObj.name])) {
        state.cacheArrName.push(data.routeObj.name)
        _.remove(state.notCacheArrName, function (n) {
          return n == data.routeObj.name;
        });
      }
    } else if (data.status == "del") {
      if (!_.find(state.notCacheArrName, ["name", data.routeObj.name])) {
        state.notCacheArrName.push(data.routeObj.name)
        _.remove(state.cacheArrName, function (n) {
          return n == data.routeObj.name;
        });
        delete state.cacheArrScrollTop[data.routeObj.name];
      }
    } else if (data.status == "clear") {
      state.notCacheArrName = [];
      state.cacheArrName = [];
      state.cacheArrScrollTop = {};
    }
  },
  //设置滚动条缓存
  setCacheArrScrollTop(state,data){
    state.cacheArrScrollTop[data.name]=data.scrollTop;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations
};