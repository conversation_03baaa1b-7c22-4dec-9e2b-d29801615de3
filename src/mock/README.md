# MOCK数据统一管理

## 概述

本项目采用统一的MOCK数据管理方式，将所有演示系统的MOCK数据按照模块分类，统一存放在 `src/mock/` 目录下进行管理。

## 目录结构

```
src/mock/
├── index.js                    # 统一入口文件，导出所有MOCK数据
├── README.md                   # 说明文档
└── modules/                    # 按模块分类的MOCK数据
    ├── questionnaire.json      # 问卷模块
    ├── basicConfig/           # 基础配置模块
    │   ├── employeeBenefitProcess.js
    │   ├── industryRiskConfig.json
    │   ├── industryRiskMatrix.json
    │   ├── dicItems.json
    │   ├── onlineProductConfig.json
    │   ├── enterpriseLimitRule.json
    │   ├── enterpriseTemplate.json
    │   └── templates.js
    ├── formulaEngine/         # 公式引擎模块
    │   └── formulas.js
    ├── riskMatrix/           # 风险矩阵模块
    │   └── riskMatrix.js
    ├── constantSetting/      # 常数设置模块
    │   └── constants.js
    └── gpRule/              # GP规则模块
        └── gpruleCfg.json
```

## 模块说明

### 1. 问卷模块 (questionnaire)
- **文件**: `modules/questionnaire.json`
- **内容**: 问卷数据，包含CDE类和AB类企业的风险管理能力成熟度自评问卷
- **使用**: 问卷填写和评估功能

### 2. 基础配置模块 (basicConfig)
- **目录**: `modules/basicConfig/`
- **内容**: 
  - 员工福利流程配置
  - 行业风险配置
  - 字典项配置
  - 线上产品配置
  - 企业限额规则
  - 企业模板配置
  - 数据模板和服务流程配置

### 3. 公式引擎模块 (formulaEngine)
- **文件**: `modules/formulaEngine/formulas.js`
- **内容**: 风险管理公式和分类数据
- **使用**: 公式计算和评估功能

### 4. 风险矩阵模块 (riskMatrix)
- **文件**: `modules/riskMatrix/riskMatrix.js`
- **内容**: 风险矩阵和评分项数据
- **使用**: 风险矩阵展示和评分功能

### 5. 常数设置模块 (constantSetting)
- **文件**: `modules/constantSetting/constants.js`
- **内容**: 系统常数和分类数据
- **使用**: 系统参数配置功能

### 6. GP规则模块 (gpRule)
- **文件**: `modules/gpRule/gpruleCfg.json`
- **内容**: GP规则配置数据
- **使用**: 规则引擎功能

## 使用方式

### 1. 统一导入
```javascript
import { 
  questionnaireMock,
  employeeBenefitProcessMock,
  mockFormulas,
  mockRiskMatrixList,
  mockConstants,
  gpruleCfgMock,
  mockDelay
} from '@/mock'
```

### 2. 模块化导入
```javascript
// 导入特定模块的MOCK数据
import { questionnaireMock } from '@/mock/modules/questionnaire.json'
import { mockFormulas } from '@/mock/modules/formulaEngine/formulas.js'
```

## 数据格式规范

### JSON文件格式
- 使用标准的JSON格式
- 包含必要的元数据（创建时间、更新时间、创建用户等）
- 数据结构清晰，便于维护

### JS文件格式
- 使用ES6模块导出
- 包含详细的注释说明
- 支持复杂的数据结构和函数

## 维护指南

### 1. 添加新的MOCK数据
1. 在对应的模块目录下创建新文件
2. 在 `src/mock/index.js` 中添加导出
3. 更新相关API文件中的引用

### 2. 修改现有MOCK数据
1. 直接修改对应的MOCK数据文件
2. 确保数据结构的一致性
3. 更新相关的API调用

### 3. 删除MOCK数据
1. 删除对应的MOCK数据文件
2. 从 `src/mock/index.js` 中移除导出
3. 更新相关API文件中的引用

## 注意事项

1. **数据一致性**: 确保MOCK数据与实际业务逻辑一致
2. **版本管理**: 重要数据变更需要记录版本信息
3. **性能考虑**: 大型MOCK数据文件建议使用懒加载
4. **类型安全**: 建议使用TypeScript定义数据类型

## 迁移计划

### 第一阶段：创建统一目录结构
- [x] 创建 `src/mock/` 目录
- [x] 创建模块化子目录
- [x] 迁移现有MOCK数据

### 第二阶段：更新API引用
- [ ] 更新所有API文件中的MOCK数据引用
- [ ] 删除原有的分散MOCK数据文件
- [ ] 测试所有功能正常

### 第三阶段：优化和完善
- [ ] 添加数据类型定义
- [ ] 完善文档说明
- [ ] 建立数据维护规范

## 版本信息

- **版本**: 1.0.0
- **最后更新**: 2024-01-15
- **维护者**: 开发团队 