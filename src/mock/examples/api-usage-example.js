// API使用示例 - 展示如何使用统一的MOCK数据管理

// 方式1：统一导入所有MOCK数据
import { 
  questionnaireMock,
  employeeBenefitProcessMock,
  mockFormulas,
  mockRiskMatrixList,
  mockConstants,
  gpruleCfgMock,
  mockDelay
} from '@/mock'

// 方式2：按模块导入特定MOCK数据
import { questionnaireMock } from '@/mock/modules/questionnaire.json'
import { mockFormulas, mockCategories } from '@/mock/modules/formulaEngine/formulas.js'
import { mockRiskMatrixList } from '@/mock/modules/riskMatrix/riskMatrix.js'

// 示例1：问卷模块API
export const getQuestionnaireData = async (params = {}) => {
  await mockDelay();
  
  const { enterpriseType = 'CDE' } = params;
  const questionnaires = questionnaireMock.questionnaires;
  
  // 根据企业类型获取对应的问卷
  let questionnaire = null;
  
  if (enterpriseType === 'CDE' || enterpriseType.includes('C') || enterpriseType.includes('D') || enterpriseType.includes('E')) {
    questionnaire = questionnaires.CDE;
  } else if (enterpriseType === 'AB' || enterpriseType.includes('A') || enterpriseType.includes('B')) {
    questionnaire = questionnaires.AB;
  } else {
    questionnaire = questionnaires.CDE;
  }
  
  return {
    code: 200,
    message: "success",
    data: questionnaire
  };
};

// 示例2：公式引擎API
export const getFormulaList = async (params = {}) => {
  await mockDelay();
  
  let filteredData = [...mockFormulas];
  
  // 根据参数过滤数据
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(params.name.toLowerCase())
    );
  }
  
  return {
    code: 200,
    message: "success",
    data: {
      list: filteredData,
      total: filteredData.length,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    }
  };
};

// 示例3：风险矩阵API
export const getRiskMatrixList = async (params = {}) => {
  await mockDelay();
  
  let filteredData = [...mockRiskMatrixList];
  
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  return {
    code: 200,
    message: "success",
    data: {
      list: filteredData,
      total: filteredData.length,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    }
  };
};

// 示例4：常数设置API
export const getConstantList = async (params = {}) => {
  await mockDelay();
  
  let filteredData = [...mockConstants];
  
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  return {
    code: 200,
    message: "success",
    data: {
      list: filteredData,
      total: filteredData.length,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    }
  };
};

// 示例5：基础配置API
export const getEmployeeBenefitProcess = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: employeeBenefitProcessMock
  };
};

// 示例6：GP规则API
export const getGpRuleList = async (params = {}) => {
  await mockDelay();
  
  const rules = gpruleCfgMock.rules;
  let filteredData = [...rules];
  
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  return {
    code: 200,
    message: "success",
    data: {
      list: filteredData,
      total: filteredData.length,
      categories: gpruleCfgMock.categories,
      operators: gpruleCfgMock.operators,
      actions: gpruleCfgMock.actions
    }
  };
};

// 示例7：组合使用多个MOCK数据
export const getDashboardData = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: {
      questionnaireCount: Object.keys(questionnaireMock.questionnaires).length,
      formulaCount: mockFormulas.length,
      riskMatrixCount: mockRiskMatrixList.length,
      constantCount: mockConstants.length,
      recentUpdates: [
        {
          type: 'questionnaire',
          count: Object.keys(questionnaireMock.questionnaires).length,
          lastUpdate: '2024-01-15'
        },
        {
          type: 'formula',
          count: mockFormulas.length,
          lastUpdate: '2024-01-15'
        },
        {
          type: 'riskMatrix',
          count: mockRiskMatrixList.length,
          lastUpdate: '2024-01-15'
        }
      ]
    }
  };
};

// 示例8：数据转换和格式化
export const getFormattedQuestionnaireData = async (enterpriseType) => {
  await mockDelay();
  
  const questionnaires = questionnaireMock.questionnaires;
  const questionnaire = questionnaires[enterpriseType] || questionnaires.CDE;
  
  // 转换数据结构以匹配组件期望的格式
  const transformedData = {
    id: questionnaire.id,
    title: questionnaire.title,
    description: questionnaire.description,
    enterpriseTypes: questionnaire.enterpriseTypes,
    questions: questionnaire.questions.map(q => ({
      ...q,
      title: q.question, // 将question字段映射为title
      type: 'single' // 默认都是单选题
    }))
  };
  
  return {
    code: 200,
    message: "success",
    data: transformedData
  };
};

// 示例9：错误处理
export const getMockDataWithErrorHandling = async (params = {}) => {
  try {
    await mockDelay();
    
    // 模拟可能的错误情况
    if (params.forceError) {
      throw new Error('模拟的错误');
    }
    
    return {
      code: 200,
      message: "success",
      data: mockFormulas
    };
  } catch (error) {
    return {
      code: 500,
      message: error.message || "服务器错误",
      data: null
    };
  }
};

// 示例10：分页和排序
export const getPaginatedMockData = async (params = {}) => {
  await mockDelay();
  
  const { pageNum = 1, pageSize = 10, sortBy = 'id', sortOrder = 'asc' } = params;
  
  let data = [...mockFormulas];
  
  // 排序
  data.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
  
  // 分页
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = data.slice(startIndex, endIndex);
  
  return {
    code: 200,
    message: "success",
    data: {
      list: paginatedData,
      total: data.length,
      pageNum,
      pageSize,
      totalPages: Math.ceil(data.length / pageSize)
    }
  };
}; 