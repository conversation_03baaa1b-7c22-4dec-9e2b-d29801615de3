// 员工福利保障流程配置MOCK数据
export const employeeBenefitProcess = {
  "processInfo": {
    "id": "emp_benefit_001",
    "name": "员工福利保障流程",
    "code": "EMPLOYEE_BENEFIT_PROCESS",
    "description": "员工福利保障服务流程配置，包含需求收集、方案设计、审批确认、合同签署、服务实施等环节",
    "category": "员工福利",
    "status": 1,
    "version": "1.0.0",
    "createTime": "2024-01-15 10:30:00",
    "updateTime": "2024-01-15 10:30:00",
    "createUser": "admin",
    "updateUser": "admin"
  },
  "nodeList": [
    {
      "nodeId": "start_001",
      "nodeName": "开始",
      "nodeType": "-1",
      "type": "-1",
      "logImg": "el-icon-video-play",
      "positionLeft": "100px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "流程开始节点",
        "autoStart": true,
        "timeout": 0
      }
    },
    {
      "nodeId": "requirement_001",
      "nodeName": "需求收集",
      "nodeType": "requirement",
      "type": "requirement",
      "logImg": "el-icon-document",
      "positionLeft": "300px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "收集客户员工福利需求信息",
        "estimatedTime": 2,
        "unit": "天",
        "required": true,
        "assignee": "客户经理",
        "formFields": [
          {
            "fieldName": "company_name",
            "fieldDesc": "企业名称",
            "fieldType": "input",
            "required": true
          },
          {
            "fieldName": "employee_count",
            "fieldDesc": "员工人数",
            "fieldType": "number",
            "required": true
          },
          {
            "fieldName": "benefit_requirements",
            "fieldDesc": "福利需求",
            "fieldType": "textarea",
            "required": true
          },
          {
            "fieldName": "budget_range",
            "fieldDesc": "预算范围",
            "fieldType": "select",
            "options": ["10万以下", "10-50万", "50-100万", "100万以上"],
            "required": true
          }
        ]
      }
    },
    {
      "nodeId": "kyc_001",
      "nodeName": "企业KYC查询",
      "nodeType": "kyc",
      "type": "kyc",
      "logImg": "el-icon-search",
      "positionLeft": "500px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "对企业进行KYC合规查询",
        "estimatedTime": 1,
        "unit": "天",
        "required": true,
        "assignee": "合规专员",
        "queryTypes": [
          "企业基本信息",
          "经营状况",
          "信用记录",
          "风险等级"
        ],
        "autoQuery": true
      }
    },
    {
      "nodeId": "design_001",
      "nodeName": "方案设计",
      "nodeType": "design",
      "type": "design",
      "logImg": "el-icon-edit",
      "positionLeft": "700px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "设计员工福利保障方案",
        "estimatedTime": 3,
        "unit": "天",
        "required": true,
        "assignee": "产品经理",
        "benefitTypes": [
          "医疗保险",
          "意外保险",
          "养老保险",
          "失业保险",
          "住房公积金"
        ],
        "customizable": true
      }
    },
    {
      "nodeId": "risk_001",
      "nodeName": "风险评估",
      "nodeType": "risk",
      "type": "risk",
      "logImg": "el-icon-warning-outline",
      "positionLeft": "900px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "评估企业风险等级",
        "estimatedTime": 2,
        "unit": "天",
        "required": true,
        "assignee": "风控专员",
        "riskFactors": [
          "行业风险",
          "经营风险",
          "财务风险",
          "合规风险"
        ],
        "riskLevels": ["低风险", "中风险", "高风险"]
      }
    },
    {
      "nodeId": "approval_001",
      "nodeName": "方案审批",
      "nodeType": "approval",
      "type": "approval",
      "logImg": "el-icon-s-check",
      "positionLeft": "1100px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "审批员工福利保障方案",
        "estimatedTime": 1,
        "unit": "天",
        "required": true,
        "assignee": "部门经理",
        "approvalLevel": 2,
        "approvers": [
          "产品经理",
          "风控经理",
          "业务总监"
        ]
      }
    },
    {
      "nodeId": "confirmation_001",
      "nodeName": "方案确认",
      "nodeType": "confirmation",
      "type": "confirmation",
      "logImg": "el-icon-s-opportunity",
      "positionLeft": "1300px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "与客户确认方案细节",
        "estimatedTime": 1,
        "unit": "天",
        "required": true,
        "assignee": "客户经理",
        "confirmationItems": [
          "保障范围",
          "保费金额",
          "服务条款",
          "理赔流程"
        ]
      }
    },
    {
      "nodeId": "contract_001",
      "nodeName": "合同签署",
      "nodeType": "contract",
      "type": "contract",
      "logImg": "el-icon-document-copy",
      "positionLeft": "1500px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "签署员工福利保障合同",
        "estimatedTime": 1,
        "unit": "天",
        "required": true,
        "assignee": "法务专员",
        "contractTypes": [
          "团体医疗保险合同",
          "团体意外保险合同",
          "团体养老保险合同"
        ],
        "autoGenerate": true
      }
    },
    {
      "nodeId": "implementation_001",
      "nodeName": "服务实施",
      "nodeType": "implementation",
      "type": "implementation",
      "logImg": "el-icon-s-operation",
      "positionLeft": "1700px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "实施员工福利保障服务",
        "estimatedTime": 5,
        "unit": "天",
        "required": true,
        "assignee": "服务专员",
        "implementationSteps": [
          "保单生效",
          "员工信息录入",
          "服务培训",
          "理赔服务"
        ],
        "qualityCheck": true
      }
    },
    {
      "nodeId": "end_001",
      "nodeName": "结束",
      "nodeType": "-2",
      "type": "-2",
      "logImg": "el-icon-video-pause",
      "positionLeft": "1900px",
      "positionTop": "200px",
      "fixed": false,
      "config": {
        "description": "流程结束节点",
        "autoComplete": true
      }
    }
  ],
  "connectionList": [
    {
      "id": "conn_001",
      "sourceId": "start_001",
      "targetId": "requirement_001",
      "condition": "always"
    },
    {
      "id": "conn_002",
      "sourceId": "requirement_001",
      "targetId": "kyc_001",
      "condition": "requirement_complete"
    },
    {
      "id": "conn_003",
      "sourceId": "kyc_001",
      "targetId": "design_001",
      "condition": "kyc_pass"
    },
    {
      "id": "conn_004",
      "sourceId": "design_001",
      "targetId": "risk_001",
      "condition": "design_complete"
    },
    {
      "id": "conn_005",
      "sourceId": "risk_001",
      "targetId": "approval_001",
      "condition": "risk_assessment_complete"
    },
    {
      "id": "conn_006",
      "sourceId": "approval_001",
      "targetId": "confirmation_001",
      "condition": "approval_pass"
    },
    {
      "id": "conn_007",
      "sourceId": "confirmation_001",
      "targetId": "contract_001",
      "condition": "confirmation_agree"
    },
    {
      "id": "conn_008",
      "sourceId": "contract_001",
      "targetId": "implementation_001",
      "condition": "contract_signed"
    },
    {
      "id": "conn_009",
      "sourceId": "implementation_001",
      "targetId": "end_001",
      "condition": "implementation_complete"
    }
  ]
} 