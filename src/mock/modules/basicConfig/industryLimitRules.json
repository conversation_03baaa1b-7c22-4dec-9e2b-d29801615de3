[{"id": 1, "name": "企业类型限额规则", "code": "ENTERPRISE_TYPE_LIMIT", "description": "根据企业类型设置不同的限额规则，动态添加企业字段，设置匹配规则，指定可用服务流程", "conditions": [{"field": "enterpriseType", "operator": "eq", "value": "A", "description": "企业类型等于A类"}, {"field": "employeeCount", "operator": "range", "value": "5000-10000", "description": "员工数量在5000-10000之间"}], "actionList": [{"type": "applyRule", "serviceIds": ["1", "2"]}, {"type": "noAction", "serviceIds": ["3"]}], "status": 1, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 2, "name": "行业风险限制规则", "code": "INDUSTRY_RISK_LIMIT", "description": "根据行业风险等级设置不同的限制规则", "conditions": [{"field": "industryCode", "operator": "eq", "value": "FINANCE", "description": "行业代码等于金融"}], "actionList": [{"type": "applyRule", "serviceIds": ["2", "3"]}], "status": 1, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}]