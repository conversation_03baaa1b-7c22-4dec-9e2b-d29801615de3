// 基础配置模板MOCK数据
export const mockDataTemplates = [
  {
    id: 1,
    name: '企业信息模板',
    code: 'ENTERPRISE_INFO',
    description: '企业基本信息字段配置',
    category: '企业信息',
    status: 1,
    fields: [
      {
        id: 1,
        name: '企业名称',
        code: 'enterprise_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '企业全称'
      },
      {
        id: 2,
        name: '企业类型',
        code: 'enterprise_type',
        type: 'select',
        required: true,
        options: ['A类', 'B类', 'C类', 'D类', 'E类'],
        description: '企业分类类型'
      },
      {
        id: 3,
        name: '注册资本',
        code: 'registered_capital',
        type: 'number',
        required: false,
        unit: '万元',
        description: '企业注册资本'
      },
      {
        id: 4,
        name: '成立时间',
        code: 'establish_date',
        type: 'date',
        required: false,
        description: '企业成立日期'
      },
      {
        id: 5,
        name: '经营范围',
        code: 'business_scope',
        type: 'textarea',
        required: false,
        maxLength: 500,
        description: '企业经营范围'
      },
      {
        id: 11,
        name: '控股股东',
        code: 'major_shareholder',
        type: 'object',
        required: false,
        relatedTemplateId: 3,
        description: '企业控股股东信息'
      },
      {
        id: 12,
        name: '股东列表',
        code: 'shareholders',
        type: 'objectArray',
        required: false,
        relatedTemplateId: 3,
        description: '企业所有股东信息列表'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '客户机会模板',
    code: 'CUSTOMER_OPPORTUNITY',
    description: '客户机会信息字段配置',
    category: '客户机会',
    status: 1,
    fields: [
      {
        id: 6,
        name: '机会名称',
        code: 'opportunity_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '客户机会名称'
      },
      {
        id: 7,
        name: '客户名称',
        code: 'customer_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '客户企业名称'
      },
      {
        id: 8,
        name: '机会类型',
        code: 'opportunity_type',
        type: 'select',
        required: true,
        options: ['员工福利', '企业保障', '线上产品'],
        description: '机会分类'
      },
      {
        id: 9,
        name: '预计金额',
        code: 'expected_amount',
        type: 'number',
        required: false,
        unit: '万元',
        description: '预计合同金额'
      },
      {
        id: 10,
        name: '机会状态',
        code: 'opportunity_status',
        type: 'select',
        required: true,
        options: ['初步接触', '需求确认', '方案制定', '商务谈判', '合同签署'],
        description: '机会当前状态'
      },
      {
        id: 11,
        name: '备注信息',
        code: 'remarks',
        type: 'textarea',
        required: false,
        maxLength: 200,
        description: '机会备注信息'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: '股东信息模板',
    code: 'SHAREHOLDER_TEMPLATE',
    description: '股东信息收集模板',
    category: '股东信息',
    status: 1,
    fields: [
      {
        id: 17,
        name: '股东名称',
        code: 'shareholder_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '股东全称'
      },
      {
        id: 18,
        name: '股东类型',
        code: 'shareholder_type',
        type: 'select',
        required: true,
        options: ['个人', '企业', '政府', '其他组织'],
        description: '股东类型'
      },
      {
        id: 19,
        name: '持股比例',
        code: 'shareholding_ratio',
        type: 'number',
        required: true,
        unit: '%',
        description: '股东持股比例'
      },
      {
        id: 20,
        name: '出资金额',
        code: 'investment_amount',
        type: 'number',
        required: false,
        unit: '万元',
        description: '股东出资金额'
      },
      {
        id: 21,
        name: '证件类型',
        code: 'id_type',
        type: 'select',
        required: false,
        options: ['身份证', '护照', '营业执照', '组织机构代码证'],
        description: '股东证件类型'
      },
      {
        id: 22,
        name: '证件号码',
        code: 'id_number',
        type: 'text',
        required: false,
        maxLength: 50,
        description: '股东证件号码'
      },
      {
        id: 23,
        name: '联系方式',
        code: 'contact_info',
        type: 'text',
        required: false,
        maxLength: 50,
        description: '股东联系方式'
      },
      {
        id: 24,
        name: '入股时间',
        code: 'investment_date',
        type: 'date',
        required: false,
        description: '股东入股时间'
      },
      {
        id: 25,
        name: '备注',
        code: 'remarks',
        type: 'textarea',
        required: false,
        maxLength: 200,
        description: '股东信息备注'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 4,
    name: '雇主责任险投保信息模板',
    code: 'EMPLOYER_LIABILITY_INSURANCE',
    description: '雇主责任险投保信息字段配置',
    category: '企业保障',
    status: 1,
    fields: [
      {
        id: 26,
        name: '投保企业名称',
        code: 'insured_enterprise_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '投保企业全称'
      },
      {
        id: 27,
        name: '统一社会信用代码',
        code: 'social_credit_code',
        type: 'text',
        required: true,
        maxLength: 18,
        description: '企业统一社会信用代码'
      },
      {
        id: 28,
        name: '保险期限',
        code: 'insurance_period',
        type: 'select',
        required: true,
        options: ['1年', '2年', '3年', '5年', '10年'],
        description: '保险合同期限'
      },
      {
        id: 29,
        name: '生效日期',
        code: 'effective_date',
        type: 'date',
        required: true,
        description: '保险生效日期'
      },
      {
        id: 30,
        name: '终止日期',
        code: 'expiry_date',
        type: 'date',
        required: true,
        description: '保险终止日期'
      },
      {
        id: 31,
        name: '员工总人数',
        code: 'employee_count',
        type: 'number',
        required: true,
        description: '企业员工总人数'
      },
      {
        id: 32,
        name: '保险费率',
        code: 'premium_rate',
        type: 'number',
        required: true,
        unit: '‰',
        description: '每千元工资的保险费率'
      },
      {
        id: 33,
        name: '年工资总额',
        code: 'annual_payroll',
        type: 'number',
        required: true,
        unit: '万元',
        description: '企业年工资总额'
      },
      {
        id: 34,
        name: '医疗费用赔偿限额',
        code: 'medical_expense_limit',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人医疗费用赔偿限额'
      },
      {
        id: 35,
        name: '死亡伤残赔偿限额',
        code: 'death_disability_limit',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人死亡或伤残赔偿限额'
      },
      {
        id: 36,
        name: '累计赔偿限额',
        code: 'aggregate_limit',
        type: 'number',
        required: true,
        unit: '万元',
        description: '累计最高赔偿限额'
      },
      {
        id: 37,
        name: '行业类别',
        code: 'industry_category',
        type: 'select',
        required: true,
        options: ['第一类', '第二类', '第三类', '特殊行业'],
        description: '企业行业风险类别'
      },
      {
        id: 38,
        name: '免赔额',
        code: 'deductible',
        type: 'number',
        required: false,
        unit: '元/次',
        description: '每次事故免赔额'
      },
      {
        id: 39,
        name: '附加险选择',
        code: 'additional_coverage',
        type: 'multiSelect',
        required: false,
        options: ['24小时扩展责任', '职业病责任', '法律费用补偿', '误工费用补偿'],
        description: '附加险种选择'
      },
      {
        id: 40,
        name: '特别约定',
        code: 'special_agreements',
        type: 'textarea',
        required: false,
        maxLength: 500,
        description: '保险合同特别约定事项'
      }
    ],
    createTime: '2024-05-20 14:30:00',
    updateTime: '2024-05-20 14:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 5,
    name: '团体意外险投保信息模板',
    code: 'GROUP_ACCIDENT_INSURANCE',
    description: '团体意外险投保信息字段配置',
    category: '企业保障',
    status: 1,
    fields: [
      {
        id: 41,
        name: '投保企业名称',
        code: 'insured_enterprise_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '投保企业全称'
      },
      {
        id: 42,
        name: '统一社会信用代码',
        code: 'social_credit_code',
        type: 'text',
        required: true,
        maxLength: 18,
        description: '企业统一社会信用代码'
      },
      {
        id: 43,
        name: '保险期限',
        code: 'insurance_period',
        type: 'select',
        required: true,
        options: ['1年', '2年', '3年'],
        description: '保险合同期限'
      },
      {
        id: 44,
        name: '生效日期',
        code: 'effective_date',
        type: 'date',
        required: true,
        description: '保险生效日期'
      },
      {
        id: 45,
        name: '终止日期',
        code: 'expiry_date',
        type: 'date',
        required: true,
        description: '保险终止日期'
      },
      {
        id: 46,
        name: '投保人数',
        code: 'insured_count',
        type: 'number',
        required: true,
        description: '团体投保总人数'
      },
      {
        id: 47,
        name: '投保人员清单',
        code: 'insured_list',
        type: 'file',
        required: true,
        fileTypes: ['.xlsx', '.csv'],
        description: '投保人员名单（Excel或CSV格式）'
      },
      {
        id: 48,
        name: '意外身故保额',
        code: 'death_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人意外身故保险金额'
      },
      {
        id: 49,
        name: '意外伤残保额',
        code: 'disability_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人意外伤残保险金额'
      },
      {
        id: 50,
        name: '意外医疗保额',
        code: 'medical_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人意外医疗保险金额'
      },
      {
        id: 51,
        name: '保险类型',
        code: 'insurance_type',
        type: 'select',
        required: true,
        options: ['综合保障型', '意外保障型', '高额保障型'],
        description: '团体意外险类型'
      },
      {
        id: 52,
        name: '保险费率',
        code: 'premium_rate',
        type: 'number',
        required: true,
        unit: '‰',
        description: '每千元保额的保险费率'
      },
      {
        id: 53,
        name: '总保险费',
        code: 'total_premium',
        type: 'number',
        required: true,
        unit: '元',
        description: '团体意外险总保险费'
      },
      {
        id: 54,
        name: '免赔额',
        code: 'deductible',
        type: 'number',
        required: false,
        unit: '元/次',
        description: '每次事故免赔额'
      },
      {
        id: 55,
        name: '附加险选择',
        code: 'additional_coverage',
        type: 'multiSelect',
        required: false,
        options: ['住院津贴', '交通工具意外', '疾病身故', '医疗补充'],
        description: '附加险种选择'
      },
      {
        id: 56,
        name: '特别约定',
        code: 'special_agreements',
        type: 'textarea',
        required: false,
        maxLength: 500,
        description: '保险合同特别约定事项'
      }
    ],
    createTime: '2024-05-20 14:45:00',
    updateTime: '2024-05-20 14:45:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 6,
    name: '境外意外险投保信息模板',
    code: 'OVERSEAS_ACCIDENT_INSURANCE',
    description: '境外意外险投保信息字段配置',
    category: '企业保障',
    status: 1,
    fields: [
      {
        id: 57,
        name: '投保企业名称',
        code: 'insured_enterprise_name',
        type: 'text',
        required: true,
        maxLength: 100,
        description: '投保企业全称'
      },
      {
        id: 58,
        name: '统一社会信用代码',
        code: 'social_credit_code',
        type: 'text',
        required: true,
        maxLength: 18,
        description: '企业统一社会信用代码'
      },
      {
        id: 59,
        name: '保险期限',
        code: 'insurance_period',
        type: 'select',
        required: true,
        options: ['7天', '15天', '1个月', '3个月', '6个月', '1年'],
        description: '保险合同期限'
      },
      {
        id: 60,
        name: '生效日期',
        code: 'effective_date',
        type: 'date',
        required: true,
        description: '保险生效日期'
      },
      {
        id: 61,
        name: '终止日期',
        code: 'expiry_date',
        type: 'date',
        required: true,
        description: '保险终止日期'
      },
      {
        id: 62,
        name: '投保人数',
        code: 'insured_count',
        type: 'number',
        required: true,
        description: '境外出行总人数'
      },
      {
        id: 63,
        name: '投保人员清单',
        code: 'insured_list',
        type: 'file',
        required: true,
        fileTypes: ['.xlsx', '.csv'],
        description: '投保人员名单（Excel或CSV格式）'
      },
      {
        id: 64,
        name: '出行国家/地区',
        code: 'destination_countries',
        type: 'multiSelect',
        required: true,
        options: ['亚洲', '欧洲', '北美洲', '南美洲', '大洋洲', '非洲'],
        description: '境外出行目的地国家或地区'
      },
      {
        id: 65,
        name: '旅行目的',
        code: 'travel_purpose',
        type: 'select',
        required: true,
        options: ['商务出差', '项目考察', '展会参展', '培训学习', '其他商务活动'],
        description: '境外出行目的'
      },
      {
        id: 66,
        name: '意外身故保额',
        code: 'death_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人意外身故保险金额'
      },
      {
        id: 67,
        name: '意外伤残保额',
        code: 'disability_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人意外伤残保险金额'
      },
      {
        id: 68,
        name: '医疗费用保额',
        code: 'medical_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人医疗费用保险金额'
      },
      {
        id: 69,
        name: '紧急救援保额',
        code: 'emergency_rescue_sum_insured',
        type: 'number',
        required: true,
        unit: '万元/人',
        description: '每人紧急救援保险金额'
      },
      {
        id: 70,
        name: '保险计划',
        code: 'insurance_plan',
        type: 'select',
        required: true,
        options: ['基础计划', '标准计划', '高端计划', '定制计划'],
        description: '境外意外险计划类型'
      },
      {
        id: 71,
        name: '总保险费',
        code: 'total_premium',
        type: 'number',
        required: true,
        unit: '元',
        description: '境外意外险总保险费'
      },
      {
        id: 72,
        name: '附加服务选择',
        code: 'additional_services',
        type: 'multiSelect',
        required: false,
        options: ['行李延误', '旅程取消', '旅行证件遗失', '个人财物损失', '签证拒签保障'],
        description: '附加服务选择'
      },
      {
        id: 73,
        name: '紧急联系人',
        code: 'emergency_contact',
        type: 'text',
        required: true,
        maxLength: 50,
        description: '境内紧急联系人姓名'
      },
      {
        id: 74,
        name: '紧急联系电话',
        code: 'emergency_contact_phone',
        type: 'text',
        required: true,
        maxLength: 20,
        description: '境内紧急联系人电话'
      },
      {
        id: 75,
        name: '特别约定',
        code: 'special_agreements',
        type: 'textarea',
        required: false,
        maxLength: 500,
        description: '保险合同特别约定事项'
      }
    ],
    createTime: '2024-05-20 15:00:00',
    updateTime: '2024-05-20 15:00:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
]

// 服务流程配置MOCK数据
export const mockServiceProcesses = [
  {
    id: 1,
    name: '员工福利保障流程',
    code: 'EMPLOYEE_BENEFIT',
    description: '员工福利保障服务流程配置',
    category: '员工福利',
    status: 1,
    steps: [
      {
        id: 1,
        nodeId: '1',
        name: '需求收集',
        code: 'requirement_collection',
        description: '收集客户员工福利需求',
        order: 1,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 2,
        nodeId: '2',
        name: '方案设计',
        code: 'solution_design',
        description: '设计员工福利保障方案',
        order: 2,
        required: true,
        estimatedTime: 3,
        unit: '天'
      },
      {
        id: 3,
        nodeId: '3',
        name: '方案确认',
        code: 'solution_confirmation',
        description: '与客户确认方案细节',
        order: 3,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 4,
        nodeId: '4',
        name: '合同签署',
        code: 'contract_signing',
        description: '签署员工福利保障合同',
        order: 4,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 5,
        nodeId: '5',
        name: '服务实施',
        code: 'service_implementation',
        description: '实施员工福利保障服务',
        order: 5,
        required: true,
        estimatedTime: 5,
        unit: '天'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '企业财产保险流程',
    code: 'ENTERPRISE_PROPERTY',
    description: '企业财产保险服务流程配置',
    category: '企业保障',
    status: 1,
    steps: [
      {
        id: 6,
        nodeId: '6',
        name: '风险评估',
        code: 'risk_assessment',
        description: '评估企业财产风险',
        order: 1,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 7,
        nodeId: '7',
        name: '方案设计',
        code: 'solution_design',
        description: '设计财产保险方案',
        order: 2,
        required: true,
        estimatedTime: 3,
        unit: '天'
      },
      {
        id: 8,
        nodeId: '8',
        name: '方案确认',
        code: 'solution_confirmation',
        description: '与客户确认方案细节',
        order: 3,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 9,
        nodeId: '9',
        name: '合同签署',
        code: 'contract_signing',
        description: '签署财产保险合同',
        order: 4,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 10,
        nodeId: '10',
        name: '服务实施',
        code: 'service_implementation',
        description: '实施财产保险服务',
        order: 5,
        required: true,
        estimatedTime: 5,
        unit: '天'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: '线上产品销售流程',
    code: 'ONLINE_PRODUCT_SALES',
    description: '线上产品销售服务流程配置',
    category: '线上产品',
    status: 1,
    steps: [
      {
        id: 11,
        nodeId: '11',
        name: '产品选择',
        code: 'product_selection',
        description: '用户选择线上产品',
        order: 1,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 12,
        nodeId: '12',
        name: '用户信息录入',
        code: 'user_info_input',
        description: '用户填写个人信息',
        order: 2,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 13,
        nodeId: '13',
        name: '风险评估',
        code: 'risk_assessment',
        description: '系统自动评估用户风险',
        order: 3,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 14,
        nodeId: '14',
        name: '支付',
        code: 'payment',
        description: '用户在线支付',
        order: 4,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 15,
        nodeId: '15',
        name: '保单生成',
        code: 'policy_generation',
        description: '系统自动生成电子保单',
        order: 5,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 16,
        nodeId: '16',
        name: '服务跟踪',
        code: 'service_tracking',
        description: '系统跟踪服务状态',
        order: 6,
        required: true,
        estimatedTime: 1,
        unit: '天'
      }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 4,
    name: '雇主责任险服务流程',
    code: 'EMPLOYER_LIABILITY_INSURANCE_PROCESS',
    description: '雇主责任险服务流程配置',
    category: '企业保障',
    status: 1,
    steps: [
      {
        id: 17,
        nodeId: '17',
        name: '企业KYC查询',
        code: 'enterprise_kyc_query',
        description: '查询企业KYC信息',
        order: 1,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 6,
        nodeId: '6',
        name: '风险评估',
        code: 'risk_assessment',
        description: '评估企业用工风险',
        order: 2,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 18,
        nodeId: '18',
        name: '企业问卷收集',
        code: 'enterprise_questionnaire',
        description: '收集企业员工风险问卷',
        order: 3,
        required: true,
        estimatedTime: 3,
        unit: '天'
      },
      {
        id: 7,
        nodeId: '7',
        name: '方案设计',
        code: 'solution_design',
        description: '设计雇主责任险方案',
        order: 4,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 19,
        nodeId: '19',
        name: '企业合规审批',
        code: 'enterprise_compliance_approval',
        description: '进行企业合规审批',
        order: 5,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 8,
        nodeId: '8',
        name: '方案确认',
        code: 'solution_confirmation',
        description: '与客户确认方案细节',
        order: 6,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 9,
        nodeId: '9',
        name: '合同签署',
        code: 'contract_signing',
        description: '签署雇主责任险合同',
        order: 7,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 20,
        nodeId: '20',
        name: '企业风险管理方案',
        code: 'enterprise_risk_management',
        description: '提供企业风险管理方案',
        order: 8,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 10,
        nodeId: '10',
        name: '服务实施',
        code: 'service_implementation',
        description: '实施雇主责任险服务',
        order: 9,
        required: true,
        estimatedTime: 5,
        unit: '天'
      }
    ],
    createTime: '2024-05-20 15:30:00',
    updateTime: '2024-05-20 15:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 5,
    name: '团体意外险服务流程',
    code: 'GROUP_ACCIDENT_INSURANCE_PROCESS',
    description: '团体意外险服务流程配置',
    category: '企业保障',
    status: 1,
    steps: [
      {
        id: 17,
        nodeId: '17',
        name: '企业KYC查询',
        code: 'enterprise_kyc_query',
        description: '查询企业KYC信息',
        order: 1,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 1,
        nodeId: '1',
        name: '需求收集',
        code: 'requirement_collection',
        description: '收集客户团体意外险需求',
        order: 2,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 18,
        nodeId: '18',
        name: '企业问卷收集',
        code: 'enterprise_questionnaire',
        description: '收集企业员工风险问卷',
        order: 3,
        required: false,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 6,
        nodeId: '6',
        name: '风险评估',
        code: 'risk_assessment',
        description: '评估团体成员风险',
        order: 4,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 2,
        nodeId: '2',
        name: '方案设计',
        code: 'solution_design',
        description: '设计团体意外险方案',
        order: 5,
        required: true,
        estimatedTime: 2,
        unit: '天'
      },
      {
        id: 19,
        nodeId: '19',
        name: '企业合规审批',
        code: 'enterprise_compliance_approval',
        description: '进行企业合规审批',
        order: 6,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 3,
        nodeId: '3',
        name: '方案确认',
        code: 'solution_confirmation',
        description: '与客户确认方案细节',
        order: 7,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 4,
        nodeId: '4',
        name: '合同签署',
        code: 'contract_signing',
        description: '签署团体意外险合同',
        order: 8,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 5,
        nodeId: '5',
        name: '服务实施',
        code: 'service_implementation',
        description: '实施团体意外险服务',
        order: 9,
        required: true,
        estimatedTime: 3,
        unit: '天'
      },
      {
        id: 16,
        nodeId: '16',
        name: '服务跟踪',
        code: 'service_tracking',
        description: '团体意外险服务跟踪',
        order: 10,
        required: true,
        estimatedTime: 30,
        unit: '天'
      }
    ],
    createTime: '2024-05-20 15:45:00',
    updateTime: '2024-05-20 15:45:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 6,
    name: '境外意外险服务流程',
    code: 'OVERSEAS_ACCIDENT_INSURANCE_PROCESS',
    description: '境外意外险服务流程配置',
    category: '企业保障',
    status: 1,
    steps: [
      {
        id: 17,
        nodeId: '17',
        name: '企业KYC查询',
        code: 'enterprise_kyc_query',
        description: '查询企业KYC信息',
        order: 1,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 12,
        nodeId: '12',
        name: '用户信息录入',
        code: 'user_info_input',
        description: '录入出境人员信息',
        order: 2,
        required: true,
        estimatedTime: 2,
        unit: '小时'
      },
      {
        id: 13,
        nodeId: '13',
        name: '风险评估',
        code: 'risk_assessment',
        description: '评估境外出行风险',
        order: 3,
        required: true,
        estimatedTime: 4,
        unit: '小时'
      },
      {
        id: 19,
        nodeId: '19',
        name: '企业合规审批',
        code: 'enterprise_compliance_approval',
        description: '进行企业合规审批',
        order: 4,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 20,
        nodeId: '20',
        name: '企业风险管理方案',
        code: 'enterprise_risk_management',
        description: '提供境外风险管理方案',
        order: 5,
        required: true,
        estimatedTime: 1,
        unit: '天'
      },
      {
        id: 8,
        nodeId: '8',
        name: '方案确认',
        code: 'solution_confirmation',
        description: '与客户确认保障方案',
        order: 6,
        required: true,
        estimatedTime: 4,
        unit: '小时'
      },
      {
        id: 14,
        nodeId: '14',
        name: '支付',
        code: 'payment',
        description: '支付保险费用',
        order: 7,
        required: true,
        estimatedTime: 1,
        unit: '小时'
      },
      {
        id: 15,
        nodeId: '15',
        name: '保单生成',
        code: 'policy_generation',
        description: '生成境外意外险保单',
        order: 8,
        required: true,
        estimatedTime: 2,
        unit: '小时'
      },
      {
        id: 16,
        nodeId: '16',
        name: '服务跟踪',
        code: 'service_tracking',
        description: '境外意外险服务跟踪',
        order: 9,
        required: true,
        estimatedTime: 90,
        unit: '天'
      }
    ],
    createTime: '2024-05-20 16:00:00',
    updateTime: '2024-05-20 16:00:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
] 