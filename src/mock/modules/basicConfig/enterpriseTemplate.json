[{"id": 1, "name": "标准企业信息模板", "code": "STANDARD_ENTERPRISE_TEMPLATE", "description": "标准企业信息收集模板", "category": "企业信息", "status": 1, "fields": [{"id": 1, "name": "企业名称", "code": "enterprise_name", "type": "text", "required": true, "maxLength": 100, "description": "企业全称"}, {"id": 2, "name": "企业类型", "code": "enterprise_type", "type": "select", "required": true, "options": ["A类", "B类", "C类", "D类", "E类"], "description": "企业分类类型"}, {"id": 3, "name": "注册资本", "code": "registered_capital", "type": "number", "required": false, "unit": "万元", "description": "企业注册资本"}, {"id": 4, "name": "成立时间", "code": "establish_date", "type": "date", "required": false, "description": "企业成立日期"}, {"id": 5, "name": "经营范围", "code": "business_scope", "type": "textarea", "required": false, "maxLength": 500, "description": "企业经营范围"}, {"id": 6, "name": "员工人数", "code": "employee_count", "type": "number", "required": false, "description": "企业员工总数"}, {"id": 7, "name": "年营业额", "code": "annual_revenue", "type": "number", "required": false, "unit": "万元", "description": "企业年营业额"}, {"id": 8, "name": "联系人", "code": "contact_person", "type": "text", "required": true, "maxLength": 50, "description": "企业联系人"}, {"id": 9, "name": "联系电话", "code": "contact_phone", "type": "text", "required": true, "maxLength": 20, "description": "企业联系电话"}, {"id": 10, "name": "企业地址", "code": "enterprise_address", "type": "textarea", "required": false, "maxLength": 200, "description": "企业注册地址"}, {"id": 11, "name": "控股股东", "code": "major_shareholder", "type": "object", "required": false, "relatedTemplateId": 3, "description": "企业控股股东信息"}, {"id": 12, "name": "股东列表", "code": "shareholders", "type": "objectArray", "required": false, "relatedTemplateId": 3, "description": "企业所有股东信息列表"}], "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 2, "name": "客户机会信息模板", "code": "CUSTOMER_OPPORTUNITY_TEMPLATE", "description": "客户机会信息收集模板", "category": "客户机会", "status": 1, "fields": [{"id": 11, "name": "机会名称", "code": "opportunity_name", "type": "text", "required": true, "maxLength": 100, "description": "客户机会名称"}, {"id": 12, "name": "客户名称", "code": "customer_name", "type": "text", "required": true, "maxLength": 100, "description": "客户企业名称"}, {"id": 13, "name": "机会类型", "code": "opportunity_type", "type": "select", "required": true, "options": ["员工福利", "企业保障", "线上产品"], "description": "机会分类"}, {"id": 14, "name": "预计金额", "code": "expected_amount", "type": "number", "required": false, "unit": "万元", "description": "预计合同金额"}, {"id": 15, "name": "机会状态", "code": "opportunity_status", "type": "select", "required": true, "options": ["初步接触", "需求确认", "方案制定", "商务谈判", "合同签署"], "description": "机会当前状态"}, {"id": 16, "name": "备注信息", "code": "remarks", "type": "textarea", "required": false, "maxLength": 200, "description": "机会备注信息"}], "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 3, "name": "股东信息模板", "code": "SHAREHOLDER_TEMPLATE", "description": "股东信息收集模板", "category": "股东信息", "status": 1, "fields": [{"id": 17, "name": "股东名称", "code": "shareholder_name", "type": "text", "required": true, "maxLength": 100, "description": "股东全称"}, {"id": 18, "name": "股东类型", "code": "shareholder_type", "type": "select", "required": true, "options": ["个人", "企业", "政府", "其他组织"], "description": "股东类型"}, {"id": 19, "name": "持股比例", "code": "shareholding_ratio", "type": "number", "required": true, "unit": "%", "description": "股东持股比例"}, {"id": 20, "name": "出资金额", "code": "investment_amount", "type": "number", "required": false, "unit": "万元", "description": "股东出资金额"}, {"id": 21, "name": "证件类型", "code": "id_type", "type": "select", "required": false, "options": ["身份证", "护照", "营业执照", "组织机构代码证"], "description": "股东证件类型"}, {"id": 22, "name": "证件号码", "code": "id_number", "type": "text", "required": false, "maxLength": 50, "description": "股东证件号码"}, {"id": 23, "name": "联系方式", "code": "contact_info", "type": "text", "required": false, "maxLength": 50, "description": "股东联系方式"}, {"id": 24, "name": "入股时间", "code": "investment_date", "type": "date", "required": false, "description": "股东入股时间"}, {"id": 25, "name": "备注", "code": "remarks", "type": "textarea", "required": false, "maxLength": 200, "description": "股东信息备注"}], "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}]