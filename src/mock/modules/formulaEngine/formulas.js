// 公式引擎MOCK数据
export const mockFormulas = [
  {
    id: 1,
    name: '管理层承诺与文化导向公式',
    description: '用于评估管理层承诺与文化导向的风险管理公式',
    formula: '(A * pow(x, 4) + B * sqrt(pow(x, 3) + 1) + C * sin(2 * PI * x / 5) + D * factorial(x) * log(x + 1) + E * pow(2, x)) / 3 * 100',
    variables: [
      { name: 'x', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '战略文化与治理框架',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '风险责任明确公式',
    description: '用于风险责任明确的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '组织责任与指标体系',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: '风险流程标准化公式',
    description: '用于风险流程标准化的风险管理公式',
    formula: 'a * X + b * X^2 + c * exp(X/5)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.4, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '指数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 4,
    name: '风险沟通与认知一致性公式',
    description: '用于风险沟通与认知一致性的风险管理公式',
    formula: 'a * X + b * sin(X * PI / 10) + c * X^2',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.6, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.2, description: '正弦权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '二次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '沟通系统与技术支持',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 5,
    name: '风险监控与响应机制公式',
    description: '用于风险监控与响应机制的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 6,
    name: '风险偏好与容忍度管理公式',
    description: '用于风险偏好与容忍度管理的风险管理公式',
    formula: 'a * X + b * X^2 + c * X^3',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.3, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.3, description: '三次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '战略文化与治理框架',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 7,
    name: '根本原因分析机制公式',
    description: '用于根本原因分析机制的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 8,
    name: '风险责任与指标设计公式',
    description: '用于风险责任与指标设计的风险管理公式',
    formula: 'a * X + b * X^2 + c * exp(X/5)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.4, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '指数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '组织责任与指标体系',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 9,
    name: '风险识别与机遇挖掘公式',
    description: '用于风险识别与机遇挖掘的风险管理公式',
    formula: 'a * X + b * sin(X * PI / 10) + c * X^2',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.6, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.2, description: '正弦权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '二次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 10,
    name: 'ERM集成战略与目标公式',
    description: '用于ERM集成战略与目标的风险管理公式',
    formula: 'a * X + b * X^2 + c * X^3',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.3, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.3, description: '三次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '战略文化与治理框架',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 11,
    name: '绩效联动与风险反馈公式',
    description: '用于绩效联动与风险反馈的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '组织责任与指标体系',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 12,
    name: '分析建模与业务弹性设计公式',
    description: '用于分析建模与业务弹性设计的风险管理公式',
    formula: 'a * X + b * X^2 + c * exp(X/5)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.4, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '指数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 13,
    name: '风险事件和结果管理与响应公式',
    description: '用于风险事件和结果管理与响应的风险管理公式',
    formula: 'a * X + b * sin(X * PI / 10) + c * X^2',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.6, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.2, description: '正弦权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '二次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 14,
    name: 'IT与流程系统整合支持公式',
    description: '用于IT与流程系统整合支持的风险管理公式',
    formula: 'a * X + b * X^2 + c * X^3',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.3, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.4, description: '二次权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.3, description: '三次权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '沟通系统与技术支持',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 15,
    name: '持续改进与审查机制公式',
    description: '用于持续改进与审查机制的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '风险评估与流程执行',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
]

// 公式分类MOCK数据
export const mockCategories = [
  {
    id: 1,
    name: '战略文化与治理框架',
    description: '战略层面的风险管理公式',
    count: 4
  },
  {
    id: 2,
    name: '组织责任与指标体系',
    description: '组织层面的风险管理公式',
    count: 3
  },
  {
    id: 3,
    name: '风险评估与流程执行',
    description: '执行层面的风险管理公式',
    count: 7
  },
  {
    id: 4,
    name: '沟通系统与技术支持',
    description: '技术层面的风险管理公式',
    count: 2
  }
] 