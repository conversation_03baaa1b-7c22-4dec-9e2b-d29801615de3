{"rules": [{"id": 1, "name": "企业类型风险规则", "code": "ENTERPRISE_TYPE_RISK_RULE", "description": "根据企业类型进行风险分类的规则", "category": "企业分类", "status": 1, "conditions": [{"field": "enterprise_type", "operator": "in", "value": ["A", "B"], "description": "企业类型为A类或B类"}], "actions": [{"type": "set_risk_level", "value": "低风险", "description": "设置为低风险等级"}], "priority": 1, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 2, "name": "企业规模风险规则", "code": "ENTERPRISE_SIZE_RISK_RULE", "description": "根据企业规模进行风险分类的规则", "category": "企业分类", "status": 1, "conditions": [{"field": "employee_count", "operator": ">", "value": 1000, "description": "员工人数大于1000人"}], "actions": [{"type": "set_risk_level", "value": "中风险", "description": "设置为中风险等级"}], "priority": 2, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 3, "name": "行业风险规则", "code": "INDUSTRY_RISK_RULE", "description": "根据行业类型进行风险分类的规则", "category": "行业分类", "status": 1, "conditions": [{"field": "industry_code", "operator": "in", "value": ["C", "D", "E"], "description": "行业代码为C、D或E类"}], "actions": [{"type": "set_risk_level", "value": "高风险", "description": "设置为高风险等级"}], "priority": 3, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 4, "name": "注册资本风险规则", "code": "REGISTERED_CAPITAL_RISK_RULE", "description": "根据注册资本进行风险分类的规则", "category": "财务分类", "status": 1, "conditions": [{"field": "registered_capital", "operator": "<", "value": 1000, "description": "注册资本小于1000万元"}], "actions": [{"type": "set_risk_level", "value": "高风险", "description": "设置为高风险等级"}], "priority": 4, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}, {"id": 5, "name": "成立时间风险规则", "code": "ESTABLISH_TIME_RISK_RULE", "description": "根据企业成立时间进行风险分类的规则", "category": "时间分类", "status": 1, "conditions": [{"field": "establish_years", "operator": "<", "value": 3, "description": "成立时间少于3年"}], "actions": [{"type": "set_risk_level", "value": "中风险", "description": "设置为中风险等级"}], "priority": 5, "createTime": "2024-01-15 10:30:00", "updateTime": "2024-01-15 10:30:00", "createUser": "admin", "updateUser": "admin"}], "categories": [{"id": 1, "name": "企业分类", "description": "基于企业基本信息的分类规则", "count": 2}, {"id": 2, "name": "行业分类", "description": "基于行业信息的分类规则", "count": 1}, {"id": 3, "name": "财务分类", "description": "基于财务信息的分类规则", "count": 1}, {"id": 4, "name": "时间分类", "description": "基于时间信息的分类规则", "count": 1}], "operators": [{"code": "eq", "name": "等于", "description": "字段值等于指定值"}, {"code": "ne", "name": "不等于", "description": "字段值不等于指定值"}, {"code": "gt", "name": "大于", "description": "字段值大于指定值"}, {"code": "gte", "name": "大于等于", "description": "字段值大于等于指定值"}, {"code": "lt", "name": "小于", "description": "字段值小于指定值"}, {"code": "lte", "name": "小于等于", "description": "字段值小于等于指定值"}, {"code": "in", "name": "包含", "description": "字段值包含在指定列表中"}, {"code": "not_in", "name": "不包含", "description": "字段值不包含在指定列表中"}, {"code": "like", "name": "模糊匹配", "description": "字段值模糊匹配指定模式"}, {"code": "not_like", "name": "不模糊匹配", "description": "字段值不模糊匹配指定模式"}], "actions": [{"code": "set_risk_level", "name": "设置风险等级", "description": "设置企业的风险等级"}, {"code": "set_enterprise_type", "name": "设置企业类型", "description": "设置企业的类型分类"}, {"code": "set_industry_code", "name": "设置行业代码", "description": "设置企业的行业代码"}, {"code": "set_score", "name": "设置评分", "description": "设置企业的风险评分"}, {"code": "set_status", "name": "设置状态", "description": "设置企业的状态"}]}