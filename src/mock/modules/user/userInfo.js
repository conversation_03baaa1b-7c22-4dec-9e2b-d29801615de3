// 用户信息MOCK数据
export const mockUserInfo = {
  userId: "user001",
  userName: "张三",
  nickName: "张三",
  email: "<PERSON><PERSON><PERSON>@example.com",
  phone: "13800138000",
  avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
  department: "技术部",
  position: "高级工程师",
  tenantId: "tenant001",
  tenantName: "示例企业",
  roles: [
    {
      roleId: "role001",
      roleName: "系统管理员",
      roleCode: "ADMIN"
    },
    {
      roleId: "role002",
      roleName: "业务操作员",
      roleCode: "OPERATOR"
    }
  ],
  permissions: [
    "user:view",
    "user:edit",
    "enterprise:view",
    "enterprise:edit",
    "config:view",
    "config:edit",
    "formula:view",
    "formula:edit",
    "risk:view",
    "risk:edit"
  ],
  // 功能权限DTO，用于store中的权限设置
  funcAuthDTO: [
    {
      authCode: "user:view",
      authName: "用户查看"
    },
    {
      authCode: "user:edit",
      authName: "用户编辑"
    },
    {
      authCode: "enterprise:view",
      authName: "企业查看"
    },
    {
      authCode: "enterprise:edit",
      authName: "企业编辑"
    },
    {
      authCode: "config:view",
      authName: "配置查看"
    },
    {
      authCode: "config:edit",
      authName: "配置编辑"
    },
    {
      authCode: "formula:view",
      authName: "公式查看"
    },
    {
      authCode: "formula:edit",
      authName: "公式编辑"
    },
    {
      authCode: "risk:view",
      authName: "风险查看"
    },
    {
      authCode: "risk:edit",
      authName: "风险编辑"
    }
  ],
  loginTime: "2024-01-15 09:30:00",
  lastLoginTime: "2024-01-14 17:45:00",
  status: 1,
  createTime: "2023-06-01 10:00:00",
  updateTime: "2024-01-15 09:30:00"
};

// 获取用户信息API
export const getUserInfo = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return {
    code: 200,
    message: "获取成功",
    data: mockUserInfo
  };
};

// 获取Web用户信息API (与getUserInfo功能相同，为了兼容不同的API命名)
export const getWebUserInfo = getUserInfo;

// 更新用户信息API
export const updateUserInfo = async (userData) => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 模拟更新用户信息
  Object.assign(mockUserInfo, userData, {
    updateTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-')
  });
  
  return {
    code: 200,
    message: "更新成功",
    data: mockUserInfo
  };
};

// 获取用户权限列表
export const getUserPermissions = async (userId) => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return {
    code: 200,
    message: "获取成功",
    data: {
      userId: userId || mockUserInfo.userId,
      permissions: mockUserInfo.permissions,
      roles: mockUserInfo.roles
    }
  };
};

// 修改用户密码
export const changeUserPassword = async (passwordData) => {
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // 模拟密码验证
  if (!passwordData.oldPassword || !passwordData.newPassword) {
    return {
      code: 400,
      message: "密码不能为空",
      data: null
    };
  }
  
  if (passwordData.newPassword.length < 6) {
    return {
      code: 400,
      message: "新密码长度不能少于6位",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "密码修改成功",
    data: null
  };
};