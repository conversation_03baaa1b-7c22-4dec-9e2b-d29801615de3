// 策略匹配管理MOCK数据
export const mockStrategyMatchList = [
  {
    id: 1,
    name: "高价值客户匹配策略",
    code: "HIGH_VALUE_MATCH",
    description: "针对高价值客户的匹配策略",
    status: 1,
    priority: 1,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "活跃客户匹配策略",
    code: "ACTIVE_CUSTOMER_MATCH",
    description: "针对活跃客户的匹配策略",
    status: 1,
    priority: 2,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 3,
    name: "新客户匹配策略",
    code: "NEW_CUSTOMER_MATCH",
    description: "针对新客户的匹配策略",
    status: 1,
    priority: 3,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  }
]

export const mockStrategyExecutedRecords = [
  {
    id: 1,
    strategyId: 1,
    strategyName: "高价值客户匹配策略",
    executionTime: "2024-01-15 10:30:00",
    status: "completed",
    totalRecords: 1000,
    matchedRecords: 850,
    unmatchedRecords: 150,
    executionDuration: 300,
    createTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    strategyId: 2,
    strategyName: "活跃客户匹配策略",
    executionTime: "2024-01-15 11:00:00",
    status: "running",
    totalRecords: 2000,
    matchedRecords: 1200,
    unmatchedRecords: 800,
    executionDuration: 180,
    createTime: "2024-01-15 11:00:00"
  }
]

export const mockProcessMindData = {
  strategyId: 1,
  strategyName: "高价值客户匹配策略",
  nodes: [
    {
      id: "start",
      name: "开始",
      type: "start",
      position: { x: 100, y: 100 }
    },
    {
      id: "data_source",
      name: "数据源",
      type: "data_source",
      position: { x: 300, y: 100 }
    },
    {
      id: "filter",
      name: "过滤条件",
      type: "filter",
      position: { x: 500, y: 100 }
    },
    {
      id: "match",
      name: "匹配规则",
      type: "match",
      position: { x: 700, y: 100 }
    },
    {
      id: "output",
      name: "输出结果",
      type: "output",
      position: { x: 900, y: 100 }
    }
  ],
  edges: [
    { from: "start", to: "data_source" },
    { from: "data_source", to: "filter" },
    { from: "filter", to: "match" },
    { from: "match", to: "output" }
  ]
} 