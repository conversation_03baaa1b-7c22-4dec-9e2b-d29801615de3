// 企业信息管理MOCK数据
export const mockEnterpriseList = [
  {
    id: 1,
    name: "北京科技有限公司",
    code: "BJ001",
    type: "A类",
    industry: "信息技术",
    employeeCount: 8500,
    revenue: 45000000000,
    establishDate: "2010-03-15",
    address: "北京市海淀区中关村软件园",
    contactPerson: "张总",
    contactPhone: "010-88888888",
    status: 1,
    createTime: "2023-01-15 10:30:00",
    updateTime: "2023-12-01 14:20:00"
  },
  {
    id: 2,
    name: "上海金融集团",
    code: "SH002",
    type: "A类",
    industry: "金融服务",
    employeeCount: 12000,
    revenue: 68000000000,
    establishDate: "2008-06-20",
    address: "上海市浦东新区陆家嘴金融区",
    contactPerson: "李总",
    contactPhone: "021-66666666",
    status: 1,
    createTime: "2023-02-10 09:15:00",
    updateTime: "2023-11-28 16:45:00"
  },
  {
    id: 3,
    name: "深圳制造有限公司",
    code: "SZ003",
    type: "B类",
    industry: "制造业",
    employeeCount: 3200,
    revenue: 18000000000,
    establishDate: "2012-09-08",
    address: "深圳市宝安区工业园",
    contactPerson: "王总",
    contactPhone: "0755-55555555",
    status: 1,
    createTime: "2023-03-05 11:20:00",
    updateTime: "2023-12-05 10:30:00"
  },
  {
    id: 4,
    name: "广州贸易公司",
    code: "GZ004",
    type: "C类",
    industry: "贸易",
    employeeCount: 850,
    revenue: 8500000000,
    establishDate: "2015-04-12",
    address: "广州市天河区商务区",
    contactPerson: "陈总",
    contactPhone: "020-77777777",
    status: 1,
    createTime: "2023-04-20 14:10:00",
    updateTime: "2023-11-30 09:15:00"
  },
  {
    id: 5,
    name: "杭州网络科技",
    code: "HZ005",
    type: "B类",
    industry: "互联网",
    employeeCount: 2800,
    revenue: 22000000000,
    establishDate: "2013-11-25",
    address: "杭州市西湖区科技园",
    contactPerson: "赵总",
    contactPhone: "0571-99999999",
    status: 1,
    createTime: "2023-05-12 16:30:00",
    updateTime: "2023-12-03 13:45:00"
  }
]

// 企业类型MOCK数据
export const mockEnterpriseTypes = [
  { value: "A类", label: "A类企业", description: "大型企业" },
  { value: "B类", label: "B类企业", description: "中型企业" },
  { value: "C类", label: "C类企业", description: "小型企业" },
  { value: "D类", label: "D类企业", description: "微型企业" },
  { value: "E类", label: "E类企业", description: "其他企业" }
]

// 行业分类MOCK数据
export const mockIndustries = [
  { value: "信息技术", label: "信息技术", description: "IT行业" },
  { value: "金融服务", label: "金融服务", description: "金融行业" },
  { value: "制造业", label: "制造业", description: "制造行业" },
  { value: "贸易", label: "贸易", description: "贸易行业" },
  { value: "互联网", label: "互联网", description: "互联网行业" },
  { value: "房地产", label: "房地产", description: "房地产行业" },
  { value: "医疗健康", label: "医疗健康", description: "医疗行业" },
  { value: "教育", label: "教育", description: "教育行业" }
] 