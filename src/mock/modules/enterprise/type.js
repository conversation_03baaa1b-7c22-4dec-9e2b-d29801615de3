// 企业类型MOCK数据
export const mockEnterpriseTypeList = [
  {
    id: 1,
    name: "A类",
    code: "A",
    employeeRange: "≥5000人",
    revenueRange: "≥300亿元",
    rules: [
      { field: "employeeCount", operator: ">=", value: 5000 },
      { field: "revenue", operator: ">=", value: 30000000000 }
    ],
    priority: 1,
    description: "大型企业，员工规模大，营收能力强",
    status: 1,
    createTime: "2023-01-01 00:00:00",
    updateTime: "2023-12-01 00:00:00"
  },
  {
    id: 2,
    name: "B类",
    code: "B",
    employeeRange: "≥1000人＜5000人",
    revenueRange: "≥100亿元＜300亿元",
    rules: [
      { field: "employeeCount", operator: ">=", value: 1000 },
      { field: "employeeCount", operator: "<", value: 5000 },
      { field: "revenue", operator: ">=", value: 10000000000 },
      { field: "revenue", operator: "<", value: 30000000000 }
    ],
    priority: 2,
    description: "中型企业，发展稳定，有一定规模",
    status: 1,
    createTime: "2023-01-01 00:00:00",
    updateTime: "2023-12-01 00:00:00"
  },
  {
    id: 3,
    name: "C类",
    code: "C",
    employeeRange: "≥500人＜1000人",
    revenueRange: "≥30亿元＜100亿元",
    rules: [
      { field: "employeeCount", operator: ">=", value: 500 },
      { field: "employeeCount", operator: "<", value: 1000 },
      { field: "revenue", operator: ">=", value: 3000000000 },
      { field: "revenue", operator: "<", value: 10000000000 }
    ],
    priority: 3,
    description: "中小型企业，成长型企业",
    status: 1,
    createTime: "2023-01-01 00:00:00",
    updateTime: "2023-12-01 00:00:00"
  },
  {
    id: 4,
    name: "D类",
    code: "D",
    employeeRange: "≥200人＜500人",
    revenueRange: "≥5000万元＜30亿元",
    rules: [
      { field: "employeeCount", operator: ">=", value: 200 },
      { field: "employeeCount", operator: "<", value: 500 },
      { field: "revenue", operator: ">=", value: 50000000 },
      { field: "revenue", operator: "<", value: 3000000000 }
    ],
    priority: 4,
    description: "小型企业，初创或成长期企业",
    status: 1,
    createTime: "2023-01-01 00:00:00",
    updateTime: "2023-12-01 00:00:00"
  },
  {
    id: 5,
    name: "E类",
    code: "E",
    employeeRange: "<200人",
    revenueRange: "<5000万元",
    rules: [
      { field: "employeeCount", operator: "<", value: 200 },
      { field: "revenue", operator: "<", value: 50000000 }
    ],
    priority: 5,
    description: "微型企业，初创企业",
    status: 1,
    createTime: "2023-01-01 00:00:00",
    updateTime: "2023-12-01 00:00:00"
  }
] 