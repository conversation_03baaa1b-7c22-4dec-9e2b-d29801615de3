// 风险矩阵MOCK数据
export const mockRiskMatrixList = [
  {
    id: 1,
    name: '企业风险管理矩阵',
    code: 'ENTERPRISE_RISK_MATRIX',
    description: '企业风险管理能力评估矩阵',
    category: '企业风险',
    status: 1,
    enterpriseTypes: ['A', 'B', 'C', 'D', 'E', 'CDE'],
    dimensions: [
      {
        name: '风险识别能力',
        weight: 0.3,
        levels: ['一档', '二档', '三档', '四档', '五档']
      },
      {
        name: '风险评估能力',
        weight: 0.3,
        levels: ['一档', '二档', '三档', '四档', '五档']
      },
      {
        name: '风险应对能力',
        weight: 0.4,
        levels: ['一档', '二档', '三档', '四档', '五档']
      }
    ],
    matrix: [
      { x: 1, y: 1, level: '一档', color: '#67C23A', description: '风险识别能力一档，评估能力一档' },
      { x: 2, y: 1, level: '二档', color: '#E6A23C', description: '风险识别能力二档，评估能力一档' },
      { x: 3, y: 1, level: '三档', color: '#F56C6C', description: '风险识别能力三档，评估能力一档' },
      { x: 4, y: 1, level: '四档', color: '#F56C6C', description: '风险识别能力四档，评估能力一档' },
      { x: 5, y: 1, level: '五档', color: '#F56C6C', description: '风险识别能力五档，评估能力一档' },
      { x: 1, y: 2, level: '二档', color: '#E6A23C', description: '风险识别能力一档，评估能力二档' },
      { x: 2, y: 2, level: '二档', color: '#E6A23C', description: '风险识别能力二档，评估能力二档' },
      { x: 3, y: 2, level: '三档', color: '#F56C6C', description: '风险识别能力三档，评估能力二档' },
      { x: 4, y: 2, level: '四档', color: '#F56C6C', description: '风险识别能力四档，评估能力二档' },
      { x: 5, y: 2, level: '五档', color: '#F56C6C', description: '风险识别能力五档，评估能力二档' },
      { x: 1, y: 3, level: '三档', color: '#F56C6C', description: '风险识别能力一档，评估能力三档' },
      { x: 2, y: 3, level: '三档', color: '#F56C6C', description: '风险识别能力二档，评估能力三档' },
      { x: 3, y: 3, level: '三档', color: '#F56C6C', description: '风险识别能力三档，评估能力三档' },
      { x: 4, y: 3, level: '四档', color: '#F56C6C', description: '风险识别能力四档，评估能力三档' },
      { x: 5, y: 3, level: '五档', color: '#F56C6C', description: '风险识别能力五档，评估能力三档' },
      { x: 1, y: 4, level: '四档', color: '#F56C6C', description: '风险识别能力一档，评估能力四档' },
      { x: 2, y: 4, level: '四档', color: '#F56C6C', description: '风险识别能力二档，评估能力四档' },
      { x: 3, y: 4, level: '四档', color: '#F56C6C', description: '风险识别能力三档，评估能力四档' },
      { x: 4, y: 4, level: '四档', color: '#F56C6C', description: '风险识别能力四档，评估能力四档' },
      { x: 5, y: 4, level: '五档', color: '#F56C6C', description: '风险识别能力五档，评估能力四档' },
      { x: 1, y: 5, level: '五档', color: '#F56C6C', description: '风险识别能力一档，评估能力五档' },
      { x: 2, y: 5, level: '五档', color: '#F56C6C', description: '风险识别能力二档，评估能力五档' },
      { x: 3, y: 5, level: '五档', color: '#F56C6C', description: '风险识别能力三档，评估能力五档' },
      { x: 4, y: 5, level: '五档', color: '#F56C6C', description: '风险识别能力四档，评估能力五档' },
      { x: 5, y: 5, level: '五档', color: '#F56C6C', description: '风险识别能力五档，评估能力五档' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '行业风险矩阵',
    code: 'INDUSTRY_RISK_MATRIX',
    description: '行业风险分类评估矩阵',
    category: '行业风险',
    status: 1,
    enterpriseTypes: ['A', 'B'],
    dimensions: [
      {
        name: '行业风险等级',
        weight: 0.5,
        levels: ['低风险', '中风险', '高风险']
      },
      {
        name: '企业规模',
        weight: 0.5,
        levels: ['小型', '中型', '大型']
      }
    ],
    matrix: [
      { x: 1, y: 1, level: '低', color: '#67C23A', description: '低风险行业，小型企业' },
      { x: 2, y: 1, level: '中', color: '#E6A23C', description: '中风险行业，小型企业' },
      { x: 3, y: 1, level: '高', color: '#F56C6C', description: '高风险行业，小型企业' },
      { x: 1, y: 2, level: '中', color: '#E6A23C', description: '低风险行业，中型企业' },
      { x: 2, y: 2, level: '中', color: '#E6A23C', description: '中风险行业，中型企业' },
      { x: 3, y: 2, level: '高', color: '#F56C6C', description: '高风险行业，中型企业' },
      { x: 1, y: 3, level: '中', color: '#E6A23C', description: '低风险行业，大型企业' },
      { x: 2, y: 3, level: '高', color: '#F56C6C', description: '中风险行业，大型企业' },
      { x: 3, y: 3, level: '高', color: '#F56C6C', description: '高风险行业，大型企业' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: '产品风险矩阵',
    code: 'PRODUCT_RISK_MATRIX',
    description: '产品风险评估矩阵',
    category: '产品风险',
    status: 1,
    enterpriseTypes: ['C', 'D', 'E', 'CDE'],
    dimensions: [
      {
        name: '产品复杂度',
        weight: 0.4,
        levels: ['简单', '中等', '复杂']
      },
      {
        name: '市场风险',
        weight: 0.6,
        levels: ['低', '中', '高']
      }
    ],
    matrix: [
      { x: 1, y: 1, level: '低', color: '#67C23A', description: '简单产品，低市场风险' },
      { x: 2, y: 1, level: '中', color: '#E6A23C', description: '中等产品，低市场风险' },
      { x: 3, y: 1, level: '高', color: '#F56C6C', description: '复杂产品，低市场风险' },
      { x: 1, y: 2, level: '中', color: '#E6A23C', description: '简单产品，中市场风险' },
      { x: 2, y: 2, level: '中', color: '#E6A23C', description: '中等产品，中市场风险' },
      { x: 3, y: 2, level: '高', color: '#F56C6C', description: '复杂产品，中市场风险' },
      { x: 1, y: 3, level: '高', color: '#F56C6C', description: '简单产品，高市场风险' },
      { x: 2, y: 3, level: '高', color: '#F56C6C', description: '中等产品，高市场风险' },
      { x: 3, y: 3, level: '高', color: '#F56C6C', description: '复杂产品，高市场风险' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
]

// 评分项MOCK数据
export const mockScoreItemList = [
  {
    id: 1,
    name: '管理层承诺与文化导向',
    code: 'MANAGEMENT_COMMITMENT',
    description: '评估管理层对风险管理的承诺程度',
    category: '风险识别能力',
    weight: 0.15,
    maxScore: 5,
    formulaId: 1,
    formulaName: '管理层承诺与文化导向公式',
    coefficient: 1.2,
    enterpriseTypes: ['C', 'D', 'E'],
    criteria: [
      { score: 1, description: '临时关注，只看重短期运营风险' },
      { score: 2, description: '刚开始认识到重要性，但参与得少' },
      { score: 3, description: '明确支持风险管控，参与部分决策' },
      { score: 4, description: '深度参与决策，按风险情况分配资源' },
      { score: 5, description: '走在行业前列，能引领风险管理方向' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '风险责任明确',
    code: 'RISK_RESPONSIBILITY',
    description: '评估企业风险责任划分的明确程度',
    category: '风险识别能力',
    weight: 0.15,
    maxScore: 5,
    formulaId: 2,
    formulaName: '风险责任明确公式',
    coefficient: 1.0,
    enterpriseTypes: ['A', 'B', 'C', 'D', 'E'],
    criteria: [
      { score: 1, description: '业务流程乱，责任不明确' },
      { score: 2, description: '初步建立了流程，相关人员开始参与' },
      { score: 3, description: '明确了流程和责任，有基本监督' },
      { score: 4, description: '流程和责任体系很完善，相关专业人员深度参与' },
      { score: 5, description: '流程和责任体系在行业标准，样样都能对标行业标杆' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: '风险流程标准化',
    code: 'RISK_PROCESS_STANDARDIZATION',
    description: '评估企业风险流程的标准化程度',
    category: '风险评估能力',
    weight: 0.15,
    maxScore: 5,
    formulaId: 3,
    formulaName: '风险流程标准化公式',
    coefficient: 1.1,
    enterpriseTypes: ['B', 'C', 'D'],
    criteria: [
      { score: 1, description: '没有流程，步骤不全，各环节也不合' },
      { score: 2, description: '有初步流程框架，但不标准，执行监督跟不上' },
      { score: 3, description: '有标准流程和模板，各类都有指导' },
      { score: 4, description: '流程和模板很完善，能数字化动态管理' },
      { score: 5, description: '流程和模板成行业标准，到处能借鉴' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 4,
    name: '风险沟通与认知一致性',
    code: 'RISK_COMMUNICATION',
    description: '评估企业风险沟通和认知的一致性',
    category: '风险评估能力',
    weight: 0.15,
    maxScore: 5,
    formulaId: 4,
    formulaName: '风险沟通与认知一致性公式',
    coefficient: 0.9,
    enterpriseTypes: ['C', 'D', 'E'],
    criteria: [
      { score: 1, description: '基本没风险文化，流程不统一，责任不清楚' },
      { score: 2, description: '有风险文化，传达机制初步建立' },
      { score: 3, description: '有了风险文化和传达体系，流程统一' },
      { score: 4, description: '把风险责任和考核挂钩，跨部门沟通顺畅' },
      { score: 5, description: '风险文化成了企业共识，和外部交流也很顺畅' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 5,
    name: '风险监控与响应机制',
    code: 'RISK_MONITORING_RESPONSE',
    description: '评估企业风险监控和应急响应机制',
    category: '风险应对能力',
    weight: 0.2,
    maxScore: 5,
    formulaId: 5,
    formulaName: '风险监控与响应机制公式',
    coefficient: 1.3,
    enterpriseTypes: ['A', 'B', 'C'],
    criteria: [
      { score: 1, description: '没有监控机制，出了事才处理' },
      { score: 2, description: '有监控机制，但响应慢，报告不规范' },
      { score: 3, description: '有监控机制和应急预案，能自动监测和报警' },
      { score: 4, description: '监控和应急响应机制完善，能自动联动' },
      { score: 5, description: '监控和应急响应机制行业标杆，能预测风险趋势' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 6,
    name: '风险偏好与容忍度管理',
    code: 'RISK_PREFERENCE_TOLERANCE',
    description: '评估企业风险偏好和容忍度的管理',
    category: '风险应对能力',
    weight: 0.2,
    maxScore: 5,
    formulaId: 6,
    formulaName: '风险偏好与容忍度管理公式',
    coefficient: 1.15,
    enterpriseTypes: ['D', 'E'],
    criteria: [
      { score: 1, description: '没有风险偏好，简单应对风险' },
      { score: 2, description: '有风险偏好，但不全面，有些模糊' },
      { score: 3, description: '有风险偏好，定期更新，有基本监控' },
      { score: 4, description: '风险偏好全面明确，与战略相关，有监控' },
      { score: 5, description: '风险偏好与容忍度管理行业领先，动态调整' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
] 