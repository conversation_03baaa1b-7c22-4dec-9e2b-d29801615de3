// 模板引擎MOCK数据
export const mockDataSources = [
  {
    id: 1,
    name: "客户信息数据源",
    code: "CUSTOMER_INFO",
    description: "客户基本信息数据源",
    type: "mysql",
    status: 1,
    fields: [
      { name: "customer_id", label: "客户ID", type: "string" },
      { name: "customer_name", label: "客户名称", type: "string" },
      { name: "customer_type", label: "客户类型", type: "string" },
      { name: "create_time", label: "创建时间", type: "datetime" }
    ],
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "交易记录数据源",
    code: "TRANSACTION_RECORD",
    description: "客户交易记录数据源",
    type: "oracle",
    status: 1,
    fields: [
      { name: "transaction_id", label: "交易ID", type: "string" },
      { name: "customer_id", label: "客户ID", type: "string" },
      { name: "amount", label: "交易金额", type: "decimal" },
      { name: "transaction_time", label: "交易时间", type: "datetime" }
    ],
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  }
]

export const mockIndicators = [
  {
    id: 1,
    name: "客户活跃度指标",
    code: "CUSTOMER_ACTIVITY",
    description: "评估客户活跃程度的指标",
    formula: "COUNT(transaction_id) / DATEDIFF(NOW(), MIN(create_time))",
    dataSource: "TRANSACTION_RECORD",
    status: 1,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "客户价值指标",
    code: "CUSTOMER_VALUE",
    description: "评估客户价值的指标",
    formula: "SUM(amount) / COUNT(DISTINCT customer_id)",
    dataSource: "TRANSACTION_RECORD",
    status: 1,
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  }
]

export const mockStrategies = [
  {
    id: 1,
    name: "高价值客户策略",
    code: "HIGH_VALUE_STRATEGY",
    description: "针对高价值客户的营销策略",
    status: 1,
    indicators: ["CUSTOMER_VALUE"],
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "活跃客户策略",
    code: "ACTIVE_CUSTOMER_STRATEGY",
    description: "针对活跃客户的营销策略",
    status: 1,
    indicators: ["CUSTOMER_ACTIVITY"],
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-15 10:30:00"
  }
]

export const mockBatches = [
  {
    id: 1,
    strategyId: 1,
    name: "高价值客户策略批次1",
    status: "completed",
    startTime: "2024-01-15 10:30:00",
    endTime: "2024-01-15 11:30:00",
    totalRecords: 1000,
    processedRecords: 1000,
    successRecords: 950,
    failedRecords: 50
  },
  {
    id: 2,
    strategyId: 2,
    name: "活跃客户策略批次1",
    status: "running",
    startTime: "2024-01-15 12:00:00",
    endTime: null,
    totalRecords: 2000,
    processedRecords: 1200,
    successRecords: 1150,
    failedRecords: 50
  }
] 