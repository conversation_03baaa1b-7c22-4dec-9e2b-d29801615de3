// 常数设置MOCK数据
export const mockConstants = [
  {
    id: 1,
    name: 'A',
    code: 'COEFFICIENT_A',
    description: '风险评分计算系数A，用于公式计算',
    category: '系数参数',
    value: '0.25',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: 'B',
    code: 'COEFFICIENT_B',
    description: '风险评分计算系数B，用于公式计算',
    category: '系数参数',
    value: '0.35',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 3,
    name: 'C',
    code: 'COEFFICIENT_C',
    description: '风险评分计算系数C，用于公式计算',
    category: '系数参数',
    value: '0.15',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 4,
    name: 'D',
    code: 'COEFFICIENT_D',
    description: '风险评分计算系数D，用于公式计算',
    category: '系数参数',
    value: '0.1',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 5,
    name: 'E',
    code: 'COEFFICIENT_E',
    description: '风险评分计算系数E，用于公式计算',
    category: '系数参数',
    value: '0.15',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 6,
    name: 'PI',
    code: 'MATHEMATICAL_PI',
    description: '圆周率常数，用于公式计算',
    category: '数学常数',
    value: '3.14159265359',
    unit: '',
    dataType: 'number',
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
]

// 常数分类MOCK数据
export const mockCategories = [
  {
    id: 1,
    name: '系数参数',
    description: '系数相关的参数设置',
    count: 5
  },
  {
    id: 2,
    name: '数学常数',
    description: '数学计算相关的常数设置',
    count: 1
  }
] 