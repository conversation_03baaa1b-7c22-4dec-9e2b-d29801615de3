// 统一MOCK数据管理入口
// 问卷模块
export { default as questionnaireMock } from './modules/questionnaire.json'

// 基础配置模块
// Export employeeBenefitProcess correctly to avoid conflicts
export { employeeBenefitProcess } from './modules/basicConfig/employeeBenefitProcess.js'
export { default as industryRiskConfig } from './modules/basicConfig/industryRiskConfig.json'
export { default as industryRiskMatrixMock } from './modules/basicConfig/industryRiskMatrix.json'
export { default as dicItems } from './modules/basicConfig/dicItems.json'
export { default as onlineProductConfig } from './modules/basicConfig/onlineProductConfig.json'
export { default as enterpriseLimitRule } from './modules/basicConfig/enterpriseLimitRule.json'
export { default as enterpriseTemplateMock } from './modules/basicConfig/enterpriseTemplate.json'
export { default as industryLimitRules } from './modules/basicConfig/industryLimitRules.json'

// 公式引擎模块 - 直接从原始文件导入
import { mockFormulas as formulaData, mockCategories as formulaCategoryData } from './modules/formulaEngine/formulas.js'
export const mockFormulas = formulaData;
export const mockFormulaCategories = formulaCategoryData;

// 风险矩阵模块
export { mockRiskMatrixList, mockScoreItemList } from './modules/riskMatrix/riskMatrix.js'

// 常数设置模块
export { mockConstants, mockCategories as constantCategories } from './modules/constantSetting/constants.js'

// 基础配置模块 - 数据模板和服务流程
import { mockDataTemplates as dataTemplates, mockServiceProcesses as serviceProcesses } from './modules/basicConfig/templates_fixed.js'
export const mockDataTemplates = dataTemplates;
export const mockServiceProcesses = serviceProcesses;

// GP规则模块
export { default as gpruleCfg } from './modules/gpRule/gpruleCfg.json'

// 企业模块
export { mockEnterpriseList, mockEnterpriseTypes, mockIndustries } from './modules/enterprise/enterprise.js'
export { mockStatsData } from './modules/enterprise/stats.js'
export { mockEnterpriseTypeList } from './modules/enterprise/type.js'

// 模板引擎模块
export { mockDataSources, mockIndicators, mockStrategies, mockBatches } from './modules/template/template.js'

// 策略匹配管理模块
export { mockStrategyMatchList, mockStrategyExecutedRecords, mockProcessMindData } from './modules/strategyMatchManage/strategyMatch.js'

// 用户信息模块
export { mockUserInfo, getUserInfo, getWebUserInfo, updateUserInfo, getUserPermissions, changeUserPassword } from './modules/user/userInfo.js'

// 通用工具函数
export const mockDelay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

// MOCK数据版本信息
export const mockVersion = {
  version: '1.0.0',
  lastUpdate: '2024-01-15',
  description: '统一MOCK数据管理'
}