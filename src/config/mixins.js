//使用方法:1，拷贝html。2、normalResetQuery中调用this.mixinsNormalResetQuery();
export const searchFormExpand = {
    data(){
      return {
        startDate:'',
        cascaderFormTemp:[
          {
            lable: "时间范围",
            name:"timeType",
            type: "radio-date-picker",
            width:"600px",
            list: [
              {
                dicItemName:"过去1小时",
                dicItemCode:"1"
              },
              {
                dicItemName:"过去48小时",
                dicItemCode:"2"
              },
              {
                dicItemName:"指定日期",
                dicItemCode:"3"
              }
            ],
            datePicker:{
              elType: "DatePicker",
              clearable:false,
              options: [
                {
                  name: "startDate",
                  placeholder: "开始时间",
                  value: "",
                },
                {
                  name: "endDate",
                  placeholder: "结束时间",
                  value: ""
                }
              ],
              pickerOptions:{
                disabledDate:(time) =>{
                    const afterTime = time.getTime() > new Date().getTime();//今天之后的时间
                    let month = 7*24*60*60*1000 // 设定日期范围
                    const minTime = this.startDate - month
                    const maxTime = this.startDate + month
                    let customTime = null; 
                    if(this.startDate){
                        customTime = time.getTime() < minTime || time.getTime() > maxTime;// 选中第一个时，前后30天[-30,30]共60天可选选择，超出的不可选，这里的天数按自己喜欢设定
                    }
                    console.log(customTime,"customTime")
                    return customTime || afterTime //禁用今天及今天之后
                },
                onPick:({maxDate,minDate}) =>{
                    this.startDate = minDate && minDate.getTime();
                    if (maxDate) {
                        this.startDate = ''// 选中后一个时，要把第一个的赋值清空
                    }
                }
              },
              
            },
          },
        ],
      }
    },
    methods:{
      searchRadioGroup(e){
        let options = this.cascaderFormTemp[0].datePicker.options;
        let param = this.initParam.param;
        if(e!=3){
          delete param[options[0].name];
          delete param[options[1].name];
        }else{
          let localeDate = new Date().toLocaleDateString().replace(/\//g,'-');
          this.getDateTime([localeDate,localeDate],options);
        }
      },
      getDateTime(arr,options) {
        if(!arr)arr=['',''];
        this.initParam.param[options[0].name] = options[0].value = arr[0]+' 00:00:00';
        this.initParam.param[options[1].name] = options[1].value = arr[1]+' 23:59:59';
        this.$forceUpdate()
      },
      mixinsNormalResetQuery(){
        this.cascaderFormTemp = this.$options.data().cascaderFormTemp;
      }
    }
}