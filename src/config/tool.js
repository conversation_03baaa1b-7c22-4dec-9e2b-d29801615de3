import {
  getDicItems
} from "@/api/dictionary/index";

import store from "@/store/index.js";
import _ from "lodash";


/**
 * 1.判断对象是否为空
 * 2.判断字符串是否有值
 * @param o {Object}
 * @return 不为空返回true 为空返回false
 */
export const isNotEmpty = o => {
  if (o === "" || o === undefined || o === null) return false;

  if (typeof o == "String" && o.trim().length == 0) return false;

  return true;
};

/**
 * 将source和target共有属性，由source赋值至target
 * @param target {Object}
 * @param source {Object}
 * @param ignoreProperties {Array} 忽略的属性
 * @param isDeep {Boolean} 是否进行深层赋值
 */
export const mergeProperties = function (
  target,
  source,
  ignoreProperties,
  isDeep
) {
  if (!target || !source) return target;

  if (isDeep == null || isDeep == "undefined") isDeep = false;

  ignoreProperties = ignoreProperties || [];

  for (let i in target) {
    if (contains(ignoreProperties, i)) continue;

    if (i in source) {
      target[i] = source[i];
    } else if (typeof target[i] == "object" && isDeep) {
      mergeProperties(target[i], source, ignoreProperties, isDeep);
    }
  }
  return target;
};

/**
 * 将source属性及值复制至target
 * @param target {Object}
 * @param source {Object}
 * @param ignoreProperties {Array} 忽略的属性
 */
export const unionProperties = function (target, source, ignoreProperties) {
  if (!target || !source) return target;

  ignoreProperties = ignoreProperties || [];

  for (let i in source) {
    if (contains(ignoreProperties, i)) continue;

    target[i] = source[i];
  }

  return target;
};

/**
 * 判断数组是否包含指定元素
 * @param arr {Array}
 * @param obj {Object}
 */
export const contains = function (arr, obj) {
  let i = arr.length;
  while (i--) {
    if (arr[i] === obj) {
      return true;
    }
  }
  return false;
};

/**
 * 转码通用方法
 * @param arr {Array} 字典配置
 * @param obj {Object} 转码对象,可以是数组或对象
 * @param isReverse {Boolean} 是否反转,默认fale
 */
export const dicFormatter = function (dicConfig, obj, isReverse) {
  if (obj instanceof Array) {
    for (let i = 0; i < obj.length; i++) {
      objFormatter(obj[i]);
    }
  } else if (obj instanceof Object) {
    objFormatter(obj);
  }

  function objFormatter(o) {
    dicConfig.forEach(element => {
      if (o.hasOwnProperty(element.property)) {
        let items = store.state.dicMap.get(element.id) || [];
        items.forEach(function (value, key, mapObj) {
          if (isReverse) {
            if (o[element.property] == value.dicItemName) {
              o[element.property] = value.dicItemCode + "";
            }
          } else {
            if (o[element.property] == value.dicItemCode) {
              o[element.property] = value.dicItemName;
            }
          }
        });
      }
    });
  }
};




export const hasRights = btn => {
  let authSet = store.state["layoutStore"].authSet
  // 部分权限
  if (btn == ''||!btn) {
    return true
  }
  for (let i = 0; i < authSet.length; i++) {
    if (btn == authSet[i]) {
      return true;
    }
  }
  return false
};




// 获取字典数据项
// dicCode 字典类型   force 是否强制刷新字典
export const getDicItemList = async (dicCode, force = false) => {
  let arr = [];
  let dicMap = store.state.dicMap
  if (_.has(dicMap, dicCode) && dicMap[dicCode] && dicMap[dicCode].length > 0 && !force ) {
    return dicMap[dicCode]
  }
  let res = await getDicItems({
    dicCode: dicCode
  });
  if (res) {
    arr = res;
    store.commit("mapDicData", {
      dicCode: dicCode,
      dicItems: arr
    })
  }
  return arr;
}

export const getTenants = () => {
  let arr = [];
  let tenants = store.state.userTenants;
  if (tenants) {
    tenants.forEach(el => {
      let item = {};
      item.dicItemCode = el.tenantId;
      item.dicItemName = el.shortName;
      arr.push(item);
    });
  }
  return arr;
}