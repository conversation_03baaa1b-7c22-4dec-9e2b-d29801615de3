import { mockDelay } from '@/mock'
import { mockRiskMatrixList, mockScoreItemList, industryRiskConfig } from '@/mock'

// 获取风险矩阵列表
export const getRiskMatrixList = async (params = {}) => {
  await mockDelay();
  
  let filteredData = [...mockRiskMatrixList];
  
  // 根据参数过滤数据
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(params.name.toLowerCase())
    );
  }
  
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  // 分页处理
  const pageNum = params.pageNum || 1;
  const pageSize = params.pageSize || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);
  
  return {
    code: 200,
    message: "success",
    data: {
      list: paginatedData,
      total: filteredData.length,
      pageNum,
      pageSize,
      totalPages: Math.ceil(filteredData.length / pageSize)
    }
  };
};

// 获取风险矩阵详情
export const getRiskMatrixDetail = async (id) => {
  await mockDelay();
  
  const matrix = mockRiskMatrixList.find(item => item.id === parseInt(id));
  
  if (!matrix) {
    return {
      code: 404,
      message: "风险矩阵不存在",
      data: null
    };
  }
  
  // 将dimensions映射为categories以匹配UI期望的数据结构
  const mappedMatrix = {
    ...matrix,
    categories: matrix.dimensions ? matrix.dimensions.map((dimension, dimIndex) => {
      // 根据维度名称查找对应的评分项
      const relatedScoreItems = mockScoreItemList.filter(item => 
        item.category === dimension.name
      ).map(item => item.id);
      
      console.log(`矩阵 ${matrix.name} 的维度 ${dimension.name} 关联的评分项:`, relatedScoreItems);
      
      // 根据levels数量设置档次范围
      let levelValues = [];
      if (dimension.levels && dimension.levels.length > 0) {
        const levelCount = dimension.levels.length;
        const step = 100 / levelCount;
        
        // 为每个档次创建评分范围
        levelValues = dimension.levels.map((level, index) => {
          const minValue = index * step;
          const maxValue = (index + 1) * step;
          return {
            name: level,
            minValue: parseFloat(minValue.toFixed(2)),
            maxValue: parseFloat((index === levelCount - 1 ? 100 : maxValue).toFixed(2)),
            description: `${dimension.name}${level}`
          };
        });
      } else {
        // 默认五档
        levelValues = [
          { name: '一档', minValue: 0, maxValue: 20.00, description: '一档风险级别' },
          { name: '二档', minValue: 20.01, maxValue: 40.00, description: '二档风险级别' },
          { name: '三档', minValue: 40.01, maxValue: 60.00, description: '三档风险级别' },
          { name: '四档', minValue: 60.01, maxValue: 80.00, description: '四档风险级别' },
          { name: '五档', minValue: 80.01, maxValue: 100.00, description: '五档风险级别' }
        ];
      }
      
      return {
        id: parseInt(id) * 1000 + dimIndex + 1, // 使用矩阵id和维度索引生成固定id
        name: dimension.name,
        weight: dimension.weight || 1.0,
        scoreItems: relatedScoreItems, // 关联评分项
        calculationMethod: 'sum', // 默认为sum
        levels: levelValues
      };
    }) : []
  };
  
  // 确保最后一个level的maxValue为100
  if (mappedMatrix.categories && mappedMatrix.categories.length > 0) {
    mappedMatrix.categories.forEach(category => {
      if (category.levels && category.levels.length > 0) {
        category.levels[category.levels.length - 1].maxValue = 100.00;
      }
    });
  }
  
  console.log(`风险矩阵 ${matrix.name} 详情加载完成，包含 ${mappedMatrix.categories.length} 个类别`);
  
  return {
    code: 200,
    message: "success",
    data: mappedMatrix
  };
};

// 创建风险矩阵
export const createRiskMatrix = async (data) => {
  await mockDelay();
  
  const newMatrix = {
    id: Math.max(...mockRiskMatrixList.map(m => m.id)) + 1,
    ...data,
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };
  
  mockRiskMatrixList.push(newMatrix);
  
  return {
    code: 200,
    message: "创建成功",
    data: newMatrix
  };
};

// 更新风险矩阵
export const updateRiskMatrix = async (id, data) => {
  await mockDelay();
  
  const index = mockRiskMatrixList.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "风险矩阵不存在",
      data: null
    };
  }
  
  mockRiskMatrixList[index] = {
    ...mockRiskMatrixList[index],
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: mockRiskMatrixList[index]
  };
};

// 删除风险矩阵
export const deleteRiskMatrix = async (id) => {
  await mockDelay();
  
  const index = mockRiskMatrixList.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "风险矩阵不存在",
      data: null
    };
  }
  
  mockRiskMatrixList.splice(index, 1);
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 获取评分项列表
export const getScoreItemList = async (params = {}) => {
  await mockDelay();
  
  let filteredData = [...mockScoreItemList];
  
  // 根据参数过滤数据
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(params.name.toLowerCase())
    );
  }
  
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  // 分页处理
  const pageNum = params.pageNum || 1;
  const pageSize = params.pageSize || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);
  
  return {
    code: 200,
    message: "success",
    data: {
      list: paginatedData,
      total: filteredData.length,
      pageNum,
      pageSize,
      totalPages: Math.ceil(filteredData.length / pageSize)
    }
  };
};

// 获取评分项详情
export const getScoreItemDetail = async (id) => {
  await mockDelay();
  
  const item = mockScoreItemList.find(item => item.id === parseInt(id));
  
  if (!item) {
    return {
      code: 404,
      message: "评分项不存在",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: item
  };
};

// 创建评分项
export const createScoreItem = async (data) => {
  await mockDelay();
  
  const newItem = {
    id: Math.max(...mockScoreItemList.map(i => i.id)) + 1,
    ...data,
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };
  
  mockScoreItemList.push(newItem);
  
  return {
    code: 200,
    message: "创建成功",
    data: newItem
  };
};

// 更新评分项
export const updateScoreItem = async (id, data) => {
  await mockDelay();
  
  const index = mockScoreItemList.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "评分项不存在",
      data: null
    };
  }
  
  mockScoreItemList[index] = {
    ...mockScoreItemList[index],
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: mockScoreItemList[index]
  };
};

// 删除评分项
export const deleteScoreItem = async (id) => {
  await mockDelay();
  
  const index = mockScoreItemList.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "评分项不存在",
      data: null
    };
  }
  
  mockScoreItemList.splice(index, 1);
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 创建或更新评分项
export const saveScoreItem = async (data) => {
  await mockDelay();
  
  if (data.id) {
    // 更新现有评分项
    return updateScoreItem(data.id, data);
  } else {
    // 创建新评分项
    return createScoreItem(data);
  }
};

// 保存风险矩阵
export const saveRiskMatrix = async (formData) => {
  await mockDelay();
  
  try {
    if (formData.id) {
      // 更新现有矩阵
      const index = mockRiskMatrixList.findIndex(item => item.id === parseInt(formData.id));
      
      if (index === -1) {
        return {
          code: 404,
          message: "风险矩阵不存在",
          data: null
        };
      }
      
      // 将categories转换回dimensions格式
      const dimensions = formData.categories.map(category => {
        // 获取该类别关联的评分项
        const scoreItems = category.scoreItems || [];
        
        return {
          name: category.name,
          weight: category.weight || 1.0,
          levels: category.levels ? category.levels.map(level => level.name) : []
        };
      });
      
      mockRiskMatrixList[index] = {
        ...mockRiskMatrixList[index],
        name: formData.name,
        description: formData.description,
        dimensions: dimensions,
        updateTime: new Date().toLocaleString(),
        updateUser: 'admin'
      };
      
      // 更新评分项的类别关联
      formData.categories.forEach(category => {
        if (category.scoreItems && category.scoreItems.length > 0) {
          category.scoreItems.forEach(scoreItemId => {
            const scoreItemIndex = mockScoreItemList.findIndex(item => item.id === scoreItemId);
            if (scoreItemIndex !== -1) {
              mockScoreItemList[scoreItemIndex].category = category.name;
            }
          });
        }
      });
      
      return {
        code: 200,
        message: "更新成功",
        data: mockRiskMatrixList[index]
      };
    } else {
      // 创建新矩阵
      const newId = Math.max(...mockRiskMatrixList.map(m => m.id)) + 1;
      
      // 将categories转换为dimensions格式
      const dimensions = formData.categories.map(category => {
        // 获取该类别关联的评分项
        const scoreItems = category.scoreItems || [];
        
        return {
          name: category.name,
          weight: category.weight || 1.0,
          levels: category.levels ? category.levels.map(level => level.name) : []
        };
      });
      
      const newMatrix = {
        id: newId,
        name: formData.name,
        code: formData.name.toUpperCase().replace(/\s+/g, '_'),
        description: formData.description,
        category: formData.category || '风险矩阵',
        status: 1,
        dimensions: dimensions,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        createUser: 'admin',
        updateUser: 'admin'
      };
      
      mockRiskMatrixList.push(newMatrix);
      
      // 更新评分项的类别关联
      formData.categories.forEach(category => {
        if (category.scoreItems && category.scoreItems.length > 0) {
          category.scoreItems.forEach(scoreItemId => {
            const scoreItemIndex = mockScoreItemList.findIndex(item => item.id === scoreItemId);
            if (scoreItemIndex !== -1) {
              mockScoreItemList[scoreItemIndex].category = category.name;
            }
          });
        }
      });
      
      return {
        code: 200,
        message: "创建成功",
        data: newMatrix
      };
    }
  } catch (error) {
    console.error("保存风险矩阵失败:", error);
    return {
      code: 500,
      message: "保存失败: " + error.message,
      data: null
    };
  }
};

// 获取行业风险配置列表
export const getIndustryRiskConfigList = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: industryRiskConfig
  };
};

// 获取行业风险配置详情
export const getIndustryRiskConfigDetail = async (industryCode) => {
  await mockDelay();
  
  const config = industryRiskConfig.find(item => item.industryCode === industryCode);
  
  if (!config) {
    return {
      code: 404,
      message: "行业风险配置不存在",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: config
  };
};

// 更新行业风险配置
export const updateIndustryRiskConfig = async (industryCode, data) => {
  await mockDelay();
  
  const index = industryRiskConfig.findIndex(item => item.industryCode === industryCode);
  
  if (index === -1) {
    return {
      code: 404,
      message: "行业风险配置不存在",
      data: null
    };
  }
  
  // 更新配置
  industryRiskConfig[index] = {
    ...industryRiskConfig[index],
    ...data,
    updateTime: new Date().toLocaleString()
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: industryRiskConfig[index]
  };
};

// 获取风险等级选项
export const getRiskLevelOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 'HIGH', label: '高' },
      { value: 'MEDIUM', label: '中' },
      { value: 'LOW', label: '低' }
    ]
  };
};

// 获取行业级别选项
export const getIndustryLevelOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 1, label: '一级行业' },
      { value: 2, label: '二级行业' }
    ]
  };
};