import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";

// 角色列表分页接口
export const getRolePage = (data) =>
  http.Axios.post(rootPath + "/api/role/page", data);

// 角色权限树接口
export const selectAuthTree = (data) =>
  http.Axios.post(rootPath + "/api/role/selectAuthTree", data);

// 角色增改
export const saveOrUpdate = (data) =>
  http.Axios.post(rootPath + "/api/role/saveOrUpdate", data);

// 角色删除
export const deleteRole = (data) =>
  http.Axios.post(rootPath + "/api/role/delete", data);

// 角色权限配置
export const saveOrUpdateRoleAuth = (data) =>
  http.Axios.post(rootPath + "/api/role/saveOrUpdateRoleAuth", data);


// 角色增改
export const permissionSaveOrUpdate = (data) =>
  http.Axios.post(rootPath + "/api/auth/saveOrUpdate", data);

// 角色删除
export const deletePermission = (data) =>
  http.Axios.post(rootPath + "/api/auth/delete", data);

