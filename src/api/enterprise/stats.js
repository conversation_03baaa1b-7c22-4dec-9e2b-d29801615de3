// 企业统计相关 API
import { mockRequest } from '@/utils/httpService'
import { mockStatsData, mockDelay } from '@/mock'

// 获取企业统计概览
export const getEnterpriseStatsOverview = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData.overview
  };
};

// 获取企业类型分布图表数据
export const getEnterpriseTypeChartData = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData.chartData
  };
};

// 获取企业类型详细统计
export const getEnterpriseTypeStats = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData.tableData
  };
};

// 获取行业分布统计
export const getIndustryStats = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData.industryStats
  };
};

// 获取风险矩阵统计
export const getRiskMatrixStats = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData.riskMatrixStats
  };
};

// 获取完整统计数据
export const getCompleteStats = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "获取成功",
    data: mockStatsData
  };
}; 