import { get, post, put, del } from "@/utils/httpService";
import { questionnaireMock, mockDelay } from '@/mock'

// 获取问卷数据
export const getQuestionnaireData = async (params = {}) => {
  await mockDelay();
  
  const { enterpriseType = 'CDE' } = params;
  const questionnaires = questionnaireMock.questionnaires;
  
  // 根据企业类型获取对应的问卷
  let questionnaire = null;
  
  if (enterpriseType === 'CDE' || enterpriseType.includes('C') || enterpriseType.includes('D') || enterpriseType.includes('E')) {
    questionnaire = questionnaires.CDE;
  } else if (enterpriseType === 'AB' || enterpriseType.includes('A') || enterpriseType.includes('B')) {
    questionnaire = questionnaires.AB;
  } else {
    // 默认使用CDE问卷
    questionnaire = questionnaires.CDE;
  }
  
  if (!questionnaire) {
    return {
      code: 404,
      message: "未找到对应企业类型的问卷",
      data: null
    };
  }
  
  // 转换数据结构以匹配组件期望的格式
  const transformedData = {
    id: questionnaire.id,
    title: questionnaire.title,
    description: questionnaire.description,
    enterpriseTypes: questionnaire.enterpriseTypes,
    questions: questionnaire.questions.map(q => ({
      ...q,
      title: q.question, // 将question字段映射为title
      type: 'single' // 默认都是单选题
    }))
  };
  
  return {
    code: 200,
    message: "success",
    data: transformedData
  };
};

// 获取问卷列表
export const getQuestionnaireList = async (params = {}) => {
  await mockDelay();
  
  const questionnaires = Object.values(questionnaireMock.questionnaires);
  
  return {
    code: 200,
    message: "success",
    data: {
      list: questionnaires,
      total: questionnaires.length,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    }
  };
};

// 保存问卷答案
export const saveQuestionnaireAnswers = async (data) => {
  await mockDelay();
  
  // 这里可以添加保存答案的逻辑
  console.log('保存问卷答案:', data);
  
  return {
    code: 200,
    message: "保存成功",
    data: {
      id: Date.now(),
      ...data,
      createTime: new Date().toLocaleString()
    }
  };
}; 