import { mockDelay } from '@/mock'
import { questionnaireMock } from '@/mock'

// 获取问卷列表
export const getQuestionnaireList = async (params = {}) => {
  await mockDelay();
  
  const { pageNum = 1, pageSize = 10, name = '', description = '', enterpriseType = '' } = params;
  
  let questionnaires = Object.values(questionnaireMock.questionnaires).map(q => ({
    id: q.id,
    name: q.title,
    description: q.description,
    enterpriseTypes: q.enterpriseTypes,
    questionCount: q.questions.length,
    status: 'active',
    createTime: '2024-06-01 10:00:00',
    updateTime: '2024-06-01 10:00:00',
    createUser: 'admin',
    updateUser: 'admin'
  }));
  
  // 过滤逻辑
  if (name) {
    questionnaires = questionnaires.filter(q => q.name.includes(name));
  }
  if (description) {
    questionnaires = questionnaires.filter(q => q.description.includes(description));
  }
  if (enterpriseType) {
    questionnaires = questionnaires.filter(q => q.enterpriseTypes.includes(enterpriseType));
  }
  
  const total = questionnaires.length;
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;
  const list = questionnaires.slice(start, end);
  
  return {
    code: 200,
    message: "success",
    data: {
      list,
      total,
      pageNum,
      pageSize
    }
  };
};

// 获取问卷详情
export const getQuestionnaireDetail = async (id) => {
  await mockDelay();
  
  const questionnaires = Object.values(questionnaireMock.questionnaires);
  const questionnaire = questionnaires.find(q => q.id === id);
  
  if (!questionnaire) {
    return {
      code: 404,
      message: "问卷不存在",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: questionnaire
  };
};

// 创建问卷
export const createQuestionnaire = async (data) => {
  await mockDelay();
  
  const newQuestionnaire = {
    id: `questionnaire_${Date.now()}`,
    ...data,
    status: 'active',
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "创建成功",
    data: newQuestionnaire
  };
};

// 更新问卷
export const updateQuestionnaire = async (id, data) => {
  await mockDelay();
  
  const updatedQuestionnaire = {
    id,
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: updatedQuestionnaire
  };
};

// 删除问卷
export const deleteQuestionnaire = async (id) => {
  await mockDelay();
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 获取企业类型选项
export const getEnterpriseTypeOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 'A', label: 'A类企业' },
      { value: 'B', label: 'B类企业' },
      { value: 'C', label: 'C类企业' },
      { value: 'D', label: 'D类企业' },
      { value: 'E', label: 'E类企业' }
    ]
  };
};

// 获取问卷状态选项
export const getQuestionnaireStatusOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '禁用' },
      { value: 'draft', label: '草稿' }
    ]
  };
};