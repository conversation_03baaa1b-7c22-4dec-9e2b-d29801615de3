import { mockDelay } from '@/mock'
import { onlineProductConfig } from '@/mock'

// 获取线上产品配置列表
export const getOnlineProductConfig = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: onlineProductConfig || []
  };
};

// 获取行业选项
export const getIndustryOptions = async () => {
  await mockDelay();
  
  // 返回行业选项，格式与组件期望的格式一致
  return {
    code: 200,
    message: "success",
    data: [
      { dicItemCode: 'IT', dicItemName: '信息技术' },
      { dicItemCode: 'FINANCE', dicItemName: '金融服务' },
      { dicItemCode: 'MANUFACTURE', dicItemName: '制造业' },
      { dicItemCode: 'TRADE', dicItemName: '贸易' },
      { dicItemCode: 'INTERNET', dicItemName: '互联网' },
      { dicItemCode: 'EDUCATION', dicItemName: '教育' },
      { dicItemCode: 'MEDICAL', dicItemName: '医疗健康' },
      { dicItemCode: 'REAL_ESTATE', dicItemName: '房地产' },
      { dicItemCode: 'TRANSPORT', dicItemName: '交通运输' },
      { dicItemCode: 'ENERGY', dicItemName: '能源化工' }
    ]
  };
};

// 获取指定行业的配置
export const getIndustryConfig = async (industryCode) => {
  await mockDelay();
  
  // 确保onlineProductConfig存在
  if (!onlineProductConfig || !Array.isArray(onlineProductConfig)) {
    return {
      code: 200,
      message: "success",
      data: {
        industryCode,
        industryName: getIndustryName(industryCode),
        configs: []
      }
    };
  }
  
  // 查找指定行业代码的配置
  const config = onlineProductConfig.find(item => item.industryCode === industryCode);
  
  if (!config) {
    // 如果没有找到配置，返回一个空的配置结构
    return {
      code: 200,
      message: "success",
      data: {
        industryCode,
        industryName: getIndustryName(industryCode),
        configs: []
      }
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: config
  };
};

// 保存行业配置
export const saveIndustryConfig = async (industryCode, configs) => {
  await mockDelay();
  
  // 确保onlineProductConfig是一个数组
  if (!global.onlineProductConfig || !Array.isArray(global.onlineProductConfig)) {
    // 如果onlineProductConfig不存在或不是数组，创建一个新数组
    global.onlineProductConfig = [];
  }
  
  const index = onlineProductConfig.findIndex(item => item.industryCode === industryCode);
  
  if (index === -1) {
    // 新增配置
    const newConfig = {
      industryCode,
      industryName: getIndustryName(industryCode),
      configs: configs
    };
    onlineProductConfig.push(newConfig);
    
    return {
      code: 200,
      message: "保存成功",
      data: newConfig
    };
  } else {
    // 更新配置
    onlineProductConfig[index].configs = configs;
  
    return {
      code: 200,
      message: "保存成功",
      data: onlineProductConfig[index]
    };
  }
};

// 删除行业配置
export const deleteIndustryConfig = async (industryCode) => {
  await mockDelay();
  
  const index = onlineProductConfig.findIndex(item => item.industryCode === industryCode);
  
  if (index === -1) {
    return {
      code: 404,
      message: "行业配置不存在",
      data: null
    };
  }
  
  onlineProductConfig.splice(index, 1);
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 获取险种类型选项
export const getInsuranceTypeOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { label: '雇主责任险', value: 'employer' },
      { label: '团体意外险', value: 'group' },
      { label: '境外意外险', value: 'overseas' },
      { label: '健康保险', value: 'health' },
      { label: '养老保险', value: 'pension' },
      { label: '财产保险', value: 'property' }
    ]
  };
};

// 获取产品配置详情
export const getOnlineProductConfigDetail = async (id) => {
  await mockDelay();
  
  // 确保onlineProductConfig存在
  if (!onlineProductConfig || !Array.isArray(onlineProductConfig)) {
    return {
      code: 404,
      message: "配置不存在",
      data: null
    };
  }
  
  // 在所有行业的配置中查找指定ID的配置
  for (const industry of onlineProductConfig) {
    if (industry.configs && Array.isArray(industry.configs)) {
      const config = industry.configs.find(item => item.id == id);
      if (config) {
        return {
          code: 200,
          message: "success",
          data: {
            ...config,
            industryCode: industry.industryCode
          }
        };
      }
    }
  }
  
  return {
    code: 404,
    message: "配置不存在",
    data: null
  };
};

// 辅助函数：根据行业代码获取行业名称
function getIndustryName(industryCode) {
  const industryMap = {
    'IT': '信息技术',
    'FINANCE': '金融服务',
    'MANUFACTURE': '制造业',
    'TRADE': '贸易',
    'INTERNET': '互联网',
    'EDUCATION': '教育',
    'MEDICAL': '医疗健康',
    'REAL_ESTATE': '房地产',
    'TRANSPORT': '交通运输',
    'ENERGY': '能源化工'
  };
  return industryMap[industryCode] || industryCode;
} 