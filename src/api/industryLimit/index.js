import { mockDelay } from '@/mock'
import { industryLimitRules } from '@/mock'

// 获取行业限制规则列表
export const getIndustryLimitRules = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: industryLimitRules
  };
};

// 获取行业限制规则详情
export const getIndustryLimitRuleDetail = async (id) => {
  await mockDelay();
  
  const rule = industryLimitRules.find(item => item.id === parseInt(id));
  
  if (!rule) {
    return {
      code: 404,
      message: "行业限制规则不存在",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: rule
  };
};

// 创建行业限制规则
export const createIndustryLimitRule = async (data) => {
  await mockDelay();
  
  const newRule = {
    id: Date.now(),
    ...data,
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "创建成功",
    data: newRule
  };
};

// 更新行业限制规则
export const updateIndustryLimitRule = async (id, data) => {
  await mockDelay();
  
  const updatedRule = {
    id: parseInt(id),
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: updatedRule
  };
};

// 删除行业限制规则
export const deleteIndustryLimitRule = async (id) => {
  await mockDelay();
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 获取字段选项
export const getFieldOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { code: 'enterpriseType', name: '企业类型' },
      { code: 'employeeCount', name: '员工数量' },
      { code: 'revenue', name: '营收' },
      { code: 'industryCode', name: '行业代码' },
      { code: 'riskLevel', name: '风险等级' },
      { code: 'registeredCapital', name: '注册资本' },
      { code: 'establishDate', name: '成立时间' },
      { code: 'businessScope', name: '经营范围' }
    ]
  };
};

// 获取操作符选项
export const getOperatorOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 'eq', label: '等于' },
      { value: 'contains', label: '包含' },
      { value: 'range', label: '区间' },
      { value: 'gt', label: '大于' },
      { value: 'lt', label: '小于' },
      { value: 'gte', label: '大于等于' },
      { value: 'lte', label: '小于等于' }
    ]
  };
};

// 获取动作类型选项
export const getActionTypeOptions = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: [
      { value: 'applyRule', label: '执行规则' },
      { value: 'noAction', label: '不执行' },
      { value: 'applyLimit', label: '应用限额' },
      { value: 'applyService', label: '应用服务' }
    ]
  };
}; 