import { get, post, put, del } from "@/utils/httpService";
import { mockFormulas, mockFormulaCategories, mockDelay } from '@/mock'
import { mockConstants } from '@/mock/modules/constantSetting/constants.js';

// 获取常量列表
const getConstants = async () => {
  await mockDelay();
  
  return {
    code: 200,
    message: "success",
    data: mockConstants || []
  };
};

// 创建静态备份，以防导入的数据丢失
const staticMockFormulas = [
  {
    id: 1,
    name: '管理层承诺与文化导向公式',
    description: '用于评估管理层承诺与文化导向的风险管理公式',
    formula: '(A * pow(x, 4) + B * sqrt(pow(x, 3) + 1) + C * sin(2 * PI * x / 5) + D * factorial(x) * log(x + 1) + E * pow(2, x)) / 3 * 100',
    variables: [
      { name: 'x', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '战略文化与治理框架',
    enterpriseType: ['A', 'B'], // 添加企业类型字段
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  },
  {
    id: 2,
    name: '风险责任明确公式',
    description: '用于风险责任明确的风险管理公式',
    formula: 'a * X + b * sqrt(X) + c * log(X + 1)',
    variables: [
      { name: 'a', type: 'number', defaultValue: 0.5, description: '线性权重系数' },
      { name: 'b', type: 'number', defaultValue: 0.3, description: '平方根权重系数' },
      { name: 'c', type: 'number', defaultValue: 0.2, description: '对数权重系数' },
      { name: 'X', type: 'variable', defaultValue: 5, description: '问卷得分' }
    ],
    category: '组织责任与指标体系',
    enterpriseType: ['C', 'D', 'E'], // 添加企业类型字段
    status: 1,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    createUser: 'admin',
    updateUser: 'admin'
  }
];

const staticMockCategories = [
  {
    id: 1,
    name: '战略文化与治理框架',
    description: '战略层面的风险管理公式',
    count: 4
  },
  {
    id: 2,
    name: '组织责任与指标体系',
    description: '组织层面的风险管理公式',
    count: 3
  },
  {
    id: 3,
    name: '风险评估与流程执行',
    description: '执行层面的风险管理公式',
    count: 7
  },
  {
    id: 4,
    name: '沟通系统与技术支持',
    description: '技术层面的风险管理公式',
    count: 2
  }
];

// 获取公式列表
export const getFormulaList = async (params = {}) => {
  await mockDelay();
  
  // 使用静态备份如果导入的数据丢失
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = [...mockFormulas];
  } else {
    formulas = [...staticMockFormulas];
  }
  
  let filteredData = [...formulas];
  
  // 根据参数过滤数据
  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }
  
  // 添加企业类型筛选
  if (params.enterpriseType && params.enterpriseType.length > 0) {
    filteredData = filteredData.filter(item => {
      // 如果公式没有指定企业类型，则认为适用于所有类型
      if (!item.enterpriseType || item.enterpriseType.length === 0) {
        return true;
      }
      // 检查是否有交集
      return params.enterpriseType.some(type => item.enterpriseType.includes(type));
    });
  }
  
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.toLowerCase().includes(params.name.toLowerCase())
    );
  }
  
  // 修改状态筛选逻辑，只有当status是数字类型时才进行筛选
  if (params.status !== undefined && params.status !== '' && params.status !== null) {
    // 将字符串转换为数字
    const statusValue = parseInt(params.status);
    if (!isNaN(statusValue)) {
      filteredData = filteredData.filter(item => item.status === statusValue);
    }
  }
  
  // 分页处理
  const pageNum = params.pageNum || 1;
  const pageSize = params.pageSize || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);
  
  return {
    code: 200,
    message: "success",
    data: {
      list: paginatedData,
      total: filteredData.length,
      pageNum,
      pageSize,
      totalPages: Math.ceil(filteredData.length / pageSize)
    }
  };
};

// 获取公式详情
export const getFormulaDetail = async (id) => {
  await mockDelay();
  
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = mockFormulas;
  } else {
    formulas = staticMockFormulas;
  }
  
  const formula = formulas.find(item => item.id === parseInt(id));
  
  if (!formula) {
    return {
      code: 404,
      message: "公式不存在",
      data: null
    };
  }
  
  return {
    code: 200,
    message: "success",
    data: formula
  };
};

// 创建公式
export const createFormula = async (data) => {
  await mockDelay();
  
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = mockFormulas;
  } else {
    formulas = staticMockFormulas;
  }
  
  const newFormula = {
    id: Math.max(...formulas.map(f => f.id)) + 1,
    ...data,
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };
  
  formulas.push(newFormula);
  
  return {
    code: 200,
    message: "创建成功",
    data: newFormula
  };
};

// 更新公式
export const updateFormula = async (id, data) => {
  await mockDelay();
  
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = mockFormulas;
  } else {
    formulas = staticMockFormulas;
  }
  
  const index = formulas.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "公式不存在",
      data: null
    };
  }
  
  formulas[index] = {
    ...formulas[index],
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };
  
  return {
    code: 200,
    message: "更新成功",
    data: formulas[index]
  };
};

// 删除公式
export const deleteFormula = async (id) => {
  await mockDelay();
  
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = mockFormulas;
  } else {
    formulas = staticMockFormulas;
  }
  
  const index = formulas.findIndex(item => item.id === parseInt(id));
  
  if (index === -1) {
    return {
      code: 404,
      message: "公式不存在",
      data: null
    };
  }
  
  formulas.splice(index, 1);
  
  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 批量删除公式
export const batchDeleteFormula = async (ids) => {
  await mockDelay();
  
  let formulas = [];
  if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
    formulas = mockFormulas;
  } else {
    formulas = staticMockFormulas;
  }
  
  const deletedCount = formulas.filter(item => ids.includes(item.id)).length;
  
  // 从后往前删除，避免索引变化
  for (let i = formulas.length - 1; i >= 0; i--) {
    if (ids.includes(formulas[i].id)) {
      formulas.splice(i, 1);
    }
  }
  
  return {
    code: 200,
    message: `成功删除${deletedCount}条记录`,
    data: null
  };
};

// 获取公式分类列表
export const getFormulaCategoryList = async () => {
  await mockDelay();
  
  // 使用静态备份如果导入的数据丢失
  let categories = [];
  if (Array.isArray(mockFormulaCategories) && mockFormulaCategories.length > 0) {
    categories = mockFormulaCategories;
  } else {
    categories = staticMockCategories;
  }
  
  return {
    code: 200,
    message: "success",
    data: categories
  };
};

// 计算公式
export const calculateFormula = async (id, variables) => {
  await mockDelay();
  
  try {
    // 获取公式详情
    let formulas = [];
    if (Array.isArray(mockFormulas) && mockFormulas.length > 0) {
      formulas = mockFormulas;
    } else {
      formulas = staticMockFormulas;
    }
    
    const formula = formulas.find(item => item.id === parseInt(id));
  
  if (!formula) {
    return {
      code: 404,
      message: "公式不存在",
      data: null
    };
  }
    
    // 获取公式变量
    const formulaVariables = formula.variables || [];
    
    // 获取常量
    let constants = [];
    try {
      const constantsResponse = await getConstants();
      if (constantsResponse.code === 200) {
        constants = constantsResponse.data.filter(item => item.status === 1);
      }
    } catch (error) {
      console.error("获取常量失败:", error);
    }
    
    // 合并变量和常量
    const allVariables = {};
    
    // 添加常量
    constants.forEach(constant => {
      allVariables[constant.name] = constant.value;
    });
    
    // 添加标准常量
    allVariables['PI'] = Math.PI;
    allVariables['E'] = Math.E;
    allVariables['A'] = 0.25; // 风险评分系数A
    allVariables['B'] = 0.35; // 风险评分系数B
    allVariables['C'] = 0.15; // 风险评分系数C
    allVariables['D'] = 0.1;  // 风险评分系数D
    allVariables['E'] = 0.15; // 风险评分系数E
    
    // 添加小写别名
    allVariables['pi'] = Math.PI;
    allVariables['e'] = Math.E;
    allVariables['a'] = 0.25;
    allVariables['b'] = 0.35;
    allVariables['c'] = 0.15;
    allVariables['d'] = 0.1;
    allVariables['e'] = 0.15;
    
    // 添加变量默认值
    formulaVariables.forEach(variable => {
      allVariables[variable.name] = variable.defaultValue;
    });
    
    // 覆盖用户提供的变量值
    if (variables && typeof variables === 'object') {
      Object.keys(variables).forEach(key => {
        if (allVariables.hasOwnProperty(key)) {
          // 将字符串转换为数字
          const value = parseFloat(variables[key]);
          if (!isNaN(value)) {
            allVariables[key] = value;
          }
        }
      });
    }
    
    // 获取公式内容
    const formulaStr = formula.formula;
    
    // 定义安全计算函数
    const calculateSafeFormula = (formula, vars) => {
      try {
        // 定义数学函数
        const sin = Math.sin;
        const cos = Math.cos;
        const tan = Math.tan;
        const sqrt = Math.sqrt;
        const pow = Math.pow;
        const abs = Math.abs;
        
        // 定义对数函数
        const log = (x) => {
          if (x <= 0) return 0;
          return Math.log10(x);
        };
        
        // 定义自然对数函数
        const ln = (x) => {
          if (x <= 0) return 0;
          return Math.log(x);
        };
        
        // 定义指数函数
        const exp = Math.exp;
        
        // 定义阶乘函数
        const factorial = (n) => {
          if (n < 0) return 0;
          if (n <= 1) return 1;
          let result = 1;
          for (let i = 2; i <= n; i++) {
            result *= i;
          }
          return result;
        };
        
        // 为所有可能的单字母变量添加默认值，防止未定义变量错误
        for (let i = 65; i <= 90; i++) {
          const upperChar = String.fromCharCode(i);
          const lowerChar = String.fromCharCode(i + 32);
          if (!vars[upperChar]) vars[upperChar] = 0;
          if (!vars[lowerChar]) vars[lowerChar] = 0;
        }
        
        // 替换幂运算符为pow函数
        let processedFormula = formula;
        
        // 先处理括号内的表达式
        processedFormula = processedFormula.replace(/\(([^()]+)\)\s*\^\s*([A-Za-z0-9_]+|\d+(\.\d+)?)/g, 'pow($1, $2)');
        
        // 再处理变量或数字的幂
        processedFormula = processedFormula.replace(/([A-Za-z0-9_]+|\d+(\.\d+)?)\s*\^\s*([A-Za-z0-9_]+|\d+(\.\d+)?)/g, 'pow($1, $3)');
        
        try {
          // 使用Function构造器创建一个函数来计算公式
          const calculate = new Function(
            ...Object.keys(vars),
            'sin', 'cos', 'tan', 'sqrt', 'pow', 'abs', 'log', 'ln', 'exp', 'factorial',
            `return ${processedFormula};`
          );
          
          // 执行计算
          return calculate(
            ...Object.values(vars),
            sin, cos, tan, sqrt, pow, abs, log, ln, exp, factorial
          );
        } catch (error) {
          // 如果计算失败，尝试使用更宽松的方式
          console.error("公式计算失败，尝试备用方法:", error);
          
          // 创建一个沙箱环境
          const sandbox = {
            ...vars,
            sin, cos, tan, sqrt, pow, abs, log, ln, exp, factorial,
            // 添加更多常用的数学函数
            Math: Math
          };
          
          // 使用Function构造器创建一个函数来计算公式
          const safeEval = new Function(
            ...Object.keys(sandbox),
            `try { 
              return ${processedFormula}; 
            } catch(e) { 
              console.error("计算错误:", e); 
              return 0; 
            }`
          );
          
          return safeEval(...Object.values(sandbox));
        }
      } catch (error) {
        console.error("计算公式出错:", error);
        throw new Error(`计算错误: ${error.message}`);
      }
    };
    
    // 计算结果
    const startTime = new Date();
    const result = calculateSafeFormula(formulaStr, allVariables);
    const endTime = new Date();
    const calculationTime = endTime.toLocaleString();
    
    return {
      code: 200,
      message: "计算成功",
      data: {
        formulaId: id,
        variables: allVariables,
        result: Number.isFinite(result) ? parseFloat(result.toFixed(2)) : "计算错误",
        unit: '分',
        calculationTime,
        formula: formulaStr
      }
    };
  } catch (error) {
    console.error("计算公式出错:", error);
    return {
      code: 500,
      message: "计算失败: " + error.message,
      data: null
    };
  }
};

// 阶乘函数
function factorial(n) {
  if (n <= 1) return 1;
  return n * factorial(n - 1);
}

// 验证公式
export const validateFormula = async (formula) => {
  await mockDelay();
  
  // 实现更详细的公式验证逻辑
  let isValid = true;
  const errors = [];
  const suggestions = [];
  
  // 检查公式是否为空
  if (!formula || formula.trim() === '') {
    isValid = false;
    errors.push('公式不能为空');
    suggestions.push('请输入有效的公式表达式');
    return {
      code: 200,
      message: isValid ? "公式有效" : "公式无效",
      data: {
        isValid,
        errors,
        suggestions
      }
    };
  }
  
  try {
    // 检查括号匹配
    const brackets = {
      '(': ')',
      '[': ']',
      '{': '}'
    };
    const stack = [];
    
    for (let i = 0; i < formula.length; i++) {
      const char = formula[i];
      if (char === '(' || char === '[' || char === '{') {
        stack.push(char);
      } else if (char === ')' || char === ']' || char === '}') {
        const last = stack.pop();
        if (!last || brackets[last] !== char) {
          isValid = false;
          errors.push(`括号不匹配: 位置 ${i+1} 的 ${char}`);
        }
      }
    }
    
    if (stack.length > 0) {
      isValid = false;
      errors.push(`括号不匹配: 有 ${stack.length} 个未闭合的括号`);
    }
    
    // 检查是否使用了方括号 (建议使用圆括号)
    if (formula.includes('[') || formula.includes(']')) {
      suggestions.push('建议使用圆括号 () 代替方括号 []');
    }
    
    // 检查函数名称是否有效
    const validFunctions = ['sin', 'cos', 'tan', 'log', 'ln', 'sqrt', 'abs', 'exp', 'pow', 'factorial'];
    const functionPattern = /[a-zA-Z]+\s*\(/g;
    let match;
    
    while ((match = functionPattern.exec(formula)) !== null) {
      const func = match[0].replace(/\s*\($/, '');
      // 允许单个大写字母作为常量（如A、B、C等）
      if (!validFunctions.includes(func.toLowerCase()) && 
          !['A', 'B', 'C', 'D', 'E', 'PI', 'a', 'b', 'c', 'd', 'e', 'pi'].includes(func) &&
          !/^[A-Za-z]$/.test(func)) {
        suggestions.push(`未识别的函数名: ${func}, 请检查拼写`);
      }
    }
    
    // 检查是否使用了幂运算符 (^)
    if (formula.includes('^')) {
      suggestions.push('检测到幂运算符 (^)，建议使用 pow(x, y) 函数代替 x^y');
    }
    
    // 检查是否使用了除号
    if (formula.includes('/0')) {
      errors.push('检测到可能的除零错误');
    }
    
    // 检查公式中是否有无效字符，但允许更多有效字符
    const invalidCharPattern = /[^A-Za-z0-9\s\+\-\*\/\(\)\[\]\{\}\.\,\_\^\!\%]/g;
    const invalidChars = formula.match(invalidCharPattern);
    if (invalidChars) {
      errors.push(`检测到无效字符: ${invalidChars.join(', ')}`);
    }
    
    // 尝试转换和验证公式
    try {
      // 替换幂运算符为pow函数 - 更强大的正则表达式
      let testFormula = formula;
      
      // 先处理括号内的表达式
      testFormula = testFormula.replace(/\(([^()]+)\)\s*\^\s*([A-Za-z0-9_]+|\d+(\.\d+)?)/g, 'pow($1, $2)');
      
      // 再处理变量或数字的幂
      testFormula = testFormula.replace(/([A-Za-z0-9_]+|\d+(\.\d+)?)\s*\^\s*([A-Za-z0-9_]+|\d+(\.\d+)?)/g, 'pow($1, $3)');
      
      // 定义测试用的数学函数
      const sin = Math.sin;
      const cos = Math.cos;
      const tan = Math.tan;
      const sqrt = Math.sqrt;
      const pow = Math.pow;
      const abs = Math.abs;
      const log = Math.log10;
      const ln = Math.log;
      const exp = Math.exp;
      
      // 定义阶乘函数
      const factorial = (n) => {
        if (n <= 1) return 1;
        let result = 1;
        for (let i = 2; i <= n; i++) {
          result *= i;
        }
        return result;
      };
      
      // 创建一个更全面的测试变量集
      const testVars = {
        A: 0.25, B: 0.35, C: 0.15, D: 0.1, E: 0.15, PI: 3.14159265359,
        a: 0.25, b: 0.35, c: 0.15, d: 0.1, e: 0.15, pi: 3.14159265359,
        x: 5, X: 5, y: 3, Y: 3, z: 2, Z: 2
      };
      
      // 为所有可能的单字母变量添加默认值
      for (let i = 65; i <= 90; i++) {
        const upperChar = String.fromCharCode(i);
        const lowerChar = String.fromCharCode(i + 32);
        if (!testVars[upperChar]) testVars[upperChar] = 1;
        if (!testVars[lowerChar]) testVars[lowerChar] = 1;
      }
      
      try {
        // 使用函数而不是eval，更安全
        const calculate = new Function(
          ...Object.keys(testVars),
          'sin', 'cos', 'tan', 'sqrt', 'pow', 'abs', 'log', 'ln', 'exp', 'factorial',
          `return ${testFormula};`
        );
        
        // 尝试执行计算
        calculate(
          ...Object.values(testVars),
          sin, cos, tan, sqrt, pow, abs, log, ln, exp, factorial
        );
      } catch (syntaxError) {
        // 尝试使用更宽松的方式解析公式
        try {
          // 创建一个沙箱环境，避免直接使用eval
          const sandbox = {
            ...testVars,
            sin, cos, tan, sqrt, pow, abs, log, ln, exp, factorial,
            // 添加更多常用的数学函数
            Math: Math,
            // 安全的eval替代品
            result: undefined
          };
          
          // 使用Function构造器创建一个函数来计算公式
          const safeEval = new Function(
            ...Object.keys(sandbox),
            `try { return ${testFormula}; } catch(e) { return e; }`
          );
          
          const result = safeEval(...Object.values(sandbox));
          
          // 如果结果是Error对象，说明计算失败
          if (result instanceof Error) {
            throw result;
          }
        } catch (fallbackError) {
          isValid = false;
          errors.push(`公式语法错误: ${syntaxError.message}`);
          suggestions.push('请检查公式语法，确保所有函数和变量都正确定义');
        }
      }
    } catch (error) {
      // 如果验证过程中出现错误，但不是语法错误，我们可以尝试更宽松的验证
      // 例如，检查括号是否匹配，函数名是否有效等
      if (errors.length === 0 && stack.length === 0) {
        // 如果只有括号匹配问题，我们可以给出更具体的建议
        suggestions.push('公式复杂，但结构看起来正确。请确保所有函数和变量都已定义。');
        // 不将复杂但结构正确的公式标记为无效
        isValid = true;
      }
    }
    
  } catch (error) {
    // 捕获验证过程中的任何错误
    console.error('公式验证过程中出现错误:', error);
    suggestions.push('公式验证过程中出现错误，但可能仍然有效。请在测试时再次验证。');
    // 不因为验证过程的错误而直接标记公式为无效
    isValid = true;
  }
  
  return {
    code: 200,
    message: isValid ? "公式有效" : "公式无效",
    data: {
      isValid: isValid && errors.length === 0,
      errors,
      suggestions
    }
  };
}; 