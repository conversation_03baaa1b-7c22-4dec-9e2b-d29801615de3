package com.kbao.kbcelms;


import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "kbc-bpm-web")
public class KbcBpmApiClientService {

    public static void main(String[] args) {
        SpringApplication.run(KbcBpmApiClientService.class, args);
    }

}
