create table  t_data_template
(
    id         INT auto_increment not null comment '主键',
	template_name     VARCHAR(50) not null comment '模板名称',
    biz_code          VARCHAR(50) not null comment '模板编号',
	type			  varchar(50) comment '分类信息',
	status			  CHAR(1) DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
    remark            VARCHAR(500) comment '描述',
    create_time       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_id         VARCHAR(50) not null comment '创建人',
    update_time       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_id         VARCHAR(50) comment '更新人',
    is_deleted        TINYINT not null comment '是否删除 0-未删除 1-已删除',
    tenant_id         VARCHAR(10) not null comment '租户ID',
        PRIMARY KEY (id),
    unique index uniq_biz_code(biz_code)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment '数据模板表';



----- mongoDB 表结构
DataTemplateField -- 模板字段表
{
  "_id": "ObjectId",                // 主键，MongoDB自动生成
  "template_id": "NumberInt",       // 模板ID
  "field_name": "String",           // 字段名称
  "field_code": "String",           // 字段编码
  "show_type": "String",            // 展示类型：0-不可见, 1-输入框，2-单选框，3-下拉框，4-日期选择，5-日期范围，6-文本域
  "required": "String",             // 是否必填：0-否，1-是
  "default_value": "String",        // 默认值
  "verify_rule": "String",          // 校验规则
  "change": "String",               // 是否可修改：0-否，1-是
  "additional": {
    "input_unit": "String",         // 输入框-单位,
    "select_options": "Array",      // 下拉框-取值
  },
  "is_index": "String",            // 是否添加索引：0-否，1-是
  "remark": "String",               // 描述
  "create_time": "Date",            // 创建时间
  "create_id": "String",            // 创建人
  "update_time": "Date",            // 更新时间
  "update_id": "String",            // 更新人
  "is_deleted": "NumberInt",        // 是否删除 0-未删除 1-已删除
  "tenant_id": "String"             // 租户ID
}