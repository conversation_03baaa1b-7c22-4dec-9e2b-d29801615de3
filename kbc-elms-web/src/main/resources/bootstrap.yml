#服务名称
spring:
  main: 
    allow-bean-definition-overriding: true
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      config:
        # 配置中心地址
        server-addr: https://config-sta-lan.kbao123.com
        # 账号
        username: kbc-elms-r
        # 密码
        password: qhciSv7Oo4#MxCrnzh3D3b!WuxR0Bh0U
        # 自动刷新配置
        refresh-enabled: false
        # 配置文件格式
        file-extension: yml
        # 指定group 默认 DEFAULT_GROUP
        group: group-kbc-elms
        # 指定namespace id 默认public
        namespace: ${spring.profiles.active}
        # 自定义dataId，默认spring.application.name
        prefix: sta-kbcs-app-elms-web-jar
        enabled: true

#禁用自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
#
redis:
  application:
    name: kbc-elms-web

