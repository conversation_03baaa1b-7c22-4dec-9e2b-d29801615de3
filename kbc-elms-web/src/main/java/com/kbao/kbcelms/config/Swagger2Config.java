package com.kbao.kbcelms.config;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import com.kbao.commons.constant.UaaConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * Swagger2 配置
 *
 * <AUTHOR>
 *
 */
@Configuration
@EnableSwagger2
// 在生产环境不开启
@Profile({"local","dev", "test","sta","uat"})
public class Swagger2Config {

    // 定义分隔符,配置Swagger多包
    private static final String SPLITOR = ";";

    @Bean
    public Docket createRestApi() {
        //添加head参数start
        List<Parameter> headerParam = getHeader();
        return new Docket(DocumentationType.SWAGGER_2)
//                .pathProvider(new RelativePathProvider(servletContext){
//                    @Override
//                    public String getApplicationBasePath() {
//                        return "/gateway";
//                    }
//                })
                .apiInfo(apiInfo())
                .select()
                .apis(//RequestHandlerSelectors.
                        basePackage("com.kbao.kbcelms"+SPLITOR+"com.kbao.kbckm"))
                .paths(PathSelectors.any())
                .build()
                //在header 添加参数
                .globalOperationParameters(headerParam);
    }
    private List<Parameter> getHeader() {

        //添加head参数start
        List<Parameter> headerParam = new ArrayList<Parameter>();

        {
            ParameterBuilder parameterBuilder = new ParameterBuilder();
            parameterBuilder.name(UaaConstant.TOKEN_HEADER).defaultValue("da90bf43e61c8c1ac048fe4c55edd399")
                    .description("客户端认证的token(登录接口返回的accessToken)")
                    .modelRef(new ModelRef("string"))
                    .parameterType("header")
                    .required(false).build();
            headerParam.add(parameterBuilder.build());
        }
        {
            ParameterBuilder parameterBuilder = new ParameterBuilder();
            parameterBuilder.name("tenantId").defaultValue("T0001")
                    .description("租户id")
                    .modelRef(new ModelRef("string"))
                    .parameterType("header")
                    .required(false).build();
            headerParam.add(parameterBuilder.build());
        }
        {
            ParameterBuilder parameterBuilder = new ParameterBuilder();
            parameterBuilder.name("funcId").defaultValue("F00111")
                    .description("菜单ID")
                    .modelRef(new ModelRef("string"))
                    .parameterType("header")
                    .required(false).build();
            headerParam.add(parameterBuilder.build());
        }
        return headerParam;
    }
    private ApiInfo apiInfo(){
        return new ApiInfoBuilder()
                .title("快保知识库Api文档")
                .description("给前端人员调用API接口文档")
                .version("0.0.1-SNAPSHOT")
                .termsOfServiceUrl("http://localhost:7000/")
                /*.contact(new Contact("admin", "", "z.com"))*/
                .build();
    }

    /**
     * 重写basePackage方法，使能够实现多包访问，复制贴上去
     * <AUTHOR>
     * @date 2019/1/26
     * @param basePackage
     * @return com.google.common.base.Predicate<springfox.documentation.RequestHandler>
     */
    public static Predicate<RequestHandler> basePackage(final String basePackage) {
        return input -> declaringClass(input).transform(handlerPackage(basePackage)).or(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final String basePackage)     {
        return input -> {
            // 循环判断匹配
            for (String strPackage : basePackage.split(SPLITOR)) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<? extends Class<?>> declaringClass(RequestHandler input) {
        return Optional.fromNullable(input.declaringClass());
    }

}
