package com.kbao.kbcelms.controller.role;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.auth.vo.AuthRequestVO;
import com.kbao.kbcelms.auth.vo.AuthTreeVO;
import com.kbao.kbcelms.role.service.RoleService;
import com.kbao.kbcelms.role.vo.RoleAddVO;
import com.kbao.kbcelms.role.vo.RoleRequestVO;
import com.kbao.kbcelms.role.vo.RoleResponseVO;
import com.kbao.kbcelms.roleauth.vo.RoleAuthAddVO;
import com.kbao.kbcelms.user.vo.UserRequestVO;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 11:22
 */
@Slf4j
@Api(tags = "角色管理")
@RestController
@RequestMapping("/api/role")
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "查询", desc = "分页查询角色列表")
    @ApiOperation(value = "查询", notes = "分页查询角色列表")
    @PostMapping("/page")
    public Result<PageInfo<RoleResponseVO>> page(@RequestBody PageRequest<RoleRequestVO> pageRequest) {
        try {
            PageInfo<RoleResponseVO> pageInfo = roleService.pageRole(pageRequest);
            return Result.succeed(pageInfo, "查询成功");
        } catch (Exception e) {
            log.error("分页查询角色出错：{}", e);
            throw new BusinessException("分页查询角色出错");
        }
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "查询", desc = "查询角色列表")
    @ApiOperation(value = "查询", notes = "查询角色列表")
    @PostMapping("/list")
    public Result<List<RoleResponseVO>> list() {
        return Result.succeedWith(roleService.list());
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "查询", desc = "查询权限树")
    @ApiOperation(value = "查询", notes = "查询权限树")
    @PostMapping("/selectAuthTree")
    public Result<List<AuthTreeVO>> selectAuthTree(@RequestBody AuthRequestVO requestVO) {
        List<AuthTreeVO> list = roleService.selectAuthTree(requestVO);
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "查询", desc = "角色详情")
    @ApiOperation(value = "查询", notes = "角色详情")
    @PostMapping("/detail")
    public Result<RoleResponseVO> detail(@RequestBody RoleRequestVO requestVO) {
        RoleResponseVO responseVO = roleService.detail(requestVO.getRoleId());
        return Result.succeedWith(responseVO);
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "增改", desc = "角色增改")
    @ApiOperation(value = "增改", notes = "角色增改")
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody RoleAddVO addVO) {
        roleService.saveOrUpdate(addVO);
        return Result.succeed("操作成功");
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "删除", desc = "角色删除")
    @ApiOperation(value = "删除", notes = "角色删除")
    @PostMapping("/delete")
    public Result<String> delete(@RequestBody RoleRequestVO requestVO) {
        roleService.delete(requestVO.getRoleId());
        return Result.succeed("删除成功");
    }

    @LogAnnotation(module = "角色管理", recordRequestParam = true, action = "权限配置", desc = "权限配置")
    @ApiOperation(value = "权限配置", notes = "权限配置")
    @PostMapping("/saveOrUpdateRoleAuth")
    public Result<String> saveOrUpdateRoleAuth(@RequestBody RoleAuthAddVO addVO) {
        roleService.saveOrUpdateRoleAuth(addVO);
        return Result.succeed("操作成功");
    }

}
