package com.kbao.kbcelms.controller.auth;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.auth.service.AuthService;
import com.kbao.kbcelms.auth.vo.AuthAddVO;
import com.kbao.kbcelms.auth.vo.AuthRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/22 15:04
 */
@Slf4j
@Api(tags = "权限管理")
@RestController
@RequestMapping("/api/auth")
public class AuthController extends BaseController {

    @Autowired
    private AuthService authService;

    @LogAnnotation(module = "权限管理", recordRequestParam = true, action = "增改", desc = "权限增改")
    @ApiOperation(value = "增改", notes = "权限增改")
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody AuthAddVO authAddVO) {
        authService.saveOrUpdate(authAddVO);
        return Result.succeed("操作成功");
    }

    @LogAnnotation(module = "权限管理", recordRequestParam = true, action = "删除", desc = "权限删除")
    @ApiOperation(value = "删除", notes = "权限删除")
    @PostMapping("/delete")
    public Result<String> delete(@RequestBody AuthRequestVO requestVO) {
        authService.delete(requestVO.getId());
        return Result.succeed("删除成功");
    }
}
