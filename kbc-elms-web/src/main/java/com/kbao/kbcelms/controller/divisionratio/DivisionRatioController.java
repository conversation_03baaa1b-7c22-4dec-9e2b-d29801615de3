package com.kbao.kbcelms.controller.divisionratio;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.divisionratio.model.DivisionRatio;
import com.kbao.kbcelms.divisionratio.service.DivisionRatioService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分工比例控制器
 * <AUTHOR>
 */
@Slf4j
@Api(tags = {"分工比例管理"})
@RestController
@RequestMapping("/api/division/ratio")
public class DivisionRatioController {

    @Autowired
    private DivisionRatioService divisionRatioService;

    /**
     * 新增分工比例
     * @param divisionRatio 分工比例对象
     * @return 新增后的分工比例对象
     */
    @PostMapping("/add")
    public Result<DivisionRatio> addDivisionRatio(@RequestBody DivisionRatio divisionRatio) {
        DivisionRatio ratio = divisionRatioService.addDivisionRatio(divisionRatio);
        return Result.succeed(ratio, "新增成功");
    }

    /**
     * 更新分工比例
     * @param divisionRatio 分工比例对象
     * @return 更新后的分工比例对象
     */
    @PostMapping("/update")
    public Result<DivisionRatio> updateDivisionRatio(@RequestBody DivisionRatio divisionRatio) {
        DivisionRatio ratio = divisionRatioService.updateDivisionRatio(divisionRatio);
        return Result.succeed(ratio, "更新成功");
    }

    /**
     * 删除分工比例
     * @param id 分工比例id
     */
    @PostMapping("/delete")
    public Result<Void> deleteDivisionRatio(String id) {
        divisionRatioService.deleteDivisionRatio(id);
        return Result.succeed("删除成功");
    }

    /**
     * 批量删除分工比例
     * @param ids 分工比例id数组
     */
    @PostMapping("/delete/batch")
    public Result<Void> deleteDivisionRatios(String[] ids) {
        divisionRatioService.deleteDivisionRatios(ids);
        return Result.succeed("删除成功");
    }

    /**
     * 分页查询
     * @param pageRequest 分页请求对象
     * @return 分页结果对象
     */
    @PostMapping("/page")
    public Result<PageInfo<DivisionRatio>> pageDivisionRatio(@RequestBody PageRequest<DivisionRatio> pageRequest) {
        PageInfo<DivisionRatio> pageInfo = divisionRatioService.pageDivisionRatio(pageRequest);
        return Result.succeed(pageInfo, "查询成功");
    }

    /**
     * 查询所有启用的比例
     * @return
     */
    @PostMapping("/list")
    public Result<List<DivisionRatio>> listDivisionRatio() {
        List<DivisionRatio> list = divisionRatioService.getDivisionRatioList();
        return Result.succeed(list, "查询成功");
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @PostMapping("/get")
    public Result<DivisionRatio> getDivisionRatio(String id) {
        DivisionRatio ratio = divisionRatioService.getDivisionRatio(id);
        return Result.succeed(ratio, "查询成功");
    }
}