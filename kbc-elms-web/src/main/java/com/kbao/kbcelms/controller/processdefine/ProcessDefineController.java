package com.kbao.kbcelms.controller.processdefine;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcelms.processdefine.entity.ProcessDefine;
import com.kbao.kbcelms.processdefine.service.ProcessDefineService;
import com.kbao.kbcelms.processdefine.vo.ProcessDefineQueryVO;
import com.kbao.kbcelms.processdefine.vo.ProcessDefineDetailRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流程定义控制器
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Slf4j
@Api(tags = "流程定义")
@RestController
@RequestMapping("/api/processDefine")
public class ProcessDefineController {

    @Autowired
    private ProcessDefineService processDefineService;

    /**
     * 查询流程定义接口
     */
    @ApiOperation(value = "查询流程定义", notes = "查询流程定义")
    @PostMapping("/list")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "查询", desc = "查询流程定义")
    public Result<PageInfo<ProcessDefine>> getPageList(@RequestBody RequestObjectPage<ProcessDefineQueryVO> pageRequest) {
        PageInfo<ProcessDefine> page = processDefineService.pageWithQuery(pageRequest);
        return Result.succeed(page, "查询成功！");
    }

    /**
     * 根据ID查询流程定义详情接口
     */
    @ApiOperation(value = "查询流程定义详情", notes = "根据ID查询流程定义详情")
    @PostMapping("/detail")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "查询详情", desc = "根据ID查询流程定义详情")
    public Result<ProcessDefine> getProcessDefineDetail(@RequestBody ProcessDefineDetailRequestVO requestVO) {
        ProcessDefine detail = processDefineService.getProcessDefineDetail(requestVO.getId());
        return Result.succeed(detail, "查询成功！");
    }

    /**
     * 查询流程定义接口
     */
    @ApiOperation(value = "启用流程定义", notes = "启用流程定义")
    @PostMapping("/start")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "启用", desc = "启用流程定义")
    public Result processStart(@RequestBody ProcessDefine processDefine) {
        processDefineService.processStart(processDefine);
        return Result.succeed("成功");
    }

    /**
     * 停止流程定义接口
     */
    @ApiOperation(value = "停止流程定义", notes = "停止流程定义")
    @PostMapping("/stop")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "停止", desc = "停止流程定义")
    public Result processStop(@RequestBody ProcessDefine processDefine) {
        processDefineService.processStop(processDefine);
        return Result.succeed("成功");
    }

    /**
     * 删除流程定义接口
     */
    @ApiOperation(value = "删除流程定义", notes = "删除流程定义")
    @PostMapping("/delete")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "删除", desc = "删除流程定义")
    public Result processDelete(@RequestBody ProcessDefine processDefine) {
        processDefineService.processDelete(processDefine);
        return Result.succeed("成功");
    }

    /**
     * 复制流程定义接口
     */
    @ApiOperation(value = "复制流程定义", notes = "复制流程定义")
    @PostMapping("/copy")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "复制", desc = "复制流程定义")
    public Result<ProcessDefine> processCopy(@RequestBody ProcessDefine processDefine) {
        return Result.succeed(processDefineService.processCopy(processDefine),"成功");
    }

    /**
     * 保存流程定义
     */
    @ApiOperation(value = "保存流程定义", notes = "保存流程定义")
    @PostMapping("/save")
    @LogAnnotation(module = "流程定义接口", recordRequestParam = true, action = "保存", desc = "保存流程定义")
    public Result processSave(@RequestBody ProcessDefine processDefine) {
        processDefineService.processSave(processDefine);
        return Result.succeed("成功");
    }

    
}
