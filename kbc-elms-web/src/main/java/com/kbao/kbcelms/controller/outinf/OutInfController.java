package com.kbao.kbcelms.controller.outinf;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.OrgClientAdapter;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.organization.model.OrgIdReq;
import com.kbao.kbcbsc.organization.model.OrgTypeReq;
import com.kbao.kbcelms.outinf.vo.OrgCodesRequestVO;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.kbcelms.usertenant.service.UserTenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Collections;

/**
 * 外部系统接口服务
 */
@RestController
@RequestMapping("/api/basecode")
@Api(tags = "流程定义接口")
public class OutInfController {

    @Autowired
    OrgClientAdapter orgClientAdapter;

    @Autowired
    private UserTenantService userTenantService;

    /**
     * 查询所有法人分公司
     * @return
     */
    @ApiOperation(value = "查询法人公司", notes = "查询法人公司")
    @LogAnnotation(module = "外部接口", recordRequestParam = true, action = "查询", desc = "查询法人公司")
    @PostMapping("/getLegalList")
    public Result<List<Organization>> getLegalList() {
        OrgTypeReq orgTypeReq = new OrgTypeReq();
        orgTypeReq.setOrgType("2");
        orgTypeReq.setTenantId(SysLoginUtils.getUser().getTenantId());
        Result<List<Organization>> organsByType = orgClientAdapter.getOrgansByType(orgTypeReq);
        return organsByType;
    }

    @ApiOperation(value = "查询营业部", notes = "根据组织机构编码数组查询其下层节点中orgType为4的营业部")
    @LogAnnotation(module = "外部接口", recordRequestParam = true, action = "查询", desc = "根据组织机构编码数组查询营业部")
    @PostMapping("/getTransactionsList")
    public Result<List<Organization>> getTransactions(@RequestBody OrgCodesRequestVO requestVO) {
        if (requestVO.getOrgCodes() == null || requestVO.getOrgCodes().isEmpty()) {
            return Result.succeed(Collections.emptyList(), "组织机构编码列表不能为空");
        }
        
        // 获取所有组织机构数据
        OrgIdReq orgIdReq = new OrgIdReq();
        orgIdReq.setTenantId(SysLoginUtils.getUser().getTenantId());
        Result<List<Organization>> organsByType = orgClientAdapter.getOrgList(orgIdReq);
        
        if (organsByType.getDatas() == null || organsByType.getDatas().isEmpty()) {
            return Result.succeed(Collections.emptyList(), "未找到组织机构数据");
        }
        
        // 递归查找指定编码下层节点中orgType为4的数据
        List<Organization> result = new ArrayList<>();
        for (String orgCode : requestVO.getOrgCodes()) {
            List<Organization> foundOrgs = findOrgType4InChildren(organsByType.getDatas(), orgCode);
            result.addAll(foundOrgs);
        }
        
        // 去重
        List<Organization> uniqueResult = result.stream()
            .collect(Collectors.toMap(
                Organization::getOrgCode,
                org -> org,
                (existing, replacement) -> existing
            ))
            .values()
            .stream()
            .collect(Collectors.toList());
        
        organsByType.setDatas(uniqueResult);
        return organsByType;
    }
    
    /**
     * 递归查找指定组织机构编码下层节点中orgType为4的数据
     * @param organizations 组织机构列表
     * @param targetOrgCode 目标组织机构编码
     * @return orgType为4的组织机构列表
     */
    private List<Organization> findOrgType4InChildren(List<Organization> organizations, String targetOrgCode) {
        List<Organization> result = new ArrayList<>();
        
        for (Organization org : organizations) {
            if (targetOrgCode.equals(org.getOrgCode())) {
                // 找到目标组织机构，递归查找其子节点中orgType为4的数据
                findOrgType4Recursively(org, result);
                break;
            } else if (org.getChildren() != null && !org.getChildren().isEmpty()) {
                // 递归查找子节点
                List<Organization> childResult = findOrgType4InChildren(org.getChildren(), targetOrgCode);
                result.addAll(childResult);
            }
        }
        
        return result;
    }
    
    /**
     * 递归查找组织机构及其子节点中orgType为4的数据
     * @param organization 组织机构
     * @param result 结果列表
     */
    private void findOrgType4Recursively(Organization organization, List<Organization> result) {
        // 检查当前节点是否为orgType为4
        if ("4".equals(organization.getOrgType())) {
            result.add(organization);
        }
        
        // 递归查找子节点
        if (organization.getChildren() != null && !organization.getChildren().isEmpty()) {
            for (Organization child : organization.getChildren()) {
                findOrgType4Recursively(child, result);
            }
        }
    }


}
