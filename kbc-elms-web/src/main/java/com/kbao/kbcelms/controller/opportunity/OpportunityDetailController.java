package com.kbao.kbcelms.controller.opportunity;

import com.kbao.commons.web.Result;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.model.OpportunityDetailVO;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/opportunity/detail")
@Api(tags = "机会项目成员相关接口")
public class OpportunityDetailController {

    @Autowired
    private OpportunityDetailService opportunityDetailService;

    @ApiOperation(value = "获取机会明细", notes = "获取机会明细")
    @PostMapping("/get")
    public Result<OpportunityDetail> get(@RequestBody OpportunityDetailVO detailVO){
        OpportunityDetail result=  opportunityDetailService.selectByOpportunityId(detailVO.getOpportunityId(),SysLoginUtils.getUser().getTenantId());
        return Result.succeed(result,"操作成功");
    }

    @ApiOperation(value = "保存机会明细", notes = "保存机会明细")
    @PostMapping("/save")
    public Result save(@RequestBody OpportunityDetailVO detailVO){
        opportunityDetailService.saveByOpportunityId(detailVO,SysLoginUtils.getUser().getTenantId(),SysLoginUtils.getUser().getUserId());
        return Result.succeed("操作成功");
    }

}
