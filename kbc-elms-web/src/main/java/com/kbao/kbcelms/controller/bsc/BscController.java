package com.kbao.kbcelms.controller.bsc;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.tenant.bean.TenantIdReq;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcelms.bsc.vo.BscWebUser;
import com.kbao.kbcelms.bsc.vo.DicCodeRequestVO;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:09
 */
@RestController
@RequestMapping("/api/bsc")
@Api(tags = "快保云服接口控制器")
public class BscController extends BaseController {

    @Autowired
    private BscClientService bscClientService;

    @PostMapping("/dic/getDicItems")
    public Result<List<DicItems>> getDicItems(@Validated @RequestBody DicCodeRequestVO req, BindingResult bindRes) {
        checkValidator("getDicItems", bindRes, req);
        Result<List<DicItems>> dicItems = bscClientService.getDicItems(req.getDicCode());
        return dicItems;
    }

    @PostMapping("/getWebUserInfo")
    public Result<BscWebUser> getWebUserInfo() {
        BscWebUser bscWwebUser = new BscWebUser();
        bscWwebUser.setIsAdmin(BscUserUtils.getUser().getUser().getIsAdmin());
        bscWwebUser.setFuncAuthDTO(BscUserUtils.getUser().getFunction().getFuncAuths());
        bscWwebUser.setAppTenantListVo(bscClientService.getWebUserTenants().getDatas());
        return Result.succeed(bscWwebUser, "获取登录用户信息成功");
    }

    @PostMapping("/tenant/getTenantUsers")
    public Result<List<UserIdReq>> getTenantUsers() {
        return bscClientService.getTenantUsers();
    }
}
