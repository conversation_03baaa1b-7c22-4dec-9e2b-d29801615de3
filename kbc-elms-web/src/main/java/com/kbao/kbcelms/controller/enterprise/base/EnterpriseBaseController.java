package com.kbao.kbcelms.controller.enterprise.base;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseFinancial;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBeneficiaryService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseFinancialService;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseShareholderService;
import com.kbao.kbcelms.enterprise.base.service.TianyanchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业基本信息管理控制器
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/api/enterprise/base")
@Api(tags = "企业基本信息管理")
public class EnterpriseBaseController extends BaseController {
    
    @Autowired
    private TianyanchaService tianyanchaService;
    
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    
    @Autowired
    private EnterpriseShareholderService enterpriseShareholderService;
    
    @Autowired
    private EnterpriseBeneficiaryService enterpriseBeneficiaryService;
    
    @Autowired
    private EnterpriseFinancialService enterpriseFinancialService;

    /**
     * 分页查询企业基本信息列表
     */
    @ApiOperation(value = "分页查询企业基本信息列表", notes = "支持按企业名称、信用代码、企业状态等条件查询")
    @PostMapping("/info/page")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "分页查询企业基本信息列表")
    public Result<PageInfo<EnterpriseBasicInfo>> page(@RequestBody PageRequest<EnterpriseBasicInfo> page) {
        PageInfo<EnterpriseBasicInfo> pageInfo = enterpriseBasicInfoService.page(page);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 同步企业所有信息
     */
    @ApiOperation(value = "同步企业所有信息", notes = "从天眼查同步企业基本信息、股东信息、受益人信息、财务信息")
    @PostMapping("/sync/all")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步企业所有信息")
    public Result<String> syncEnterpriseAllInfo(
            @ApiParam(value = "企业名称", required = true) @RequestParam String companyName) {
        String result = tianyanchaService.syncEnterpriseAllInfo(companyName);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 同步企业基本信息
     */
    @ApiOperation(value = "同步企业基本信息", notes = "从天眼查同步企业基本信息和企业规模")
    @PostMapping("/sync/basic")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步企业基本信息")
    public Result<String> syncEnterpriseBasicInfo(
            @ApiParam(value = "企业名称", required = true) @RequestParam String companyName) {
        String creditCode = tianyanchaService.syncEnterpriseBasicInfoByName(companyName);
        if (StringUtils.hasText(creditCode)) {
            return Result.succeed("企业基本信息同步成功，统一社会信用代码：" + creditCode, ResultStatusEnum.SUCCESS.getMsg());
        } else {
            return Result.succeed("企业基本信息同步成功，但未获取到统一社会信用代码", ResultStatusEnum.SUCCESS.getMsg());
        }
    }
    
    /**
     * 同步企业股东信息
     */
    @ApiOperation(value = "同步企业股东信息", notes = "从天眼查同步企业股东信息")
    @PostMapping("/sync/shareholder")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步企业股东信息")
    public Result<String> syncEnterpriseShareholder(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        tianyanchaService.syncEnterpriseShareholder(creditCode);
        return Result.succeed("企业股东信息同步成功", ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 同步最终受益人信息
     */
    @ApiOperation(value = "同步最终受益人信息", notes = "从天眼查同步最终受益人信息")
    @PostMapping("/sync/beneficiary")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步最终受益人信息")
    public Result<String> syncEnterpriseBeneficiary(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        tianyanchaService.syncEnterpriseBeneficiary(creditCode);
        return Result.succeed("最终受益人信息同步成功", ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 同步财务信息
     */
    @ApiOperation(value = "同步财务信息", notes = "从天眼查同步上市公司财务信息")
    @PostMapping("/sync/financial")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "同步", desc = "同步财务信息")
    public Result<String> syncEnterpriseFinancial(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        tianyanchaService.syncEnterpriseFinancial(creditCode);
        return Result.succeed("财务信息同步成功", ResultStatusEnum.SUCCESS.getMsg());
    }


    
    /**
     * 查询企业基本信息
     */
    @ApiOperation(value = "查询企业基本信息", notes = "根据统一社会信用代码查询企业基本信息")
    @GetMapping("/basic")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "查询企业基本信息")
    public Result<EnterpriseBasicInfo> getEnterpriseBasicInfo(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(creditCode);
        return Result.succeed(basicInfo, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 查询企业股东信息
     */
    @ApiOperation(value = "查询企业股东信息", notes = "根据统一社会信用代码查询企业股东信息")
    @GetMapping("/shareholder")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "查询企业股东信息")
    public Result<List<EnterpriseShareholder>> getEnterpriseShareholder(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        List<EnterpriseShareholder> shareholderList = enterpriseShareholderService.findByCreditCode(creditCode);
        return Result.succeed(shareholderList, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 查询最终受益人信息
     */
    @ApiOperation(value = "查询最终受益人信息", notes = "根据统一社会信用代码查询最终受益人信息")
    @GetMapping("/beneficiary")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "查询最终受益人信息")
    public Result<List<EnterpriseBeneficiary>> getEnterpriseBeneficiary(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        List<EnterpriseBeneficiary> beneficiaryList = enterpriseBeneficiaryService.findByCreditCode(creditCode);
        return Result.succeed(beneficiaryList, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 查询财务信息
     */
    @ApiOperation(value = "查询财务信息", notes = "根据统一社会信用代码查询财务信息")
    @GetMapping("/financial")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "查询财务信息")
    public Result<List<EnterpriseFinancial>> getEnterpriseFinancial(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        List<EnterpriseFinancial> financialList = enterpriseFinancialService.findByCreditCode(creditCode);
        return Result.succeed(financialList, ResultStatusEnum.SUCCESS.getMsg());
    }


    
    /**
     * 查询企业完整信息
     */
    @ApiOperation(value = "查询企业完整信息", notes = "根据统一社会信用代码查询企业所有信息")
    @GetMapping("/complete")
    @LogAnnotation(module = "企业信息管理", recordRequestParam = true, action = "查询", desc = "查询企业完整信息")
    public Result<Map<String, Object>> getEnterpriseCompleteInfo(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String creditCode) {
        Map<String, Object> result = new HashMap<>();
        
        // 基本信息
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(creditCode);
        result.put("basicInfo", basicInfo);
        
        // 股东信息
        List<EnterpriseShareholder> shareholderList = enterpriseShareholderService.findByCreditCode(creditCode);
        result.put("shareholderList", shareholderList);
        
        // 受益人信息
        List<EnterpriseBeneficiary> beneficiaryList = enterpriseBeneficiaryService.findByCreditCode(creditCode);
        result.put("beneficiaryList", beneficiaryList);
        
        // 财务信息
        List<EnterpriseFinancial> financialList = enterpriseFinancialService.findByCreditCode(creditCode);
        result.put("financialList", financialList);

        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }
}
