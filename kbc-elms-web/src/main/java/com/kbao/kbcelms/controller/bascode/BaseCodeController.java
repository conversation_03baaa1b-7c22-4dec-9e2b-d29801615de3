package com.kbao.kbcelms.controller.bascode;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.bascode.service.BasCodeService;
import com.kbao.kbcelms.bascode.vo.BaseCodeTreeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 流程定义接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/basecode")
@Api(tags = "流程定义接口")
public class BaseCodeController {

    @Autowired
    private BasCodeService basCodeService;


    /**
     * 查询BaseCode树形结构
     */
    @ApiOperation(value = "查询BaseCode树形结构", notes = "查询所有BaseCode并组装为树形结构")
    @PostMapping("/tree")
    @LogAnnotation(module = "基础代码", recordRequestParam = false, action = "查询", desc = "查询BaseCode树形结构")
    public Result<List<BaseCodeTreeVO>> getBaseCodeTree() {
        List<BaseCodeTreeVO> tree = basCodeService.getBaseCodeTree();
        return Result.succeed(tree, "查询成功");
    }
}
