create table t_gen_agent_enterprise
(
    id                  int auto_increment comment '主键'
        primary key,
    name                varchar(50)                           null comment '企业名称',
    creditCode          varchar(50)                           null comment '社会信用代码',
    dtType              varchar(20)                           null comment '企业类型-A、B、C、D、E',
    enterpriseScale     varchar(20)                           null comment '企业规模-央企、上市公司、大型企业',
    staffScale          varchar(30)                           null comment '人员规模',
    city                varchar(30)                           null comment '所在城市',
    districtCode        varchar(20)                           null comment '行政区划代码',
    annualIncome        varchar(30)                           null comment '年收入',
    categoryCode        varchar(10)                           null comment '行业代码',
    categoryName        varchar(100)                          null comment '行业类名',
    isVerified          char                                  null comment '是否验真',
    enterpriseContacter varchar(20)                           null comment '企业联系人',
    contacterPhone      varchar(20)                           null comment '联系人电话',
    dtRemark            varchar(300)                          null comment '备注信息',
    createId            varchar(50)                           null comment '创建人 当前用户ID',
    createTime          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updateId            varchar(50)                           null comment '更新人 默认为当前时间',
    updateTime          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    isDeleted           tinyint     default 0                 not null comment '是否删除 0-未删除 1-已删除',
    tenantId            varchar(10) default 'T0001'           not null comment '租户ID'
)
    comment '顾问企业信息表' charset = utf8mb4;

create index idx_field_creditCode
    on t_gen_agent_enterprise (creditCode);

