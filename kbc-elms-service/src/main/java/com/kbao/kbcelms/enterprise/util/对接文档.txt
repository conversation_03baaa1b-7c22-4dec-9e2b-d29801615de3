# 天眼查API完整对接文档

## 1. 基础信息

### 1.1 授权方式
所有接口都需要在HTTP请求头中添加Authorization字段：
```java
HttpGet get = new HttpGet(url);
get.setHeader("Authorization", token);
```

### 1.2 公共参数
- 请求方式：GET
- 支持格式：JSON
- 所有请求参数都需要URLEncode编码

### 1.3 错误代码说明

| 代码 | 说明 |
|------|------|
| 0 | 请求成功 |
| 300000 | 经查无结果 |
| 300001 | 请求失败 |
| 300002 | 账号失效 |
| 300003 | 账号过期 |
| 300004 | 访问频率过快 |
| 300005 | 无权限访问此api |
| 300006 | 余额不足 |
| 300007 | 剩余次数不足 |
| 300008 | 缺少必要参数 |
| 300009 | 账号信息有误 |
| 300010 | URL不存在 |
| 300011 | 此IP无权限访问此api |
| 300012 | 报告生成中 |

## 2. 接口详情

### 2.1 上市公司财务简析 (798)

**接口地址**: `http://open.api.tianyancha.com/services/v4/open/financialAnalysis`

**请求参数**:

| 参数 | 必选 | 类型 | 说明 |
|------|------|------|------|
| keyword | true | String | 搜索关键字（公司名称、公司id、注册号或社会统一信用代码） |

**返回字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| result | Object | 结果对象 |
| total | Number | 总数 |
| netAssets | Object | 净资产信息 |
| netAssets.[年份] | Object | 某年份的净资产数据 |
| netAssets.[年份].amount | Number | 净资产总额（单位元） |
| netAssets.[年份].convertAmount | String | 净资产总额（带单位） |
| netAssets.[年份].year | String | 年份 |
| netAssets.info | String | 资产区间说明 |
| netAssets.title | String | 类别 |
| netAssets.time | Array | 年份数组 |
| netAssets.list | Array | 数据列表 |
| netAssets.list[].amount | Number | 净资产（元） |
| netAssets.list[].convertAmount | String | 净资产（带单位） |
| netAssets.list[].year | String | 年份 |
| netInterestRate | Object | 净利率信息 |
| netInterestRate.[年份] | Object | 某年份的净利率数据 |
| netInterestRate.[年份].amount | Number | 百分比 |
| netInterestRate.[年份].year | String | 年份 |
| netInterestRate.info | String | 利率区间说明 |
| netInterestRate.title | String | 类别 |
| netInterestRate.time | Array | 年份数组 |
| netInterestRate.list | Array | 数据列表 |
| netInterestRate.list[].amount | Number | 百分比 |
| netInterestRate.list[].year | String | 年份 |
| totalAssets | Object | 总资产信息 |
| totalAssets.[年份] | Object | 某年份的总资产数据 |
| totalAssets.[年份].amount | Number | 总资产总数（单位元） |
| totalAssets.[年份].convertAmount | String | 总资产总数（单位亿） |
| totalAssets.[年份].year | String | 年份 |
| totalAssets.info | String | 总资产区间说明 |
| totalAssets.title | String | 类别 |
| totalAssets.time | Array | 年份数组 |
| totalAssets.list | Array | 数据列表 |
| totalAssets.list[].amount | Number | 总资产（单位元） |
| totalAssets.list[].convertAmount | String | 总资产（单位亿） |
| totalAssets.list[].year | String | 年份 |
| businessIncome | Object | 营业收入信息 |
| businessIncome.[年份] | Object | 某年份的营业收入数据 |
| businessIncome.[年份].amount | Number | 营业收入总额（单位元） |
| businessIncome.[年份].convertAmount | String | 营业收入总额（单位亿） |
| businessIncome.[年份].year | String | 年份 |
| businessIncome.info | String | 营业收入区间说明 |
| businessIncome.title | String | 类别 |
| businessIncome.time | Array | 年份数组 |
| businessIncome.list | Array | 数据列表 |
| businessIncome.list[].amount | Number | 营业收入总额（单位元） |
| businessIncome.list[].convertAmount | String | 营业收入总额（单位亿） |
| businessIncome.list[].year | String | 年份 |
| netProfit | Object | 净利润信息 |
| netProfit.[年份] | Object | 某年份的净利润数据 |
| netProfit.[年份].amount | Number | 净利润总额（单位元） |
| netProfit.[年份].convertAmount | String | 净利润总额（单位亿） |
| netProfit.[年份].year | String | 年份 |
| netProfit.info | String | 利润区间说明 |
| netProfit.title | String | 类别 |
| netProfit.time | Array | 年份数组 |
| netProfit.list | Array | 数据列表 |
| netProfit.list[].amount | Number | 净利润总额（单位元） |
| netProfit.list[].convertAmount | String | 净利润总额（单位亿） |
| netProfit.list[].year | String | 年份 |
| grossInterestRate | Object | 毛利率信息 |
| grossInterestRate.[年份] | Object | 某年份的毛利率数据 |
| grossInterestRate.[年份].amount | Number | 百分比 |
| grossInterestRate.[年份].year | String | 年份 |
| grossInterestRate.info | String | 利率区间说明 |
| grossInterestRate.title | String | 类别 |
| grossInterestRate.time | Array | 年份数组 |
| grossInterestRate.list | Array | 数据列表 |
| grossInterestRate.list[].amount | Number | 百分比 |
| grossInterestRate.list[].year | String | 年份 |

### 2.2 企业基本信息 (818)

**接口地址**: `http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0`

**请求参数**:

| 参数 | 必选 | 类型 | 说明 |
|------|------|------|------|
| keyword | true | String | 搜索关键字（公司名称、公司id、注册号或社会统一信用代码） |

**返回字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| result | Object | 结果对象 |
| historyNames | String | 曾用名 |
| regStatus | String | 企业状态 |
| emailList | Array | 全部邮箱列表 |
| emailList[] | String | 邮箱地址 |
| bondNum | String | 股票号 |
| bondName | String | 股票名 |
| type | Number | 法人类型（1-人，2-公司） |
| revokeReason | String | 吊销原因 |
| property3 | String | 英文名 |
| usedBondName | String | 股票曾用名 |
| approvedTime | Number | 核准时间（时间戳） |
| id | Number | 企业id |
| orgNumber | String | 组织机构代码 |
| businessScope | String | 经营范围 |
| taxNumber | String | 纳税人识别号 |
| regCapitalCurrency | String | 注册资本币种 |
| tags | String | 企业标签 |
| phoneNumber | String | 企业联系方式 |
| district | String | 区 |
| economicFunctionZone1 | String | 经济功能区1 |
| economicFunctionZone2 | String | 经济功能区2 |
| name | String | 企业名 |
| percentileScore | Number | 企业评分（万分制） |
| industryAll | Object | 国民经济行业分类 |
| industryAll.category | String | 行业门类 |
| industryAll.categoryMiddle | String | 行业中类 |
| industryAll.categoryBig | String | 行业大类 |
| industryAll.categorySmall | String | 行业小类 |
| industryAll.categoryCodeFirst | String | 门类行业代码 |
| industryAll.categoryCodeSecond | String | 大类行业代码 |
| industryAll.categoryCodeThird | String | 中类行业代码 |
| industryAll.categoryCodeFourth | String | 小类行业代码 |
| isMicroEnt | Number | 是否是小微企业（0-否，1-是） |
| cancelDate | Number | 注销日期（时间戳） |
| districtCode | String | 行政区划代码 |
| regCapital | String | 注册资本 |
| city | String | 市 |
| staffNumRange | String | 人员规模 |
| historyNameList | Array | 曾用名列表 |
| historyNameList[] | String | 曾用名 |
| industry | String | 行业 |
| revokeDate | Number | 吊销日期（时间戳） |
| updateTimes | Number | 更新时间（时间戳） |
| BRNNumber | String | 商业登记号 |
| legalPersonName | String | 法人 |
| regNumber | String | 注册号 |
| creditCode | String | 统一社会信用代码 |
| fromTime | Number | 经营开始时间（时间戳） |
| socialStaffNum | Number | 参保人数 |
| actualCapitalCurrency | String | 实收注册资本币种 |
| alias | String | 简称 |
| companyOrgType | String | 企业类型 |
| cancelReason | String | 注销原因 |
| toTime | Number | 经营结束时间（时间戳） |
| email | String | 邮箱 |
| actualCapital | String | 实收注册资金 |
| establishTime | Number | 成立日期（时间戳） |
| regInstitute | String | 登记机关 |
| regLocation | String | 注册地址 |
| websiteList | String | 网址 |
| bondType | String | 股票类型 |
| base | String | 省份简称 |

### 2.3 企业股东信息 (821)

**接口地址**: `http://open.api.tianyancha.com/services/open/ic/holder/2.0`

**请求参数**:

| 参数 | 必选 | 类型 | 说明 |
|------|------|------|------|
| keyword | true | String | 搜索关键字（公司名称、公司id、注册号或社会统一信用代码） |
| pageSize | false | Number | 每页条数（默认20条，最大20条） |
| pageNum | false | Number | 当前页数（默认第1页） |
| source | false | Number | 数据来源（1-工商登记，2-最新公示） |

**返回字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| result | Object | 结果对象 |
| total | Number | 股东总数 |
| items | Array | 股东列表 |
| items[].cgid | Number | 公司id |
| items[].capital | Array | 认缴信息列表 |
| items[].capital[].amomon | String | 出资金额 |
| items[].capital[].payment | String | 认缴方式 |
| items[].capital[].time | String | 出资时间 |
| items[].capital[].percent | String | 占比 |
| items[].ftShareholding | Number | 首次持股日期（时间戳） |
| items[].name | String | 股东名 |
| items[].capitalActl | Array | 实缴信息列表 |
| items[].capitalActl[].amomon | String | 出资金额 |
| items[].capitalActl[].payment | String | 实缴方式 |
| items[].capitalActl[].time | String | 出资时间 |
| items[].capitalActl[].percent | String | 占比 |
| items[].logo | String | logo |
| items[].alias | String | 简称 |
| items[].id | Number | 对应表id |
| items[].type | Number | 股东类型（1-公司，2-人，3-其它） |
| items[].hcgid | String | 人员hcgid |

### 2.4 最终受益人 (945)

**接口地址**: `http://open.api.tianyancha.com/services/open/ic/humanholding/2.0`

**请求参数**:

| 参数 | 必选 | 类型 | 说明 |
|------|------|------|------|
| keyword | true | String | 搜索关键字（公司名称、公司id、注册号或社会统一信用代码） |
| pageSize | false | Number | 每页条数（默认20条，最大20条） |
| pageNum | false | Number | 当前页数（默认第1页） |

**返回字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| result | Object | 结果对象 |
| total | Number | 受益人总数 |
| items | Array | 受益人列表 |
| items[].toco | Number | 关联公司数 |
| items[].total | Number | 总数 |
| items[].chainList | Array | 法人列表 |
| items[].chainList[].investType | Number | 投资类型（1-直接，2-间接） |
| items[].chainList[].id | Number | 人id |
| items[].chainList[].type | String | 类型（human/company/title/percent） |
| items[].chainList[].title | String | type说明 |
| items[].chainList[].value | String | 法人名称或公司名称 |
| items[].chainList[].cid | Number | 公司id |
| items[].chainList[].info | String | 信息 |
| items[].name | String | 姓名 |
| items[].logo | String | logo |
| items[].id | Number | 人id |
| items[].type | String | 类型（human/company） |
| items[].percent | String | 占比 |
| items[].hcgid | String | 无用 |
| items[].cid | Number | 公司id |

### 2.5 企业规模 (1149)

**接口地址**: `http://open.api.tianyancha.com/services/open/ic/scale`

**请求参数**:

| 参数 | 必选 | 类型 | 说明 |
|------|------|------|------|
| keyword | true | String | 搜索关键字（公司名称、公司id、注册号或社会统一信用代码） |

**返回字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| result | String | 企业规模（如"大型"） |

## 3. 调用示例

### Java调用示例
```java
public static String getMessageByUrlToken(String path, String token) {
    String result = "";
    try {
        HttpGet request = new HttpGet(path);
        request.setHeader("Authorization", token);
        HttpClient httpClient = new DefaultHttpClient();
        HttpResponse response = httpClient.execute(request);
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            result = EntityUtils.toString(response.getEntity(), "utf-8");
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return result;
}
```

### Python调用示例
```python
import requests

def get_company_info(keyword, token):
    url = "http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0"
    params = {"keyword": keyword}
    headers = {"Authorization": token}
    
    response = requests.get(url, params=params, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": "API请求失败", "code": response.status_code}
```

## 4. 注意事项

1. **频率限制**：所有接口都有访问频率限制，避免短时间内多次调用
2. **参数编码**：所有请求参数都需要进行URLEncode编码
3. **分页处理**：分页接口默认返回20条数据，最大不超过20条
4. **错误处理**：调用后需检查error_code字段，0表示成功，其他值为错误
5. **数据更新**：企业信息可能存在延迟，关键数据需二次确认
6. **数据类型**：注意区分数字类型和字符串类型（如金额可能是字符串格式）

> 本文档涵盖了天眼查API的核心接口及完整字段定义，开发时请以实际返回数据为准。建议在正式集成前进行充分测试，确保数据格式和业务逻辑符合预期。