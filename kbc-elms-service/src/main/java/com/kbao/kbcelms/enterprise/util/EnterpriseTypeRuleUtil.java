package com.kbao.kbcelms.enterprise.util;

import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型规则工具类
 * <AUTHOR>
 * @date 2025-07-28
 */
public class EnterpriseTypeRuleUtil {
    
    /**
     * 生成员工规模范围展示文本
     * @param rules 规则列表
     * @return 员工规模范围文本
     */
    public static String generateEmployeeRangeText(List<EnterpriseTypeRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        List<EnterpriseTypeRule> employeeRules = rules.stream()
            .filter(rule -> "employeeCount".equals(rule.getField()))
            .collect(Collectors.toList());
        return generateFieldText(employeeRules, "人");
    }

    /**
     * 生成营收规模范围展示文本
     * @param rules 规则列表
     * @return 营收规模范围文本
     */
    public static String generateRevenueRangeText(List<EnterpriseTypeRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        List<EnterpriseTypeRule> revenueRules = rules.stream()
            .filter(rule -> "revenue".equals(rule.getField()))
            .collect(Collectors.toList());
        return generateFieldText(revenueRules, "万元");
    }
    
    /**
     * 生成字段展示文本（将同一字段的条件用分号分割）
     * @param rules 规则列表
     * @param unit 单位
     * @return 展示文本
     */
    private static String generateFieldText(List<EnterpriseTypeRule> rules, String unit) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        return rules.stream()
            .map(rule -> rule.getOperator() + formatValue(rule.getValue(), unit))
            .collect(Collectors.joining(";"));
    }
    
    /**
     * 格式化数值显示
     * @param value 数值
     * @param unit 单位
     * @return 格式化后的文本
     */
    private static String formatValue(Long value, String unit) {
        if ("万元".equals(unit)) {
            // 营收以万元为单位显示
            if (value >= 10000) {
                DecimalFormat df = new DecimalFormat("#.##");
                double wanValue = value / 10000.0;
                return df.format(wanValue) + unit;
            } else {
                return value + "元";
            }
        } else {
            // 员工数直接显示
            return value + unit;
        }
    }

}
