package com.kbao.kbcelms.dataTemplate.service;

import com.alibaba.fastjson.JSONObject;import com.kbao.commons.export.ExcelUtils;import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;
import com.kbao.kbcelms.dataTemplate.dao.DataTemplateFieldDao;
import com.kbao.tool.util.EmptyUtils;import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Sort;import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
public class DataTemplateFieldService extends BaseMongoServiceImpl<DataTemplateField, String, DataTemplateFieldDao> {
    @Autowired
    private MongoTemplate mongoTemplate;

    public void saveBatch(Integer templateId, List<DataTemplateField> list) {
        this.delTemplateFields(templateId);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int sort = 1;
        for (DataTemplateField dataTemplateField : list) {
            dataTemplateField.setTemplateId(templateId);
            dataTemplateField.setSort(sort++);
        }
        mongoTemplate.insertAll(list);
    }

    public List<DataTemplateField> getFieldList(Integer templateId) {
        Query query = new Query(Criteria.where("templateId").is(templateId));
        query.with(Sort.by(Sort.Direction.ASC, "sort"));
        return mongoTemplate.find(query, DataTemplateField.class);
    }

    public List<DataTemplateField> getExportFieldList(Integer templateId) {
        List<DataTemplateField> fieldList = this.getFieldList(templateId);
        for (DataTemplateField field : fieldList) {
            if (EmptyUtils.isNotEmpty(field.getAdditional())) {
                field.setAdditionalStr(JSONObject.toJSONString(field.getAdditional()));
            }
        }
        return fieldList;
    }

    public List<DataTemplateField> importFields(Integer templateId, MultipartFile file) {
        ExcelUtils<DataTemplateField> excelUtils = new ExcelUtils<>(DataTemplateField.class);
        List<DataTemplateField> dataTemplateFields = excelUtils.readExcel(DataTemplateField.class, file);
        for (DataTemplateField field : dataTemplateFields) {
            if (EmptyUtils.isNotEmpty(field.getAdditionalStr())) {
                field.setAdditional(JSONObject.parseObject(field.getAdditionalStr()));
            }
        }
        return dataTemplateFields;
    }

    private void delTemplateFields(Integer templateId) {
        Query query = new Query(Criteria.where("templateId").is(templateId));
        this.remove(query);
    }

} 