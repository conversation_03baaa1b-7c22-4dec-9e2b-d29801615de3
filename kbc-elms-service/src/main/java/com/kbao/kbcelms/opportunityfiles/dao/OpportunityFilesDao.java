package com.kbao.kbcelms.opportunityfiles.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description 目录树状结构
 * <AUTHOR>
 * @version V1.0
 * @since V1.0
 * @date 2020/5/7 9:17
 * @param
 *
 */
@Repository
public class OpportunityFilesDao extends BaseMongoDaoImpl<OpportunityFiles,String> {

    public List<OpportunityFiles> findByOpportunityId(Integer opportunityId,String tenantId){
        Criteria criteria = Criteria.
                where("tenantId").is(tenantId)
                .and("opportunityId").is(opportunityId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }

}
