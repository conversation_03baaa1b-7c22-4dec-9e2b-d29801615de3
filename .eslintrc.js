module.exports = {
	root: false,
	parserOptions: {
		parser: 'babel-eslint',
		sourceType: 'module'
	},
	env: {
		browser: true,
		node: true,
		es6: true,
	},
	extends: [
		'plugin:vue/recommended',
	],
	rules: {
		"linebreak-style": [0 ,"error", "windows"],
		"no-console":"off",
		"no-unused-vars": "off",
		'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
		'generator-star-spacing': 'off',
		"vue/no-parsing-error": ["off"],
		"vue/attribute-hyphenation": ["error", "never", {
				"ignore": ["-",]
			}
		],
		"vue/html-indent": ["error", 'tab', {
				"attribute": 1,
				"baseIndent": 1,
				"closeBracket": 0,
				"alignAttributesVertically": true,
				"ignores": []
			}
		],
		"vue/max-attributes-per-line": ["error", {
			"singleline": 30,
			"multiline": {
				"max": 1,
				"allowFirstLine": false
			}
		}],
	},
	"globals":{
		"UE": true,
		"h5config":true,
	}
}

