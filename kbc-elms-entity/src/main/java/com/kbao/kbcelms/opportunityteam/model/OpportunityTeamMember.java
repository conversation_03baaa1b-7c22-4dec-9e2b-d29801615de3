package com.kbao.kbcelms.opportunityteam.model;

import lombok.Data;

/**
 * 机会项目成员列表
 */
@Data
public class OpportunityTeamMember {

    /** 机会项目成员id */
    private Integer id;
    /** 机会id */
    private Integer opportunityId;

    /** 用户id */
    private String userId;

    /**
     * 角色类型
     */
    private Integer roleType;


    /**
     * 姓名
     */
    private String nickname;

    /**
     * 系统账号名
     */
    private String userName;
    /**
     * 人员归属
     */
    private String organNamePath;
    /**
     * 角色名
     */
    private String roleName;
    /** 参与状态 0 待确认，1已确认  5 已拒绝 */
    private Integer joinType;
    /**
     * 是否默认成员
     */
    private Integer isDefault;

    /** 邀请次数 */
    private Integer times;

    /**
     * 邮箱
     */
    private String email;


}
