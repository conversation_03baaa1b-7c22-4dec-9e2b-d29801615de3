package com.kbao.kbcelms.opportunityteam.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机会项目成员列表
 */
@Data
public class OpportunityTeam {
    /** 编号 */
    private Integer id;
    /** 机会id */
    private Integer opportunityId;
    /** 租户id */
    private String tenantId;
    /** 用户id */
    private String userId;

    /**
     * 是否默认成员
     */
    private Integer isDefault;

    /**
     * 角色类型
     */
    private Integer roleType;

    /** 参与状态 0 待确认，1已确认  5 已拒绝 */
    private Integer joinType;
    /** 邀请次数 */
    private Integer times;
    /** 创建人编号 当前用户ID */
    private String createId;
    /** 创建人姓名 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 创建日期 默认为当前时间 */
    private Date updateTime;
    /** 排序号（is_deleted） */
    private Integer isDeleted;

} 