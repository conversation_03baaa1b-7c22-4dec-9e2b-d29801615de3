<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="processKey" column="process_key" jdbcType="VARCHAR" />
        <result property="processName" column="process_name" jdbcType="VARCHAR" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="bpmProcessId" column="bpm_process_id" jdbcType="VARCHAR" />
        <result property="processStatus" column="process_status" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="DATE" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="DATE" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tenant_id, process_key, process_name, opportunity_id, bpm_process_id, process_status, create_id, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.tenant_id,
        t.process_key,
        t.process_name,
        t.opportunity_id,
        t.bpm_process_id,
        t.process_status,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="processKey != null">
                and t.process_key = #{processKey,jdbcType=VARCHAR}
            </if>
            <if test="processName != null">
                and t.process_name = #{processName,jdbcType=VARCHAR}
            </if>
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="bpmProcessId != null">
                and t.bpm_process_id = #{bpmProcessId,jdbcType=VARCHAR}
            </if>
            <if test="processStatus != null">
                and t.process_status = #{processStatus,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=DATE}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=DATE}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_process where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_process t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_process t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process (
            tenant_id, process_key, process_name, opportunity_id, bpm_process_id, process_status, create_id, create_time, update_id, update_time, is_deleted
        ) values (
            #{tenantId}, #{processKey}, #{processName}, #{opportunityId}, #{bpmProcessId}, #{processStatus}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">tenant_id,</if>
            <if test="processKey != null">process_key,</if>
            <if test="processName != null">process_name,</if>
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="bpmProcessId != null">bpm_process_id,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">#{tenantId},</if>
            <if test="processKey != null">#{processKey},</if>
            <if test="processName != null">#{processName},</if>
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="bpmProcessId != null">#{bpmProcessId},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess">
        update t_opportunity_process set
            tenant_id = #{tenantId},
            process_key = #{processKey},
            process_name = #{processName},
            opportunity_id = #{opportunityId},
            bpm_process_id = #{bpmProcessId},
            process_status = #{processStatus},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess">
        update t_opportunity_process
        <set>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="processKey != null">process_key = #{processKey},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="bpmProcessId != null">bpm_process_id = #{bpmProcessId},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_process set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_process (
            tenant_id, process_key, process_name, opportunity_id, bpm_process_id, process_status, create_id, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tenantId}, #{item.processKey}, #{item.processName}, #{item.opportunityId}, #{item.bpmProcessId}, #{item.processStatus}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_process set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 