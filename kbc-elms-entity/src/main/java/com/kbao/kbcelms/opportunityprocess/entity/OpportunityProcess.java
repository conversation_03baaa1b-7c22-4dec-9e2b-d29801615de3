package com.kbao.kbcelms.opportunityprocess.entity;

import java.util.Date;

/**
 * 流程定义表
 */
public class OpportunityProcess {
    /** 编号 */
    private Integer id;
    /** 租户id */
    private String tenantId;
    /** 流程定义key */
    private String processKey;
    /** 流程名称 */
    private String processName;
    /** 机会id */
    private Integer opportunityId;
    /** 业务流程表Id */
    private String bpmProcessId;
    /** 流程状态 */
    private String processStatus;
    /** 创建人编号 当前用户ID */
    private String createId;
    /** 创建日期 */
    private Date createTime;
    /** 更新人 默认为当前时间 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;

    /** 删除 */
    private Integer isDeleted;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getProcessKey() { return processKey; }
    public void setProcessKey(String processKey) { this.processKey = processKey; }
    public String getProcessName() { return processName; }
    public void setProcessName(String processName) { this.processName = processName; }
    public Integer getOpportunityId() { return opportunityId; }
    public void setOpportunityId(Integer opportunityId) { this.opportunityId = opportunityId; }
    public String getBpmProcessId() { return bpmProcessId; }
    public void setBpmProcessId(String bpmProcessId) { this.bpmProcessId = bpmProcessId; }
    public String getProcessStatus() { return processStatus; }
    public void setProcessStatus(String processStatus) { this.processStatus = processStatus; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
} 