package com.kbao.kbcelms.opportunityleaderlog.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.sql.Date;

/** 
 * 机会项目经理变更日志
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document
public class OpportunityLeaderLog
{
  /**
   * 编号
   */
  protected Integer id;
  /**
   * 变更机会id
   */
  protected Integer opportunityId;
  /**
   * 原项目经理id
   */
  protected Integer sourceUserId;
  /**
   * 新项目经理id
   */
  protected String targetUserId;
  protected String sourceName;
  /**
   * 创建人编号 当前用户ID
   */
  protected Integer creatorId;
  /**
   * 创建人姓名
   */
  protected String creatorName;
  /**
   * 创建日期 默认为当前时间
   */
  protected Date createDate;
  protected String targetName;

}

