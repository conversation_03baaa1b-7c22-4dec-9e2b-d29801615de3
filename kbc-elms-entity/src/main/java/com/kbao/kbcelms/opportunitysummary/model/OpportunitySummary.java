package com.kbao.kbcelms.opportunitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/** 
 * 机会总结表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document
public class OpportunitySummary
{
  /**
   * 主键
   */
  protected String id;
  /**
   * 总结编号
   */
  protected String summaryId;
  /**
   * 机会id
   */
  protected String opportunityId;
  /**
   * 文件名称
   */
  protected String fileName;
  /**
   * 文件路径
   */
  protected String filePath;
  /**
   * 文件类型
   */
  protected String fileType;
  /**
   * 创建人
   */
  protected String createId;
  /**
   * 创建时间
   */
  protected Date createTime;
  /**
   * 更新人
   */
  protected String updateId;
  /**
   * 更新时间
   */
  protected Date updateTime;
  /**
   * 租户Id
   */
  protected String tenantId;
}

