package com.kbao.kbcelms.opportunitydetail.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机会明细表
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
public class OpportunityDetail {
    /** 主键 */
    private Integer id;
    /** 机会ID */
    private Integer opportunityId;
    /** 投保人员规模 */
    private Integer insureNum;
    /** 是否有历史保单：0-否，1-是 */
    private String hasHistoryPolicy;
    /** 保单到期日期 */
    private Date policyExpireTime;
    /** 是否需要投标：0-否，1-是 */
    private Integer isBid;
    /** 投标结果 0-失败 1-成功 */
    private Integer bidResult;
    /** 投标开始时间 */
    private Date bidStartDate;
    /** 投标结束时间 */
    private Date bidEndDate;
    /** 保费预算 */
    private Integer premiumBudget;
    /** 企业对接人 */
    private String contacter;
    /** 企业对接人职务 */
    private String contacterPost;
    /** 是否添加健康服务产品 */
    private String addHealthService;
    /** 是否添加救援服务产品 */
    private String addRescueService;
    /** 健康服务产品编码 */
    private String healthServiceCode;
    /** 健康服务产品名称 */
    private String healthServiceName;
    /** 救援服务产品编码 */
    private String rescueServiceCode;
    /** 救援服务产品名称 */
    private String rescueServiceName;
    /** 员福险种类型, 存储员福险种类型编码，用逗号分隔 */
    private String employeeInsuranceType;
    /** 综合险种类型，存储综合险种名称，多个用逗号分隔 */
    private String generalInsuranceType;
    /** 备注 */
    private String remark;
    /** 组队完成时间 */
    private Date teamTime;
    /** 机会提交时间 */
    private Date submitTime;
    /** 日志更新时间 */
    private Date logTime;
    /** 创建人 当前用户ID */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 租户ID */
    private String tenantId;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
} 