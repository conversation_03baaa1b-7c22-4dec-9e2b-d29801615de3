package com.kbao.kbcelms.divisionratio.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;

/**
 * 分工比例
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document
public class DivisionRatio {
    @Id
    String id;
     //编号
    String ratioId;
    //分工名称
    String name;
    //比例
    @Min(1)
    @Max(100)
    Integer ratio;
    //人数
    @Min(1)
    @Max(100)
    Integer number;
    //状态 0 未启用 1 启用
    String status;
    //创建时间
    Date createTime;
    //更新时间
    Date updateTime;
    //创建人
    String createId;
    //更新人
    String updateId;
    //租户id
    String tenantId;
}
