package com.kbao.kbcelms.constant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 常数配置数据传输对象
 */
@Data
@ApiModel(value = "ConstantConfigDTO", description = "常数配置数据传输对象")
public class ConstantConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 常数ID
     */
    @ApiModelProperty(value = "常数ID")
    private Long id;

    /**
     * 常数名称
     */
    @NotBlank(message = "常数名称不能为空")
    @ApiModelProperty(value = "常数名称", required = true)
    private String name;

    /**
     * 常数编码
     */
    @NotBlank(message = "常数编码不能为空")
    @ApiModelProperty(value = "常数编码", required = true)
    private String code;

    /**
     * 常数描述
     */
    @ApiModelProperty(value = "常数描述")
    private String description;

    /**
     * 分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数
     */
    @ApiModelProperty(value = "分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数")
    private Integer category;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空")
    @ApiModelProperty(value = "数据类型：string-字符串，number-数值，boolean-布尔值，date-日期，json-JSON", required = true)
    private String dataType;

    /**
     * 常数值
     */
    @NotBlank(message = "常数值不能为空")
    @ApiModelProperty(value = "常数值", required = true)
    private String constantValue;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 最小值（数值类型）
     */
    @ApiModelProperty(value = "最小值")
    private String minValue;

    /**
     * 最大值（数值类型）
     */
    @ApiModelProperty(value = "最大值")
    private String maxValue;

    /**
     * 取值范围说明
     */
    @ApiModelProperty(value = "取值范围说明")
    private String valueRange;



    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;
}
