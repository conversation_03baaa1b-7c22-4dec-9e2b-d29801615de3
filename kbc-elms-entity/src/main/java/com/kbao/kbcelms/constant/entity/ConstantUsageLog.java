package com.kbao.kbcelms.constant.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 常数使用记录实体类
 */
@ApiModel(value = "ConstantUsageLog", description = "常数使用记录实体")
public class ConstantUsageLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ApiModelProperty(value = "记录ID")
    private Long id;

    /**
     * 常数ID
     */
    @ApiModelProperty(value = "常数ID")
    private Long constantId;

    /**
     * 常数编码
     */
    @ApiModelProperty(value = "常数编码")
    private String constantCode;

    /**
     * 使用模块
     */
    @ApiModelProperty(value = "使用模块")
    private String usageModule;

    /**
     * 使用功能
     */
    @ApiModelProperty(value = "使用功能")
    private String usageFunction;

    /**
     * 使用上下文
     */
    @ApiModelProperty(value = "使用上下文")
    private String usageContext;

    /**
     * 使用的值
     */
    @ApiModelProperty(value = "使用的值")
    private String usedValue;

    /**
     * 使用时间
     */
    @ApiModelProperty(value = "使用时间")
    private Date usageTime;

    /**
     * 使用用户ID
     */
    @ApiModelProperty(value = "使用用户ID")
    private String userId;

    /**
     * 使用用户名
     */
    @ApiModelProperty(value = "使用用户名")
    private String userName;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConstantId() {
        return constantId;
    }

    public void setConstantId(Long constantId) {
        this.constantId = constantId;
    }

    public String getConstantCode() {
        return constantCode;
    }

    public void setConstantCode(String constantCode) {
        this.constantCode = constantCode;
    }

    public String getUsageModule() {
        return usageModule;
    }

    public void setUsageModule(String usageModule) {
        this.usageModule = usageModule;
    }

    public String getUsageFunction() {
        return usageFunction;
    }

    public void setUsageFunction(String usageFunction) {
        this.usageFunction = usageFunction;
    }

    public String getUsageContext() {
        return usageContext;
    }

    public void setUsageContext(String usageContext) {
        this.usageContext = usageContext;
    }

    public String getUsedValue() {
        return usedValue;
    }

    public void setUsedValue(String usedValue) {
        this.usedValue = usedValue;
    }

    public Date getUsageTime() {
        return usageTime;
    }

    public void setUsageTime(Date usageTime) {
        this.usageTime = usageTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
