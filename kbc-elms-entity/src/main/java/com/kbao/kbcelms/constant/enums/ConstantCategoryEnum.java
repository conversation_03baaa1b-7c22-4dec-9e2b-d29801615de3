package com.kbao.kbcelms.constant.enums;

/**
 * 常数分类枚举
 */
public enum ConstantCategoryEnum {

    /**
     * 系统参数
     */
    SYSTEM_PARAM(1, "系统参数", "系统运行相关的参数配置"),

    /**
     * 数学常数
     */
    MATHEMATICAL_CONSTANT(2, "数学常数", "数学计算相关的常数"),

    /**
     * 业务常数
     */
    BUSINESS_CONSTANT(3, "业务常数", "业务逻辑相关的常数"),

    /**
     * 系数参数
     */
    COEFFICIENT_PARAM(4, "系数参数", "各种计算系数参数");

    /**
     * 分类代码
     */
    private final Integer code;

    /**
     * 分类名称
     */
    private final String name;

    /**
     * 分类描述
     */
    private final String description;

    ConstantCategoryEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 分类代码
     * @return 枚举值
     */
    public static ConstantCategoryEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ConstantCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     *
     * @param code 分类代码
     * @return 分类名称
     */
    public static String getNameByCode(Integer code) {
        ConstantCategoryEnum category = getByCode(code);
        return category != null ? category.getName() : null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 分类代码
     * @return 分类描述
     */
    public static String getDescriptionByCode(Integer code) {
        ConstantCategoryEnum category = getByCode(code);
        return category != null ? category.getDescription() : null;
    }

    /**
     * 验证分类代码是否有效
     *
     * @param code 分类代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
