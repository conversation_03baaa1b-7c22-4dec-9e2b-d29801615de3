package com.kbao.kbcelms.constant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 常数配置查询条件DTO
 */
@Data
@ApiModel(value = "ConstantConfigQueryDTO", description = "常数配置查询条件DTO")
public class ConstantConfigQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 常数名称（模糊查询）
     */
    @ApiModelProperty(value = "常数名称（模糊查询）")
    private String name;

    /**
     * 常数编码（模糊查询）
     */
    @ApiModelProperty(value = "常数编码（模糊查询）")
    private String code;

    /**
     * 分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数
     */
    @ApiModelProperty(value = "分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数")
    private Integer category;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型：string-字符串，number-数值，boolean-布尔值，date-日期，json-JSON")
    private String dataType;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;
}
