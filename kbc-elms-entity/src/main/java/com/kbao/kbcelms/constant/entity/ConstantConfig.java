package com.kbao.kbcelms.constant.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 常数配置实体类
 */
@Data
@ApiModel(value = "ConstantConfig", description = "常数配置实体")
public class ConstantConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 常数ID
     */
    @ApiModelProperty(value = "常数ID")
    private Long id;

    /**
     * 常数名称
     */
    @ApiModelProperty(value = "常数名称")
    private String name;

    /**
     * 常数编码
     */
    @ApiModelProperty(value = "常数编码")
    private String code;

    /**
     * 常数描述
     */
    @ApiModelProperty(value = "常数描述")
    private String description;

    /**
     * 分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数
     */
    @ApiModelProperty(value = "分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数")
    private Integer category;

    /**
     * 数据类型：string-字符串，number-数值，boolean-布尔值，date-日期，json-JSON
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * 常数值
     */
    @ApiModelProperty(value = "常数值")
    private String constantValue;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 最小值（数值类型）
     */
    @ApiModelProperty(value = "最小值")
    private String minValue;

    /**
     * 最大值（数值类型）
     */
    @ApiModelProperty(value = "最大值")
    private String maxValue;

    /**
     * 取值范围说明
     */
    @ApiModelProperty(value = "取值范围说明")
    private String valueRange;



    /**
     * 使用次数
     */
    @ApiModelProperty(value = "使用次数")
    private Integer usageCount;

    /**
     * 最后使用时间
     */
    @ApiModelProperty(value = "最后使用时间")
    private Date lastUsedTime;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @ApiModelProperty(value = "是否删除：0-未删除，1-已删除")
    private Integer deleted;
}
