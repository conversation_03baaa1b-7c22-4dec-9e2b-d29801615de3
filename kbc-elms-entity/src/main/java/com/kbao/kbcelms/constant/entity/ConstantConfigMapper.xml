<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.constant.dao.ConstantConfigMapper">

    <!-- 常数配置VO结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.constant.vo.ConstantConfigVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="category" property="category"/>
        <result column="category_name" property="categoryName"/>
        <result column="data_type" property="dataType"/>
        <result column="constant_value" property="constantValue"/>
        <result column="default_value" property="defaultValue"/>
        <result column="unit" property="unit"/>
        <result column="min_value" property="minValue"/>
        <result column="max_value" property="maxValue"/>
        <result column="value_range" property="valueRange"/>
        <result column="usage_count" property="usageCount"/>
        <result column="last_used_time" property="lastUsedTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 插入常数配置 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.constant.entity.ConstantConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_constant_config (
            name, code, description, category, data_type,
            constant_value, default_value, unit, min_value, max_value, value_range,
            usage_count, status,
            create_user, update_user, deleted
        ) VALUES (
            #{name}, #{code}, #{description}, #{category}, #{dataType},
            #{constantValue}, #{defaultValue}, #{unit}, #{minValue}, #{maxValue}, #{valueRange},
            #{usageCount}, #{status},
            #{createUser}, #{updateUser}, 0
        )
    </insert>

    <!-- 根据ID更新常数配置 -->
    <update id="updateById" parameterType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        UPDATE t_constant_config SET
        <if test="name != null">
            name = #{name},
        </if>
        <if test="code != null">
            code = #{code},
        </if>
        <if test="description != null">
            description = #{description},
        </if>
        <if test="category != null">
            category = #{category},
        </if>
        <if test="dataType != null">
            data_type = #{dataType},
        </if>
        <if test="constantValue != null">
            constant_value = #{constantValue},
        </if>
        <if test="defaultValue != null">
            default_value = #{defaultValue},
        </if>
        <if test="unit != null">
            unit = #{unit},
        </if>
        <if test="minValue != null">
            min_value = #{minValue},
        </if>
        <if test="maxValue != null">
            max_value = #{maxValue},
        </if>
        <if test="valueRange != null">
            value_range = #{valueRange},
        </if>

        <if test="usageCount != null">
            usage_count = #{usageCount},
        </if>
        <if test="lastUsedTime != null">
            last_used_time = #{lastUsedTime},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="updateUser != null">
            update_user = #{updateUser},
        </if>
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据主键更新常数配置（BaseMapper标准方法） -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        UPDATE t_constant_config SET
            name = #{name},
            code = #{code},
            description = #{description},
            category = #{category},
            data_type = #{dataType},
            constant_value = #{constantValue},
            default_value = #{defaultValue},
            unit = #{unit},
            min_value = #{minValue},
            max_value = #{maxValue},
            value_range = #{valueRange},
            usage_count = #{usageCount},
            last_used_time = #{lastUsedTime},
            status = #{status},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据主键选择性更新常数配置（BaseMapper标准方法） -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        UPDATE t_constant_config
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="category != null">category = #{category},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="constantValue != null">constant_value = #{constantValue},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="minValue != null">min_value = #{minValue},</if>
            <if test="maxValue != null">max_value = #{maxValue},</if>
            <if test="valueRange != null">value_range = #{valueRange},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="lastUsedTime != null">last_used_time = #{lastUsedTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据ID删除常数配置（逻辑删除） -->
    <update id="deleteById">
        UPDATE t_constant_config SET deleted = 1, update_time = NOW() WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据主键删除常数配置（BaseMapper标准方法） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_constant_config SET deleted = 1, update_time = NOW() WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 选择性插入常数配置（BaseMapper标准方法） -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.constant.entity.ConstantConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_constant_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="category != null">category,</if>
            <if test="dataType != null">data_type,</if>
            <if test="constantValue != null">constant_value,</if>
            <if test="defaultValue != null">default_value,</if>
            <if test="unit != null">unit,</if>
            <if test="minValue != null">min_value,</if>
            <if test="maxValue != null">max_value,</if>
            <if test="valueRange != null">value_range,</if>
            <if test="usageCount != null">usage_count,</if>
            <if test="status != null">status,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            deleted
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="category != null">#{category},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="constantValue != null">#{constantValue},</if>
            <if test="defaultValue != null">#{defaultValue},</if>
            <if test="unit != null">#{unit},</if>
            <if test="minValue != null">#{minValue},</if>
            <if test="maxValue != null">#{maxValue},</if>
            <if test="valueRange != null">#{valueRange},</if>
            <if test="usageCount != null">#{usageCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            0
        </trim>
    </insert>

    <!-- 根据ID查询常数配置 -->
    <select id="selectById" resultType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        SELECT * FROM t_constant_config WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据主键查询常数配置（BaseMapper标准方法） -->
    <select id="selectByPrimaryKey" resultType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        SELECT * FROM t_constant_config WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据编码查询常数配置 -->
    <select id="selectByCode" resultType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        SELECT * FROM t_constant_config WHERE code = #{code} AND deleted = 0
    </select>

    <!-- 分页查询常数配置列表 -->
    <select id="selectConstantConfigList" resultMap="BaseResultMap">
        SELECT 
            cc.id,
            cc.name,
            cc.code,
            cc.description,
            cc.category,
            cc.data_type,
            cc.constant_value,
            cc.default_value,
            cc.unit,
            cc.min_value,
            cc.max_value,
            cc.value_range,
            cc.usage_count,
            cc.last_used_time,
            cc.status,
            cc.create_time,
            cc.update_time,
            cc.create_user,
            cc.update_user
        FROM t_constant_config cc
        WHERE cc.deleted = 0
        <if test="name != null and name != ''">
            AND cc.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code != ''">
            AND cc.code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="category != null">
            AND cc.category = #{category}
        </if>
        <if test="dataType != null and dataType != ''">
            AND cc.data_type = #{dataType}
        </if>
        <if test="status != null">
            AND cc.status = #{status}
        </if>
        ORDER BY cc.update_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询常数配置总数 -->
    <select id="countConstantConfigList" resultType="java.lang.Long">
        SELECT COUNT(*) FROM t_constant_config cc
        WHERE cc.deleted = 0
        <if test="name != null and name != ''">
            AND cc.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code != ''">
            AND cc.code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="category != null">
            AND cc.category = #{category}
        </if>
        <if test="dataType != null and dataType != ''">
            AND cc.data_type = #{dataType}
        </if>
        <if test="status != null">
            AND cc.status = #{status}
        </if>
    </select>

    

    <!-- 批量删除常数配置（逻辑删除） -->
    <update id="batchDeleteByIds">
        UPDATE t_constant_config SET deleted = 1, update_time = NOW() 
        WHERE deleted = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新使用统计 -->
    <update id="updateUsageStatistics">
        UPDATE t_constant_config SET
            usage_count = usage_count + 1,
            last_used_time = NOW(),
            update_time = NOW()
        WHERE code = #{code} AND deleted = 0
    </update>

    <!-- 检查编码是否存在 -->
    <select id="checkCodeExists" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_constant_config
        WHERE code = #{code} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据编码列表查询常数配置 -->
    <select id="selectByCodes" resultType="com.kbao.kbcelms.constant.entity.ConstantConfig">
        SELECT * FROM t_constant_config
        WHERE deleted = 0 AND code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <!-- 检查分类下是否有常数配置 -->
    <select id="checkCategoryHasConstants" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_constant_config
        WHERE category = #{category} AND deleted = 0
    </select>

    <!-- 分页查询常数配置列表（配合PageHelper使用） -->
    <select id="selectConstantConfigListForPage" resultMap="BaseResultMap">
        SELECT
            cc.id,
            cc.name,
            cc.code,
            cc.description,
            cc.category,
            cc.data_type,
            cc.constant_value,
            cc.default_value,
            cc.unit,
            cc.min_value,
            cc.max_value,
            cc.value_range,
            cc.usage_count,
            cc.last_used_time,
            cc.status,
            cc.create_time,
            cc.update_time,
            cc.create_user,
            cc.update_user
        FROM t_constant_config cc
        WHERE cc.deleted = 0
        <if test="name != null and name != ''">
            AND cc.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code != ''">
            AND cc.code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="category != null">
            AND cc.category = #{category}
        </if>
        <if test="dataType != null and dataType != ''">
            AND cc.data_type = #{dataType}
        </if>
        <if test="status != null">
            AND cc.status = #{status}
        </if>
        ORDER BY cc.update_time DESC
    </select>

    <!-- 根据分类查询常数配置列表 -->
    <select id="selectConstantConfigByCategory" resultMap="BaseResultMap">
        SELECT
            cc.id,
            cc.name,
            cc.code,
            cc.description,
            cc.category,
            cc.data_type,
            cc.constant_value,
            cc.default_value,
            cc.unit,
            cc.min_value,
            cc.max_value,
            cc.value_range,
            cc.usage_count,
            cc.last_used_time,
            cc.status,
            cc.create_time,
            cc.update_time,
            cc.create_user,
            cc.update_user
        FROM t_constant_config cc
        WHERE cc.deleted = 0 AND cc.category = #{category} AND cc.status = 1
        ORDER BY cc.update_time DESC
    </select>

    <!-- 查询启用的常数配置列表 -->
    <select id="selectEnabledConstantConfigs" resultMap="BaseResultMap">
        SELECT
            cc.id,
            cc.name,
            cc.code,
            cc.description,
            cc.category,
            cc.data_type,
            cc.constant_value,
            cc.default_value,
            cc.unit,
            cc.min_value,
            cc.max_value,
            cc.value_range,
            cc.usage_count,
            cc.last_used_time,
            cc.status,
            cc.create_time,
            cc.update_time,
            cc.create_user,
            cc.update_user
        FROM t_constant_config cc
        WHERE cc.deleted = 0 AND cc.status = 1
        ORDER BY cc.update_time DESC
    </select>

    <!-- 根据数据类型查询常数配置列表 -->
    <select id="selectConstantConfigByDataType" resultMap="BaseResultMap">
        SELECT
            cc.id,
            cc.name,
            cc.code,
            cc.description,
            cc.category,
            cc.data_type,
            cc.constant_value,
            cc.default_value,
            cc.unit,
            cc.min_value,
            cc.max_value,
            cc.value_range,
            cc.usage_count,
            cc.last_used_time,
            cc.status,
            cc.create_time,
            cc.update_time,
            cc.create_user,
            cc.update_user
        FROM t_constant_config cc
        WHERE cc.deleted = 0 AND cc.data_type = #{dataType} AND cc.status = 1
        ORDER BY cc.update_time DESC
    </select>

    <!-- 更新常数值 -->
    <update id="updateConstantValue">
        UPDATE t_constant_config
        SET constant_value = #{value},
            update_time = NOW()
        WHERE code = #{code} AND deleted = 0
    </update>

    <!-- 更新常数配置状态 -->
    <update id="updateConstantConfigStatus">
        UPDATE t_constant_config
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 重置常数为默认值 -->
    <update id="resetToDefaultValue">
        UPDATE t_constant_config
        SET constant_value = default_value,
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0 AND default_value IS NOT NULL
    </update>

</mapper>
