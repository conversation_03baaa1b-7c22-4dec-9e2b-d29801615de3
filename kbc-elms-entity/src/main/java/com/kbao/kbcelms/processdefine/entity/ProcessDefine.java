package com.kbao.kbcelms.processdefine.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 流程定义表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProcessDefine", description = "流程定义表")
public class ProcessDefine {
    @ApiModelProperty(value = "编号")
    private Integer id;
    @ApiModelProperty(value = "租户id")
    private String tenantId;
    @ApiModelProperty(value = "流程定义key")
    private String processKey;
    @ApiModelProperty(value = "流程名称")
    private String processName;
    @ApiModelProperty(value = "流程状态 0 草稿  1 启用 2 停用")
    private Integer processStatus;
    @ApiModelProperty(value = "流程描述")
    private String processDesc;
    @ApiModelProperty(value = "流程条件 1 员工福利 2 综合保障")
    private Integer processCondition;
    @ApiModelProperty(value = "流程类型 1 默认  2自定义")
    private Integer processType;
    @ApiModelProperty(value = "公司所在地 多个地址，号隔开")
    private String companyAddrs;
    @ApiModelProperty(value = "公司行业 多个行业，号隔开")
    private String companyIndustry;
    @ApiModelProperty(value = "企业规模 A,B,C,D")
    private String companyType;
    @ApiModelProperty(value = "代理人法人公司")
    private String agentLegalCode;
    @ApiModelProperty(value = "营业部编码")
    private String agentTradingCenterCode;
    @ApiModelProperty(value = "创建人编号 当前用户ID")
    private String createId;
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    @ApiModelProperty(value = "更新人 默认为当前时间")
    private String updateId;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "删除")
    private Integer isDeleted;

    //以下数据不进入数据库存储
    @ApiModelProperty(value = "流程图定义")
    private BpmJson bpmJson;

    /**
     * BPM JSON 结构
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "BpmJson", description = "BPM流程图定义")
    public static class BpmJson {
        @ApiModelProperty(value = "流程ID")
        private String id;
        @ApiModelProperty(value = "业务键")
        private String businessKey;
        @ApiModelProperty(value = "租户ID")
        private String tenantId;
        @ApiModelProperty(value = "流程定义key")
        private String processKey;
        @ApiModelProperty(value = "流程名称")
        private String processName;
        @ApiModelProperty(value = "节点列表")
        private List<Node> nodes;
        @ApiModelProperty(value = "连线列表")
        private List<Flow> flows;
    }

    /**
     * 节点
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Node", description = "流程节点")
    public static class Node {
        @ApiModelProperty(value = "节点ID")
        private String id;
        @ApiModelProperty(value = "节点名称")
        private String name;
        @ApiModelProperty(value = "节点类型")
        private String type;
        @ApiModelProperty(value = "节点属性")
        private NodeProperties properties;
    }

    /**
     * 节点属性
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Properties", description = "节点属性")
    public static class NodeProperties {
        @ApiModelProperty(value = "指定人")
        private Assignee assignee;
        @ApiModelProperty(value = "候选组")
        private CandidateGroups candidateGroups;
        @ApiModelProperty(value = "步骤类型 1-9")
        private Integer stepType;
        @ApiModelProperty(value = "待办事项列表")
        private List<DoListItem> dolist;
    }

    /**
     * 指定人
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Assignee", description = "指定人")
    public static class Assignee {
        @ApiModelProperty(value = "用户ID")
        private String userId;
        @ApiModelProperty(value = "用户名称")
        private String userName;
    }

    /**
     * 候选组
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "CandidateGroups", description = "候选组")
    public static class CandidateGroups {
        @ApiModelProperty(value = "角色类型 1 分公司统筹")
        private String roleType;
        @ApiModelProperty(value = "机构类型 1 全国 2 顾问机构")
        private Integer orgType;
    }

    /**
     * 待办事项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "DoListItem", description = "待办事项")
    public static class DoListItem {
        @ApiModelProperty(value = "事项ID")
        private String id;
        @ApiModelProperty(value = "事项名称")
        private String name;
        @ApiModelProperty(value = "是否必填")
        private Boolean required;
    }

    /**
     * 连线
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Flow", description = "流程连线")
    public static class Flow {
        @ApiModelProperty(value = "连线ID")
        private String id;
        @ApiModelProperty(value = "源节点引用")
        private String sourceRef;
        @ApiModelProperty(value = "目标节点引用")
        private String targetRef;
        @ApiModelProperty(value = "连线名称")
        private String name;
    }
}