<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.processdefine.entity.ProcessDefine">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="processKey" column="process_key" jdbcType="VARCHAR" />
        <result property="processName" column="process_name" jdbcType="VARCHAR" />
        <result property="processStatus" column="process_status" jdbcType="INTEGER" />
        <result property="processDesc" column="process_desc" jdbcType="VARCHAR" />
        <result property="processCondition" column="process_condition" jdbcType="INTEGER" />
        <result property="processType" column="process_type" jdbcType="INTEGER" />
        <result property="companyAddrs" column="company_addrs" jdbcType="VARCHAR" />
        <result property="companyIndustry" column="company_industry" jdbcType="VARCHAR" />
        <result property="companyType" column="company_type" jdbcType="VARCHAR" />
        <result property="agentLegalCode" column="agent_legal_code" jdbcType="VARCHAR" />
        <result property="agentTradingCenterCode" column="agent_trading_center_code" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="DATE" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="DATE" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tenant_id, process_key, process_name, process_status, process_desc, process_condition, process_type, company_addrs, company_industry, company_type, agent_legal_code, agent_trading_center_code, create_id, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.tenant_id,
        t.process_key,
        t.process_name,
        t.process_status,
        t.process_desc,
        t.process_condition,
        t.process_type,
        t.company_addrs,
        t.company_industry,
        t.company_type,
        t.agent_legal_code,
        t.agent_trading_center_code,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="processKey != null">
                and t.process_key = #{processKey,jdbcType=VARCHAR}
            </if>
            <if test="processName != null">
                and t.process_name like concat('%', #{processName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="processStatus != null">
                and t.process_status = #{processStatus,jdbcType=INTEGER}
            </if>
            <if test="processDesc != null">
                and t.process_desc = #{processDesc,jdbcType=VARCHAR}
            </if>
            <if test="processCondition != null">
                and t.process_condition = #{processCondition,jdbcType=INTEGER}
            </if>
            <if test="processType != null">
                and t.process_type = #{processType,jdbcType=INTEGER}
            </if>
            <if test="companyAddrs != null">
                and t.company_addrs = #{companyAddrs,jdbcType=VARCHAR}
            </if>
            <if test="companyIndustry != null">
                and t.company_industry = #{companyIndustry,jdbcType=VARCHAR}
            </if>
            <if test="companyType != null">
                and t.company_type = #{companyType,jdbcType=VARCHAR}
            </if>
            <if test="agentLegalCode != null">
                and t.agent_legal_code = #{agentLegalCode,jdbcType=VARCHAR}
            </if>
            <if test="agentTradingCenterCode != null">
                and t.agent_trading_center_code = #{agentTradingCenterCode,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=DATE}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=DATE}
            </if>
            <if test="createTimeStart != null">
                <![CDATA[ and t.create_time >= #{createTimeStart,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="createTimeEnd != null">
                <![CDATA[ and t.create_time <= #{createTimeEnd,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="createName != null and createName != ''">
                <![CDATA[ and u.nick_name LIKE CONCAT('%', #{createName,jdbcType=VARCHAR}, '%') ]]>
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_process_define where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_process_define t
        LEFT JOIN t_user u ON t.create_id = u.user_id
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_process_define t
        LEFT JOIN t_user u ON t.create_id = u.user_id
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.processdefine.entity.ProcessDefine" useGeneratedKeys="true" keyProperty="id">
        insert into t_process_define (
            tenant_id, process_key, process_name, process_status, process_desc, process_condition, process_type, company_addrs, company_industry, company_type, agent_legal_code, agent_trading_center_code, create_id, create_time, update_id, update_time, is_deleted
        ) values (
            #{tenantId}, #{processKey}, #{processName}, #{processStatus}, #{processDesc}, #{processCondition}, #{processType}, #{companyAddrs}, #{companyIndustry}, #{companyType}, #{agentLegalCode}, #{agentTradingCenterCode}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.processdefine.entity.ProcessDefine" useGeneratedKeys="true" keyProperty="id">
        insert into t_process_define
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">tenant_id,</if>
            <if test="processKey != null">process_key,</if>
            <if test="processName != null">process_name,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="processDesc != null">process_desc,</if>
            <if test="processCondition != null">process_condition,</if>
            <if test="processType != null">process_type,</if>
            <if test="companyAddrs != null">company_addrs,</if>
            <if test="companyIndustry != null">company_industry,</if>
            <if test="companyType != null">company_type,</if>
            <if test="agentLegalCode != null">agent_legal_code,</if>
            <if test="agentTradingCenterCode != null">agent_trading_center_code,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">#{tenantId},</if>
            <if test="processKey != null">#{processKey},</if>
            <if test="processName != null">#{processName},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="processDesc != null">#{processDesc},</if>
            <if test="processCondition != null">#{processCondition},</if>
            <if test="processType != null">#{processType},</if>
            <if test="companyAddrs != null">#{companyAddrs},</if>
            <if test="companyIndustry != null">#{companyIndustry},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="agentLegalCode != null">#{agentLegalCode},</if>
            <if test="agentTradingCenterCode != null">#{agentTradingCenterCode},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.processdefine.entity.ProcessDefine">
        update t_process_define set
            tenant_id = #{tenantId},
            process_key = #{processKey},
            process_name = #{processName},
            process_status = #{processStatus},
            process_desc = #{processDesc},
            process_condition = #{processCondition},
            process_type = #{processType},
            company_addrs = #{companyAddrs},
            company_industry = #{companyIndustry},
            company_type = #{companyType},
            agent_legal_code = #{agentLegalCode},
            agent_trading_center_code = #{agentTradingCenterCode},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.processdefine.entity.ProcessDefine">
        update t_process_define
        <set>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="processKey != null">process_key = #{processKey},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="processDesc != null">process_desc = #{processDesc},</if>
            <if test="processCondition != null">process_condition = #{processCondition},</if>
            <if test="processType != null">process_type = #{processType},</if>
            <if test="companyAddrs != null">company_addrs = #{companyAddrs},</if>
            <if test="companyIndustry != null">company_industry = #{companyIndustry},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="agentLegalCode != null">agent_legal_code = #{agentLegalCode},</if>
            <if test="agentTradingCenterCode != null">agent_trading_center_code = #{agentTradingCenterCode},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_process_define set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_process_define (
            tenant_id, process_key, process_name, process_status, process_desc, process_condition, process_type, company_addrs, company_industry, company_type, agent_legal_code, agent_trading_center_code, create_id, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tenantId}, #{item.processKey}, #{item.processName}, #{item.processStatus}, #{item.processDesc}, #{item.processCondition}, #{item.processType}, #{item.companyAddrs}, #{item.companyIndustry}, #{item.companyType}, #{item.agentLegalCode}, #{item.agentTradingCenterCode}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_process_define set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 