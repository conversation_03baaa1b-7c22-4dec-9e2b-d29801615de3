package com.kbao.kbcelms.processdefine.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 流程定义详情查询请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "ProcessDefineDetailRequestVO", description = "流程定义详情查询请求VO")
public class ProcessDefineDetailRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程定义ID", required = true)
    private Integer id;
} 