package com.kbao.kbcelms.processdefine.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程定义查询请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "ProcessDefineQueryVO", description = "流程定义查询请求VO")
public class ProcessDefineQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程类型 1 默认  2自定义")
    private Integer processType;

    @ApiModelProperty(value = "流程名称（模糊查询）")
    private String processName;

    @ApiModelProperty(value = "流程状态 0 草稿  1 启用 2 停用")
    private Integer processStatus;

    @ApiModelProperty(value = "流程创建开始时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "流程创建结束时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "流程条件 1 员工福利 2 综合保障")
    private Integer processCondition;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人名称（模糊查询）")
    private String createName;
} 