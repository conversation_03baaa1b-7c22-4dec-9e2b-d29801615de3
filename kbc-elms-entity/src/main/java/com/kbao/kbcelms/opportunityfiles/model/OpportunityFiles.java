package com.kbao.kbcelms.opportunityfiles.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;


/**
 * 机会文件资料表
 */
@Data
@Document
public class OpportunityFiles
{
  /**
   * 编号
   */
  @Id
  private String id;
  /**
   * 机会id
   */
  private Integer opportunityId;
  /**
   * 文件类型 FileTypeEnum
   */
  private String fileType;
  /**
   * 名称
   */
  private String fileName;

  /**
   * 文件oss地址
   */
  private String filePath;
  /**
   * 保险公司id
   */
  private String companyId;
  /**
   * 保险公司名称
   */
  private String companyName;
  /**
   * 创建人编号 当前用户ID
   */
  private Integer createId;
  /**
   * 创建人姓名
   */
  private String createName;
  /**
   * 创建日期 默认为当前时间
   */
  private Date createTime;

}

