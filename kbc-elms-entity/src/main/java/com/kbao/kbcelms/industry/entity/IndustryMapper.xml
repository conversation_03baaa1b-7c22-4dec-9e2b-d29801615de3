<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.industry.dao.IndustryMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.industry.entity.Industry">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="TINYINT"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="fullPath" column="full_path" jdbcType="VARCHAR"/>
        <result property="fullName" column="full_name" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="TINYINT"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, code, name, level, parent_code, full_path, full_name, is_valid, sort_order, create_time
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.code,
        t.name,
        t.level,
        t.parent_code,
        t.full_path,
        t.full_name,
        t.is_valid,
        t.sort_order,
        t.create_time
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_valid = 1
            <if test="code != null and code != ''">
                and t.code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != ''">
                and t.name like concat('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="level != null">
                and t.level = #{level,jdbcType=TINYINT}
            </if>
            <if test="parentCode != null and parentCode != ''">
                and t.parent_code = #{parentCode,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null">
                and t.is_valid = #{isValid,jdbcType=TINYINT}
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List"/> from t_industry where id = #{id} and is_valid = 1
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_industry t
        <include refid="Base_Condition"/>
        order by t.sort_order asc, t.code asc
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_industry t
        <include refid="Base_Condition"/>
    </select>

    <select id="isExistCode" resultType="java.lang.Integer">
        select count(0) from t_industry 
        where code = #{code,jdbcType=VARCHAR}
        <if test="id != null">
            and id != #{id,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select <include refid="Base_Column_List"/> from t_industry 
        where code = #{code,jdbcType=VARCHAR} and is_valid = 1
    </select>

    <select id="selectByParentCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select <include refid="Base_Column_List"/> from t_industry 
        where parent_code = #{parentCode,jdbcType=VARCHAR} and is_valid = 1
        order by sort_order asc, code asc
    </select>

    <select id="selectByLevel" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List"/> from t_industry 
        where level = #{level,jdbcType=TINYINT} and is_valid = 1
        order by sort_order asc, code asc
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.industry.entity.Industry" useGeneratedKeys="true" keyProperty="id">
        insert into t_industry (
            code, name, level, parent_code, full_path, full_name, is_valid, sort_order, create_time
        ) values (
            #{code}, #{name}, #{level}, #{parentCode}, #{fullPath}, #{fullName}, #{isValid}, #{sortOrder}, #{createTime}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.industry.entity.Industry" useGeneratedKeys="true" keyProperty="id">
        insert into t_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="level != null">level,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="fullPath != null">full_path,</if>
            <if test="fullName != null">full_name,</if>
            <if test="isValid != null">is_valid,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="level != null">#{level},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="fullPath != null">#{fullPath},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="isValid != null">#{isValid},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.industry.entity.Industry">
        update t_industry set
            code = #{code},
            name = #{name},
            level = #{level},
            parent_code = #{parentCode},
            full_path = #{fullPath},
            full_name = #{fullName},
            is_valid = #{isValid},
            sort_order = #{sortOrder}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.industry.entity.Industry">
        update t_industry
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="fullPath != null">full_path = #{fullPath},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_industry set is_valid = 0 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_industry (
            code, name, level, parent_code, full_path, full_name, is_valid, sort_order, create_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.code}, #{item.name}, #{item.level}, #{item.parentCode}, #{item.fullPath}, 
                #{item.fullName}, #{item.isValid}, #{item.sortOrder}, #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update t_industry set
                name = #{item.name},
                level = #{item.level},
                parent_code = #{item.parentCode},
                full_path = #{item.fullPath},
                full_name = #{item.fullName},
                is_valid = #{item.isValid},
                sort_order = #{item.sortOrder}
            where code = #{item.code}
        </foreach>
    </update>

    <delete id="truncateTable">
        truncate table t_industry
    </delete>

</mapper>
