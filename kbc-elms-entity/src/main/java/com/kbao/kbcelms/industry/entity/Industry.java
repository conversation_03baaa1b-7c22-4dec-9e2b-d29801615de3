package com.kbao.kbcelms.industry.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Description 行业分类表实体
 * @Date 2025-07-30
 */
@Data
public class Industry implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键ID
     */
    private Integer id;

    /**
     * 对应表中code
     * 行业代码
     */
    private String code;

    /**
     * 对应表中name
     * 行业名称
     */
    private String name;

    /**
     * 对应表中level
     * 行业级别(1-门类,2-大类,3-中类,4-小类)
     */
    private Integer level;

    /**
     * 对应表中parent_code
     * 父级行业代码
     */
    private String parentCode;

    /**
     * 对应表中full_path
     * 完整路径(如A/01/011/0111)
     */
    private String fullPath;

    /**
     * 对应表中full_name
     * 完整名称(如农/农业/谷物种植/稻谷种植)
     */
    private String fullName;

    /**
     * 对应表中is_valid
     * 是否有效(0-无效,1-有效)
     */
    private Integer isValid;

    /**
     * 对应表中sort_order
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
