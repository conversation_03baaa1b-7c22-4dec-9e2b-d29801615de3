<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.bascode.dao.BasCodeMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.bascode.entity.BasCode">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="code" column="code" jdbcType="VARCHAR" />
        <result property="codeType" column="code_type" jdbcType="VARCHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR" />
        <result property="pinyinCode" column="pinyin_code" jdbcType="VARCHAR" />
        <result property="enable" column="enable" jdbcType="VARCHAR" />
        <result property="sort" column="sort" jdbcType="VARCHAR" />
        <result property="remark" column="remark" jdbcType="VARCHAR" />
        <result property="postCode" column="post_code" jdbcType="VARCHAR" />
        <result property="companyId" column="company_id" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        id, code, code_type, name, parent_code, pinyin_code, enable, sort, remark, post_code, company_id
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.code,
        t.code_type,
        t.name,
        t.parent_code,
        t.pinyin_code,
        t.enable,
        t.sort,
        t.remark,
        t.post_code,
        t.company_id
    </sql>

    <sql id="Base_Condition">
        <where>
            <if test="code != null">
                and t.code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="codeType != null">
                and t.code_type = #{codeType,jdbcType=VARCHAR}
            </if>
            <if test="name != null">
                and t.name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="parentCode != null">
                and t.parent_code = #{parentCode,jdbcType=VARCHAR}
            </if>
            <if test="pinyinCode != null">
                and t.pinyin_code = #{pinyinCode,jdbcType=VARCHAR}
            </if>
            <if test="enable != null">
                and t.enable = #{enable,jdbcType=VARCHAR}
            </if>
            <if test="sort != null">
                and t.sort = #{sort,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and t.remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="postCode != null">
                and t.post_code = #{postCode,jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                and t.company_id = #{companyId,jdbcType=VARCHAR}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_bas_code where id = #{id}
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_bas_code t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_bas_code t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.bascode.entity.BasCode" useGeneratedKeys="true" keyProperty="id">
        insert into t_bas_code (
            code, code_type, name, parent_code, pinyin_code, enable, sort, remark, post_code, company_id
        ) values (
            #{code}, #{codeType}, #{name}, #{parentCode}, #{pinyinCode}, #{enable}, #{sort}, #{remark}, #{postCode}, #{companyId}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.bascode.entity.BasCode" useGeneratedKeys="true" keyProperty="id">
        insert into t_bas_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="codeType != null">code_type,</if>
            <if test="name != null">name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="pinyinCode != null">pinyin_code,</if>
            <if test="enable != null">enable,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="postCode != null">post_code,</if>
            <if test="companyId != null">company_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="codeType != null">#{codeType},</if>
            <if test="name != null">#{name},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="pinyinCode != null">#{pinyinCode},</if>
            <if test="enable != null">#{enable},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="postCode != null">#{postCode},</if>
            <if test="companyId != null">#{companyId},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.bascode.entity.BasCode">
        update t_bas_code set
            code = #{code},
            code_type = #{codeType},
            name = #{name},
            parent_code = #{parentCode},
            pinyin_code = #{pinyinCode},
            enable = #{enable},
            sort = #{sort},
            remark = #{remark},
            post_code = #{postCode},
            company_id = #{companyId}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.bascode.entity.BasCode">
        update t_bas_code
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="codeType != null">code_type = #{codeType},</if>
            <if test="name != null">name = #{name},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="pinyinCode != null">pinyin_code = #{pinyinCode},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="postCode != null">post_code = #{postCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_bas_code where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_bas_code (
            code, code_type, name, parent_code, pinyin_code, enable, sort, remark, post_code, company_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.code}, #{item.codeType}, #{item.name}, #{item.parentCode}, #{item.pinyinCode}, #{item.enable}, #{item.sort}, #{item.remark}, #{item.postCode}, #{item.companyId}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from t_bas_code where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 