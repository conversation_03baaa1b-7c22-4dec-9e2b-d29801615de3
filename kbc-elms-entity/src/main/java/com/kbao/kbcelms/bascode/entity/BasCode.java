package com.kbao.kbcelms.bascode.entity;

/**
 * 基础代码表
 */
public class BasCode {
    /** 主键 */
    private Integer id;
    /** 编码 */
    private String code;
    /** 状态 */
    private String codeType;
    /** 名称 */
    private String name;
    /** 父级编码 */
    private String parentCode;
    /** PINYIN */
    private String pinyinCode;
    /** 是否可用 */
    private String enable;
    /** 排序 */
    private String sort;
    /** 注释 */
    private String remark;
    /** 岗位编码 */
    private String postCode;
    /** 保司ID */
    private String companyId;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }
    public String getCodeType() { return codeType; }
    public void setCodeType(String codeType) { this.codeType = codeType; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getParentCode() { return parentCode; }
    public void setParentCode(String parentCode) { this.parentCode = parentCode; }
    public String getPinyinCode() { return pinyinCode; }
    public void setPinyinCode(String pinyinCode) { this.pinyinCode = pinyinCode; }
    public String getEnable() { return enable; }
    public void setEnable(String enable) { this.enable = enable; }
    public String getSort() { return sort; }
    public void setSort(String sort) { this.sort = sort; }
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    public String getPostCode() { return postCode; }
    public void setPostCode(String postCode) { this.postCode = postCode; }
    public String getCompanyId() { return companyId; }
    public void setCompanyId(String companyId) { this.companyId = companyId; }
} 