package com.kbao.kbcelms.role.entity;

import java.util.Date;

/**
 * 角色表
 */
public class Role {
    /** 编号 */
    private Integer id;
    /** 角色名称 */
    private String roleName;
    /** 归属租户Id */
    private String tenantId;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
    /** 排序 */
    private Integer sort;
    /** 角色类型：1分公司统筹、2总公司统筹、3分公司项目经理、4总公司项目经理、99其他 */
    private Integer roleType;
    /** 角色说明 */
    private String roleDesc;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
    public Integer getSort() { return sort; }
    public void setSort(Integer sort) { this.sort = sort; }
    public Integer getRoleType() { return roleType; }
    public void setRoleType(Integer roleType) { this.roleType = roleType; }

    public String getRoleDesc() {
        return roleDesc;
    }

    public void setRoleDesc(String roleDesc) {
        this.roleDesc = roleDesc;
    }
}