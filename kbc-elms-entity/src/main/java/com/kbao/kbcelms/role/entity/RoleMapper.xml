<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.role.dao.RoleMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.role.entity.Role">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="roleName" column="role_name" jdbcType="VARCHAR" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="sort" column="sort" jdbcType="INTEGER" />
        <result property="roleType" column="role_type" jdbcType="INTEGER" />
        <result property="roleDesc" column="role_desc" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        id, role_name, tenant_id, create_id, create_time, update_id, update_time, is_deleted, sort, role_type, role_desc
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.role_name,
        t.tenant_id,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.sort,
        t.role_type,
        t.role_desc
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="roleName != null and roleName !=''">
                <![CDATA[ and t.role_name like concat('%',#{roleName,jdbcType=VARCHAR},'%') ]]>
            </if>
            <if test="tenantId != null and tenantId !=''">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sort != null">
                and t.sort = #{sort,jdbcType=INTEGER}
            </if>
            <if test="roleType != null">
                and t.role_type = #{roleType,jdbcType=INTEGER}
            </if>
            <if test="roleDesc != null">
                and t.role_desc = #{roleDesc,jdbcType=VARCHAR}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_role where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_role t
        <include refid="Base_Condition"/>
        order by t.update_time desc
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_role t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.role.entity.Role" useGeneratedKeys="true" keyProperty="id">
        insert into t_role (
            role_name, tenant_id, create_id, create_time, update_id, update_time, is_deleted, sort, role_type, role_desc
        ) values (
            #{roleName}, #{tenantId}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{sort}, #{roleType}, #{roleDesc}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.role.entity.Role" useGeneratedKeys="true" keyProperty="id">
        insert into t_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleName != null">role_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sort != null">sort,</if>
            <if test="roleType != null">role_type,</if>
            <if test="roleDesc != null">role_desc,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleName != null">#{roleName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sort != null">#{sort},</if>
            <if test="roleType != null">#{roleType},</if>
            <if test="roleDesc != null">#{roleDesc},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.role.entity.Role">
        update t_role set
            role_name = #{roleName},
            tenant_id = #{tenantId},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            sort = #{sort},
            role_type = #{roleType},
            role_desc = #{roleDesc}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.role.entity.Role">
        update t_role
        <set>
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="roleType != null">role_type = #{roleType},</if>
            <if test="roleDesc != null">role_desc = #{roleDesc},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_role set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_role (
            role_name, tenant_id, create_id, create_time, update_id, update_time, is_deleted, sort, role_type, role_desc
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.roleName}, #{item.tenantId}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.sort}, #{item.roleType}, #{item.roleDesc}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_role set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByRoleName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from t_role t
        where t.role_name = #{roleName,jdbcType=VARCHAR} and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <select id="selectRolesByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from t_role t
        inner join t_user_role ur on t.id = ur.role_id
        where ur.user_id = #{userId,jdbcType=VARCHAR}
        and t.is_deleted = 0
        and ur.is_deleted = 0
        order by t.sort asc, t.create_time desc
    </select>

</mapper>