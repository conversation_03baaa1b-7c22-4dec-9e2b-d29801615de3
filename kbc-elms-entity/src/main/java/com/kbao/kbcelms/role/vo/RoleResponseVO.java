package com.kbao.kbcelms.role.vo;

import com.kbao.kbcelms.user.vo.UserRoleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 13:47
 */
@Data
public class RoleResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "角色ID")
    private Integer id;

    @ApiModelProperty(name = "角色名称")
    private String roleName;

    @ApiModelProperty(name = "创建人编号")
    private String createId;

    @ApiModelProperty(name = "创建时间")
    private Date createTime;

    @ApiModelProperty(name = "角色类型：1 统筹 2 项目经理 3 其他")
    private Integer roleType;

    @ApiModelProperty(name = "角色说明")
    private String roleDesc;

    @ApiModelProperty(name = "用户角色管理表ID")
    private Integer userRoleId;

    @ApiModelProperty(name = "用户角色集合")
    private List<UserRoleVO> userRoles;
}
