<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="VARCHAR" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="companyCode" column="company_code" jdbcType="VARCHAR" />
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR" />
        <result property="orderCode" column="order_code" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, tenant_id, company_code, policy_no, order_code, create_id, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.tenant_id,
        t.company_code,
        t.policy_no,
        t.order_code,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="companyCode != null">
                and t.company_code = #{companyCode,jdbcType=VARCHAR}
            </if>
            <if test="policyNo != null">
                and t.policy_no = #{policyNo,jdbcType=VARCHAR}
            </if>
            <if test="orderCode != null">
                and t.order_code = #{orderCode,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_order where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_order t
        <include refid="Base_Condition"/>
        order by t.update_time desc
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_order t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_order (
            opportunity_id, tenant_id, company_code, policy_no, order_code, create_id, create_time, update_id, update_time, is_deleted
        ) values (
            #{opportunityId}, #{tenantId}, #{companyCode}, #{policyNo}, #{orderCode}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="policyNo != null">policy_no,</if>
            <if test="orderCode != null">order_code,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="policyNo != null">#{policyNo},</if>
            <if test="orderCode != null">#{orderCode},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder">
        update t_opportunity_order set
            opportunity_id = #{opportunityId},
            tenant_id = #{tenantId},
            company_code = #{companyCode},
            policy_no = #{policyNo},
            order_code = #{orderCode},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder">
        update t_opportunity_order
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="policyNo != null">policy_no = #{policyNo},</if>
            <if test="orderCode != null">order_code = #{orderCode},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_order set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_order (
        opportunity_id, tenant_id, company_code, order_code, policy_no, create_id, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.opportunityId}, #{item.tenantId}, #{item.companyCode}, #{item.policyNo}, #{item.orderCode}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_order set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 