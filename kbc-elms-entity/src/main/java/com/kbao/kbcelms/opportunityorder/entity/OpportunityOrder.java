package com.kbao.kbcelms.opportunityorder.entity;

import java.util.Date;
import javax.validation.constraints.Size;

/**
 * 用户表（机会订单）
 */
public class OpportunityOrder {
    /** 编号 */
    private Integer id;
    /** 机会id */
    private String opportunityId;
    /** 租户id */
    private String tenantId;
    /** 公司编码 */
    @Size(max = 16, message = "companyCode长度不能超过16个字符")
    private String companyCode;
    /** 保单号 */  // 新增字段注释
    @Size(max = 16, message = "policyNo长度不能超过16个字符")
    private String policyNo;
    /** 订单编码 */
    @Size(max = 16, message = "orderCode长度不能超过16个字符")
    private String orderCode;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getOpportunityId() { return opportunityId; }
    public void setOpportunityId(String opportunityId) { this.opportunityId = opportunityId; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getCompanyCode() { return companyCode; }
    public void setCompanyCode(String companyCode) { this.companyCode = companyCode; }
    public String getOrderCode() { return orderCode; }
    public void setOrderCode(String orderCode) { this.orderCode = orderCode; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    // 新增保单号的 getter 方法
    public String getPolicyNo() {
        return policyNo;
    }

    // 新增保单号的 setter 方法
    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
} 