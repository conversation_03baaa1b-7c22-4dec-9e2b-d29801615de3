package com.kbao.kbcelms.userorg.entity;
import org.springframework.format.annotation.DateTimeFormat; 
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;  
/**  
 * 对应表 t_user_org
 * 用户机构权限表  
 *  
 */ 
  
public class UserOrg implements Serializable{
	private static final long serialVersionUID = 1L;
 
	
    /**
     * 对应表中id
     * 
     */  
	private Integer id;
	
    /**
     * 对应表中user_id
     * 云服用户ID
     */  
	private String userId;
	
    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;
	
    /**
     * 对应表中organ_code
     * 机构编码
     */  
	private String organCode;
	
    /**
     * 对应表中organ_name
     * 机构名称
     */  
	private String organName;
	
    /**
     * 对应表中org_type
     * 机构类型 全国机构 org ，总公司 com
     */  
	private String orgType;
	
    /**
     * 对应表中create_id
     * 创建人ID
     */  
	private String createId;
	
    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
    /**
     * 对应表中update_id
     * 修改人ID
     */  
	private String updateId;
	
    /**
     * 对应表中update_time
     * 修改时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
    /**
     * 对应表中is_deleted
     * 是否删除0否 1是
     */  
	private Integer isDeleted;
	
    public UserOrg(){
    } 
    public Integer getId(){    
    	return id;    
    }    
    public void setId(Integer id){    
    	this.id = id;    
    }    
    public String getUserId(){    
    	return userId;    
    }    
    public void setUserId(String userId){    
    	this.userId = userId;    
    }    
    public String getTenantId(){    
    	return tenantId;    
    }    
    public void setTenantId(String tenantId){    
    	this.tenantId = tenantId;    
    }    
    public String getOrganCode(){    
    	return organCode;    
    }    
    public void setOrganCode(String organCode){    
    	this.organCode = organCode;    
    }    
    public String getOrganName(){    
    	return organName;    
    }    
    public void setOrganName(String organName){    
    	this.organName = organName;    
    }    
    public String getOrgType(){    
    	return orgType;    
    }    
    public void setOrgType(String orgType){    
    	this.orgType = orgType;    
    }    
    public String getCreateId(){    
    	return createId;    
    }    
    public void setCreateId(String createId){    
    	this.createId = createId;    
    }    
    public Date getCreateTime(){    
    	return createTime;    
    }    
    public void setCreateTime(Date createTime){    
    	this.createTime = createTime;    
    }    
    public String getUpdateId(){    
    	return updateId;    
    }    
    public void setUpdateId(String updateId){    
    	this.updateId = updateId;    
    }    
    public Date getUpdateTime(){    
    	return updateTime;    
    }    
    public void setUpdateTime(Date updateTime){    
    	this.updateTime = updateTime;    
    }    
    public Integer getIsDeleted(){    
    	return isDeleted;    
    }    
    public void setIsDeleted(Integer isDeleted){    
    	this.isDeleted = isDeleted;    
    }    
}   