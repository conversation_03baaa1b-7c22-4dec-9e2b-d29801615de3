<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.userorg.dao.UserOrgMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcelms.userorg.entity.UserOrg">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="userId" jdbcType="VARCHAR"  column="user_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="organCode" jdbcType="VARCHAR"  column="organ_code" />  
        <result property="organName" jdbcType="VARCHAR"  column="organ_name" />  
        <result property="orgType" jdbcType="VARCHAR"  column="org_type" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		user_id,  
		tenant_id,  
		organ_code,  
		organ_name,  
		org_type,  
		create_id,  
		create_time,  
		update_id,  
		update_time,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.user_id, 
		t.tenant_id, 
		t.organ_code, 
		t.organ_name, 
		t.org_type, 
		t.create_id, 
		t.create_time, 
		t.update_id, 
		t.update_time, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="userId != null"> 
	   		and t.user_id = #{userId,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null"> 
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="organCode != null"> 
	   		and t.organ_code = #{organCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="organName != null"> 
	   		and t.organ_name = #{organName,jdbcType=VARCHAR}  
	    </if>
	    <if test="orgType != null"> 
	   		and t.org_type = #{orgType,jdbcType=VARCHAR}  
	    </if>
	    <if test="createId != null"> 
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null"> 
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null"> 
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null"> 
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="isDeleted != null"> 
	   		and t.is_deleted = #{isDeleted,jdbcType=INTEGER}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_user_org t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_user_org t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_user_org
		where  is_deleted = 0 and id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_user_org set is_deleted = 1
		where id = #{id,jdbcType=INTEGER} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcelms.userorg.entity.UserOrg">
		insert into t_user_org(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{userId,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{organCode,jdbcType=VARCHAR}, 
                 
                #{organName,jdbcType=VARCHAR}, 
                 
                #{orgType,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{isDeleted,jdbcType=INTEGER} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcelms.userorg.entity.UserOrg">
		insert into t_user_org
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="userId != null ">  
	       		user_id,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="organCode != null ">  
	       		organ_code,
	        </if>  
	        <if test="organName != null ">  
	       		organ_name,
	        </if>  
	        <if test="orgType != null ">  
	       		org_type,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="userId != null">  
            	#{userId,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="organCode != null">  
            	#{organCode,jdbcType=VARCHAR},
            </if>  
            <if test="organName != null">  
            	#{organName,jdbcType=VARCHAR},
            </if>  
            <if test="orgType != null">  
            	#{orgType,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=INTEGER},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.userorg.entity.UserOrg">
		update t_user_org
		<set>
	        <if test="userId != null ">  
	        	user_id = #{userId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="organCode != null ">  
	        	organ_code = #{organCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="organName != null ">  
	        	organ_name = #{organName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="orgType != null ">  
	        	org_type = #{orgType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=INTEGER},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.userorg.entity.UserOrg">
		update t_user_org
		set
           user_id = #{userId,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           organ_code = #{organCode,jdbcType=VARCHAR},
           organ_name = #{organName,jdbcType=VARCHAR},
           org_type = #{orgType,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_user_org(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.userId != null">,#{item.userId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.organCode != null">,#{item.organCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.organName != null">,#{item.organName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.orgType != null">,#{item.orgType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_user_org(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.userId != null">,#{item.userId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.organCode != null">,#{item.organCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.organName != null">,#{item.organName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.orgType != null">,#{item.orgType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   user_id=values(user_id), 
		   tenant_id=values(tenant_id), 
		   organ_code=values(organ_code), 
		   organ_name=values(organ_name), 
		   org_type=values(org_type), 
		   create_id=values(create_id), 
		   create_time=values(create_time), 
		   update_id=values(update_id), 
		   update_time=values(update_time), 
		   is_deleted=values(is_deleted) 
	</update>
	
	<!-- 批量删除-->
	<update id="batchDelete" parameterType="java.util.List">
	update t_user_org set is_deleted = 1 where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	and is_deleted = 0
	</update>
	
	<!-- 自定义查询 -->
	<select id="findUserOrgByCondition" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Alias_Column_List"/>
		from t_user_org t
		where t.user_id =#{userId,jdbcType=VARCHAR}
		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
		and t.organ_code = #{organCode,jdbcType=VARCHAR}
	</select>

	<update id="deleteByUserId" parameterType="java.lang.String">
		update t_user_org set is_deleted = 1 where user_id = #{userId,jdbcType=VARCHAR} and tenant_id = #{tenantId,jdbcType=VARCHAR}
	</update>

</mapper>
