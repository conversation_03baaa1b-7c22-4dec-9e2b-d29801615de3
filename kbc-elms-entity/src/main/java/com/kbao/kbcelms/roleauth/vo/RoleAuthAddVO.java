package com.kbao.kbcelms.roleauth.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 10:08
 */
@Data
public class RoleAuthAddVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(name = "角色ID")
    private Integer roleId;

    @ApiModelProperty(name = "角色名称")
    private String roleName;

    @ApiModelProperty(name = "权限位集合")
    private List<RoleAuthRequestVO> auths;

}
