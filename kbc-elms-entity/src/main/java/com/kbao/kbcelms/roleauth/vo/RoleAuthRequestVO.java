package com.kbao.kbcelms.roleauth.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/23 10:16
 */
@Data
public class RoleAuthRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "角色权限位")
    private String authCode;

    @ApiModelProperty(name = "权限位路径 1/2/3")
    private String authPath;
}
