<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.roleauth.dao.RoleAuthMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.roleauth.entity.RoleAuth">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="roleId" column="role_id" jdbcType="INTEGER" />
        <result property="roleName" column="role_name" jdbcType="VARCHAR" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="authCode" column="auth_code" jdbcType="VARCHAR" />
        <result property="authPath" column="auth_path" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="sort" column="sort" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, role_id, role_name, tenant_id, auth_code, auth_path, create_id, create_time, update_id, update_time, is_deleted, sort
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.role_id,
        t.role_name,
        t.tenant_id,
        t.auth_code,
        t.auth_path,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.sort
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="roleId != null">
                and t.role_id = #{roleId,jdbcType=VARCHAR}
            </if>
            <if test="roleName != null">
                and t.role_name = #{roleName,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="authCode != null">
                and t.auth_code = #{authCode,jdbcType=VARCHAR}
            </if>
            <if test="authPath != null">
                and t.auth_path = #{authPath,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sort != null">
                and t.sort = #{sort,jdbcType=INTEGER}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_role_auth where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_role_auth t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_role_auth t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.roleauth.entity.RoleAuth" useGeneratedKeys="true" keyProperty="id">
        insert into t_role_auth (
            role_id, role_name, tenant_id, auth_code, auth_path, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values (
            #{roleId}, #{roleName}, #{tenantId}, #{authCode}, #{authPath}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{sort}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.roleauth.entity.RoleAuth" useGeneratedKeys="true" keyProperty="id">
        insert into t_role_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="roleName != null">role_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="authCode != null">auth_code,</if>
            <if test="authPath != null">auth_path,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sort != null">sort,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="roleName != null">#{roleName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="authCode != null">#{authCode},</if>
            <if test="authPath != null">#{authPath},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sort != null">#{sort},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.roleauth.entity.RoleAuth">
        update t_role_auth set
            role_id = #{roleId},
            role_name = #{roleName},
            tenant_id = #{tenantId},
            auth_code = #{authCode},
            auth_path = #{authPath},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            sort = #{sort}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.roleauth.entity.RoleAuth">
        update t_role_auth
        <set>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="authCode != null">auth_code = #{authCode},</if>
            <if test="authPath != null">auth_path = #{authPath},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="sort != null">sort = #{sort},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_role_auth set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_role_auth (
            role_id, role_name, tenant_id, auth_code, auth_path, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.roleId}, #{item.roleName}, #{item.tenantId}, #{item.authCode}, #{item.authPath}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.sort}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_role_auth set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deleteByRoleId" parameterType="java.lang.Integer">
        update t_role_auth set is_deleted = 1 where role_id = #{roleId}
    </update>

</mapper> 