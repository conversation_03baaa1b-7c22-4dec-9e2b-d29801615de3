package com.kbao.kbcelms.roleauth.entity;

import java.util.Date;

/**
 * 角色功能权限
 */
public class RoleAuth {
    /** 主键 */
    private Integer id;
    /** 编号 */
    private Integer roleId;
    /** 角色名称 */
    private String roleName;
    /** 归属租户Id */
    private String tenantId;
    /** 功能ID */
    private String authCode;
    /** 权限位路径 1/2/3 */
    private String authPath;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
    /** 排序 */
    private Integer sort;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getRoleId() { return roleId; }
    public void setRoleId(Integer roleId) { this.roleId = roleId; }
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getAuthCode() { return authCode; }
    public void setAuthCode(String authCode) { this.authCode = authCode; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
    public Integer getSort() { return sort; }
    public void setSort(Integer sort) { this.sort = sort; }

    public String getAuthPath() {
        return authPath;
    }

    public void setAuthPath(String authPath) {
        this.authPath = authPath;
    }
}