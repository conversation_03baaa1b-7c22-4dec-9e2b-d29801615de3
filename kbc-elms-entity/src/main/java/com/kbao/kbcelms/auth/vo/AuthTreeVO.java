package com.kbao.kbcelms.auth.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 15:15
 */
@Data
public class AuthTreeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "主键")
    private Integer id;

    @ApiModelProperty(name = "权限编码")
    private String authCode;

    @ApiModelProperty(name = "权限名称")
    private String authName;

    @ApiModelProperty(name = "父级权限编码")
    private String parentCode;

    @ApiModelProperty(name = "排序")
    private Integer sort;

    @ApiModelProperty(name = "是否选中")
    private Boolean isSelected;

    private List<AuthTreeVO> children;

}

