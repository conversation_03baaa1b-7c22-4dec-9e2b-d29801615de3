package com.kbao.kbcelms.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 功能权限位
 */
@ApiModel(value = "Auth", description = "功能权限位")
public class Auth {
    @ApiModelProperty(value = "功能编号")
    private Integer id;
    @ApiModelProperty(value = "权限编号")
    private String authCode;
    @ApiModelProperty(value = "权限位名称")
    private String authName;
    @ApiModelProperty(value = "权限路由")
    private String parentCode;
    @ApiModelProperty(value = "权限说明")
    private String authDesc;
    @ApiModelProperty(value = "创建人编号")
    private String createId;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新人编号")
    private String updateId;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "是否删除 0 未删除  1已删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "排序")
    private Integer sort;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getAuthCode() { return authCode; }
    public void setAuthCode(String authCode) { this.authCode = authCode; }
    public String getAuthName() { return authName; }
    public void setAuthName(String authName) { this.authName = authName; }
    public String getParentCode() { return parentCode; }
    public void setParentCode(String parentCode) { this.parentCode = parentCode; }
    public String getAuthDesc() { return authDesc; }
    public void setAuthDesc(String authDesc) { this.authDesc = authDesc; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
    public Integer getSort() { return sort; }
    public void setSort(Integer sort) { this.sort = sort; }
} 