package com.kbao.kbcelms.dataTemplate.model;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.annotation.Excel;import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import java.io.Serializable;
import java.util.Date;

@Data
@Document(collection = "DataTemplateField")
public class DataTemplateField implements Serializable {
    @Id
    private String id; // MongoDB主键
    @Excel(name = "模板ID")
    private Integer templateId; // 模板ID
    @Excel(name = "字段名称")
    private String fieldName; // 字段名称
    @Excel(name = "字段编码")
    private String fieldCode; // 字段编码
    @Excel(name = "字段类型")
    private String fieldType; // 字段类型
    @Excel(name = "字段长度")
    private String fieldLength; // 字段长度
    @Excel(name = "是否索引")
    private String isIndex; // 是否索引
    @Excel(name = "展示类型")
    private String showType; // 展示类型
    @Excel(name = "是否必填")
    private String required; // 是否必填
    @Excel(name = "默认值")
    private String defaultValue; // 默认值
    @Excel(name = "校验规则")
    private String validation; // 校验规则
    @Excel(name = "是否可修改")
    private String change; // 是否可修改
    private JSONObject additional; // 附加信息
    @Excel(name = "描述")
    private String remark; // 描述
    private Integer sort;
    private Date createTime;

    @Transient
    private String fieldTypeStr;
    @Transient
    @Excel(name= "附加信息")
    private String additionalStr;
} 