package com.kbao.kbcelms.dataTemplate.entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-07-22
*/
@Data
public class DataTemplate implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中template_name
     * 模板名称
     */  
	private String templateName;

    /**
     * 对应表中biz_code
     * 模板编号
     */  
	private String bizCode;

    /**
     * 对应表中type
     * 分类信息
     */  
	private String type;

    /**
     * 对应表中status
     * 状态：0-禁用，1-启用
     */  
	private String status;

    /**
     * 对应表中remark
     * 描述
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除 0-未删除 1-已删除
     */  
	private Integer isDeleted;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

	List<DataTemplateField> fields;

}   