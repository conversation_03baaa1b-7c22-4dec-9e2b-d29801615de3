package com.kbao.kbcelms.userrole.entity;

import java.util.Date;

/**
 * 用户表（用户角色关系）
 */
public class UserRole {
    /** 编号 */
    private Integer id;
    /** 用户ID */
    private String userId;
    /** 角色ID */
    private Integer roleId;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
    /** 排序 */
    private Integer sort;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public Integer getRoleId() { return roleId; }
    public void setRoleId(Integer roleId) { this.roleId = roleId; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
    public Integer getSort() { return sort; }
    public void setSort(Integer sort) { this.sort = sort; }
} 