<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.userrole.dao.UserRoleMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.userrole.entity.UserRole">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="userId" column="user_id" jdbcType="VARCHAR" />
        <result property="roleId" column="role_id" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="sort" column="sort" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, role_id, create_id, create_time, update_id, update_time, is_deleted, sort
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.user_id,
        t.role_id,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.sort
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="userId != null">
                and t.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="roleId != null">
                and t.role_id = #{roleId,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sort != null">
                and t.sort = #{sort,jdbcType=INTEGER}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_user_role where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user_role t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_user_role t
        <include refid="Base_Condition"/>
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Alias_Column_List"/>
        from t_user_role t
        where t.user_id = #{userId,jdbcType=VARCHAR}
        and t.is_deleted = 0
    </select>

    <select id="selectRoleIdsByUserId" resultType="java.lang.String" parameterType="java.lang.String">
        select t.role_id
        from t_user_role t
        where t.user_id = #{userId,jdbcType=VARCHAR}
        and t.is_deleted = 0
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.userrole.entity.UserRole" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_role (
            user_id, role_id, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values (
            #{userId}, #{roleId}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{sort}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.userrole.entity.UserRole" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sort != null">sort,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sort != null">#{sort},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.userrole.entity.UserRole">
        update t_user_role set
            user_id = #{userId},
            role_id = #{roleId},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            sort = #{sort}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.userrole.entity.UserRole">
        update t_user_role
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="sort != null">sort = #{sort},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_user_role set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_user_role (
            user_id, role_id, create_id, create_time, update_id, update_time, is_deleted, sort
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.roleId}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.sort}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_user_role set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findByUserIdAndRoleId" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user_role t
        where
            t.user_id = #{userId,jdbcType=VARCHAR}
            and t.role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <update id="deleteByUserIdAndRoleId" parameterType="com.kbao.kbcelms.userrole.entity.UserRole">
        update t_user_role
        set update_id   = #{updateId},
            update_time = #{updateTime},
            is_deleted  = 1
        where user_id = #{userId}
          and role_id = #{roleId}
    </update>

</mapper>