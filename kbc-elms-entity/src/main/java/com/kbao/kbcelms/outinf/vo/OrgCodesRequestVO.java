package com.kbao.kbcelms.outinf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 组织机构编码请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "OrgCodesRequestVO", description = "组织机构编码请求VO")
public class OrgCodesRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织机构编码列表", required = true)
    private List<String> orgCodes;
} 