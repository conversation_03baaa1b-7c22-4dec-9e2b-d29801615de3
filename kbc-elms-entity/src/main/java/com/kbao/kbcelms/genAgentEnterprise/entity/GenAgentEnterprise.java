package com.kbao.kbcelms.genAgentEnterprise.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Description 顾问企业表实体
 * @Date 2025-07-31
 */
@Data
public class GenAgentEnterprise implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键
     */
    private Integer id;

    /**
     * 对应表中name
     * 企业名称
     */
    private String name;

    /**
     * 对应表中credit_code
     * 社会信用代码
     */
    private String creditCode;

    /**
     * 对应表中dt_type
     * 企业类型：A-央企，B-上市公司，C-大型企业，D-中小企业
     */
    private String dtType;

    /**
     * 对应表中enterprise_scale
     * 企业规模
     */
    private String enterpriseScale;

    /**
     * 对应表中staff_scale
     * 人员规模
     */
    private String staffScale;

    /**
     * 对应表中city
     * 所在城市
     */
    private String city;

    /**
     * 对应表中district_code
     * 行政区划代码
     */
    private String districtCode;

    /**
     * 对应表中annual_income
     * 年收入规模
     */
    private String annualIncome;

    /**
     * 对应表中category_code
     * 行业分类代码
     */
    private String categoryCode;

    /**
     * 对应表中category_name
     * 行业分类名称
     */
    private String categoryName;

    /**
     * 对应表中is_verified
     * 是否验真：0-未验真，1-已验真
     */
    private String isVerified;

    /**
     * 对应表中enterprise_contacter
     * 企业联系人
     */
    private String enterpriseContacter;

    /**
     * 对应表中contacter_phone
     * 联系人电话
     */
    private String contacterPhone;

    /**
     * 对应表中dt_remark
     * 备注
     */
    private String dtRemark;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */
    private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */
    private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 对应表中tenant_id
     * 租户ID
     */
    private String tenantId;
}
