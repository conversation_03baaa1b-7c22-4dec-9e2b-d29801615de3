<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result property="name" jdbcType="VARCHAR" column="name"/>
        <result property="creditCode" jdbcType="VARCHAR" column="credit_code"/>
        <result property="dtType" jdbcType="CHAR" column="dt_type"/>
        <result property="enterpriseScale" jdbcType="VARCHAR" column="enterprise_scale"/>
        <result property="staffScale" jdbcType="VARCHAR" column="staff_scale"/>
        <result property="city" jdbcType="VARCHAR" column="city"/>
        <result property="districtCode" jdbcType="VARCHAR" column="district_code"/>
        <result property="annualIncome" jdbcType="VARCHAR" column="annual_income"/>
        <result property="categoryCode" jdbcType="VARCHAR" column="category_code"/>
        <result property="categoryName" jdbcType="VARCHAR" column="category_name"/>
        <result property="isVerified" jdbcType="CHAR" column="is_verified"/>
        <result property="enterpriseContacter" jdbcType="VARCHAR" column="enterprise_contacter"/>
        <result property="contacterPhone" jdbcType="VARCHAR" column="contacter_phone"/>
        <result property="dtRemark" jdbcType="VARCHAR" column="dt_remark"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="createId" jdbcType="VARCHAR" column="create_id"/>
        <result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
        <result property="updateId" jdbcType="VARCHAR" column="update_id"/>
        <result property="isDeleted" jdbcType="TINYINT" column="is_deleted"/>
        <result property="tenantId" jdbcType="VARCHAR" column="tenant_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        name,
        credit_code,
        dt_type,
        enterprise_scale,
        staff_scale,
        city,
        district_code,
        annual_income,
        category_code,
        category_name,
        is_verified,
        enterprise_contacter,
        contacter_phone,
        dt_remark,
        create_time,
        create_id,
        update_time,
        update_id,
        is_deleted,
        tenant_id
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.name,
        t.credit_code,
        t.dt_type,
        t.enterprise_scale,
        t.staff_scale,
        t.city,
        t.district_code,
        t.annual_income,
        t.category_code,
        t.category_name,
        t.is_verified,
        t.enterprise_contacter,
        t.contacter_phone,
        t.dt_remark,
        t.create_time,
        t.create_id,
        t.update_time,
        t.update_id,
        t.is_deleted,
        t.tenant_id
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="name != null and name != ''">
                and t.name like concat('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="creditCode != null and creditCode != ''">
                and t.credit_code = #{creditCode,jdbcType=VARCHAR}
            </if>
            <if test="dtType != null and dtType != ''">
                and t.dt_type = #{dtType,jdbcType=CHAR}
            </if>
            <if test="enterpriseScale != null and enterpriseScale != ''">
                and t.enterprise_scale = #{enterpriseScale,jdbcType=VARCHAR}
            </if>
            <if test="staffScale != null and staffScale != ''">
                and t.staff_scale = #{staffScale,jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != ''">
                and t.city like concat('%', #{city,jdbcType=VARCHAR}, '%')
            </if>
            <if test="districtCode != null and districtCode != ''">
                and t.district_code = #{districtCode,jdbcType=VARCHAR}
            </if>
            <if test="annualIncome != null and annualIncome != ''">
                and t.annual_income = #{annualIncome,jdbcType=VARCHAR}
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                and t.category_code = #{categoryCode,jdbcType=VARCHAR}
            </if>
            <if test="categoryName != null and categoryName != ''">
                and t.category_name like concat('%', #{categoryName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="isVerified != null and isVerified != ''">
                and t.is_verified = #{isVerified,jdbcType=CHAR}
            </if>
            <if test="enterpriseContacter != null and enterpriseContacter != ''">
                and t.enterprise_contacter like concat('%', #{enterpriseContacter,jdbcType=VARCHAR}, '%')
            </if>
            <if test="contacterPhone != null and contacterPhone != ''">
                and t.contacter_phone = #{contacterPhone,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List"/> from t_gen_agent_enterprise where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_gen_agent_enterprise t
        <include refid="Base_Condition"/>
        order by t.create_time desc
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_gen_agent_enterprise t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise" useGeneratedKeys="true" keyProperty="id">
        insert into t_gen_agent_enterprise (
            name, credit_code, dt_type, enterprise_scale, staff_scale, city, district_code, annual_income, 
            category_code, category_name, is_verified, enterprise_contacter, contacter_phone, dt_remark, 
            create_time, create_id, update_time, update_id, is_deleted, tenant_id
        ) values (
            #{name}, #{creditCode}, #{dtType}, #{enterpriseScale}, #{staffScale}, #{city}, #{districtCode}, #{annualIncome}, 
            #{categoryCode}, #{categoryName}, #{isVerified}, #{enterpriseContacter}, #{contacterPhone}, #{dtRemark}, 
            #{createTime}, #{createId}, #{updateTime}, #{updateId}, 0, #{tenantId}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise" useGeneratedKeys="true" keyProperty="id">
        insert into t_gen_agent_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="dtType != null">dt_type,</if>
            <if test="enterpriseScale != null">enterprise_scale,</if>
            <if test="staffScale != null">staff_scale,</if>
            <if test="city != null">city,</if>
            <if test="districtCode != null">district_code,</if>
            <if test="annualIncome != null">annual_income,</if>
            <if test="categoryCode != null">category_code,</if>
            <if test="categoryName != null">category_name,</if>
            <if test="isVerified != null">is_verified,</if>
            <if test="enterpriseContacter != null">enterprise_contacter,</if>
            <if test="contacterPhone != null">contacter_phone,</if>
            <if test="dtRemark != null">dt_remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            is_deleted
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="dtType != null">#{dtType},</if>
            <if test="enterpriseScale != null">#{enterpriseScale},</if>
            <if test="staffScale != null">#{staffScale},</if>
            <if test="city != null">#{city},</if>
            <if test="districtCode != null">#{districtCode},</if>
            <if test="annualIncome != null">#{annualIncome},</if>
            <if test="categoryCode != null">#{categoryCode},</if>
            <if test="categoryName != null">#{categoryName},</if>
            <if test="isVerified != null">#{isVerified},</if>
            <if test="enterpriseContacter != null">#{enterpriseContacter},</if>
            <if test="contacterPhone != null">#{contacterPhone},</if>
            <if test="dtRemark != null">#{dtRemark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            0
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise">
        update t_gen_agent_enterprise set
            name = #{name},
            credit_code = #{creditCode},
            dt_type = #{dtType},
            enterprise_scale = #{enterpriseScale},
            staff_scale = #{staffScale},
            city = #{city},
            district_code = #{districtCode},
            annual_income = #{annualIncome},
            category_code = #{categoryCode},
            category_name = #{categoryName},
            is_verified = #{isVerified},
            enterprise_contacter = #{enterpriseContacter},
            contacter_phone = #{contacterPhone},
            dt_remark = #{dtRemark},
            update_time = #{updateTime},
            update_id = #{updateId}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise">
        update t_gen_agent_enterprise
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="dtType != null">dt_type = #{dtType},</if>
            <if test="enterpriseScale != null">enterprise_scale = #{enterpriseScale},</if>
            <if test="staffScale != null">staff_scale = #{staffScale},</if>
            <if test="city != null">city = #{city},</if>
            <if test="districtCode != null">district_code = #{districtCode},</if>
            <if test="annualIncome != null">annual_income = #{annualIncome},</if>
            <if test="categoryCode != null">category_code = #{categoryCode},</if>
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="isVerified != null">is_verified = #{isVerified},</if>
            <if test="enterpriseContacter != null">enterprise_contacter = #{enterpriseContacter},</if>
            <if test="contacterPhone != null">contacter_phone = #{contacterPhone},</if>
            <if test="dtRemark != null">dt_remark = #{dtRemark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_gen_agent_enterprise set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_gen_agent_enterprise (
            name, credit_code, dt_type, enterprise_scale, staff_scale, city, district_code, annual_income, 
            category_code, category_name, is_verified, enterprise_contacter, contacter_phone, dt_remark, 
            create_time, create_id, update_time, update_id, is_deleted, tenant_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.name}, #{item.creditCode}, #{item.dtType}, #{item.enterpriseScale}, #{item.staffScale}, 
                #{item.city}, #{item.districtCode}, #{item.annualIncome}, #{item.categoryCode}, #{item.categoryName}, 
                #{item.isVerified}, #{item.enterpriseContacter}, #{item.contacterPhone}, #{item.dtRemark}, 
                #{item.createTime}, #{item.createId}, #{item.updateTime}, #{item.updateId}, 0, #{item.tenantId}
            )
        </foreach>
    </insert>

</mapper>
