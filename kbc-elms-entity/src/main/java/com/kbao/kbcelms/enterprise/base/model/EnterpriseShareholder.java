package com.kbao.kbcelms.enterprise.base.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 企业股东信息
 * 对应天眼查API：821-企业股东
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Document(collection = "enterprise_shareholder")
@ApiModel(value = "EnterpriseShareholder", description = "企业股东信息")
public class EnterpriseShareholder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 统一社会信用代码
     */
    @Indexed
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long cgid;

    /**
     * 认缴信息列表
     */
    @ApiModelProperty(value = "认缴信息列表")
    private List<CapitalInfo> capital;

    /**
     * 首次持股日期（时间戳）
     */
    @ApiModelProperty(value = "首次持股日期（时间戳）")
    private Long ftShareholding;

    /**
     * 股东名
     */
    @ApiModelProperty(value = "股东名")
    private String name;

    /**
     * 实缴信息列表
     */
    @ApiModelProperty(value = "实缴信息列表")
    private List<CapitalInfo> capitalActl;

    /**
     * logo
     */
    @ApiModelProperty(value = "logo")
    private String logo;

    /**
     * 简称
     */
    @ApiModelProperty(value = "简称")
    private String alias;

    /**
     * 对应表id
     */
    @ApiModelProperty(value = "对应表id")
    private Long shareholderId;

    /**
     * 股东类型（1-公司，2-人，3-其它）
     */
    @ApiModelProperty(value = "股东类型（1-公司，2-人，3-其它）")
    private Integer type;

    /**
     * 人员hcgid
     */
    @ApiModelProperty(value = "人员hcgid")
    private String hcgid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 租户ID
     */
    @Indexed
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 出资信息内部类
     */
    @Data
    public static class CapitalInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 出资金额
         */
        @ApiModelProperty(value = "出资金额")
        private String amomon;

        /**
         * 出资方式
         */
        @ApiModelProperty(value = "出资方式")
        private String payment;

        /**
         * 出资时间
         */
        @ApiModelProperty(value = "出资时间")
        private String time;

        /**
         * 占比
         */
        @ApiModelProperty(value = "占比")
        private String percent;
    }
}
