package com.kbao.kbcelms.enterprise.base.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 上市公司财务简析
 * 对应天眼查API：798-上市公司财务简析
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Document(collection = "enterprise_financial")
@ApiModel(value = "EnterpriseFinancial", description = "上市公司财务简析")
public class EnterpriseFinancial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 统一社会信用代码
     */
    @Indexed
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Integer total;

    /**
     * 净资产信息
     */
    @ApiModelProperty(value = "净资产信息")
    private FinancialData netAssets;

    /**
     * 净利率信息
     */
    @ApiModelProperty(value = "净利率信息")
    private FinancialData netInterestRate;

    /**
     * 总资产信息
     */
    @ApiModelProperty(value = "总资产信息")
    private FinancialData totalAssets;

    /**
     * 营业收入信息
     */
    @ApiModelProperty(value = "营业收入信息")
    private FinancialData businessIncome;

    /**
     * 净利润信息
     */
    @ApiModelProperty(value = "净利润信息")
    private FinancialData netProfit;

    /**
     * 毛利率信息
     */
    @ApiModelProperty(value = "毛利率信息")
    private FinancialData grossInterestRate;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 租户ID
     */
    @Indexed
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 财务数据内部类
     */
    @Data
    public static class FinancialData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 区间说明
         */
        @ApiModelProperty(value = "区间说明")
        private String info;

        /**
         * 类别
         */
        @ApiModelProperty(value = "类别")
        private String title;

        /**
         * 年份数组
         */
        @ApiModelProperty(value = "年份数组")
        private List<String> time;

        /**
         * 数据列表
         */
        @ApiModelProperty(value = "数据列表")
        private List<FinancialItem> list;
    }

    /**
     * 财务项目内部类
     */
    @Data
    public static class FinancialItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 金额或百分比
         */
        @ApiModelProperty(value = "金额或百分比")
        private Long amount;

        /**
         * 带单位的金额
         */
        @ApiModelProperty(value = "带单位的金额")
        private String convertAmount;

        /**
         * 年份
         */
        @ApiModelProperty(value = "年份")
        private String year;
    }
}
