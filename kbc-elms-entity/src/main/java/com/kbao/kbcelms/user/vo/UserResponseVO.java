package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/22 9:02
 */
@Data
public class UserResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "主键")
    private Integer id;

    @ApiModelProperty(name = "用户ID")
    private String userId;

    @ApiModelProperty(name = "云服登录账户")
    private String bscUserName;

    @ApiModelProperty(name = "姓名")
    private String nickName;

    @ApiModelProperty(name = "工号")
    private String agentCode;

    @ApiModelProperty(name = "电话")
    private String phone;

    @ApiModelProperty(name = "邮箱")
    private String email;

    @ApiModelProperty(name = "企微账号")
    private String wechatUserId;

    @ApiModelProperty(name = "ehr账户")
    private String ehrUserCode;

    @ApiModelProperty(name = "个人简介")
    private String personDesc;

    @ApiModelProperty(name = "擅长险种")
    private String insuranceTypes;

    @ApiModelProperty(name = "擅长险种名称")
    private String insuranceNames;

    @ApiModelProperty(name = "擅长行业")
    private String industryTypes;

    @ApiModelProperty(name = "擅长行业名称")
    private String industryNames;

    @ApiModelProperty(name = "租户ID")
    private String tenantId;

    @ApiModelProperty(name = "关联类型： org 机构 dept部门 out 外部")
    private String relationType;

    @ApiModelProperty(name = "机构code")
    private String organCode;

    @ApiModelProperty(name = "停用原因")
    private String stopReason;

    @ApiModelProperty(name = "状态 1 启用 0 停用")
    private String status;

    @ApiModelProperty(name = "企微账号")
    private String wechatAccount;

    @ApiModelProperty(name = "专家身份 1 团财专员、2 代理人、3 经纪人、4 营业部负责人")
    private Integer expertType;

    @ApiModelProperty(name = "归属机构code路径")
    private String organCodePath;

    @ApiModelProperty(name = "归属机构名称路径")
    private String organNamePath;

    @ApiModelProperty(name = "角色名称")
    private String roleName;

    @ApiModelProperty(name = "用户租户角色关联表主键")
    private String userTenantId;

    @ApiModelProperty(name = "权限机构类型 org 全国机构 com 总公司")
    private String orgType;
}
