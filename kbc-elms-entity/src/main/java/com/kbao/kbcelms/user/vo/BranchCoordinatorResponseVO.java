package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分公司统筹角色人员响应VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "BranchCoordinatorResponseVO", description = "分公司统筹角色人员响应VO")
public class BranchCoordinatorResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "角色类型")
    private Integer roleType;

    @ApiModelProperty(value = "机构编码")
    private String organCode;

    @ApiModelProperty(value = "机构名称")
    private String organName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;
} 