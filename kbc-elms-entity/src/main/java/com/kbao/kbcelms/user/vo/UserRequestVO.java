package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/21 17:53
 */
@Data
public class UserRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String userId;

    private String tenantId;

    @ApiModelProperty(name = "云服登录账号")
    private String bscUserName;

    @ApiModelProperty(name = "姓名")
    private String nickName;

    @ApiModelProperty(name = "角色ID")
    private Integer roleId;

    @ApiModelProperty(name = "归属机构名称")
    private String orgName;

    @ApiModelProperty(name = "擅长险种")
    private String insuranceTypes;

    @ApiModelProperty(name = "擅长行业")
    private String industryTypes;

    @ApiModelProperty(name = "邮箱")
    private String email;

    @ApiModelProperty(name = "状态 1 启用 0 停用")
    private String status;

    private Integer userTenantId;

    @ApiModelProperty(name = "停用原因")
    private String stopReason;

}
