package com.kbao.kbcelms.user.entity;

import java.util.Date;

/**
 * 用户表
 */
public class User {
    /** 主键 */
    private Integer id;
    /** 编号 */
    private String userId;
    /** 云服登录账户 */
    private String bscUseName;
    /** 姓名 */
    private String nickName;
    /** 电话 */
    private String phone;
    /** 邮箱 */
    private String email;
    /** ehr账户 */
    private String ehrUserCode;
    /** 个人简介 */
    private String personDesc;
    /** 擅长险种 */
    private String insuranceTypes;
    /** 擅长险种名称 */
    private String insuranceNames;
    /** 擅长行业 */
    private String industryTypes;
    /** 擅长行业名称 */
    private String industryNames;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
    /** 大童销售工号 */
    private String agentCode;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public String getBscUseName() { return bscUseName; }
    public void setBscUseName(String bscUseName) { this.bscUseName = bscUseName; }
    public String getNickName() { return nickName; }
    public void setNickName(String nickName) { this.nickName = nickName; }
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getEhrUserCode() { return ehrUserCode; }
    public void setEhrUserCode(String ehrUserCode) { this.ehrUserCode = ehrUserCode; }
    public String getPersonDesc() { return personDesc; }
    public void setPersonDesc(String personDesc) { this.personDesc = personDesc; }
    public String getInsuranceTypes() { return insuranceTypes; }
    public void setInsuranceTypes(String insuranceTypes) { this.insuranceTypes = insuranceTypes; }
    public String getIndustryTypes() { return industryTypes; }
    public void setIndustryTypes(String industryTypes) { this.industryTypes = industryTypes; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getInsuranceNames() {
        return insuranceNames;
    }

    public void setInsuranceNames(String insuranceNames) {
        this.insuranceNames = insuranceNames;
    }

    public String getIndustryNames() {
        return industryNames;
    }

    public void setIndustryNames(String industryNames) {
        this.industryNames = industryNames;
    }
}