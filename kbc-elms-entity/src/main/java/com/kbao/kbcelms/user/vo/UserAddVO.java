package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/21 11:37
 */
@Data
public class UserAddVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(name = "编号")
    private String userId;

    @NotNull(message = "姓名不能为空")
    @ApiModelProperty(name = "姓名")
    private String nickName;

    @NotNull(message = "手机号不能为空")
    @ApiModelProperty(name = "手机号")
    private String phone;

    @NotNull(message = "邮箱不能为空")
    @ApiModelProperty(name = "邮箱")
    private String email;

    @ApiModelProperty(name = "租户ID")
    private String tenantId;

    @ApiModelProperty(name = "ehr账户")
    private String ehrUserCode;

    @ApiModelProperty(name = "云服登录账户")
    private String bscUserName;

    @ApiModelProperty(name = "大童销售工号")
    private String agentCode;

    @ApiModelProperty(name = "擅长险种")
    private String insuranceTypes;

    @ApiModelProperty(name = "擅长险种名称")
    private String insuranceNames;

    @ApiModelProperty(name = "擅长行业")
    private String industryTypes;

    @ApiModelProperty(name = "擅长行业名称")
    private String industryNames;

    @ApiModelProperty(name = "个人简介")
    private String personDesc;

    @ApiModelProperty(name = "关联类型： org 机构 dept部门 out 外部")
    private String relationType;

    @ApiModelProperty(name = "机构/部门ID")
    private String organCode;

    @ApiModelProperty(name = "机构/部门/渠道Id节点路径（集合）")
    private String organCodePath;

    @ApiModelProperty(name = "机构/部门名称")
    private String organName;

    @ApiModelProperty(name = "节点路径 1/2/3/4")
    private String organNamePath;

    @ApiModelProperty(name = "企微账号")
    private String wechatAccount;

    @ApiModelProperty(name = "专家身份  1 团财专员、2 代理人、3 经纪人、4 营业部负责人")
    private Integer expertType;
}
