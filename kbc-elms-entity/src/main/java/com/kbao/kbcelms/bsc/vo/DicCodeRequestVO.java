package com.kbao.kbcelms.bsc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:11
 */
@Data
public class DicCodeRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("字典类型编码")
    @NotNull(message = "字典类型编码不能为空！")
    private String dicCode;
}
