package com.kbao.kbcelms.bsc.vo;

import com.kbao.kbcbsc.apptenant.bean.AppTenantListVo;
import com.kbao.kbcbsc.funcauth.dto.FuncAuthDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:23
 */
@Data
public class BscWebUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer isAdmin;

    private List<FuncAuthDTO> funcAuthDTO;

    private List<AppTenantListVo> appTenantListVo;
}
