<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="processId" column="process_id" jdbcType="VARCHAR" />
        <result property="operaorId" column="operaor_id" jdbcType="VARCHAR" />
        <result property="targetId" column="target_id" jdbcType="VARCHAR" />
        <result property="operaorDesc" column="operaor_desc" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, tenant_id, process_id, operaor_id, target_id, operaor_desc, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.tenant_id,
        t.process_id,
        t.operaor_id,
        t.target_id,
        t.operaor_desc,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="processId != null">
                and t.process_id = #{processId,jdbcType=VARCHAR}
            </if>
            <if test="operaorId != null">
                and t.operaor_id = #{operaorId,jdbcType=VARCHAR}
            </if>
            <if test="targetId != null">
                and t.target_id = #{targetId,jdbcType=VARCHAR}
            </if>
            <if test="operaorDesc != null">
                and t.operaor_desc = #{operaorDesc,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_process_log where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_process_log t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_process_log t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process_log (
            opportunity_id, tenant_id, process_id, operaor_id, target_id, operaor_desc, create_time, update_id, update_time, is_deleted
        ) values (
            #{opportunityId}, #{tenantId}, #{processId}, #{operaorId}, #{targetId}, #{operaorDesc}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="processId != null">process_id,</if>
            <if test="operaorId != null">operaor_id,</if>
            <if test="targetId != null">target_id,</if>
            <if test="operaorDesc != null">operaor_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="processId != null">#{processId},</if>
            <if test="operaorId != null">#{operaorId},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="operaorDesc != null">#{operaorDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        update t_opportunity_process_log set
            opportunity_id = #{opportunityId},
            tenant_id = #{tenantId},
            process_id = #{processId},
            operaor_id = #{operaorId},
            target_id = #{targetId},
            operaor_desc = #{operaorDesc},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        update t_opportunity_process_log
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="operaorId != null">operaor_id = #{operaorId},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="operaorDesc != null">operaor_desc = #{operaorDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_process_log set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_process_log (
            opportunity_id, tenant_id, process_id, operaor_id, target_id, operaor_desc, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.opportunityId}, #{item.tenantId}, #{item.processId}, #{item.operaorId}, #{item.targetId}, #{item.operaorDesc}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_process_log set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询用户参与过的机会数量 -->
    <select id="countUserParticipatedOpportunities" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(DISTINCT t.opportunity_id) 
        from t_opportunity_process_log t 
        WHERE (t.operaor_id = #{userId,jdbcType=VARCHAR} OR t.target_id = #{userId,jdbcType=VARCHAR}) 
        AND t.is_deleted = 0
    </select>

</mapper> 