package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会流程日志响应VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "OpportunityProcessLogResponseVO", description = "机会流程日志响应VO")
public class OpportunityProcessLogResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参与过的机会数")
    private Integer opportunityCount;

    @ApiModelProperty(value = "待完成的任务数")
    private Long taskCount;
} 