package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会流程日志请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "OpportunityProcessLogRequestVO", description = "机会流程日志请求VO")
public class OpportunityProcessLogRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;
} 