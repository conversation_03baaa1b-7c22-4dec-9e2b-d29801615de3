package com.kbao.kbcelms.opportunityprocesslog.entity;

import java.util.Date;

/**
 * 用户表（机会流程日志）
 */
public class OpportunityProcessLog {
    /** 编号 */
    private Integer id;
    /** 机会id */
    private Integer opportunityId;
    /** 租户id */
    private String tenantId;
    /** 流程id */
    private String processId;
    /** 操作人id */
    private String operaorId;
    /** 接收人id */
    private String targetId;
    /** 操作描述 */
    private String operaorDesc;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getOpportunityId() { return opportunityId; }
    public void setOpportunityId(Integer opportunityId) { this.opportunityId = opportunityId; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getProcessId() { return processId; }
    public void setProcessId(String processId) { this.processId = processId; }
    public String getOperaorId() { return operaorId; }
    public void setOperaorId(String operaorId) { this.operaorId = operaorId; }
    public String getTargetId() { return targetId; }
    public void setTargetId(String targetId) { this.targetId = targetId; }
    public String getOperaorDesc() { return operaorDesc; }
    public void setOperaorDesc(String operaorDesc) { this.operaorDesc = operaorDesc; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
} 