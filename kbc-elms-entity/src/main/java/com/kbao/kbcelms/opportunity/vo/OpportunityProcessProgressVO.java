package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取机会流程进度请求VO
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "OpportunityProcessProgressVO", description = "获取机会流程进度请求VO")
public class OpportunityProcessProgressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true, example = "1")
    private Integer opportunityId;
} 