package com.kbao.kbcelms.opportunity.entity;

import java.util.Date;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * 机会表
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
public class Opportunity {
    /** 主键 */
    private Integer id;
    /** 机会编码 */
    private String bizCode;
    /** 顾问工号 */
    private String agentCode;
    /** 顾问姓名 */
    private String agentName;
    /** 机会名称 */
    private String opportunityName;
    /** 关联顾问企业ID */
    private Integer agentEnterpriseId;
    /** 机会类型: 1-员服，2-综合 */
    private String opportunityType;
    /** 关联行业编码 */
    private String industryCode;
    /** 机会状态：0-待提交，1-已提交，2-锁定，3-中止，4-终止 */
    private Integer status;
    /** 区域中心编码 */
    private String areaCenterCode;
    /** 区域中心名称 */
    private String areaCenterName;
    /** 法人公司编码 */
    private String legalCode;
    /** 法人公司名称 */
    private String legalName;
    /** 分公司编码 */
    private String companyCode;
    /** 分公司名称 */
    private String companyName;
    /** 交易服务中心编码 */
    private String tradingCenterCode;
    /** 交易服务中心名称 */
    private String tradingCenterName;
    /** 销售服务中心编码 */
    private String salesCenterCode;
    /** 销售服务中心名称 */
    private String salesCenterName;
    /** 流程步骤 */
    @Size(max = 5, message = "流程步骤长度不能超过5个字符")
    private String processStep;
    /** 创建人 当前用户ID */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 租户ID */
    private String tenantId;
    /** 当前流程id，对应 opportunity_process 表 id */
    private Integer currentProcessId;
    /** 统筹人员id */
    private String coordinator;
    /** 项目经理id */
    private String projectManager;
    /** 项目归属机构code */
    private String projectOrgCode;
    /** 项目归属机构名称 */
    private String projectOrgName;
    /** 是否删除 0 未删除  1已删除 */
    private Integer isDeleted;
}