package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会任务请求VO
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "OpportunityTaskVO", description = "机会任务请求VO")
public class OpportunityTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true)
    private Integer opportunityId;

    @ApiModelProperty(value = "流程实例ID", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "指派用户ID")
    private String assignee;

    @ApiModelProperty(value = "指派用户角色类型")
    private Integer assigneeRoleType;

    @ApiModelProperty(value = "指派用户机构")
    private String assigneeOrg;

    @ApiModelProperty(value = "业务租户ID")
    private String businessTenantId;
} 