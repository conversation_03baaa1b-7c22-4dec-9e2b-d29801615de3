package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会暂停/恢复请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "OpportunitySuspendVO", description = "机会暂停/恢复请求VO")
public class OpportunitySuspendVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true)
    private Integer opportunityId;

    @ApiModelProperty(value = "操作原因", required = true)
    private String reason;
} 