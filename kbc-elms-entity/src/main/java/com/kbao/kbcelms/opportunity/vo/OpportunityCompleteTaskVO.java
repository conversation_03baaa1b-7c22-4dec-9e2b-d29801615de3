package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 完成任务请求VO
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "OpportunityCompleteTaskVO", description = "完成任务请求VO")
public class OpportunityCompleteTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true)
    private Integer opportunityId;

    @ApiModelProperty(value = "执行人ID", required = true)
    private String assignee;

    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;
} 