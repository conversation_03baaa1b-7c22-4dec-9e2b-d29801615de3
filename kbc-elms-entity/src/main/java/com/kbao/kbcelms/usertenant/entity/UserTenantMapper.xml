<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.usertenant.dao.UserTenantMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.usertenant.entity.UserTenant">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="userId" column="user_id" jdbcType="VARCHAR" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="relationType" column="relation_type" jdbcType="VARCHAR" />
        <result property="organCode" column="organ_code" jdbcType="VARCHAR" />
        <result property="organCodePath" column="organ_code_path" jdbcType="VARCHAR" />
        <result property="organNamePath" column="organ_name_path" jdbcType="VARCHAR" />
        <result property="organName" column="organ_name" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="stopReason" column="stop_reason" jdbcType="VARCHAR" />
        <result property="status" column="status" jdbcType="VARCHAR" />
        <result property="wechatAccount" column="wechat_account" jdbcType="VARCHAR" />
        <result property="expertType" column="expert_type" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, tenant_id, relation_type, organ_code, organ_code_path, organ_name_path, organ_name, create_id, create_time, update_id, update_time, is_deleted, stop_reason, status, wechat_account, expert_type
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.user_id,
        t.tenant_id,
        t.relation_type,
        t.organ_code,
        t.organ_code_path,
        t.organ_name_path,
        t.organ_name,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.stop_reason,
        t.status,
        t.wechat_account,
        t.expert_type
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="userId != null">
                and t.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="relationType != null">
                and t.relation_type = #{relationType,jdbcType=VARCHAR}
            </if>
            <if test="organCode != null">
                and t.organ_code = #{organCode,jdbcType=VARCHAR}
            </if>
            <if test="organCodePath != null">
                and t.organ_code_path = #{organCodePath,jdbcType=VARCHAR}
            </if>
            <if test="organNamePath != null">
                and t.organ_name_path = #{organNamePath,jdbcType=VARCHAR}
            </if>
            <if test="organName != null">
                and t.organ_name = #{organName,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="stopReason != null">
                and t.stop_reason = #{stopReason,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and t.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="wechatAccount != null">
                and t.wechat_account = #{wechatAccount,jdbcType=VARCHAR}
            </if>
            <if test="expertType != null">
                and t.expert_type = #{expertType,jdbcType=INTEGER}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_user_tenant where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user_tenant t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_user_tenant t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.usertenant.entity.UserTenant" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_tenant (
            user_id, tenant_id, relation_type, organ_code, organ_code_path, organ_name_path, organ_name, create_id, create_time, update_id, update_time, is_deleted, stop_reason, status, wechat_account, expert_type
        ) values (
            #{userId}, #{tenantId}, #{relationType}, #{organCode}, #{organCodePath}, #{organNamePath}, #{organName}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{stopReason}, #{status}, #{wechatAccount}, #{expertType}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.usertenant.entity.UserTenant" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="organCode != null">organ_code,</if>
            <if test="organCodePath != null">organ_code_path,</if>
            <if test="organNamePath != null">organ_name_path,</if>
            <if test="organName != null">organ_name,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="stopReason != null">stop_reason,</if>
            <if test="status != null">status,</if>
            <if test="wechatAccount != null">wechat_account,</if>
            <if test="expertType != null">expert_type,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="organCode != null">#{organCode},</if>
            <if test="organCodePath != null">#{organCodePath},</if>
            <if test="organNamePath != null">#{organNamePath},</if>
            <if test="organName != null">#{organName},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="stopReason != null">#{stopReason},</if>
            <if test="status != null">#{status},</if>
            <if test="wechatAccount != null">#{wechatAccount},</if>
            <if test="expertType != null">#{expertType},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.usertenant.entity.UserTenant">
        update t_user_tenant set
            user_id = #{userId},
            tenant_id = #{tenantId},
            relation_type = #{relationType},
            organ_code = #{organCode},
            organ_code_path = #{organCodePath},
            organ_name_path = #{organNamePath},
            organ_name = #{organName},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            stop_reason = #{stopReason},
            status = #{status},
            wechat_account = #{wechatAccount},
            expert_type = #{expertType}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.usertenant.entity.UserTenant">
        update t_user_tenant
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="organCode != null">organ_code = #{organCode},</if>
            <if test="organCodePath != null">organ_code_path = #{organCodePath},</if>
            <if test="organNamePath != null">organ_name_path = #{organNamePath},</if>
            <if test="organName != null">organ_name = #{organName},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="stopReason != null">stop_reason = #{stopReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="wechatAccount != null">wechat_account = #{wechatAccount},</if>
            <if test="expertType != null">expert_type = #{expertType},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_user_tenant set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_user_tenant (
            user_id, tenant_id, relation_type, organ_code, organ_code_path, organ_name_path, organ_name, create_id, create_time, update_id, update_time, is_deleted, stop_reason, status, wechat_account, expert_type
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.tenantId}, #{item.relationType}, #{item.organCode}, #{item.organCodePath}, #{item.organNamePath}, #{item.organName}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.stopReason}, #{item.status}, #{item.wechatAccount}, #{item.expertType}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_user_tenant set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="setUserStatus" parameterType="com.kbao.kbcelms.usertenant.entity.UserTenant">
        update t_user_tenant
        set status = #{status},stop_reason = #{stopReason}
        where id = #{id}
    </update>

    <select id="getByUserIdAndTenantId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Alias_Column_List"/>
        from t_user_tenant t
        where t.user_id=#{userId} and t.tenant_id = #{tenantId} and t.is_deleted = 0
    </select>

</mapper> 