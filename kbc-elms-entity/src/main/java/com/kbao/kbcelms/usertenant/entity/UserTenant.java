package com.kbao.kbcelms.usertenant.entity;

import java.util.Date;

/**
 * 用户表租户关系表
 */
public class UserTenant {
    /** 主键 */
    private Integer id;
    /** 编号 */
    private String userId;
    /** 租户id */
    private String tenantId;
    /** 关联类型： org 机构 dept部门 out 外部 */
    private String relationType;
    /** 机构code */
    private String organCode;
    /** 机构/部门/渠道Id节点路径（集合） */
    private String organCodePath;
    /** 节点路径 1/2/3/4 */
    private String organNamePath;
    /** 节点名称 */
    private String organName;
    /** 创建人编号 */
    private String createId;
    /** 创建时间 */
    private Date createTime;
    /** 更新人编号 */
    private String updateId;
    /** 更新时间 */
    private Date updateTime;
    /** 0 有效 1 删除 */
    private Integer isDeleted;
    /** 停用原因 */
    private String stopReason;
    /** 1 启用 0 停用 */
    private String status;
    /** 企微账号 */
    private String wechatAccount;
    /** 专家身份  1 团财专员、2 代理人、3 经纪人、4 营业部负责人 */
    private Integer expertType;

    // getter and setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }
    public String getRelationType() { return relationType; }
    public void setRelationType(String relationType) { this.relationType = relationType; }
    public String getOrganCode() { return organCode; }
    public void setOrganCode(String organCode) { this.organCode = organCode; }
    public String getOrganCodePath() { return organCodePath; }
    public void setOrganCodePath(String organCodePath) { this.organCodePath = organCodePath; }
    public String getOrganNamePath() { return organNamePath; }
    public void setOrganNamePath(String organNamePath) { this.organNamePath = organNamePath; }
    public String getOrganName() { return organName; }
    public void setOrganName(String organName) { this.organName = organName; }
    public String getCreateId() { return createId; }
    public void setCreateId(String createId) { this.createId = createId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public String getUpdateId() { return updateId; }
    public void setUpdateId(String updateId) { this.updateId = updateId; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    public Integer getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Integer isDeleted) { this.isDeleted = isDeleted; }
    public String getStopReason() { return stopReason; }
    public void setStopReason(String stopReason) { this.stopReason = stopReason; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getWechatAccount() { return wechatAccount; }
    public void setWechatAccount(String wechatAccount) { this.wechatAccount = wechatAccount; }
    public Integer getExpertType() { return expertType; }
    public void setExpertType(Integer expertType) { this.expertType = expertType; }
} 