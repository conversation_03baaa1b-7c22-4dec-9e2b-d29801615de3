[kbc-elms-web:*********:7002] [,] 2025-07-28 09:02:36.300 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:07:36.369 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:12:36.378 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:17:36.380 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:22:36.395 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:27:36.402 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:32:36.419 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.072 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.075 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e8d6e7a30fa8553a
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.076 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.078 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.079 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.080 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.082 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"33eb0597875aaf04f770c31382d1af75","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.083 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.221 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.222 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.222 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.223 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:34:59 GMT
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.223 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.223 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.224 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.224 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.224 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [e8d6e7a30fa8553a,] 2025-07-28 09:34:59.225 DEBUG 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.706 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b9341c21abe7d5cb
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.708 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"33eb0597875aaf04f770c31382d1af75","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.709 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.787 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.787 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.787 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.787 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:34:59 GMT
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [b9341c21abe7d5cb,] 2025-07-28 09:34:59.788 DEBUG 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.081 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.084 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 34e7e0985df7f4d5
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.084 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.084 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.085 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.085 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.085 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"33eb0597875aaf04f770c31382d1af75","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.085 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.174 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (88ms)
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.174 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.175 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.175 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:35:01 GMT
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.175 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.175 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.175 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.176 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.176 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [34e7e0985df7f4d5,] 2025-07-28 09:35:01.176 DEBUG 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:37:36.424 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:42:36.428 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:47:36.431 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:52:36.435 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 09:57:36.438 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
