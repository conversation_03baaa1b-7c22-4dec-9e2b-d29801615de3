[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:05.433 INFO 31496 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.660 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.661 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.695 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.695 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.697 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.699 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.699 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.699 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.702 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.702 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.838 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.845 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-29 15:23:06.847 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:07.142 INFO 31496 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.091 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.094 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.265 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 162 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.317 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.322 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:17.498 INFO 31496 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 146 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.121 INFO 31496 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=c0b4a6dd-5ccf-3f6b-8b35-d7153d573574
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.171 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.172 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.172 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.172 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.173 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.174 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.174 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.174 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.175 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.176 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.176 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.176 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:18.176 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:19.504 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:19.512 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:19.512 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.092 INFO 31496 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.125 INFO 31496 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.126 INFO 31496 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.127 INFO 31496 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.313 INFO 31496 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:20.315 INFO 31496 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 13104 ms
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:22.147 INFO 31496 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:26.448 INFO 31496 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:26.905 INFO 31496 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:27.400 INFO 31496 [cluster-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:27.400 INFO 31496 [cluster-rtt-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:27.406 INFO 31496 [cluster-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=269691400}
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:27.460 WARN 31496 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:28.009 WARN 31496 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:29.070 DEBUG 31496 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:30.490 DEBUG 31496 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:30.580 DEBUG 31496 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.008 INFO 31496 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.076 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.852 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.880 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.922 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.945 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:31.961 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.056 DEBUG 31496 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.181 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.238 DEBUG 31496 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.256 DEBUG 31496 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.299 DEBUG 31496 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.321 DEBUG 31496 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.361 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.394 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.453 DEBUG 31496 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.510 DEBUG 31496 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.563 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.603 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.638 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.700 DEBUG 31496 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.819 DEBUG 31496 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.890 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.955 DEBUG 31496 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.980 DEBUG 31496 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.085 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.183 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.285 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.315 DEBUG 31496 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.337 DEBUG 31496 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.355 DEBUG 31496 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.867 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.919 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.974 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.004 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.093 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.175 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.232 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.268 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.316 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.361 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.415 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.472 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.523 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.559 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.587 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.636 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.664 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.688 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.715 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.743 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.778 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.808 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.855 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.881 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.911 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:34.926 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.006 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.049 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.119 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.353 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.370 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.433 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.533 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.579 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.644 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.695 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.720 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.735 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.756 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.770 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.790 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.808 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.827 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.844 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.863 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.880 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.898 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.914 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.932 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.946 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:35.963 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:36.897 INFO 31496 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:37.179 INFO 31496 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:38.620 INFO 31496 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:40.634 INFO 31496 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:41.033 WARN 31496 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.370 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.389 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.410 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.426 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.446 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.463 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.482 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.503 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.524 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.545 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.565 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.594 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.625 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.656 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.683 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.689 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.708 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.726 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.744 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.766 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.783 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.807 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.841 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.873 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.901 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.927 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.955 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.961 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.965 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.977 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.983 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.989 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:42.996 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.000 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.010 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.021 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.027 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.032 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.043 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.050 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.058 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.068 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.076 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.087 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.093 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.099 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.110 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.116 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.127 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.131 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.136 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.140 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.146 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.150 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.156 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.162 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.165 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.169 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.174 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.180 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.191 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.201 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.205 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.209 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.215 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.223 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.228 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.235 INFO 31496 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.610 INFO 31496 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.758 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.774 INFO 31496 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.879 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.879 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.880 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.880 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.880 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.880 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:43.880 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.302 INFO 31496 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.309 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.320 INFO 31496 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753773825317 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.332 INFO 31496 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.334 INFO 31496 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.432 INFO 31496 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.438 INFO 31496 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.438 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.439 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.439 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.440 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.440 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.440 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.440 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.441 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.441 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.441 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.441 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.442 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.442 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.442 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.444 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.444 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.517 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.518 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.527 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.528 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.528 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.528 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.528 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.529 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.529 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.529 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.529 INFO 31496 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.538 INFO 31496 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:45.653 INFO 31496 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.051 INFO 31496 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.628 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.679 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.684 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.689 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.778 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.784 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.791 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.796 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.824 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.837 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:46.856 INFO 31496 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:47.029 INFO 31496 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 43.87 seconds (JVM running for 49.989)
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:47.779 INFO 31496 [RMI TCP Connection(3)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:47.782 INFO 31496 [RMI TCP Connection(3)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:47.821 INFO 31496 [RMI TCP Connection(3)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 39 ms
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:48.432 INFO 31496 [RMI TCP Connection(2)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.418 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.418 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.423 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 35c024592394ea9d
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.423 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0e0d60cc47195ca1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.425 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.425 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.427 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.427 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.430 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.430 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.431 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.431 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.170 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.171 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.172 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.174 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.365 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.365 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 35c024592394ea9d
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0e0d60cc47195ca1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.456 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.456 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.749 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c61aaeb848f0b7a5
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.873 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c61aaeb848f0b7a5
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.884 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.886 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.886 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.889 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.889 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (58ms)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.122 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1c8ab86b4fee06b8
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.217 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.218 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.770 INFO 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1c8ab86b4fee06b8,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.983 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.205 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: enterprise(String)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.377 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.553 DEBUG 31496 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.855 INFO 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1c8ab86b4fee06b8, 耗时=1397, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.902 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d57dd05f6ecb4d0b
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.904 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.904 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.996 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:57 GMT
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.000 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.001 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.002 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.002 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.003 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.014 INFO 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d57dd05f6ecb4d0b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.015 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.015 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_enterprise(String)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.060 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.499 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.500 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.540 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.542 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_enterprise LIMIT ? 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.542 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.585 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.593 INFO 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d57dd05f6ecb4d0b, 耗时=583, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:28:43.884 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.130 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.132 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 25d6b066e6f17076
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.133 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.135 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.292 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (157ms)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.292 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.293 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.298 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.307 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 25d6b066e6f17076
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.398 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (88ms)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.486 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5366a372dff2b21e
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.569 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.570 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.570 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.571 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.572 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.572 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.573 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.580 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.581 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.582 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.634 INFO 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5366a372dff2b21e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.699 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.699 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.737 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.737 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.739 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.777 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.777 INFO 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5366a372dff2b21e, 耗时=152, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.422 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.423 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f5d0267d1bcfebc7
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (109ms)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:55 GMT
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.537 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.537 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.541 INFO 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f5d0267d1bcfebc7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.542 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.543 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.579 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.580 DEBUG 31496 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.622 INFO 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f5d0267d1bcfebc7, 耗时=81, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:33:43.898 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:38:43.904 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:43:43.914 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:48:43.925 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:53:43.934 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:58:43.941 INFO 31496 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
