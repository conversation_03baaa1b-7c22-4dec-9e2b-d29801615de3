[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:04:54.669 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:09:54.685 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:14:54.694 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:19:54.697 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:24:54.703 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 11:28:23.851 INFO 17560 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
