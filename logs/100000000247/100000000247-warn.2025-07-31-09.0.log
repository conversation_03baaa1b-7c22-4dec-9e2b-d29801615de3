[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:29.386 WARN 39656 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:37.919 WARN 39656 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:38.491 WARN 39656 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:49.428 WARN 39656 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:41:58.454 WARN 39656 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:41:58.459 WARN 39656 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:16.820 WARN 20260 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:16.823 WARN 20260 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar.yml] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:16.825 WARN 20260 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar-dev.yml] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:22.608 WARN 20260 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:31.027 WARN 20260 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:31.485 WARN 20260 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:42:41.678 WARN 20260 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:04.685 WARN 20260 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:04.689 WARN 20260 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:28.999 WARN 35140 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:37.703 WARN 35140 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:38.283 WARN 35140 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:48.766 WARN 35140 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:50:41.093 WARN 35140 [DiscoveryClient-0] com.netflix.discovery.TimedSupervisorTask task supervisor timed out

java.util.concurrent.TimeoutException: null
	at java.util.concurrent.FutureTask.get(FutureTask.java:205)
	at com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:50:41.223 WARN 35140 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:50:41.226 WARN 35140 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:49.511 WARN 31476 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:57.800 WARN 31476 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:58.309 WARN 31476 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:09.069 WARN 31476 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:42.313 WARN 31476 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:42.317 WARN 31476 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:53:00.358 WARN 39888 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:53:08.771 WARN 39888 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:53:09.324 WARN 39888 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:53:19.815 WARN 39888 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
