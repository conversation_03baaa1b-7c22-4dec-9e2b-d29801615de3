{"@timestamp":"2025-08-01T03:01:57.688+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:06:57.710+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:11:57.722+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:16:57.724+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:21:57.740+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:26:57.742+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:31:57.758+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:36:57.772+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:41:57.781+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:46:57.788+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:45.676+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"c.n.d.s.t.d.RedirectingEurekaHttpClient","msg":"Request execution error. endpoint=DefaultEndpoint{ serviceUrl='https://kbc:<EMAIL>/eureka/}, exception=I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com\r\n\tat org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)\r\n\tat org.springframework.web.client.RestTemplate.execute(RestTemplate.java:711)\r\n\tat org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:602)\r\n\tat org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplicationsInternal(RestTemplateEurekaHttpClient.java:145)\r\n\tat org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getDelta(RestTemplateEurekaHttpClient.java:155)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)\r\n\tat com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1160)\r\n\tat com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:1041)\r\n\tat com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1556)\r\n\tat com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1523)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:87)\r\n\tat java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:111)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:66)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\nCaused by: java.net.UnknownHostException: kbc-eureka-dev.kbao123.com\r\n\tat java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)\r\n\tat java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867)\r\n\tat java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302)\r\n\tat java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815)\r\n\tat java.net.InetAddress.getAllByName0(InetAddress.java:1291)\r\n\tat java.net.InetAddress.getAllByName(InetAddress.java:1144)\r\n\tat java.net.InetAddress.getAllByName(InetAddress.java:1065)\r\n\tat org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45)\r\n\tat org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112)\r\n\tat org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)\r\n\tat org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)\r\n\tat org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)\r\n\tat org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)\r\n\tat org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)\r\n\tat org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)\r\n\tat org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)\r\n\tat org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)\r\n\tat org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)\r\n\tat org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:109)\r\n\tat org.springframework.http.client.support.BasicAuthenticationInterceptor.intercept(BasicAuthenticationInterceptor.java:79)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:93)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:77)\r\n\tat org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)\r\n\tat org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)\r\n\tat org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)\r\n\t... 854 more\r\n","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:45.683+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"c.n.d.s.t.d.RetryableEurekaHttpClient","msg":"Request execution failed with message: I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:45.690+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"com.netflix.discovery.DiscoveryClient","msg":"DiscoveryClient_KBC-ELMS-WEB/kbc-elms-web:*********:7002 - was unable to refresh its cache! This periodic background refresh will be retried in 5 seconds. status = Cannot execute request on any known server stacktrace = com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server\r\n\tat com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1160)\r\n\tat com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:1041)\r\n\tat com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1556)\r\n\tat com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1523)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:87)\r\n\tat java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:111)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:66)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:50.724+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"c.n.d.s.t.d.RedirectingEurekaHttpClient","msg":"Request execution error. endpoint=DefaultEndpoint{ serviceUrl='https://kbc:<EMAIL>/eureka/}, exception=I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com\r\n\tat org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:785)\r\n\tat org.springframework.web.client.RestTemplate.execute(RestTemplate.java:711)\r\n\tat org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:602)\r\n\tat org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getApplicationsInternal(RestTemplateEurekaHttpClient.java:145)\r\n\tat org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.getDelta(RestTemplateEurekaHttpClient.java:155)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)\r\n\tat com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1160)\r\n\tat com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:1041)\r\n\tat com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1556)\r\n\tat com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1523)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:87)\r\n\tat java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:111)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:66)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\nCaused by: java.net.UnknownHostException: kbc-eureka-dev.kbao123.com\r\n\tat java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)\r\n\tat java.net.InetAddress.getAllByName0(InetAddress.java:1291)\r\n\tat java.net.InetAddress.getAllByName(InetAddress.java:1144)\r\n\tat java.net.InetAddress.getAllByName(InetAddress.java:1065)\r\n\tat org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45)\r\n\tat org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112)\r\n\tat org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)\r\n\tat org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)\r\n\tat org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)\r\n\tat org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)\r\n\tat org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)\r\n\tat org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)\r\n\tat org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)\r\n\tat org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)\r\n\tat org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)\r\n\tat org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:109)\r\n\tat org.springframework.http.client.support.BasicAuthenticationInterceptor.intercept(BasicAuthenticationInterceptor.java:79)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:93)\r\n\tat org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:77)\r\n\tat org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)\r\n\tat org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)\r\n\tat org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:776)\r\n\t... 862 more\r\n","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:50.728+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"c.n.d.s.t.d.RetryableEurekaHttpClient","msg":"Request execution failed with message: I/O error on GET request for \"https://kbc:<EMAIL>/eureka/apps/delta\": kbc-eureka-dev.kbao123.com; nested exception is java.net.UnknownHostException: kbc-eureka-dev.kbao123.com","stack_trace":""}
{"@timestamp":"2025-08-01T03:50:50.729+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"DiscoveryClient-CacheRefreshExecutor-0","class":"com.netflix.discovery.DiscoveryClient","msg":"DiscoveryClient_KBC-ELMS-WEB/kbc-elms-web:*********:7002 - was unable to refresh its cache! This periodic background refresh will be retried in 5 seconds. status = Cannot execute request on any known server stacktrace = com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server\r\n\tat com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)\r\n\tat com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)\r\n\tat com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)\r\n\tat com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1160)\r\n\tat com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:1041)\r\n\tat com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1556)\r\n\tat com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1523)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:87)\r\n\tat java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:111)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:66)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.util.concurrent.FutureTask.<init>(FutureTask.java:151)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>(ScheduledThreadPoolExecutor.java:209)\r\n\tat java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:532)\r\n\tat com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:102)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)\r\n\tat java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n","stack_trace":""}
{"@timestamp":"2025-08-01T03:51:57.795+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T03:56:57.804+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
