[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:29.070 DEBUG 31496 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:30.490 DEBUG 31496 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:30.580 DEBUG 31496 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.056 DEBUG 31496 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.238 DEBUG 31496 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.256 DEBUG 31496 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.299 DEBUG 31496 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.321 DEBUG 31496 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.453 DEBUG 31496 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.510 DEBUG 31496 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.700 DEBUG 31496 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.819 DEBUG 31496 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.955 DEBUG 31496 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:32.980 DEBUG 31496 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.315 DEBUG 31496 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.337 DEBUG 31496 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-29 15:23:33.355 DEBUG 31496 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.418 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.418 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.423 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 35c024592394ea9d
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.423 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0e0d60cc47195ca1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.425 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.425 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.427 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.427 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.429 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:50.430 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.430 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.431 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:50.431 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.167 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.168 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.169 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.170 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.171 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.172 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.174 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.176 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.365 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.365 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 35c024592394ea9d
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0e0d60cc47195ca1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.366 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.367 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.453 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.454 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.456 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.456 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [0e0d60cc47195ca1,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.455 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [35c024592394ea9d,] 2025-07-29 15:27:51.457 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.749 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c61aaeb848f0b7a5
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.751 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.752 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.871 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.872 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.873 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c61aaeb848f0b7a5
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.880 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.884 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.886 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.886 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.889 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.889 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (58ms)
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.948 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c61aaeb848f0b7a5,] 2025-07-29 15:27:54.949 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.122 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1c8ab86b4fee06b8
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.125 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.129 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.212 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.217 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.218 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:55.983 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.205 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: enterprise(String)
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.377 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [1c8ab86b4fee06b8,] 2025-07-29 15:27:56.553 DEBUG 31496 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.902 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d57dd05f6ecb4d0b
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.903 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.904 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.904 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.996 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:57 GMT
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:56.998 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.000 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.001 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.002 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.002 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.003 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.015 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.015 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_enterprise(String)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.060 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.499 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.500 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.540 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.542 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_enterprise LIMIT ? 
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.542 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [d57dd05f6ecb4d0b,] 2025-07-29 15:27:57.585 DEBUG 31496 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.130 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.132 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 25d6b066e6f17076
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.133 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.134 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.135 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.292 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (157ms)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.292 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.293 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.296 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.297 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.298 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.307 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 25d6b066e6f17076
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.309 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.398 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (88ms)
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.400 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [25d6b066e6f17076,] 2025-07-29 15:28:46.401 DEBUG 31496 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.486 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5366a372dff2b21e
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.487 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.569 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.570 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.570 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.571 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.572 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.572 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.573 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.580 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.581 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.582 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.699 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.699 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.737 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.737 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.739 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [5366a372dff2b21e,] 2025-07-29 15:28:46.777 DEBUG 31496 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.422 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.423 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f5d0267d1bcfebc7
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.424 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.425 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (109ms)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:55 GMT
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.535 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.537 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.537 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.538 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.542 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.543 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.579 DEBUG 31496 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [f5d0267d1bcfebc7,] 2025-07-29 15:28:55.580 DEBUG 31496 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
