[kbc-elms-web:*********:7002] [,] 2025-07-25 15:01:07.852 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:06:07.869 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:11:07.875 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.254 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.259 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: cf0092a8f1c1377d
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.261 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.262 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.263 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.264 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.264 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.264 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.427 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (162ms)
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.427 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.427 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.427 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 07:11:32 GMT
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.427 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.428 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.428 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.428 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.429 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.429 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.438 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.438 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: cf0092a8f1c1377d
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.438 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.439 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.439 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.439 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.439 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.439 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.599 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (158ms)
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.599 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.599 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 07:11:32 GMT
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [cf0092a8f1c1377d,] 2025-07-25 15:11:32.601 DEBUG 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.963 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.965 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8ca8a9025b393ff3
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.965 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.965 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.965 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.965 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.966 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:33.966 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.057 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.058 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.058 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.058 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 07:11:34 GMT
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.059 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.066 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.066 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 8ca8a9025b393ff3
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.067 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.067 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.067 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.067 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.067 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.072 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (80ms)
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 07:11:34 GMT
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.154 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8ca8a9025b393ff3,] 2025-07-25 15:11:34.155 DEBUG 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.237 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.237 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: dd71ff7aaff4b276
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.237 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.237 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.238 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.238 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.238 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.238 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.311 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.312 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.313 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.314 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 07:11:34 GMT
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.314 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.315 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.315 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.315 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.317 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.318 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.328 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=dd71ff7aaff4b276,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.393 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.393 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.458 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.458 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.459 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.497 DEBUG 22208 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.498 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=dd71ff7aaff4b276, 耗时=170, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.535 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.538 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f849b3a4ba8ec90b
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.538 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.538 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.539 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.539 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.539 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.539 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.636 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (96ms)
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.636 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.637 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 07:11:41 GMT
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.638 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.639 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.643 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f849b3a4ba8ec90b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.643 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.644 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.678 DEBUG 22208 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.679 DEBUG 22208 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.717 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f849b3a4ba8ec90b, 耗时=75, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:16:07.878 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:21:07.893 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:26:07.900 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 15:31:07.910 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
