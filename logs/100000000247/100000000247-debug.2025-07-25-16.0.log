[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:12.126 DEBUG 17700 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:13.213 DEBUG 17700 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:13.275 DEBUG 17700 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.652 DEBUG 17700 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.779 DEBUG 17700 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.895 DEBUG 17700 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.944 DEBUG 17700 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.970 DEBUG 17700 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.018 DEBUG 17700 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.083 DEBUG 17700 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.100 DEBUG 17700 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.195 DEBUG 17700 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.304 DEBUG 17700 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.324 DEBUG 17700 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.611 DEBUG 17700 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.633 DEBUG 17700 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.649 DEBUG 17700 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.211 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.213 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: eac72608a74166cb
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.213 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.214 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.214 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.214 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.215 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:53.215 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.766 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.766 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a052240b84e9a2ed
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.766 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.766 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.767 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.767 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.770 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:53.771 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (965ms)
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (408ms)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.181 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:15:54 GMT
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:15:54 GMT
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.182 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.183 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.183 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.184 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.184 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.185 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.416 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.416 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: eac72608a74166cb
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.417 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.418 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.419 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.420 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.420 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.420 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.416 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.423 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: a052240b84e9a2ed
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.465 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.465 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.466 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.466 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.466 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.466 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.749 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (328ms)
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.750 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (282ms)
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.750 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.750 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.750 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.750 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.751 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:15:54 GMT
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.751 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:15:54 GMT
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.751 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.751 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.751 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.753 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.753 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.754 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.754 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.754 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [eac72608a74166cb,] 2025-07-25 16:15:54.754 DEBUG 17700 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.756 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.756 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a052240b84e9a2ed,] 2025-07-25 16:15:54.757 DEBUG 17700 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.729 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.730 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0f46b7467a24c544
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.731 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.731 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.731 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.732 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.732 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.732 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.826 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.826 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.826 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.826 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:15:55 GMT
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.827 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.827 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.827 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.827 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.830 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.830 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.839 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.839 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0f46b7467a24c544
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.840 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.840 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.840 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.841 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.841 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.841 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:15:55 GMT
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.925 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.926 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0f46b7467a24c544,] 2025-07-25 16:15:55.926 DEBUG 17700 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.963 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.963 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 731e0af419972184
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.974 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.974 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.976 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.980 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.980 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:55.982 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (74ms)
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:15:55 GMT
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.057 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.058 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.060 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.061 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.994 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.178 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.385 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.395 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.401 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.435 DEBUG 17700 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.944 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.945 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 20caf7f97035d099
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.946 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.946 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.946 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.946 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.947 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:00.947 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.048 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.049 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.049 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.050 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:16:01 GMT
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.051 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.052 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.052 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.053 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.053 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.054 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.071 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.072 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.107 DEBUG 17700 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.329 DEBUG 17700 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.549 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.550 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6ec9121d8f963015
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.550 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.550 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.551 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.551 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.551 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.551 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.648 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (97ms)
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:16:24 GMT
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.649 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.650 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.651 DEBUG 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.855 DEBUG 17700 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.892 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.893 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5c8103e7ac09830a
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.893 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.894 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.894 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.894 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.894 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:07.895 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.037 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (142ms)
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.037 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.037 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.038 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:18:08 GMT
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.038 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.038 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.039 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.039 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.039 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.039 DEBUG 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.043 DEBUG 17700 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.770 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5727d75774eca14d
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.771 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.879 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (107ms)
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.879 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.879 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.880 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:18:49 GMT
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.880 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.880 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.881 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.881 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.881 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.881 DEBUG 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.885 DEBUG 17700 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.543 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.544 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ece13e33d9ccd940
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.544 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.544 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.545 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.545 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.545 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.546 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.658 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.658 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.658 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.658 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:19:02 GMT
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.658 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.659 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.659 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.659 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.660 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.660 DEBUG 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.664 DEBUG 17700 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:20.895 DEBUG 23100 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:21.819 DEBUG 23100 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:21.872 DEBUG 23100 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.101 DEBUG 23100 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.210 DEBUG 23100 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.306 DEBUG 23100 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.378 DEBUG 23100 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.394 DEBUG 23100 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.431 DEBUG 23100 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.488 DEBUG 23100 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.502 DEBUG 23100 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.591 DEBUG 23100 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.678 DEBUG 23100 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.695 DEBUG 23100 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.923 DEBUG 23100 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.943 DEBUG 23100 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.959 DEBUG 23100 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.274 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.276 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d8df3f2b99838ba9
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.276 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.277 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.277 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.277 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.277 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.278 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.959 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (681ms)
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.959 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.959 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.960 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:21:46 GMT
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.960 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.960 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.961 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.961 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.961 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:46.962 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:47.730 DEBUG 23100 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.336 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.337 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: af0e4c76fe364edf
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.337 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.338 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.338 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.338 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.338 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.339 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.822 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (482ms)
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.822 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.822 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.822 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:26:53 GMT
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.823 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.823 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.823 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.823 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.824 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [af0e4c76fe364edf,] 2025-07-25 16:26:53.824 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.965 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.966 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f888e0265a1b6416
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.967 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.968 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.968 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.968 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.968 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:26:59.969 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.191 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (221ms)
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.191 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.191 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:27:00 GMT
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.192 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f888e0265a1b6416,] 2025-07-25 16:27:00.193 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.867 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.868 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 3046e7ff27e9c03b
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.868 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.869 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.869 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.869 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:09.869 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.151 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (282ms)
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.152 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.152 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.152 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:27:10 GMT
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.152 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.153 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.153 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.153 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.153 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [3046e7ff27e9c03b,] 2025-07-25 16:27:10.153 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.324 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.326 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 94a11e0913746387
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.327 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.328 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.328 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.329 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.330 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.331 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.509 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (177ms)
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.509 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.512 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.512 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:27:17 GMT
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.512 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.513 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.513 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.513 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.514 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [94a11e0913746387,] 2025-07-25 16:27:17.515 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.382 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.383 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1e631f1bf8fff3f4
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.383 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.384 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.386 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.387 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.387 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.388 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.507 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (117ms)
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.508 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.509 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.509 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:27:23 GMT
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.510 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.511 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.512 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.512 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.516 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1e631f1bf8fff3f4,] 2025-07-25 16:27:23.517 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.239 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.241 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 3ab39cf974d8a59e
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.245 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.246 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.247 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.247 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.248 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.248 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.357 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.357 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.357 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.358 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:27:25 GMT
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.358 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.358 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.359 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.359 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.359 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [3ab39cf974d8a59e,] 2025-07-25 16:27:25.359 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.345 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.346 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 84c3bb3f417a4d4a
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.346 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.346 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.346 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.347 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.347 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.347 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.471 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (123ms)
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.471 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.471 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.472 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:28:04 GMT
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.472 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.472 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.472 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.473 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.473 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [84c3bb3f417a4d4a,] 2025-07-25 16:28:04.473 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.357 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.358 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 82555e68ee220237
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.359 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.359 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.359 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.359 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.360 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.360 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.497 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (137ms)
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.498 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.499 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.499 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:29:28 GMT
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.499 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.499 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.499 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.500 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.500 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [82555e68ee220237,] 2025-07-25 16:29:28.500 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.781 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.782 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8a75fdd1d49b8512
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.782 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.782 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.783 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.783 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.783 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F03863","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.783 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.901 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (116ms)
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.901 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.902 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.902 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:29:57 GMT
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.902 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.904 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.904 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.905 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.905 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F03863","funcName":"策略执行批次","funcType":"2","funcLevel":4,"parentFuncId":"F03861","funcIcon":"icondt-186","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct/strategyBach","urlType":"2","sort":2,"isPublic":0,"applyId":"APP000188","funcAuths":[{"authCode":"sct:strategyBach:list","funcId":"F03863","authName":"策略批次执行列表","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.906 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2605-byte body)
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.919 DEBUG 23100 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.838 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.839 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b37535f345e63f67
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.839 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.840 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.840 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.840 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.840 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.840 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.966 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (125ms)
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.967 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.969 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.972 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:30:57 GMT
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.972 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.972 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.973 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.973 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.973 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.974 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:58.283 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:58.500 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:58.727 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:58.730 DEBUG 23100 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.789 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.791 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 85803de3c4d93d31
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.791 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.792 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.792 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.792 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.792 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.792 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.903 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.904 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.904 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.905 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:16 GMT
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.905 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.905 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.905 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.906 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.908 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.908 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.951 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 85803de3c4d93d31
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:16.952 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.035 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.036 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.036 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.036 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:31:16 GMT
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [85803de3c4d93d31,] 2025-07-25 16:31:17.037 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.106 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6135b15c938b361e
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.107 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.109 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (122ms)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:18 GMT
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.232 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.233 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.235 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.246 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.281 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.281 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 6135b15c938b361e
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.281 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.282 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.282 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.283 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.283 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.283 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.373 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.373 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.373 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.374 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:31:18 GMT
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.374 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.374 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.375 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.375 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.375 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6135b15c938b361e,] 2025-07-25 16:31:18.375 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.932 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.933 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: eab57a0cd23b5e4c
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.933 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.934 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.934 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.934 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.934 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:20.934 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.056 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.056 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.056 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.057 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:21 GMT
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.057 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.058 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.058 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.058 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.058 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.058 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.065 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.066 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: eab57a0cd23b5e4c
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.066 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.066 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.067 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.067 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.067 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.067 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.146 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (78ms)
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.146 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:31:21 GMT
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.147 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [eab57a0cd23b5e4c,] 2025-07-25 16:31:21.148 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.150 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.150 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e851df77ca71b205
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.150 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.150 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.150 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.152 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.152 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.152 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.231 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (78ms)
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.231 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.231 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.232 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:21 GMT
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.232 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.232 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.232 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.232 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.237 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.237 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.825 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.826 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.859 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.860 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.861 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.896 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.568 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.571 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: de63b0873063ca57
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.571 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.571 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.571 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.572 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.572 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.572 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.677 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (103ms)
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.677 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.677 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.678 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:24 GMT
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.678 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.678 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.679 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.679 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.681 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.681 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.705 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.705 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: enterprise(String)
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.742 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.743 DEBUG 23100 [http-nio-7002-exec-10] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 969eed6105bd9713
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.821 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.904 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.904 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.905 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.905 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:24 GMT
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.905 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.906 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.906 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.906 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.906 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.906 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.912 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.913 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_enterprise(String)
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.950 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.958 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.959 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:25.011 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:25.012 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_enterprise LIMIT ? 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:25.012 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:25.056 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.752 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.753 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 24d6bc68faef1562
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.753 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.753 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.754 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.754 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.754 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.754 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (120ms)
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:27 GMT
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.877 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.877 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.884 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.885 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.918 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.919 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.919 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.956 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.101 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.106 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c4bd3bfa37ec9de8
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.111 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.113 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.114 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.115 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.118 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.120 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (124ms)
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:31 GMT
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.248 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.249 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.249 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.252 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.253 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.289 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.290 DEBUG 23100 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.592 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 90362e0c82003035
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.593 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.713 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.713 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.713 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.713 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:31:33 GMT
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.713 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.714 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.714 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.714 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.714 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.714 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.718 DEBUG 23100 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.260 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.261 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b4dfebf869ad3f30
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.261 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.262 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.262 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.262 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.263 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.263 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.375 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.375 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:32:27 GMT
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.377 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.380 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.381 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.418 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.418 DEBUG 23100 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.410 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.412 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 3a4596c35d5611e4
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.412 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.412 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.415 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.415 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.415 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.415 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.542 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (125ms)
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.542 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.542 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.542 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:33:18 GMT
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.542 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.543 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.543 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.543 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.543 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.544 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.549 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.549 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.598 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.599 DEBUG 23100 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.916 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.918 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: dcebcbab22ba68a5
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.918 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.918 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.919 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.919 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.919 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:57.919 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.021 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.021 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:33:58 GMT
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.022 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.023 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.026 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.027 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.061 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.062 DEBUG 23100 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.705 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.706 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 56bbb43b161bc8d8
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.706 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.706 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.707 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.707 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.707 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.707 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.823 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.823 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.823 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.823 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:34:17 GMT
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.910 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.910 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.910 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.910 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.915 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.916 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.920 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.921 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.970 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.971 DEBUG 23100 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.391 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.392 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 50f48720aa775b7e
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.396 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.397 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.398 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.398 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.398 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.399 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:34:34 GMT
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.511 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.512 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.512 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.512 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.516 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.516 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.552 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.552 DEBUG 23100 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.658 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.659 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: beaa876f1e6b22e0
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.659 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.659 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.660 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.660 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.660 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.660 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.764 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (101ms)
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.764 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.764 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.765 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:34:47 GMT
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.765 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.765 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.765 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.765 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.776 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.776 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.782 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.783 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.820 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.821 DEBUG 23100 [http-nio-7002-exec-10] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.636 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.637 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a50a39ba727a784a
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.638 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.638 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.638 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.638 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.638 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.639 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.746 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (107ms)
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.746 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.746 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.746 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:35:00 GMT
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.747 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.747 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.747 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.747 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.748 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.748 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.752 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.753 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.788 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.789 DEBUG 23100 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.013 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.014 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e6684b0a1f7bf9da
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.014 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.014 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.015 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.015 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.015 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.015 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.138 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (122ms)
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.138 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.138 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.138 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:35:16 GMT
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.139 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.139 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.139 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.139 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.140 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.140 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.143 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.143 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.178 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.179 DEBUG 23100 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.384 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.386 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bbffc88b400e37c9
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.386 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.386 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.387 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.387 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.387 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.387 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.480 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (92ms)
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.480 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.480 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.481 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:35:22 GMT
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.481 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.481 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.481 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.481 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.482 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.482 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.486 DEBUG 23100 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.162 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.163 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: daac08cda3ac782f
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.163 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.164 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.164 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.164 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.164 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.164 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.270 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.271 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.272 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.276 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:35:36 GMT
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.276 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.277 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.277 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.277 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.277 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.278 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.287 DEBUG 23100 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.269 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.270 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e49406bfdb990782
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.270 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.270 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.271 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.271 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.271 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.271 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.371 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.372 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.372 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.372 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:36:20 GMT
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.373 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.373 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.375 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.376 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.377 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.428 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.429 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.465 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.466 DEBUG 23100 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.527 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.529 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6d30f6e9ca932bdc
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.560 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.561 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.561 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.561 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.567 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.567 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.676 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.677 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:36:22 GMT
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.678 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.679 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.679 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.684 DEBUG 23100 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.620 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.633 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9fdac824a07e00a7
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.633 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.633 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.633 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.633 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.634 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.634 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.762 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.762 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.762 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.763 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:40:46 GMT
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.764 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.764 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.765 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.765 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.766 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.766 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.815 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.815 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.856 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.858 DEBUG 23100 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.168 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.169 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 076423e27a87d4e5
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.170 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.170 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.170 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.171 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.171 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.171 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.250 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (78ms)
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:41:15 GMT
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.253 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.254 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.254 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.258 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.258 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.295 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.296 DEBUG 23100 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.892 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.893 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0123b85ce17209cf
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.894 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.894 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.895 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.895 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.895 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.895 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.974 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (78ms)
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:41:35 GMT
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.975 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.976 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.976 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.976 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.981 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.982 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:36.016 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:36.017 DEBUG 23100 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.711 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.712 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6a9ff11e86a1ccef
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.712 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.712 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.712 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.713 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.713 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.713 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.867 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (153ms)
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.867 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.868 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.869 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:42:51 GMT
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.869 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.871 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.871 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.872 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.872 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.873 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.938 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.938 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.976 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.978 DEBUG 23100 [http-nio-7002-exec-10] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.453 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.454 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0630623be56b818f
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.454 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.455 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.455 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.456 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.456 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.461 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (217ms)
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:43:26 GMT
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.679 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.680 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.681 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.681 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.688 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.688 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0630623be56b818f
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.688 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.688 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.688 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.689 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.689 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.689 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.853 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (163ms)
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.853 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.853 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.854 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:43:26 GMT
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.854 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.859 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.859 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.859 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.860 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0630623be56b818f,] 2025-07-25 16:43:26.861 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.641 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.642 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e8701149c90651cc
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.642 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.642 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.642 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.642 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.643 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.643 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.740 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (96ms)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.741 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.741 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.742 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:43:27 GMT
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.742 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.742 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.742 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.743 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.743 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.744 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.749 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.781 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e8701149c90651cc
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.785 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.785 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.785 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.785 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.786 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.786 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.874 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (88ms)
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:43:27 GMT
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.875 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e8701149c90651cc,] 2025-07-25 16:43:27.876 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.797 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.797 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 223b91b88ceb2df7
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.797 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.798 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.798 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.798 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.798 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.799 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (117ms)
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:43:50 GMT
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.916 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.917 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.917 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.919 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.924 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.924 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 223b91b88ceb2df7
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.925 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.925 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.925 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.925 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.926 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:50.926 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.022 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.022 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 4ae5c100378ad9e9
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.022 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.022 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.022 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.028 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.028 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:43:50 GMT
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.029 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.030 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.030 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.030 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [223b91b88ceb2df7,] 2025-07-25 16:43:51.030 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.108 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.108 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.108 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:43:50 GMT
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.109 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.116 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.116 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.149 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.150 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.150 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.189 DEBUG 23100 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.074 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.075 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e168f73331e97629
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.076 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.168 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:43:53 GMT
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.169 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.173 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.173 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.211 DEBUG 23100 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.212 DEBUG 23100 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.171 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.172 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b49ffc2dbf0f8f2c
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.172 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.173 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.173 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.173 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.173 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.173 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.551 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (377ms)
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.551 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.551 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.551 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:44:54 GMT
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.551 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.552 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.552 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.552 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.552 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.552 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.730 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.730 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.889 DEBUG 23100 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.890 DEBUG 23100 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.615 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.616 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2a461c97b35a5449
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.616 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.617 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.617 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.617 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.617 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.618 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.728 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (110ms)
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:44:57 GMT
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.729 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.731 DEBUG 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.734 DEBUG 23100 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.047 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.048 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 671167412939f3da
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.049 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.049 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.050 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.050 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.050 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.050 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.163 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (112ms)
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.163 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.163 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.163 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:45:01 GMT
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.165 DEBUG 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.169 DEBUG 23100 [http-nio-7002-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.039 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.040 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 74efacc11920ce17
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.040 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.040 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.041 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.041 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.041 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.041 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.208 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (166ms)
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.209 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.210 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.210 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:47:29 GMT
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.211 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.211 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.213 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.214 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.214 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.215 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.326 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.326 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.420 DEBUG 23100 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.425 DEBUG 23100 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.728 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.729 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5f87b40d52d5ec81
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.729 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.730 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.730 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.730 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.730 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.731 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.921 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (189ms)
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.921 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.921 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.922 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:47:32 GMT
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.922 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.922 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.922 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.922 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.923 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.923 DEBUG 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.926 DEBUG 23100 [http-nio-7002-exec-10] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.848 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.849 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 702138a6cf535462
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.849 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.849 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.881 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.881 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.881 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:07.881 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.015 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (134ms)
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:51:08 GMT
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.016 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.017 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.023 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.023 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 702138a6cf535462
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.023 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.023 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.023 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.024 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.024 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.024 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.113 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.113 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.113 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.113 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 08:51:08 GMT
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.113 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.114 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.114 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.115 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.116 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [702138a6cf535462,] 2025-07-25 16:51:08.117 DEBUG 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.280 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.282 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: db131671f300a7af
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.282 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.282 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.283 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.283 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.283 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.284 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.478 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (194ms)
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.478 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.479 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.479 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:51:10 GMT
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.483 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.485 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.485 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.486 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.486 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.488 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.538 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.539 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.599 DEBUG 23100 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.601 DEBUG 23100 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.463 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.464 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b4375f391399f3eb
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.464 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.464 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.464 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.464 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.465 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.465 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.557 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.557 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.557 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.557 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:51:15 GMT
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.557 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.558 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.558 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.558 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.558 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.558 DEBUG 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.561 DEBUG 23100 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:16.263 DEBUG 15372 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:17.336 DEBUG 15372 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:17.402 DEBUG 15372 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.661 DEBUG 15372 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.779 DEBUG 15372 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.877 DEBUG 15372 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.919 DEBUG 15372 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.937 DEBUG 15372 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.979 DEBUG 15372 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.045 DEBUG 15372 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.061 DEBUG 15372 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.139 DEBUG 15372 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.230 DEBUG 15372 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.248 DEBUG 15372 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.506 DEBUG 15372 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.526 DEBUG 15372 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.546 DEBUG 15372 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.597 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.598 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e3d52e2805373d4c
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.598 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.599 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.599 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.599 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.600 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:51.600 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.182 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (582ms)
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.183 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.183 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.183 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:54:52 GMT
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.183 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.183 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.184 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.184 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.184 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.185 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:53.137 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:53.364 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:53.544 DEBUG 15372 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:53.850 DEBUG 15372 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.802 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.803 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 88787bf2b051c4c3
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.803 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.804 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.804 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.804 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.805 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.805 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.922 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (116ms)
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.923 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.923 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.923 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:55:07 GMT
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.923 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.923 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.924 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.924 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.924 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:07.925 DEBUG 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:08.077 DEBUG 15372 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.095 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.096 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d4abc03272161338
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.097 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.097 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.098 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.098 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.098 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.099 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.251 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (151ms)
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.251 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.251 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.252 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 08:59:18 GMT
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.252 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.252 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.252 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.253 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.254 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.254 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.296 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.297 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.336 DEBUG 15372 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.337 DEBUG 15372 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
