[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:18:56.846 WARN 28616 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:18:57.090 WARN 28616 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:19:04.471 WARN 28616 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [44f17a6aba6144d4,] 2025-07-23 09:37:03.820 WARN 28616 [http-nio-7002-exec-4] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [c75413c5e76c1566,] 2025-07-23 09:37:03.820 WARN 28616 [http-nio-7002-exec-3] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [30bfb86a75e0dc6b,] 2025-07-23 09:37:04.834 WARN 28616 [http-nio-7002-exec-5] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [5d15bf8dbb3acbf7,] 2025-07-23 09:37:04.836 WARN 28616 [http-nio-7002-exec-6] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [74eeb389edf5c6fd,] 2025-07-23 09:38:09.774 WARN 28616 [http-nio-7002-exec-8] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [2d781ede6a908b5d,] 2025-07-23 09:38:09.774 WARN 28616 [http-nio-7002-exec-7] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [7fc0d3da71bb9a70,] 2025-07-23 09:38:10.730 WARN 28616 [http-nio-7002-exec-9] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [a367f7b1396e9d04,] 2025-07-23 09:38:10.730 WARN 28616 [http-nio-7002-exec-10] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [24622e7ab7c5150f,] 2025-07-23 09:38:51.551 WARN 28616 [http-nio-7002-exec-1] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [ac1053dd641750f8,] 2025-07-23 09:38:51.551 WARN 28616 [http-nio-7002-exec-2] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [0c6a59912bf21c5c,] 2025-07-23 09:38:52.416 WARN 28616 [http-nio-7002-exec-3] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [af10578a2701c82d,] 2025-07-23 09:38:52.416 WARN 28616 [http-nio-7002-exec-4] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:50:26.663 WARN 4116 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:50:26.832 WARN 4116 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-23 09:50:33.007 WARN 4116 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [bbf2beea327bff9c,] 2025-07-23 09:51:13.205 WARN 4116 [http-nio-7002-exec-1] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [e2035f87e35a9e05,] 2025-07-23 09:51:13.216 WARN 4116 [http-nio-7002-exec-2] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [596b69b58cb05838,] 2025-07-23 09:51:14.318 WARN 4116 [http-nio-7002-exec-4] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
[kbc-elms-web:10.78.8.1:7002] [6673de169fbb33c1,] 2025-07-23 09:51:14.318 WARN 4116 [http-nio-7002-exec-3] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/getWebUserInfo
[kbc-elms-web:10.78.8.1:7002] [79ac9010aa921e1f,] 2025-07-23 09:53:53.174 WARN 4116 [http-nio-7002-exec-6] org.springframework.web.servlet.PageNotFound No mapping for POST /api/bsc/tenant/getTenantUsers
