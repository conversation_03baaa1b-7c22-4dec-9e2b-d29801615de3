[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:47:41.588 DEBUG 42452 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:47:43.150 DEBUG 42452 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:47:43.237 DEBUG 42452 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:47:44.556 DEBUG 42452 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:47:44.692 DEBUG 42452 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:51.146 DEBUG 42824 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:52.783 DEBUG 42824 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:52.850 DEBUG 42824 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:54.310 DEBUG 42824 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:54.428 DEBUG 42824 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:54.787 DEBUG 42824 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:54.827 DEBUG 42824 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:54.977 DEBUG 42824 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.003 DEBUG 42824 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.046 DEBUG 42824 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.074 DEBUG 42824 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.243 DEBUG 42824 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.310 DEBUG 42824 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.356 DEBUG 42824 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.488 DEBUG 42824 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:55.642 DEBUG 42824 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:56.006 DEBUG 42824 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:56.037 DEBUG 42824 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:56.060 DEBUG 42824 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:56.083 DEBUG 42824 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 16:57:56.327 DEBUG 42824 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
