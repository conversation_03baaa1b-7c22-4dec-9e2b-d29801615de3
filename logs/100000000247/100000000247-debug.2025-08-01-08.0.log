[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:10.542 DEBUG 44740 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:12.414 DEBUG 44740 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:12.521 DEBUG 44740 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:14.505 DEBUG 44740 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:14.625 DEBUG 44740 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.069 DEBUG 44740 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.139 DEBUG 44740 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.333 DEBUG 44740 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.354 DEBUG 44740 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.384 DEBUG 44740 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.458 DEBUG 44740 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.631 DEBUG 44740 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.657 DEBUG 44740 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.679 DEBUG 44740 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.765 DEBUG 44740 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:15.888 DEBUG 44740 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:16.339 DEBUG 44740 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:16.398 DEBUG 44740 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:16.465 DEBUG 44740 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:16.528 DEBUG 44740 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 08:34:16.826 DEBUG 44740 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.555 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.557 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f7c46c6b1ea92254
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.559 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.559 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.560 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.560 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.561 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"da90bf43e61c8c1ac048fe4c55edd399","tenantId":"T0001","funcId":"F00111","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:25.561 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.201 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (638ms)
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.201 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.201 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.201 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:49:26 GMT
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.202 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.202 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.203 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.203 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.203 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [f7c46c6b1ea92254,] 2025-08-01 08:49:26.203 DEBUG 44740 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.896 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.898 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 564f4f00636369a1
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.899 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.900 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.900 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.900 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.901 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"26ffd5bec599e9dad60714f020b2292d","tenantId":"T0001","funcId":"F00111","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:50.902 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.187 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (284ms)
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.187 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.188 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:49:51 GMT
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.189 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F00111","funcName":"用户列表","funcType":"2","funcLevel":2,"parentFuncId":"F00110","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/ucs-web/userList","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000067","funcAuths":[{"authCode":"ucs:user:bindAgent","funcId":"F00111","authName":"绑定工号","sort":4},{"authCode":"ucs:user:bindAgentUrl","funcId":"F00111","authName":"生成绑定工号链接","sort":13},{"authCode":"ucs:user:blocked","funcId":"F00111","authName":"锁定用户","sort":2},{"authCode":"ucs:user:changeBind","funcId":"F00111","authName":"换绑代理人工号","sort":5},{"authCode":"ucs:user:changeReferee","funcId":"F00111","authName":"变更推荐人","sort":7},{"authCode":"ucs:user:channelagencySalesAdd","funcId":"F00111","authName":"添加渠道租户下的业务员","sort":17},{"authCode":"ucs:user:clearAuthStatus","funcId":"F00111","authName":"清除用户认证状态","sort":9},{"authCode":"ucs:user:clearReferee","funcId":"F00111","authName":"清除推荐人","sort":8},{"authCode":"ucs:user:destorySwitch","funcId":"F00111","authName":"账户注销开关","sort":3},{"authCode":"ucs:user:export:direct","funcId":"F00111","authName":"立即导出","authRoute":"/ucs/user/export","sort":10},{"authCode":"ucs:user:export:plan","funcId":"F00111","authName":"计划导出","authRoute":"/ucs/user/export","sort":14},{"authCode":"ucs:user:forcedKick","funcId":"F00111","authName":"用户详情强制踢出登录","sort":19},{"authCode":"ucs:user:gaiRiskManager","funcId":"F00111","authName":"团财风险等级流水","sort":25},{"authCode":"ucs:user:info","funcId":"F00111","authName":"查看","sort":15},{"authCode":"ucs:user:kickOut","funcId":"F00111","authName":"踢出登录","sort":18},{"authCode":"ucs:user:lockOrUnlockUser","funcId":"F00111","authName":"锁定解锁用户","sort":20},{"authCode":"ucs:user:matching","funcId":"F00111","authName":"用户匹配","sort":12},{"authCode":"ucs:user:modifyPhone","funcId":"F00111","authName":"修改登录手机号","sort":22},{"authCode":"ucs:user:openOrCloseSwitch","funcId":"F00111","authName":"打开关闭注销开关","sort":21},{"authCode":"ucs:user:riskManager","funcId":"F00111","authName":"风险等级管理","sort":24},{"authCode":"ucs:user:search","funcId":"F00111","authName":"查询","sort":16},{"authCode":"ucs:user:syncAgent","funcId":"F00111","authName":"同步代理人信息","sort":11},{"authCode":"ucs:user:unBind","funcId":"F00111","authName":"解绑代理人工号","sort":6},{"authCode":"ucs:user:updateResetDeviceIdSwitch","funcId":"F00111","authName":"重置设备标识开关","sort":23}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.190 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (4871-byte body)
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.442 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.672 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [564f4f00636369a1,] 2025-08-01 08:49:51.861 DEBUG 44740 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 0
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.546 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.548 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 24bb86e7cf136dc0
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.549 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.549 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.549 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.550 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.551 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"26ffd5bec599e9dad60714f020b2292d","tenantId":"T0001","funcId":"F03982","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.551 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.684 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (132ms)
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.684 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.684 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.684 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:51:08 GMT
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.684 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.685 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.686 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.686 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.686 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F03982","funcName":"产品资料维护","funcType":"2","funcLevel":3,"parentFuncId":"F03889","funcIcon":"icondt-103","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/chatbot/productInfo","urlType":"2","sort":9,"isPublic":0,"applyId":"APP000220","funcAuths":[{"authCode":"chatbot:productData:show","funcId":"F03982","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.686 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2593-byte body)
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.730 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.730 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [24bb86e7cf136dc0,] 2025-08-01 08:51:07.771 DEBUG 44740 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 0
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.280 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.284 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a7fd556fbb957ef4
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.285 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.288 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.291 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.291 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.291 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"26ffd5bec599e9dad60714f020b2292d","tenantId":"T0001","funcId":"F03982","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.291 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.452 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (159ms)
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.453 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.453 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.453 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:51:10 GMT
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.453 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.453 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.454 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.454 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.454 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F03982","funcName":"产品资料维护","funcType":"2","funcLevel":3,"parentFuncId":"F03889","funcIcon":"icondt-103","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/chatbot/productInfo","urlType":"2","sort":9,"isPublic":0,"applyId":"APP000220","funcAuths":[{"authCode":"chatbot:productData:show","funcId":"F03982","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.455 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2593-byte body)
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.458 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.459 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [a7fd556fbb957ef4,] 2025-08-01 08:51:09.493 DEBUG 44740 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 0
