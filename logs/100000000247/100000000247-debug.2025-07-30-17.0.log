[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:13.052 DEBUG 18664 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:14.174 DEBUG 18664 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:14.229 DEBUG 18664 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.444 DEBUG 18664 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.612 DEBUG 18664 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.631 DEBUG 18664 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.656 DEBUG 18664 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.675 DEBUG 18664 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.822 DEBUG 18664 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:15.879 DEBUG 18664 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.064 DEBUG 18664 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.141 DEBUG 18664 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.232 DEBUG 18664 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.250 DEBUG 18664 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.483 DEBUG 18664 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.502 DEBUG 18664 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-30 17:25:16.521 DEBUG 18664 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
