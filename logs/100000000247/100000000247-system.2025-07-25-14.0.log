{"@timestamp":"2025-07-25T14:00:58.882+08:00","traceId":"416f2b1f5f979364","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423258706,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"company\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":175,\"traceId\":\"416f2b1f5f979364\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:00:59.142+08:00","traceId":"63538b45de01e0f2","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423258994,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"company\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":145,\"traceId\":\"63538b45de01e0f2\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:02:13.626+08:00","traceId":"e5632b0e554b9e60","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423333509,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":116,\"traceId\":\"e5632b0e554b9e60\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:02:17.333+08:00","traceId":"1b7b48c5cec1af54","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423337258,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"1b7b48c5cec1af54\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:05:14.062+08:00","traceId":"c8320e33fe008a3f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423513944,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"company\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":118,\"traceId\":\"c8320e33fe008a3f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:05:14.291+08:00","traceId":"49b5b278becb60c9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423514176,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"company\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":114,\"traceId\":\"49b5b278becb60c9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:05:42.759+08:00","traceId":"b133b243c6ec0b01","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423542671,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":86,\"traceId\":\"b133b243c6ec0b01\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:05:44.965+08:00","traceId":"c8b3cee4381781a2","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753423544888,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":76,\"traceId\":\"c8b3cee4381781a2\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:25:37.607+08:00","traceId":"ad1d9c72462f7fce","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"修改\",\"appName\":\"kbc-elms-web\",\"createTime\":1753424737123,\"desc\":\"修改\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}\",\"remark\":\"\\r\\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\\r\\n### The error may exist in file [D:\\\\idea_workspace\\\\kbc-elms\\\\kbc-elms\\\\kbc-elms-entity\\\\target\\\\classes\\\\com\\\\kbao\\\\kbcelms\\\\dataTemplate\\\\entity\\\\DataTemplateMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select count(1) from t_tp_data_enterprise\\r\\n### Cause: java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":484,\"traceId\":\"ad1d9c72462f7fce\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:25:44.129+08:00","traceId":"d397333ede247198","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"修改\",\"appName\":\"kbc-elms-web\",\"createTime\":1753424743962,\"desc\":\"修改\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}\",\"remark\":\"\\r\\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\\r\\n### The error may exist in file [D:\\\\idea_workspace\\\\kbc-elms\\\\kbc-elms\\\\kbc-elms-entity\\\\target\\\\classes\\\\com\\\\kbao\\\\kbcelms\\\\dataTemplate\\\\entity\\\\DataTemplateMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select count(1) from t_tp_data_enterprise\\r\\n### Cause: java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'kbc_elms_sta.t_tp_data_enterprise' doesn't exist\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":167,\"traceId\":\"d397333ede247198\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:32:49.467+08:00","traceId":"202d03adf0176ce3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"修改\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425167483,\"desc\":\"修改\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1987,\"traceId\":\"202d03adf0176ce3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:32:50.150+08:00","traceId":"feaf492f7e87ac31","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425169733,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425168000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":417,\"traceId\":\"feaf492f7e87ac31\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:32:56.338+08:00","traceId":"bd1f6ef535002314","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425176119,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":7,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":8,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425168000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":218,\"traceId\":\"bd1f6ef535002314\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:33:46.935+08:00","traceId":"ab1a4a7d57d86d32","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"修改\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425226374,\"desc\":\"修改\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425168000}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":560,\"traceId\":\"ab1a4a7d57d86d32\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:33:47.473+08:00","traceId":"390d888b355b0201","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425227404,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425226000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"390d888b355b0201\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:33:49.890+08:00","traceId":"08c843897f4ec649","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425229809,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425226000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":79,\"traceId\":\"08c843897f4ec649\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:38:09.324+08:00","traceId":"83aec18b9068caf1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425489178,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425226000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":143,\"traceId\":\"83aec18b9068caf1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:39:09.081+08:00","traceId":"9b06fe82ccc83a7f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425549004,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425226000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":76,\"traceId\":\"9b06fe82ccc83a7f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:39:41.877+08:00","traceId":"e3562fed023b142a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"修改\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425581247,\"desc\":\"修改\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425226000}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":630,\"traceId\":\"e3562fed023b142a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:39:42.433+08:00","traceId":"07a68284d0f66a81","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425582359,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425581000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":73,\"traceId\":\"07a68284d0f66a81\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:41:22.019+08:00","traceId":"4c5193d4c99724ef","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425680911,\"desc\":\"查询模板字段列表\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"company\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1108,\"traceId\":\"4c5193d4c99724ef\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:41:28.311+08:00","traceId":"58caa74b8ffb6da7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425688264,\"desc\":\"查询模板字段列表\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"company\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":46,\"traceId\":\"58caa74b8ffb6da7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:41:59.451+08:00","traceId":"168ab98698075a2b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425718507,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":942,\"traceId\":\"168ab98698075a2b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:42:00.645+08:00","traceId":"f693b62efe4f4401","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425719901,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":743,\"traceId\":\"f693b62efe4f4401\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:42:45.767+08:00","traceId":"e14484d89f6247eb","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425765647,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":119,\"traceId\":\"e14484d89f6247eb\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:42:46.327+08:00","traceId":"0ab01ad050eebc4e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425766204,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":122,\"traceId\":\"0ab01ad050eebc4e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:04.866+08:00","traceId":"8f4dab687db5f597","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425784784,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":80,\"traceId\":\"8f4dab687db5f597\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:05.118+08:00","traceId":"989bc3175e3fb038","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425785006,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":112,\"traceId\":\"989bc3175e3fb038\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:10.814+08:00","traceId":"a7be1d5c4194d5c3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425790674,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425581000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":139,\"traceId\":\"a7be1d5c4194d5c3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:18.143+08:00","traceId":"d515c6ecedfdb03b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425797999,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":142,\"traceId\":\"d515c6ecedfdb03b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:18.522+08:00","traceId":"deb12f0a193055e6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425798311,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":210,\"traceId\":\"deb12f0a193055e6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:48.430+08:00","traceId":"1c8d5d9fc33ef392","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425828350,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":80,\"traceId\":\"1c8d5d9fc33ef392\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:48.668+08:00","traceId":"4f4bddc7229541d6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425828550,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":111,\"traceId\":\"4f4bddc7229541d6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:51.087+08:00","traceId":"7ddf2b5d46190181","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425831006,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":78,\"traceId\":\"7ddf2b5d46190181\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:43:51.327+08:00","traceId":"39876636ab2705a5","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425831221,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":105,\"traceId\":\"39876636ab2705a5\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:44:56.142+08:00","traceId":"688e782eb78498a0","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425895988,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":153,\"traceId\":\"688e782eb78498a0\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:44:56.365+08:00","traceId":"6dfcbeb90eda5bd3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425896252,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":112,\"traceId\":\"6dfcbeb90eda5bd3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:46:07.624+08:00","traceId":"bef3c6daedd72f1f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425967487,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":136,\"traceId\":\"bef3c6daedd72f1f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:46:07.906+08:00","traceId":"bfd13892cc4e14ed","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425967778,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":128,\"traceId\":\"bfd13892cc4e14ed\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:46:21.636+08:00","traceId":"abadbac159927ca5","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425981562,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"abadbac159927ca5\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:46:21.914+08:00","traceId":"7dea540d2c45b4e9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753425981799,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":114,\"traceId\":\"7dea540d2c45b4e9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:47:24.233+08:00","traceId":"d591b05589a942eb","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426044119,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":113,\"traceId\":\"d591b05589a942eb\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:47:24.516+08:00","traceId":"34d5ba2b488a7bbc","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426044408,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":108,\"traceId\":\"34d5ba2b488a7bbc\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:48:08.515+08:00","traceId":"b2215681ad323032","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426088313,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":202,\"traceId\":\"b2215681ad323032\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:48:09.039+08:00","traceId":"2027e98fd42af333","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426088706,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":333,\"traceId\":\"2027e98fd42af333\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:49:02.894+08:00","traceId":"dd60acf0f7e92f29","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426142816,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":77,\"traceId\":\"dd60acf0f7e92f29\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:49:03.127+08:00","traceId":"0981ee90cd748b34","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426143010,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":117,\"traceId\":\"0981ee90cd748b34\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:51:22.393+08:00","traceId":"f425444d8ca55ded","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426282249,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":143,\"traceId\":\"f425444d8ca55ded\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:51:22.611+08:00","traceId":"e26565ee50c77bea","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426282495,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":115,\"traceId\":\"e26565ee50c77bea\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:51:47.413+08:00","traceId":"7ae84024df388d05","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426307339,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753425581000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":73,\"traceId\":\"7ae84024df388d05\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:51:49.575+08:00","traceId":"d796c00bca026ae2","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426309501,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6031\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"select_options\\\":[]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6030\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6032\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6033\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68832511585c3e034bbc6034\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"d796c00bca026ae2\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T14:51:49.805+08:00","traceId":"a31471a3f1280783","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753426309689,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":115,\"traceId\":\"a31471a3f1280783\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
