{"@timestamp":"2025-08-01T11:04:34.709+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:09:34.736+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:33.653+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-9","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Start destroying common HttpClient","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:33.663+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-9","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Destruction of the end","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:33.669+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Unregistering application KBC-ELMS-WEB with eureka with status DOWN","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:53.278+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"background-preinit","class":"o.h.validator.internal.util.Version","msg":"HV000001: Hibernate Validator 6.1.7.Final","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.261+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.263+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.268+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.269+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.271+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.272+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.272+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.274+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.274+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.275+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.346+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.364+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:54.366+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:56.185+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:56 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T11:12:56.358+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:57 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T11:12:56.526+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:57 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T11:12:56.529+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.b.c.PropertySourceBootstrapConfiguration","msg":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]","stack_trace":""}
{"@timestamp":"2025-08-01T11:12:56.668+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"The following profiles are active: dev","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:03.545+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","msg":"Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.079+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.084+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.298+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 204 ms. Found 0 MongoDB repository interfaces.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.320+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.324+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:04.547+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 215 ms. Found 0 Redis repository interfaces.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.064+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.cloud.context.scope.GenericScope","msg":"BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.111+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.113+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.113+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.114+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.115+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.115+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.115+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.116+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.117+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.118+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.118+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.119+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.119+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.119+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.120+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:05.121+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.235+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.245+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.245+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat initialized with port(s): 7002 (http)","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.779+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Initializing ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.780+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardService","msg":"Starting service [Tomcat]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.780+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardEngine","msg":"Starting Servlet engine: [Apache Tomcat/9.0.53]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.949+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring embedded WebApplicationContext","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:06.950+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.s.c.ServletWebServerApplicationContext","msg":"Root WebApplicationContext: initialization completed in 10236 ms","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:08.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.d.s.b.a.DruidDataSourceAutoConfigure","msg":"Init DruidDataSource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.231+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} inited","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.mongodb.driver.cluster","msg":"Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.936+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-rtt-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.935+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.942+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.cluster","msg":"Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=118754500}","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:11.974+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:12.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:13.603+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.commons.filter.TraceContextFilter","msg":"Filter 'traceContextFilter' configured for use","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:15.086+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.auth.service.AuthService","msg":"interface com.kbao.kbcelms.auth.dao.AuthMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:15.152+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.bascode.service.BasCodeService","msg":"interface com.kbao.kbcelms.bascode.dao.BasCodeMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:15.559+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.f.a.AutowiredAnnotationBeanPostProcessor","msg":"Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:15.624+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:15.990+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.014+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.053+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.075+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.089+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.112+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.128+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.172+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.c.service.ConstantConfigService","msg":"interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.272+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.d.service.DataTemplateService","msg":"interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.643+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.g.s.GenAgentEnterpriseService","msg":"interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.676+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.industry.service.IndustryService","msg":"interface com.kbao.kbcelms.industry.dao.IndustryMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.755+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.roleauth.service.RoleAuthService","msg":"interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.831+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.role.service.RoleService","msg":"interface com.kbao.kbcelms.role.dao.RoleMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.855+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.u.service.UserTenantService","msg":"interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessLogService","msg":"interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.914+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.948+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.970+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userrole.service.UserRoleService","msg":"interface com.kbao.kbcelms.userrole.dao.UserRoleMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:16.991+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userorg.service.UserOrgService","msg":"interface com.kbao.kbcelms.userorg.dao.UserOrgMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.010+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.user.service.UserService","msg":"interface com.kbao.kbcelms.user.dao.UserMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.090+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.p.service.ProcessDefineService","msg":"interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.137+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.201+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessService","msg":"interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.252+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.325+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.419+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.445+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.455+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.465+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityTeamDivisionService","msg":"interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.487+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityTeamService","msg":"interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.517+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityDetailService","msg":"interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.538+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityService","msg":"interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.684+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityOrderService","msg":"interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:17.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.317+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.359+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.407+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.432+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.484+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.541+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.629+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.666+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.706+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.742+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.812+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.873+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.959+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:18.989+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.013+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.052+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.080+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.110+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.134+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.162+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.188+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.219+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.254+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.294+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.310+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.379+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.416+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.504+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.606+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.619+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.651+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.679+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.704+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.729+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.771+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.788+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.802+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.817+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.828+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.841+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.851+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.865+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.893+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.904+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.922+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.936+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:19.959+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:20.763+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","msg":"Exposing 2 endpoint(s) beneath base path '/actuator'","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:21.020+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.PropertySourcedRequestMappingHandlerMapping","msg":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:22.270+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.j.a.XxlJobClientAutoConfigure","msg":">>>>>>>>>>> xxl-job config init.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:26.213+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration","msg":"Eureka HTTP Client uses RestTemplate.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:26.717+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger","msg":"Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.159+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.178+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.198+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.217+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.238+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.257+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.276+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.297+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.320+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.343+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.362+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.382+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.404+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.427+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.453+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.462+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.489+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.512+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.530+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.556+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.579+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.603+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.630+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.657+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.694+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.714+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.754+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.760+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.765+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.777+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.785+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.893+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.902+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.909+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.924+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.939+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.945+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.952+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.959+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.966+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.972+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.981+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.985+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.992+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:28.998+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.002+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.011+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.016+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.027+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.031+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.036+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.040+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.044+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.048+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.062+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.066+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.070+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.074+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.081+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.092+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.107+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.114+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.123+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.132+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.138+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.145+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.153+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.515+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","msg":"Setting initial instance status as: STARTING","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.658+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Initializing Eureka in region us-east-1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.674+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Disable delta property : false","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Single vip registry refresh property : null","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Force full registry fetch : false","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application is null : false","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Registered Applications size is zero : true","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application version is -1: true","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:29.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Getting all instance registry info from the eureka server","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.849+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"The response status is 200","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.857+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Not registering with Eureka server per configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.868+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Discovery Client initialized at timestamp 1754018010866 with initial instances count: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.882+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Registering application KBC-ELMS-WEB with eureka with status UP","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.885+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Starting ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.972+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat started on port(s): 7002 (http) with context path ''","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.974+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","msg":"Updating port to 7002","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.975+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.975+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.975+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.976+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.976+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.976+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudDefaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.986+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.986+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.986+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:30.986+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.054+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.055+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Context refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.128+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Found 1 custom documentation plugin(s)","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.421+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.s.ApiListingReferenceScanner","msg":"Scanning for api listing references","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.867+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.901+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.p.ParameterDataTypeReader","msg":"Trying to infer dataType java.lang.String[]","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.964+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.980+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:31.989+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.103+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: addUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.114+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: listUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.115+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: removeUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.198+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.204+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.206+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: listUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.210+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.214+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.246+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.266+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_4","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.277+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.401+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"Started KbcElmsWebApplication in 40.693 seconds (JVM running for 42.637)","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.695+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(12)-*********","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring DispatcherServlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.695+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(12)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Initializing Servlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:32.715+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(12)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Completed initialization in 18 ms","stack_trace":""}
{"@timestamp":"2025-08-01T11:13:33.370+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(11)-*********","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.252+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.254+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: e18e736d3b75b5d9","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.255+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.255+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.255+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.256+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.256+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.256+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.339+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.340+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 6c8bbfbdb92d722b","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.340+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.340+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.341+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.341+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.341+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.342+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.849+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.849+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 0b184a2c68028450","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.849+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.850+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.850+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.850+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.850+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.850+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (650ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (142ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.993+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.996+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.995+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.996+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.998+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.998+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:38.999+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.535+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.535+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.535+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.748+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.748+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.748+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.918+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.918+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:39.922+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.027+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.028+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 6c8bbfbdb92d722b","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.028+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.028+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.029+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.033+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: e18e736d3b75b5d9","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.033+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.033+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.033+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.034+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.034+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.029+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.034+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.036+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.036+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.034+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (102ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.141+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.142+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.142+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.142+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.142+08:00","traceId":"e18e736d3b75b5d9","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.148+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (111ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.148+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.149+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.150+08:00","traceId":"6c8bbfbdb92d722b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.544+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=0b184a2c68028450,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.743+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:40.930+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:14:41.144+08:00","traceId":"0b184a2c68028450","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=0b184a2c68028450, 耗时=1009, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.197+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.198+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 7b82f0d213f35930","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.198+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.198+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.199+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.200+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.200+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.200+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.298+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (98ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.299+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.299+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.299+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:06 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.299+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.300+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.300+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.300+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.300+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.300+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.306+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.307+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.365+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.366+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=7b82f0d213f35930,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.367+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.405+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:05.445+08:00","traceId":"7b82f0d213f35930","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=7b82f0d213f35930, 耗时=79, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.081+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.082+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 26b65c7efed4cdfa","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.082+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.083+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.084+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.084+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.085+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.085+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.191+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (105ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.191+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.191+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.191+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:43 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.192+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.192+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.192+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.192+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.193+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.193+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.197+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.197+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.235+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.236+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=26b65c7efed4cdfa,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.236+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.275+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:15:43.313+08:00","traceId":"26b65c7efed4cdfa","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=26b65c7efed4cdfa, 耗时=77, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:29.762+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:44.785+08:00","traceId":"f9e432be3d2e7fc8","remoteIp":"127.0.0.1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"o.s.web.servlet.PageNotFound","msg":"Request method 'POST' not supported","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.776+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.778+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 96884663160f4ebd","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.779+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.780+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.780+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.781+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.781+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.781+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.938+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (156ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.940+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.940+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.940+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.941+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.941+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.941+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.941+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.941+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.942+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.984+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:18:59.985+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.026+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.030+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.030+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 96884663160f4ebd","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.031+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.031+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.031+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.031+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.032+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.032+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.104+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (73ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.104+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.104+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.104+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.105+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.106+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.106+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.106+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.106+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.106+08:00","traceId":"96884663160f4ebd","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.579+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.579+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 12f202063b37cdf5","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.580+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.712+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.712+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: fb461fd53e54b7c6","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.713+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.719+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.720+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.721+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.721+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.721+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.727+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.727+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.763+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 12f202063b37cdf5","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.766+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.767+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.767+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.842+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.842+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.842+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.842+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.842+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.845+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.845+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.845+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.845+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.845+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.846+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.846+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.847+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.847+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.848+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.848+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.848+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.848+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.851+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.852+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.853+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.853+08:00","traceId":"12f202063b37cdf5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.887+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.889+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=fb461fd53e54b7c6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.891+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.930+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:19:00.970+08:00","traceId":"fb461fd53e54b7c6","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=fb461fd53e54b7c6, 耗时=82, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.804+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.804+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.806+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 8df2c362dceb347d","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.806+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 96cb706f046fe9fd","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.806+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.807+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.808+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.808+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.808+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.808+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.808+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.928+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.928+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.929+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.929+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.929+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.929+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.929+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.930+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.930+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.930+08:00","traceId":"8df2c362dceb347d","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.946+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:39.947+08:00","traceId":"96cb706f046fe9fd","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.753+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.754+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 342cf46b6e2aa2a7","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.754+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.754+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.754+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.754+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.755+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.755+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: ef3268f8163babbe","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.757+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.840+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.840+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.840+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.840+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.840+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.841+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.841+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.841+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.842+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.842+08:00","traceId":"ef3268f8163babbe","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (87ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.845+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.846+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.846+08:00","traceId":"342cf46b6e2aa2a7","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.912+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.912+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 5bef080b9b112d90","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.913+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.967+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (52ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.967+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.967+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.967+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:41 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:40.968+08:00","traceId":"5bef080b9b112d90","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.577+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.578+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: c0e667e9a8ac112b","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 02e55d69093b6d61","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: *********9","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"3be3f792cb000614a02e65e6f3f8a7b1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.579+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.673+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.674+08:00","traceId":"02e55d69093b6d61","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.679+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.679+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.679+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:20:42.680+08:00","traceId":"c0e667e9a8ac112b","remoteIp":"*********9","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:23:29.783+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:28:29.791+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.326+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.330+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: c3ee8463295433ff","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.331+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.331+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.332+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.332+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.333+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.333+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.679+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (345ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.679+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.705+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.706+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.706+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.706+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.706+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.706+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.707+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.708+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.870+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:36.871+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.043+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.047+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.047+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: c3ee8463295433ff","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.047+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.047+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.047+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.048+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.048+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.048+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.132+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.132+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 0956d98654dbce00","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.132+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.133+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.133+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.133+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.133+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.133+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.240+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (191ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.240+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.240+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.240+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:37 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.240+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.245+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.246+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.247+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.247+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.247+08:00","traceId":"c3ee8463295433ff","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.489+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (356ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.489+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.489+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.489+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.489+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.490+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.490+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.490+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.490+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.502+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.505+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.507+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: af9ae3851acd2b21","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.530+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.531+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.531+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.692+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 0956d98654dbce00","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.701+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.702+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.703+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.734+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (203ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.734+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:38 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.735+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.737+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.743+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.744+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.888+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (185ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.888+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.889+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:38 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.890+08:00","traceId":"0956d98654dbce00","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.901+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.902+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=af9ae3851acd2b21,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:37.903+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:38.056+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:29:38.217+08:00","traceId":"af9ae3851acd2b21","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=af9ae3851acd2b21, 耗时=315, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.150+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.152+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: c80ec2f1bece9d79","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.152+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.154+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.154+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.154+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.154+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.154+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.282+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (126ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.282+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.282+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.283+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.284+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.323+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.324+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.388+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: c80ec2f1bece9d79","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.391+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.392+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.392+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.474+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (81ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.474+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.474+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.474+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.475+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.475+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.476+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.476+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.476+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.476+08:00","traceId":"c80ec2f1bece9d79","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.562+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.562+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 6a09c7970eb0a651","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.562+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.562+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.563+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.563+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.563+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.563+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.682+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (119ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.682+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.683+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.683+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.690+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.692+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.692+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.692+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.693+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.693+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.698+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.698+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.734+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.736+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.736+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: d7a1844987c8ac75","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.736+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.736+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.737+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.739+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.742+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 6a09c7970eb0a651","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.747+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.747+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.748+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.748+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.748+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.748+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.742+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.749+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.749+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.835+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (87ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.836+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.837+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.837+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.837+08:00","traceId":"6a09c7970eb0a651","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.861+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.861+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.861+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.861+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.861+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.862+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.862+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.862+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.862+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.862+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.865+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.866+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.906+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.913+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=d7a1844987c8ac75,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.915+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.952+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:12.993+08:00","traceId":"d7a1844987c8ac75","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=d7a1844987c8ac75, 耗时=80, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.633+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.635+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 1a0d9e997be735fc","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.635+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.635+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.636+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.636+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.636+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.636+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.729+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (92ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.729+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.729+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.729+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:51 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.730+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.730+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.730+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.731+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.731+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.731+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.733+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.734+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.777+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.778+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=1a0d9e997be735fc,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.779+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.814+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:31:50.856+08:00","traceId":"1a0d9e997be735fc","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=1a0d9e997be735fc, 耗时=78, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.307+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.308+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: a0eaca21d6795def","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.308+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.309+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.310+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.311+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.311+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.311+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:32:26 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.405+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.406+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.406+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.406+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.406+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.416+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.420+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.457+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.459+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=a0eaca21d6795def,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.459+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.497+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:32:25.537+08:00","traceId":"a0eaca21d6795def","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=a0eaca21d6795def, 耗时=78, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.303+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.339+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: e9a965fbba31fa24","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.357+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.357+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.357+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.357+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.360+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.360+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.498+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (137ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.498+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.500+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.501+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.501+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.506+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.506+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.565+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.570+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.570+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: e9a965fbba31fa24","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.570+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.570+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.570+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.571+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.571+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.571+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.651+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.653+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.653+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.654+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:14 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.655+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.655+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.655+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.657+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.658+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:13.659+08:00","traceId":"e9a965fbba31fa24","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.318+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.318+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 2eebb716d2155556","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.318+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.319+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.319+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.319+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.319+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.320+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.410+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.410+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.410+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.410+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.410+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.411+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.411+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.411+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.411+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.434+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.437+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.438+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.486+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.489+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.489+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 2eebb716d2155556","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.490+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.585+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (95ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.586+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.586+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.586+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:15 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.586+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.586+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.587+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.587+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.587+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.587+08:00","traceId":"2eebb716d2155556","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: b8017f712a0b2fd6","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:14.949+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:15 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.022+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.023+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.023+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.037+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.037+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.041+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.041+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.083+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.084+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=b8017f712a0b2fd6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.085+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.122+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:15.171+08:00","traceId":"b8017f712a0b2fd6","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=b8017f712a0b2fd6, 耗时=87, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:33:29.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.243+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.244+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 035d09a19dd3c788","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.245+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.354+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.355+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.358+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.358+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.358+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.358+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.358+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.359+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.359+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.362+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.370+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.371+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.426+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.430+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.430+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 035d09a19dd3c788","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.430+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.430+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.431+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.431+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.431+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.431+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.494+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (62ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.494+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.494+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.494+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.494+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.497+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.497+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.497+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.497+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.497+08:00","traceId":"035d09a19dd3c788","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.645+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.645+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: f933517e60e897d3","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.646+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.732+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (86ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.732+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.732+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.733+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.737+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.737+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.775+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.779+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.779+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: f933517e60e897d3","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.780+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.780+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.780+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.780+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.781+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.781+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (66ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.848+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.849+08:00","traceId":"f933517e60e897d3","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: e3bbfaeeb8a9e5c5","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.879+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"1d25ce427d27b69a93d14a90061733c1\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.880+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.963+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.963+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.963+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"***********\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.964+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.967+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:04.967+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:05.007+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 11","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:05.008+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=e3bbfaeeb8a9e5c5,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{}}} ","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:05.009+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"o.s.data.mongodb.core.MongoTemplate","msg":"Executing count: { \"tenantId\" : \"T0001\"} in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:05.046+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"tenantId\" : \"T0001\"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info","stack_trace":""}
{"@timestamp":"2025-08-01T11:34:05.083+08:00","traceId":"e3bbfaeeb8a9e5c5","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=e3bbfaeeb8a9e5c5, 耗时=75, resp={\"datas\":{\"endRow\":1,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"actualCapital\":\"20217.5636万人民币\",\"actualCapitalCurrency\":\"人民币\",\"alias\":\"百达精工\",\"approvedTime\":1749744000000,\"base\":\"zj\",\"bondName\":\"百达精工\",\"bondNum\":\"603331\",\"bondType\":\"A股\",\"businessScope\":\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\",\"city\":\"台州市\",\"companyId\":44302802,\"companyOrgType\":\"其他股份有限公司(上市)\",\"createTime\":1754011649790,\"creditCode\":\"913310007200456372\",\"district\":\"椒江区\",\"districtCode\":\"331002\",\"email\":\"<EMAIL>\",\"emailList\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"fromTime\":965577600000,\"historyNameList\":[\"台州市百达制冷有限公司\"],\"historyNames\":\"台州市百达制冷有限公司;\",\"id\":\"688c1802bf13d11683ba16ba\",\"industry\":\"通用设备制造业\",\"industryAll\":{\"category\":\"制造业\",\"categoryBig\":\"通用设备制造业\",\"categoryCodeFirst\":\"C\",\"categoryCodeSecond\":\"34\",\"categoryCodeThird\":\"346\",\"categoryMiddle\":\"烘炉、风机、包装等设备制造\",\"categorySmall\":\"\"},\"isMicroEnt\":0,\"legalPersonName\":\"施小友\",\"name\":\"浙江百达精工股份有限公司\",\"orgNumber\":\"72004563-7\",\"percentileScore\":9267,\"phoneNumber\":\"0576-88488860\",\"property3\":\"Zhejiang Baida Precision Manufacturing Corp.\",\"regCapital\":\"20217.5636万人民币\",\"regCapitalCurrency\":\"人民币\",\"regInstitute\":\"浙江省市场监督管理局\",\"regLocation\":\"浙江省台州市台州湾新区海城路2399号\",\"regNumber\":\"331000000000905\",\"regStatus\":\"存续\",\"scale\":\"大型\",\"socialStaffNum\":1246,\"staffNumRange\":\"1000-4999人\",\"tags\":\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\",\"taxNumber\":\"913310007200456372\",\"tenantId\":\"T0001\",\"toTime\":253392422400000,\"type\":1,\"updateTime\":1754013890600,\"updateTimes\":1752112735000,\"usedBondName\":\"\",\"websiteList\":\"http://www.baidapm.com\"}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":1,\"startRow\":1,\"total\":1},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:12.916+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-8","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Start destroying common HttpClient","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:12.918+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Unregistering application KBC-ELMS-WEB with eureka with status DOWN","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:12.919+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-8","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Destruction of the end","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:13.128+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"org.mongodb.driver.connection","msg":"Closed connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017 because the pool has been closed.","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:13.145+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} closed","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:13.291+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"com.netflix.discovery.DiscoveryClient","msg":"Shutting down DiscoveryClient ...","stack_trace":""}
{"@timestamp":"2025-08-01T11:38:13.295+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"com.netflix.discovery.DiscoveryClient","msg":"Completed shut down of DiscoveryClient","stack_trace":""}
