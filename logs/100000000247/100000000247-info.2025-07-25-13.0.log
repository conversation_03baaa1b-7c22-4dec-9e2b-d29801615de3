[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:00:03.889 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:05:03.909 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:10:03.912 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:15:03.928 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:20:03.938 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:25:03.942 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:30:03.956 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.950 INFO 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=59a37b797959fb4c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.934 INFO 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=59a37b797959fb4c, 耗时=1013, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"company","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.102 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6209021181f9db5a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.648 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6209021181f9db5a, 耗时=547, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.756 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a65e3d0229793fb2,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.888 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a65e3d0229793fb2, 耗时=133, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.431 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4eef5ed900dac642,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.505 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4eef5ed900dac642, 耗时=74, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.613 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d2bff4e58f39467c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.721 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d2bff4e58f39467c, 耗时=108, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:35:03.958 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.536 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1547337a8f230b83,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [1547337a8f230b83,] 2025-07-25 13:35:52.059 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1547337a8f230b83, 耗时=523, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"company","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.628 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9ac26e77e48125b1,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.912 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9ac26e77e48125b1, 耗时=284, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.154 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=258430d29fd30eec,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.947 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=258430d29fd30eec, 耗时=794, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.088 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=58ab4f0f50be9f4f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.206 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=58ab4f0f50be9f4f, 耗时=118, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.355 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=72a07329eae14b76,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.477 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=72a07329eae14b76, 耗时=122, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:40:03.969 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.232 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=873b38baab7f6e79,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.676 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=873b38baab7f6e79, 耗时=444, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.903 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bf554e6a5eb0ca79,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.391 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bf554e6a5eb0ca79, 耗时=488, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.313 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=80232ecddc9ccc08,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.853 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=80232ecddc9ccc08, 耗时=540, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [9093ed393475143c,] 2025-07-25 13:41:40.443 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9093ed393475143c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [9093ed393475143c,] 2025-07-25 13:41:40.777 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9093ed393475143c, 耗时=333, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.892 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f2ceda9340f56133,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [f2ceda9340f56133,] 2025-07-25 13:41:52.126 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f2ceda9340f56133, 耗时=234, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.679 INFO 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e6a94b1eaf655351,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:53.038 INFO 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e6a94b1eaf655351, 耗时=359, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.912 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6b1967f9012c7464,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [6b1967f9012c7464,] 2025-07-25 13:44:23.043 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6b1967f9012c7464, 耗时=131, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.184 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=39a9354e4ae3d983,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.302 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=39a9354e4ae3d983, 耗时=119, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:45:03.983 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.941 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7c56a6cfb6ae7778,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:08.058 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7c56a6cfb6ae7778, 耗时=117, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.187 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b958da8c5a480d28,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.306 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b958da8c5a480d28, 耗时=119, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:50:03.990 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.166 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9171f6ef2770422e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.299 INFO 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9171f6ef2770422e, 耗时=133, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.426 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0c2f217a316d8518,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.559 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0c2f217a316d8518, 耗时=134, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.940 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6d9dafd92b045204,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:10.78.8.1:7002] [6d9dafd92b045204,] 2025-07-25 13:54:22.069 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6d9dafd92b045204, 耗时=129, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.221 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a369618f126c2e41,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:10.78.8.1:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.334 INFO 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a369618f126c2e41, 耗时=113, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 13:55:04.003 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
