[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:13.603 DEBUG 27812 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.086 DEBUG 27812 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.152 DEBUG 27812 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.172 DEBUG 27812 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.272 DEBUG 27812 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.643 DEBUG 27812 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.676 DEBUG 27812 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.813 DEBUG 27812 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.831 DEBUG 27812 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.855 DEBUG 27812 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.879 DEBUG 27812 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.970 DEBUG 27812 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.991 DEBUG 27812 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.010 DEBUG 27812 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.090 DEBUG 27812 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.201 DEBUG 27812 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.465 DEBUG 27812 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.487 DEBUG 27812 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.517 DEBUG 27812 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.538 DEBUG 27812 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.684 DEBUG 27812 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.252 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.254 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e18e736d3b75b5d9
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.339 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6c8bbfbdb92d722b
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.342 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0b184a2c68028450
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (650ms)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (142ms)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.996 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.996 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.998 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.998 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.918 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.918 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.922 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.027 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 6c8bbfbdb92d722b
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.029 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.029 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e18e736d3b75b5d9
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.036 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.036 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.148 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.148 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.149 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.743 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.930 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.197 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7b82f0d213f35930
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.199 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.298 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (98ms)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:06 GMT
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.306 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.307 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.365 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.367 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.405 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.081 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.082 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 26b65c7efed4cdfa
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.082 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.083 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.084 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.084 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.085 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.085 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (105ms)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:43 GMT
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.193 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.193 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.197 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.197 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.235 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.236 DEBUG 27812 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.275 DEBUG 27812 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.776 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.778 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 96884663160f4ebd
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.779 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.780 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.780 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.938 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (156ms)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.942 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.984 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.985 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.026 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.030 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.030 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 96884663160f4ebd
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.032 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.032 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.105 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.579 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.579 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 12f202063b37cdf5
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.712 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.712 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: fb461fd53e54b7c6
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.719 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.727 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.727 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.763 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 12f202063b37cdf5
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.767 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.767 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.846 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.846 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.847 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.847 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.851 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.852 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.853 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.853 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.887 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.891 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.930 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.804 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.804 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8df2c362dceb347d
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 96cb706f046fe9fd
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.928 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.928 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.946 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.753 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 342cf46b6e2aa2a7
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.755 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.755 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ef3268f8163babbe
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.842 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.842 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.846 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.846 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.912 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.912 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5bef080b9b112d90
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (52ms)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:41 GMT
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.577 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.578 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c0e667e9a8ac112b
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 02e55d69093b6d61
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.673 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.326 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.330 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c3ee8463295433ff
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.331 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.331 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.332 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.332 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.333 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.333 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.679 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (345ms)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.679 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.705 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.707 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.708 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.870 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.871 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.043 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c3ee8463295433ff
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0956d98654dbce00
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (191ms)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.245 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.246 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (356ms)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.502 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.505 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.507 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: af9ae3851acd2b21
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.531 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.531 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.692 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0956d98654dbce00
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.702 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.703 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.734 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (203ms)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.734 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:38 GMT
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.743 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.744 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.888 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (185ms)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.888 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.889 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:38 GMT
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.901 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.903 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:38.056 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.150 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.152 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c80ec2f1bece9d79
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.152 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (126ms)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.284 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.323 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.324 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.388 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c80ec2f1bece9d79
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.392 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.392 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.475 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.475 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6a09c7970eb0a651
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.682 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (119ms)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.682 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.683 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.683 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.690 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.693 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.693 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.698 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.698 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.734 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d7a1844987c8ac75
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.737 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.739 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.742 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 6a09c7970eb0a651
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.747 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.747 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.742 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.749 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.749 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.835 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.865 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.866 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.906 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.915 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.952 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.633 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1a0d9e997be735fc
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (92ms)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:51 GMT
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.733 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.734 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.777 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.779 DEBUG 27812 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.814 DEBUG 27812 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.307 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.308 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a0eaca21d6795def
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.308 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.309 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.310 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:32:26 GMT
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.416 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.420 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.457 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.459 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.497 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.303 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.339 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e9a965fbba31fa24
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.360 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.360 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.498 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (137ms)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.498 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.501 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.501 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.506 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.506 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.565 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e9a965fbba31fa24
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.651 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.653 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.653 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.654 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.657 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.658 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.659 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2eebb716d2155556
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.320 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.434 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.437 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.438 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.486 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.489 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.489 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 2eebb716d2155556
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.585 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (95ms)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:15 GMT
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b8017f712a0b2fd6
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:15 GMT
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.023 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.023 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.037 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.037 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.041 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.041 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.083 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.085 DEBUG 27812 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.122 DEBUG 27812 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.243 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.244 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 035d09a19dd3c788
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.354 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.355 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.359 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.359 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.362 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.370 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.371 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.426 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 035d09a19dd3c788
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (62ms)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.645 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.645 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f933517e60e897d3
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (86ms)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.775 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.779 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.779 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: f933517e60e897d3
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.781 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.781 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (66ms)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.849 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e3bbfaeeb8a9e5c5
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.880 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.967 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.967 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.007 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.009 DEBUG 27812 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.046 DEBUG 27812 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.751 DEBUG 8636 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
