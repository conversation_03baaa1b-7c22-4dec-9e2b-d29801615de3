{"@timestamp":"2025-07-29T15:23:05.433+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"background-preinit","class":"o.h.validator.internal.util.Version","msg":"HV000001: Hibernate Validator 6.1.7.Final","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.660+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.661+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.695+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.695+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.697+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.699+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.699+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.699+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.702+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.702+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.838+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.845+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:06.847+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:07.142+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"The following profiles are active: dev","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.091+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.094+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.265+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 162 ms. Found 0 MongoDB repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.317+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.322+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:17.498+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 146 ms. Found 0 Redis repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.121+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.cloud.context.scope.GenericScope","msg":"BeanFactory id=c0b4a6dd-5ccf-3f6b-8b35-d7153d573574","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.171+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.172+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.172+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.172+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.173+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.175+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.176+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.176+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.176+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:18.176+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:19.504+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:19.512+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:19.512+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.092+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat initialized with port(s): 7002 (http)","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.125+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Initializing ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.126+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardService","msg":"Starting service [Tomcat]","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.127+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardEngine","msg":"Starting Servlet engine: [Apache Tomcat/9.0.53]","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.313+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring embedded WebApplicationContext","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:20.315+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.s.c.ServletWebServerApplicationContext","msg":"Root WebApplicationContext: initialization completed in 13104 ms","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:22.147+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.d.s.b.a.DruidDataSourceAutoConfigure","msg":"Init DruidDataSource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:26.448+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} inited","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:26.905+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.mongodb.driver.cluster","msg":"Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:27.400+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:27.400+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-rtt-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:27.406+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688876eec3add91428f4e788', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.cluster","msg":"Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=269691400}","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:27.460+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:28.009+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:29.070+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.commons.filter.TraceContextFilter","msg":"Filter 'traceContextFilter' configured for use","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:30.490+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.auth.service.AuthService","msg":"interface com.kbao.kbcelms.auth.dao.AuthMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:30.580+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.bascode.service.BasCodeService","msg":"interface com.kbao.kbcelms.bascode.dao.BasCodeMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.008+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.f.a.AutowiredAnnotationBeanPostProcessor","msg":"Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.076+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.852+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.922+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.945+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:31.961+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.d.service.DataTemplateService","msg":"interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.181+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.238+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.roleauth.service.RoleAuthService","msg":"interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.256+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.role.service.RoleService","msg":"interface com.kbao.kbcelms.role.dao.RoleMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.299+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.u.service.UserTenantService","msg":"interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.321+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityService","msg":"interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.361+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.394+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.453+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.p.service.ProcessDefineService","msg":"interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.510+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.user.service.UserService","msg":"interface com.kbao.kbcelms.user.dao.UserMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.563+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.603+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.638+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.700+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityDetailService","msg":"interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.819+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityOrderService","msg":"interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.890+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.955+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessService","msg":"interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:32.980+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessLogService","msg":"interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.085+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.183+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.285+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.315+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityTeamService","msg":"interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.337+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityTeamDivisionService","msg":"interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.355+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userrole.service.UserRoleService","msg":"interface com.kbao.kbcelms.userrole.dao.UserRoleMapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.867+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.919+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:33.974+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.004+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.093+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.175+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.232+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.268+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.316+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.361+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.415+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.472+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.523+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.559+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.587+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.636+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.664+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.688+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.715+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.743+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.778+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.808+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.855+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.881+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.911+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:34.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.006+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.049+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.119+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.353+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.370+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.433+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.533+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.579+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.644+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.695+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.720+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.735+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.770+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.790+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.808+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.827+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.844+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.863+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.898+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.914+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.932+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.946+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:35.963+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:36.897+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","msg":"Exposing 2 endpoint(s) beneath base path '/actuator'","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:37.179+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.PropertySourcedRequestMappingHandlerMapping","msg":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:38.620+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.j.a.XxlJobClientAutoConfigure","msg":">>>>>>>>>>> xxl-job config init.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:40.634+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration","msg":"Eureka HTTP Client uses RestTemplate.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:41.033+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger","msg":"Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.370+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.389+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.410+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.426+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.446+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.463+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.482+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.503+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.524+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.545+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.565+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.594+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.625+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.656+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.683+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.689+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.708+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.726+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.744+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.766+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.783+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.807+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.841+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.873+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.901+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.955+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.961+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.965+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.983+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.989+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:42.996+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.000+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.010+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.021+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.027+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.032+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.043+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.050+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.058+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.068+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.076+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.087+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.093+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.099+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.110+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.116+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.127+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.131+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.136+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.140+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.146+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.150+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.156+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.162+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.165+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.169+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.180+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.191+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.201+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.205+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.209+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.215+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.223+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.228+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.235+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","msg":"Setting initial instance status as: STARTING","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.758+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Initializing Eureka in region us-east-1","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.774+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Disable delta property : false","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Single vip registry refresh property : null","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Force full registry fetch : false","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application is null : false","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Registered Applications size is zero : true","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application version is -1: true","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:43.880+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Getting all instance registry info from the eureka server","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.302+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"The response status is 200","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.309+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Not registering with Eureka server per configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.320+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Discovery Client initialized at timestamp 1753773825317 with initial instances count: 9","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.332+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Registering application KBC-ELMS-WEB with eureka with status UP","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.334+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Starting ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.432+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat started on port(s): 7002 (http) with context path ''","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.438+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","msg":"Updating port to 7002","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.438+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.439+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.439+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.441+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.441+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.441+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudDefaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.441+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.442+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.442+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.442+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.444+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.444+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.517+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.518+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.527+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.529+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.529+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.529+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.529+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.538+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Context refreshed","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:45.653+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Found 1 custom documentation plugin(s)","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.051+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.s.ApiListingReferenceScanner","msg":"Scanning for api listing references","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.628+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.679+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.684+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.689+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.778+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.784+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.791+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.796+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.824+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.837+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:46.856+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:47.029+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"Started KbcElmsWebApplication in 43.87 seconds (JVM running for 49.989)","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:47.779+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring DispatcherServlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:47.782+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Initializing Servlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:47.821+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Completed initialization in 39 ms","stack_trace":""}
{"@timestamp":"2025-07-29T15:23:48.432+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(2)-*********","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.418+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.418+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.423+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 35c024592394ea9d","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.423+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 0e0d60cc47195ca1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.425+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.425+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.427+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.427+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.429+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.429+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.429+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.429+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.430+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.430+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.431+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:50.431+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.168+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.167+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.168+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.168+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.169+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.169+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.169+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:51 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.170+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.172+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.171+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.174+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.176+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.176+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.176+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.176+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.365+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.365+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 35c024592394ea9d","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 0e0d60cc47195ca1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.366+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.367+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (85ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.453+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.454+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.454+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.454+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.454+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:51 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.455+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.455+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.456+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.456+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.457+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.457+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.457+08:00","traceId":"0e0d60cc47195ca1","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.455+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.457+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:51.457+08:00","traceId":"35c024592394ea9d","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.749+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.751+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: c61aaeb848f0b7a5","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.751+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.751+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.751+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.752+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.752+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.752+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.871+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.871+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.871+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.872+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.873+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.880+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.880+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: c61aaeb848f0b7a5","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.880+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.884+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.886+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.886+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.889+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.889+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.948+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (58ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.948+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.948+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:27:55 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:54.949+08:00","traceId":"c61aaeb848f0b7a5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.122+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.125+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 1c8ab86b4fee06b8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.125+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.125+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.129+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.129+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.129+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.129+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:55 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.212+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.217+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.218+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.770+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=1c8ab86b4fee06b8,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={\"param1\":{\"templateCode\":\"enterprise\"}} ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:55.983+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.d.d.D.selectByBizCode","msg":"==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.205+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.d.d.D.selectByBizCode","msg":"==> Parameters: enterprise(String)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.377+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.k.d.d.D.selectByBizCode","msg":"<==      Total: 1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.553+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"templateId\" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.855+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=1c8ab86b4fee06b8, 耗时=1397, resp={\"datas\":[{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"name\",\"fieldLength\":\"50\",\"fieldName\":\"企业名称\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2d\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":1,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"enterpriseCode\",\"fieldLength\":\"50\",\"fieldName\":\"社会信用代码\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2e\",\"isIndex\":\"1\",\"required\":\"0\",\"showType\":\"1\",\"sort\":2,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"type\",\"fieldLength\":\"1\",\"fieldName\":\"企业类型\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2f\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":3,\"templateId\":3},{\"additional\":{\"inputUnit\":\"人1\"},\"change\":\"1\",\"fieldCode\":\"staffSize\",\"fieldName\":\"人员规模\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f30\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":4,\"templateId\":3},{\"additional\":{\"selectOptions\":[\"1\",\"2\",\"3\",\"4\",\"5\"]},\"change\":\"1\",\"fieldCode\":\"city\",\"fieldLength\":\"30\",\"fieldName\":\"所在城市\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f31\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"3\",\"sort\":5,\"templateId\":3},{\"additional\":{},\"change\":\"0\",\"fieldCode\":\"industryId\",\"fieldName\":\"所属行业ID\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f32\",\"isIndex\":\"1\",\"required\":\"0\",\"showType\":\"0\",\"sort\":6,\"templateId\":3},{\"additional\":{\"inputUnit\":\"万\"},\"change\":\"1\",\"fieldCode\":\"annualIncome\",\"fieldName\":\"年收入\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f33\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":7,\"templateId\":3},{\"additional\":{},\"change\":\"0\",\"fieldCode\":\"isVerified\",\"fieldLength\":\"1\",\"fieldName\":\"是否验真\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f34\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"0\",\"sort\":8,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"enterpriseContacter\",\"fieldLength\":\"20\",\"fieldName\":\"企业联系人\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f35\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":9,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"contacterPhone\",\"fieldLength\":\"20\",\"fieldName\":\"联系人电话\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f36\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":10,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"remark\",\"fieldLength\":\"300\",\"fieldName\":\"备注信息\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f37\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"6\",\"sort\":11,\"templateId\":3}],\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.902+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.903+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: d57dd05f6ecb4d0b","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.903+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.903+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.903+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.903+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.904+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.904+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.996+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.998+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.998+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.998+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:27:57 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:56.998+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.000+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.001+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.002+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.002+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.003+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.014+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=d57dd05f6ecb4d0b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{\"templateCode\":\"enterprise\"}}} ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.015+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.isExistTable","msg":"==>  Preparing: select count(1) from information_schema.tables where table_name = ? ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.015+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.isExistTable","msg":"==> Parameters: t_tp_data_enterprise(String)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.060+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.isExistTable","msg":"<==      Total: 1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.499+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.getDataList_COUNT","msg":"==>  Preparing: SELECT count(0) FROM t_tp_data_enterprise ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.500+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.getDataList_COUNT","msg":"==> Parameters: ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.540+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.D.getDataList_COUNT","msg":"<==      Total: 1","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.542+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.DataTemplateMapper.getDataList","msg":"==>  Preparing: select * from t_tp_data_enterprise LIMIT ? ","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.542+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.DataTemplateMapper.getDataList","msg":"==> Parameters: 10(Integer)","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.585+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.k.d.d.DataTemplateMapper.getDataList","msg":"<==      Total: 4","stack_trace":""}
{"@timestamp":"2025-07-29T15:27:57.593+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=d57dd05f6ecb4d0b, 耗时=583, resp={\"datas\":{\"endRow\":4,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"enterpriseCode\":\"X1111\",\"annualIncome\":10000000,\"city\":\"北京\",\"isVerified\":\"1\",\"enterpriseContacter\":\"张三\",\"updateTime\":1753425602000,\"type\":\"A\",\"industryId\":1,\"staffSize\":10000,\"isDeleted\":0,\"contacterPhone\":\"13255554444\",\"createTime\":1753425602000,\"name\":\"北京科技有限公司\",\"tenantId\":\"T0001\",\"id\":1},{\"enterpriseCode\":\"X222\",\"annualIncome\":2222222,\"city\":\"上海\",\"isVerified\":\"0\",\"enterpriseContacter\":\"李四\",\"updateTime\":1753425602000,\"type\":\"B\",\"industryId\":2,\"staffSize\":2333,\"isDeleted\":0,\"contacterPhone\":\"13944445555\",\"createTime\":1753425602000,\"name\":\"上海科技有限公司\",\"tenantId\":\"T0001\",\"id\":2},{\"enterpriseCode\":\"X2223\",\"annualIncome\":33333,\"city\":\"湖北\",\"isVerified\":\"0\",\"enterpriseContacter\":\"王五\",\"updateTime\":1753425602000,\"type\":\"C\",\"industryId\":3,\"staffSize\":45,\"isDeleted\":0,\"contacterPhone\":\"14257884444\",\"createTime\":1753425602000,\"name\":\"湖北科技有限公司\",\"tenantId\":\"T0001\",\"id\":3},{\"enterpriseCode\":\"X4433\",\"annualIncome\":444444,\"city\":\"天津\",\"isVerified\":\"0\",\"enterpriseContacter\":\"赵六\",\"updateTime\":1753425602000,\"type\":\"D\",\"industryId\":4,\"staffSize\":6777,\"isDeleted\":0,\"contacterPhone\":\"17845454545\",\"createTime\":1753425602000,\"name\":\"天津科技有限公司\",\"tenantId\":\"T0001\",\"id\":4}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":4,\"startRow\":1,\"total\":4},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:43.884+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.130+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.132+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 25d6b066e6f17076","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.133+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.134+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.134+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.134+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.134+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.135+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.292+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (157ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.292+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.293+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.296+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.296+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.296+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.297+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.297+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.297+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.298+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.307+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] app_trace_id: 25d6b066e6f17076","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Length: 40","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"userId\":\"U000162\",\"appId\":\"APP000238\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.309+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.398+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (88ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] date: Tue, 29 Jul 2025 07:28:46 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.400+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.401+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.401+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] {\"resp_code\":1,\"resp_msg\":\"登录信息已失效, 请重新登录!\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.401+08:00","traceId":"25d6b066e6f17076","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.kbao.kbcbsc.client.TenantClientService","msg":"[TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.486+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 5366a372dff2b21e","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.487+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.569+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.570+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.570+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.571+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:46 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.572+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.572+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.573+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.580+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.581+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.582+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.634+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=5366a372dff2b21e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={\"param1\":{\"pageNum\":1,\"pageSize\":10,\"param\":{\"status\":\"\",\"templateName\":\"\",\"type\":\"\"}}} ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.699+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.d.D.selectAll_COUNT","msg":"==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.699+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.d.D.selectAll_COUNT","msg":"==> Parameters: ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.737+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.d.D.selectAll_COUNT","msg":"<==      Total: 1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.737+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.dao.DataTemplateMapper.selectAll","msg":"==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.739+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.dao.DataTemplateMapper.selectAll","msg":"==> Parameters: 10(Integer)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.777+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.k.d.dao.DataTemplateMapper.selectAll","msg":"<==      Total: 2","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:46.777+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=5366a372dff2b21e, 耗时=152, resp={\"datas\":{\"endRow\":2,\"hasNextPage\":false,\"hasPreviousPage\":false,\"isFirstPage\":true,\"isLastPage\":true,\"list\":[{\"bizCode\":\"test\",\"createId\":\"U000162\",\"createTime\":1753336762000,\"id\":1,\"isDeleted\":0,\"remark\":\"xx\",\"status\":\"1\",\"templateName\":\"test\",\"tenantId\":\"T0001\",\"type\":\"xx\",\"updateId\":\"U000162\",\"updateTime\":1753342622000},{\"bizCode\":\"enterprise\",\"createId\":\"U000162\",\"createTime\":1753354888000,\"id\":3,\"isDeleted\":0,\"remark\":\"企业表测试\",\"status\":\"1\",\"templateName\":\"企业信息表\",\"tenantId\":\"T0001\",\"type\":\"企业信息\",\"updateId\":\"U000162\",\"updateTime\":1753436363000}],\"navigateFirstPage\":1,\"navigateLastPage\":1,\"navigatePages\":8,\"navigatepageNums\":[1],\"nextPage\":0,\"pageNum\":1,\"pageSize\":10,\"pages\":1,\"prePage\":0,\"size\":2,\"startRow\":1,\"total\":2},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.422+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.423+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: f5d0267d1bcfebc7","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.424+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.424+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.424+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.425+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.425+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"0b52b79cc9b86038ccff777896449ac2\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.425+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.535+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (109ms)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.535+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.535+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.535+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Tue, 29 Jul 2025 07:28:55 GMT","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.535+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.537+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.537+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.538+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.538+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F04010\",\"funcName\":\"指标列表\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F04007\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000238\",\"funcAuths\":[{\"authCode\":\"algo:indicator:show\",\"funcId\":\"F04010\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.538+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2580-byte body)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.541+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"开始请求，transId=f5d0267d1bcfebc7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={\"param1\":{\"id\":3}} ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.542+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.d.d.D.selectByPrimaryKey","msg":"==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? ","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.543+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.d.d.D.selectByPrimaryKey","msg":"==> Parameters: 3(Integer)","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.579+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.k.d.d.D.selectByPrimaryKey","msg":"<==      Total: 1","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.580+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"o.s.data.mongodb.core.MongoTemplate","msg":"find using query: { \"templateId\" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField","stack_trace":""}
{"@timestamp":"2025-07-29T15:28:55.622+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.log.aop.LogAnnotationAOP","msg":"请求完成, transId=f5d0267d1bcfebc7, 耗时=81, resp={\"datas\":{\"bizCode\":\"enterprise\",\"createId\":\"U000162\",\"createTime\":1753354888000,\"fields\":[{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"name\",\"fieldLength\":\"50\",\"fieldName\":\"企业名称\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2d\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":1,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"enterpriseCode\",\"fieldLength\":\"50\",\"fieldName\":\"社会信用代码\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2e\",\"isIndex\":\"1\",\"required\":\"0\",\"showType\":\"1\",\"sort\":2,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"type\",\"fieldLength\":\"1\",\"fieldName\":\"企业类型\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f2f\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":3,\"templateId\":3},{\"additional\":{\"inputUnit\":\"人1\"},\"change\":\"1\",\"fieldCode\":\"staffSize\",\"fieldName\":\"人员规模\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f30\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":4,\"templateId\":3},{\"additional\":{\"selectOptions\":[\"1\",\"2\",\"3\",\"4\",\"5\"]},\"change\":\"1\",\"fieldCode\":\"city\",\"fieldLength\":\"30\",\"fieldName\":\"所在城市\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f31\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"3\",\"sort\":5,\"templateId\":3},{\"additional\":{},\"change\":\"0\",\"fieldCode\":\"industryId\",\"fieldName\":\"所属行业ID\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f32\",\"isIndex\":\"1\",\"required\":\"0\",\"showType\":\"0\",\"sort\":6,\"templateId\":3},{\"additional\":{\"inputUnit\":\"万\"},\"change\":\"1\",\"fieldCode\":\"annualIncome\",\"fieldName\":\"年收入\",\"fieldType\":\"int\",\"id\":\"688350cb7854ad28146e9f33\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":7,\"templateId\":3},{\"additional\":{},\"change\":\"0\",\"fieldCode\":\"isVerified\",\"fieldLength\":\"1\",\"fieldName\":\"是否验真\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f34\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"0\",\"sort\":8,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"enterpriseContacter\",\"fieldLength\":\"20\",\"fieldName\":\"企业联系人\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f35\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":9,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"contacterPhone\",\"fieldLength\":\"20\",\"fieldName\":\"联系人电话\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f36\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"1\",\"sort\":10,\"templateId\":3},{\"additional\":{},\"change\":\"1\",\"fieldCode\":\"remark\",\"fieldLength\":\"300\",\"fieldName\":\"备注信息\",\"fieldType\":\"varchar\",\"id\":\"688350cb7854ad28146e9f37\",\"isIndex\":\"0\",\"required\":\"0\",\"showType\":\"6\",\"sort\":11,\"templateId\":3}],\"id\":3,\"isDeleted\":0,\"remark\":\"企业表测试\",\"status\":\"1\",\"templateName\":\"企业信息表\",\"tenantId\":\"T0001\",\"type\":\"企业信息\",\"updateId\":\"U000162\",\"updateTime\":1753436363000},\"resp_code\":0,\"resp_msg\":\"请求成功\"}:","stack_trace":""}
{"@timestamp":"2025-07-29T15:33:43.898+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:38:43.904+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:43:43.914+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:48:43.925+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:53:43.934+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-29T15:58:43.941+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
