{"@timestamp":"2025-07-25T12:19:34.799+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"background-preinit","class":"o.h.validator.internal.util.Version","msg":"HV000001: Hibernate Validator 6.1.7.Final","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.660+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.661+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.670+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.670+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.672+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.673+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.674+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.674+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.677+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.677+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.783+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.790+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:35.791+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:36.050+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"The following profiles are active: dev","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:41.904+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:41.907+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.019+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 104 ms. Found 0 MongoDB repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.039+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.041+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.151+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 101 ms. Found 0 Redis repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.571+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.cloud.context.scope.GenericScope","msg":"BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.612+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.612+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.612+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:42.614+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:43.596+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:43.604+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:43.604+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.081+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat initialized with port(s): 7002 (http)","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.101+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Initializing ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.102+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardService","msg":"Starting service [Tomcat]","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.103+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardEngine","msg":"Starting Servlet engine: [Apache Tomcat/9.0.53]","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.257+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring embedded WebApplicationContext","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:44.257+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.s.c.ServletWebServerApplicationContext","msg":"Root WebApplicationContext: initialization completed in 8169 ms","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:45.826+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.d.s.b.a.DruidDataSourceAutoConfigure","msg":"Init DruidDataSource","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:48.269+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} inited","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:48.604+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.mongodb.driver.cluster","msg":"Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:49.042+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688305e46a9e31259e17ab9f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:49.042+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-rtt-ClusterId{value='688305e46a9e31259e17ab9f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:49.043+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688305e46a9e31259e17ab9f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.cluster","msg":"Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=245294100}","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:49.064+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:49.419+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:50.246+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.commons.filter.TraceContextFilter","msg":"Filter 'traceContextFilter' configured for use","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:51.251+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.auth.service.AuthService","msg":"interface com.kbao.kbcelms.auth.dao.AuthMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:51.310+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.bascode.service.BasCodeService","msg":"interface com.kbao.kbcelms.bascode.dao.BasCodeMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:51.669+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.f.a.AutowiredAnnotationBeanPostProcessor","msg":"Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:51.732+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.279+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.303+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.343+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.365+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.380+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.424+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.470+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.582+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.d.service.DataTemplateService","msg":"interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.629+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.657+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.680+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.u.service.UserTenantService","msg":"interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.752+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.783+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.p.service.ProcessDefineService","msg":"interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.841+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.roleauth.service.RoleAuthService","msg":"interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.864+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.role.service.RoleService","msg":"interface com.kbao.kbcelms.role.dao.RoleMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.908+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.user.service.UserService","msg":"interface com.kbao.kbcelms.user.dao.UserMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.970+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityService","msg":"interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:52.986+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityDetailService","msg":"interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.071+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityOrderService","msg":"interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.111+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.172+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessService","msg":"interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.190+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessLogService","msg":"interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.258+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.340+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.429+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.448+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityTeamService","msg":"interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.467+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityTeamDivisionService","msg":"interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.484+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userrole.service.UserRoleService","msg":"interface com.kbao.kbcelms.userrole.dao.UserRoleMapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.868+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.908+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.953+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:53.977+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.028+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.082+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.184+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.219+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.253+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.311+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.374+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.420+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.447+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.474+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.499+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.531+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.563+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.592+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.619+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.643+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.667+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.692+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.715+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.746+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.764+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.833+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.867+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:54.937+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.080+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.089+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.119+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.145+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.167+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.185+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.209+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.223+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.233+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.246+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.258+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.271+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.280+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.296+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.308+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.326+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.343+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.360+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.373+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.393+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.406+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:55.421+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:56.097+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","msg":"Exposing 2 endpoint(s) beneath base path '/actuator'","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:56.325+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.PropertySourcedRequestMappingHandlerMapping","msg":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:57.332+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.j.a.XxlJobClientAutoConfigure","msg":">>>>>>>>>>> xxl-job config init.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:58.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration","msg":"Eureka HTTP Client uses RestTemplate.","stack_trace":""}
{"@timestamp":"2025-07-25T12:19:59.242+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger","msg":"Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.437+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.454+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.471+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.487+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.504+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.519+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.535+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.553+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.570+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.589+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.608+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.623+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.641+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.663+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.685+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.691+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.711+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.731+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.750+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.771+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.787+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.820+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.839+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.862+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.882+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.916+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.923+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.940+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.946+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.954+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.960+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.964+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.971+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.983+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.987+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.993+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:00.998+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.005+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.012+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.017+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.024+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.028+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.032+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.041+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.046+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.059+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.064+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.069+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.074+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.080+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.087+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.094+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.099+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.104+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.109+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.118+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.131+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.146+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.151+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.156+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.162+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.167+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.179+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.481+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","msg":"Setting initial instance status as: STARTING","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.595+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Initializing Eureka in region us-east-1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.607+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.677+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Disable delta property : false","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.677+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Single vip registry refresh property : null","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.678+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Force full registry fetch : false","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.678+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application is null : false","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.678+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Registered Applications size is zero : true","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.678+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application version is -1: true","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:01.678+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Getting all instance registry info from the eureka server","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.831+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"The response status is 200","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.836+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Not registering with Eureka server per configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.846+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Discovery Client initialized at timestamp 1753417202844 with initial instances count: 8","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.858+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Registering application KBC-ELMS-WEB with eureka with status UP","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.860+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Starting ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.924+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat started on port(s): 7002 (http) with context path ''","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","msg":"Updating port to 7002","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudDefaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.929+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.931+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.931+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.996+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.996+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.996+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:02.996+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Context refreshed","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.045+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Found 1 custom documentation plugin(s)","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.273+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.s.ApiListingReferenceScanner","msg":"Scanning for api listing references","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.684+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.762+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: getTenantUsersUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.770+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: getWebUserInfoUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.818+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.825+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.830+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.852+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.869+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.878+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:03.997+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"Started KbcElmsWebApplication in 30.659 seconds (JVM running for 32.656)","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:04.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(1)-*********","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring DispatcherServlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:04.145+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(1)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Initializing Servlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:04.149+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(1)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Completed initialization in 4 ms","stack_trace":""}
{"@timestamp":"2025-07-25T12:20:04.964+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-25T12:25:01.724+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:30:01.736+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:35:01.742+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:40:01.759+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:45:01.775+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:50:01.790+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-25T12:55:01.800+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
