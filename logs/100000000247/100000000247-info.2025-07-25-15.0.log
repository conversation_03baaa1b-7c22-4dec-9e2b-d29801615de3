[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:01:07.852 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:06:07.869 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:11:07.875 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.328 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=dd71ff7aaff4b276,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [dd71ff7aaff4b276,] 2025-07-25 15:11:34.498 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=dd71ff7aaff4b276, 耗时=170, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.643 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f849b3a4ba8ec90b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [f849b3a4ba8ec90b,] 2025-07-25 15:11:41.717 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f849b3a4ba8ec90b, 耗时=75, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:16:07.878 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:21:07.893 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:26:07.900 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 15:31:07.910 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
