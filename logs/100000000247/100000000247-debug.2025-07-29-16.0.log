[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.903 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.908 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 06fd7c40f2432db9
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.908 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.909 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.910 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.910 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.911 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:41.911 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.022 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (110ms)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.022 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.022 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.022 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:42 GMT
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.022 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.023 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.024 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.024 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.024 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.024 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.183 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.184 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.228 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.229 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.230 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_enterprise(String)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.265 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.266 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.266 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.302 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.313 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.314 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.456 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.457 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.458 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 企业信息表(String), enterprise(String), 企业信息(String), 1(String), 企业表测试(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.527 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.548 DEBUG 31496 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.775 DEBUG 31496 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 12 items
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.895 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_tp_data_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, staffNumRange varchar(30) comment ?, city varchar(30) comment ?, dtAnnualIncome int comment ?, dtCategoryCode varchar(10) comment ?, dtCategory varchar(100) comment ?, dtIsVerified char(1) comment ?, dtEnterpriseContacter varchar(20) comment ?, dtContacterPhone varchar(20) comment ?, dtRemark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:42.896 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 人员规模(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 企业信息表(String)
[kbc-elms-web:*********:7002] [06fd7c40f2432db9,] 2025-07-29 16:13:43.029 DEBUG 31496 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.179 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.180 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8f72f5cd5a396a40
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.180 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.180 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.180 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.181 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.181 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.181 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.275 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.275 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.275 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.275 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:43 GMT
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.276 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.276 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.276 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.276 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.276 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.277 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.282 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.282 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.317 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.319 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.320 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [8f72f5cd5a396a40,] 2025-07-29 16:13:43.357 DEBUG 31496 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.892 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.895 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 096003b80577ff52
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.895 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.896 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.897 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.897 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.897 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:45.897 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.001 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (103ms)
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.001 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.001 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.002 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:46 GMT
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.003 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.003 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.003 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.003 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.003 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.004 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.012 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.012 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: enterprise(String)
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.053 DEBUG 31496 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [096003b80577ff52,] 2025-07-29 16:13:46.057 DEBUG 31496 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.136 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.136 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 3949ca2aa4da741c
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.137 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.138 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.138 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.139 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.139 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.139 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.211 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (71ms)
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.211 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.211 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.211 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:46 GMT
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.211 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.212 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.212 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.212 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.213 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.213 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.217 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.218 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_enterprise(String)
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.253 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.254 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_enterprise 
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.255 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [3949ca2aa4da741c,] 2025-07-29 16:13:46.290 DEBUG 31496 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.166 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.168 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 95e5e26da9f848e5
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.169 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.169 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.169 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.169 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.169 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.170 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.258 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (88ms)
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.259 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.259 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.259 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:49 GMT
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.260 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.266 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.267 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.303 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.304 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.304 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [95e5e26da9f848e5,] 2025-07-29 16:13:49.343 DEBUG 31496 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.941 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.942 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d27c3855fefdff75
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.942 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.942 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.943 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.943 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.943 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"0b52b79cc9b86038ccff777896449ac2","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:52.944 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (97ms)
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 29 Jul 2025 08:13:53 GMT
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.042 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.043 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.045 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.045 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.046 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.050 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.050 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.086 DEBUG 31496 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [d27c3855fefdff75,] 2025-07-29 16:13:53.087 DEBUG 31496 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
