{"@timestamp":"2025-07-31T09:33:34.260+08:00","traceId":"7918e058ffb4b9bf","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753925613023,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753783497000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1244,\"traceId\":\"7918e058ffb4b9bf\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:33:36.130+08:00","traceId":"9dac576416ba33b7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753925616055,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753783497000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"9dac576416ba33b7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:35:02.288+08:00","traceId":"541da21b8130aa22","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"导入\",\"appName\":\"kbc-elms-web\",\"createTime\":1753925696445,\"desc\":\"导入行业数据\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.industry.IndustryController/importIndustryData\",\"module\":\"行业分类管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":\\\"导入完成！成功导入 21 条数据，跳过 1750 条数据\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"导入成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":5842,\"traceId\":\"541da21b8130aa22\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:44:17.660+08:00","traceId":"59695ccebe470b8f","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"导入\",\"appName\":\"kbc-elms-web\",\"createTime\":1753926189357,\"desc\":\"导入行业数据\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.industry.IndustryController/importIndustryData\",\"module\":\"行业分类管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":\\\"导入完成！清空原数据，成功导入 1769 条数据，跳过 2 条数据\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"导入成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":68298,\"traceId\":\"59695ccebe470b8f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:47:35.096+08:00","traceId":"acd33ee916f17ce1","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"导入\",\"appName\":\"kbc-elms-web\",\"createTime\":1753926371039,\"desc\":\"导入行业数据\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.industry.IndustryController/importIndustryData\",\"module\":\"行业分类管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":\\\"导入完成！清空原数据，成功导入 1769 条数据，跳过 2 条数据\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"导入成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":84054,\"traceId\":\"acd33ee916f17ce1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:57:42.771+08:00","traceId":"516ff232988eccad","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"导入\",\"appName\":\"kbc-elms-web\",\"createTime\":1753926810316,\"desc\":\"导入行业数据\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.industry.IndustryController/importIndustryData\",\"module\":\"行业分类管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":\\\"导入完成！清空原数据，成功导入 1769 条数据，跳过 2 条数据\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"导入成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":252453,\"traceId\":\"516ff232988eccad\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-31T09:59:18.778+08:00","traceId":"8957c1c37de8a774","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753927158012,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"creditCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"dtType\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffNumRange\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"dtAnnualIncome\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688882b6c3add91428f4e789\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"dtCategoryCode\\\",\\\"fieldLength\\\":\\\"10\\\",\\\"fieldName\\\":\\\"行业代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78a\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"最小行业代码\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":7,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"dtCategory\\\",\\\"fieldLength\\\":\\\"100\\\",\\\"fieldName\\\":\\\"行业类名\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78b\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"行业名全称，显示多级\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"dtIsVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"dtEnterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"dtContacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":11,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"dtRemark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":12,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753783497000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":765,\"traceId\":\"8957c1c37de8a774\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
