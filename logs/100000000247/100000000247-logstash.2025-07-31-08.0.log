{"@timestamp":"2025-07-31T08:39:35.706+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"background-preinit","class":"o.h.validator.internal.util.Version","msg":"HV000001: Hibernate Validator 6.1.7.Final","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.690+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.691+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.698+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.699+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.701+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.702+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.703+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.703+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.705+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.705+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.786+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.806+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:36.809+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:38.487+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 08:39:39 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-07-31T08:39:38.548+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 08:39:39 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-07-31T08:39:38.599+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 08:39:39 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-07-31T08:39:38.602+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.b.c.PropertySourceBootstrapConfiguration","msg":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:38.792+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"The following profiles are active: dev","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:46.605+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","msg":"Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.106+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.110+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.348+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 229 ms. Found 0 MongoDB repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.369+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.372+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:47.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 234 ms. Found 0 Redis repository interfaces.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.090+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.cloud.context.scope.GenericScope","msg":"BeanFactory id=afe28187-3541-37e7-9277-6114bbf8b135","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.138+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.139+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.140+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.141+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.142+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.142+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.142+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.143+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.143+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:48.145+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.326+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.337+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.337+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.891+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat initialized with port(s): 7002 (http)","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.914+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Initializing ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardService","msg":"Starting service [Tomcat]","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:49.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardEngine","msg":"Starting Servlet engine: [Apache Tomcat/9.0.53]","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:50.107+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring embedded WebApplicationContext","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:50.108+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.s.c.ServletWebServerApplicationContext","msg":"Root WebApplicationContext: initialization completed in 11225 ms","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:51.947+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.d.s.b.a.DruidDataSourceAutoConfigure","msg":"Init DruidDataSource","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:54.412+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} inited","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:54.808+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.mongodb.driver.cluster","msg":"Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:55.134+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688abb5a4b80c753a9553c96', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:55.134+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-rtt-ClusterId{value='688abb5a4b80c753a9553c96', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:55.138+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688abb5a4b80c753a9553c96', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.cluster","msg":"Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=118473800}","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:55.181+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:55.751+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:56.644+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.commons.filter.TraceContextFilter","msg":"Filter 'traceContextFilter' configured for use","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:57.930+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.auth.service.AuthService","msg":"interface com.kbao.kbcelms.auth.dao.AuthMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:57.997+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.bascode.service.BasCodeService","msg":"interface com.kbao.kbcelms.bascode.dao.BasCodeMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.373+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.f.a.AutowiredAnnotationBeanPostProcessor","msg":"Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.440+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.799+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.823+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.861+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.881+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.894+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.917+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.932+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:58.974+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.c.service.ConstantConfigService","msg":"interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.077+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.d.service.DataTemplateService","msg":"interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.174+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.industry.service.IndustryService","msg":"interface com.kbao.kbcelms.industry.dao.IndustryMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.260+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.330+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.roleauth.service.RoleAuthService","msg":"interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.350+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.role.service.RoleService","msg":"interface com.kbao.kbcelms.role.dao.RoleMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.377+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.u.service.UserTenantService","msg":"interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.399+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessLogService","msg":"interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.444+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.476+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.499+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userrole.service.UserRoleService","msg":"interface com.kbao.kbcelms.userrole.dao.UserRoleMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.524+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userorg.service.UserOrgService","msg":"interface com.kbao.kbcelms.userorg.dao.UserOrgMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.546+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.user.service.UserService","msg":"interface com.kbao.kbcelms.user.dao.UserMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.631+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.p.service.ProcessDefineService","msg":"interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.670+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.732+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessService","msg":"interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.787+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.861+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.957+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:39:59.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityTeamService","msg":"interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.001+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityService","msg":"interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.111+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityOrderService","msg":"interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.290+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.375+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityDetailService","msg":"interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.476+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityTeamDivisionService","msg":"interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:00.981+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.036+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.101+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.131+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.193+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.261+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.322+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.357+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.399+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.444+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.501+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.567+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.621+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.656+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.685+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.716+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.747+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.772+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.799+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.826+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.864+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.902+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.943+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:01.994+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.042+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.116+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.154+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.229+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.378+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.389+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.418+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.444+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.467+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.489+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.516+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.531+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.541+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.553+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.563+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.577+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.588+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.602+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.613+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.628+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.643+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.660+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.670+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.698+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:02.711+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:03.364+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","msg":"Exposing 2 endpoint(s) beneath base path '/actuator'","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:03.612+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.PropertySourcedRequestMappingHandlerMapping","msg":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:04.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.j.a.XxlJobClientAutoConfigure","msg":">>>>>>>>>>> xxl-job config init.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:06.649+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration","msg":"Eureka HTTP Client uses RestTemplate.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:07.016+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger","msg":"Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.329+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.348+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.365+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.385+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.405+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.423+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.442+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.461+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.482+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.503+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.553+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.582+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.605+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.623+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.628+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.644+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.661+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.676+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.697+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.713+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.729+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.748+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.769+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.789+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.810+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.840+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.846+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.850+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.862+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.867+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.873+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.879+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.884+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.890+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.897+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.901+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.905+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.910+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.919+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.931+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.937+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.940+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.944+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.951+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.954+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.963+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.967+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.970+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.974+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.978+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.981+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.988+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.995+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:08.999+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.003+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.007+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.015+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.024+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.037+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.042+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.046+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.051+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.056+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.065+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.072+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.384+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","msg":"Setting initial instance status as: STARTING","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.514+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Initializing Eureka in region us-east-1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.530+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Disable delta property : false","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Single vip registry refresh property : null","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Force full registry fetch : false","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application is null : false","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Registered Applications size is zero : true","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application version is -1: true","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:09.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Getting all instance registry info from the eureka server","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.503+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"The response status is 200","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.512+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Not registering with Eureka server per configuration","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.522+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Discovery Client initialized at timestamp 1753922410520 with initial instances count: 9","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.534+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Registering application KBC-ELMS-WEB with eureka with status UP","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.536+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Starting ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.610+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat started on port(s): 7002 (http) with context path ''","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.614+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","msg":"Updating port to 7002","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.614+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.616+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudDefaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.617+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.618+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.618+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.685+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.686+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.687+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.687+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.687+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Context refreshed","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:10.766+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Found 1 custom documentation plugin(s)","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.054+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.s.ApiListingReferenceScanner","msg":"Scanning for api listing references","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.495+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.582+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.586+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.592+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.671+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: addUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.767+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.774+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.775+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: listUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.779+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.783+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.811+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.830+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.841+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:11.936+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"Started KbcElmsWebApplication in 37.814 seconds (JVM running for 40.083)","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:12.178+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring DispatcherServlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:12.179+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Initializing Servlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:12.184+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(3)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Completed initialization in 5 ms","stack_trace":""}
{"@timestamp":"2025-07-31T08:40:12.817+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(5)-*********","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-07-31T08:43:07.736+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Unregistering application KBC-ELMS-WEB with eureka with status DOWN","stack_trace":""}
{"@timestamp":"2025-07-31T08:43:07.736+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-8","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Start destroying common HttpClient","stack_trace":""}
{"@timestamp":"2025-07-31T08:43:07.739+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-8","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Destruction of the end","stack_trace":""}
