[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:56.644 DEBUG 38448 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:57.930 DEBUG 38448 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:57.997 DEBUG 38448 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:58.974 DEBUG 38448 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.077 DEBUG 38448 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.174 DEBUG 38448 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.330 DEBUG 38448 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.350 DEBUG 38448 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.377 DEBUG 38448 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.399 DEBUG 38448 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.499 DEBUG 38448 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.524 DEBUG 38448 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.546 DEBUG 38448 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.631 DEBUG 38448 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.732 DEBUG 38448 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:39:59.978 DEBUG 38448 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:40:00.001 DEBUG 38448 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:40:00.111 DEBUG 38448 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:40:00.375 DEBUG 38448 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 08:40:00.476 DEBUG 38448 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
