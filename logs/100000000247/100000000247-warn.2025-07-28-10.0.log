[kbc-elms-web:10.78.8.1:7002] [d252fb147d70c757,] 2025-07-28 10:09:44.104 WARN 16728 [http-nio-7002-exec-7] org.springframework.web.servlet.PageNotFound No mapping for POST /api/enterprise/type/page
[kbc-elms-web:10.78.8.1:7002] [96e5384a3e1966d6,] 2025-07-28 10:10:05.685 WARN 16728 [http-nio-7002-exec-10] org.springframework.web.servlet.PageNotFound No mapping for POST /api/enterprise/type/page
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-28 10:12:49.432 WARN 21536 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-28 10:12:49.824 WARN 21536 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-28 10:13:01.775 WARN 21536 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
