[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.580 WARN 42824 [lettuce-nioEventLoop-6-4] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.579 WARN 42824 [lettuce-nioEventLoop-6-2] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.580 WARN 42824 [lettuce-nioEventLoop-6-3] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.580 WARN 42824 [lettuce-nioEventLoop-6-5] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.580 WARN 42824 [lettuce-nioEventLoop-6-1] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:22:29.580 WARN 42824 [lettuce-nioEventLoop-6-6] io.lettuce.core.protocol.ConnectionWatchdog Cannot reconnect to [10.176.18.9:6379]: Network is unreachable: no further information: /10.176.18.9:6379

io.netty.channel.AbstractChannel$AnnotatedSocketException: Network is unreachable: no further information: /10.176.18.9:6379
Caused by: java.net.SocketException: Network is unreachable: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:18.676 WARN 44400 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:18.684 WARN 44400 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar.yml] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:18.685 WARN 44400 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar-dev.yml] & group[group-kbc-elms]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:29.085 WARN 44400 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:38.513 WARN 44400 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:39.423 WARN 44400 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:52.946 WARN 44400 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:59.078 WARN 44400 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
