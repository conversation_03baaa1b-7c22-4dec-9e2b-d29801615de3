{"@timestamp":"2025-07-24T19:00:19.302+08:00","traceId":"f5dff2ff8111fb9e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354818342,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"company\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"}],\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"type\\\":\\\"企业信息\\\"}\",\"remark\":\"模板编号已存在\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":963,\"traceId\":\"f5dff2ff8111fb9e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:27.154+08:00","traceId":"ba44677b8a7727fc","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354886072,\"desc\":\"新增\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"company\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"\\\"}],\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"type\\\":\\\"企业信息\\\"}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1081,\"traceId\":\"ba44677b8a7727fc\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:27.705+08:00","traceId":"fd1dde2ba9eb66c6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354887339,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":365,\"traceId\":\"fd1dde2ba9eb66c6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:31.020+08:00","traceId":"5e49a30f3c2c6b8e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354890807,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":212,\"traceId\":\"5e49a30f3c2c6b8e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:34.178+08:00","traceId":"97ed450512b44ca9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354894109,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"97ed450512b44ca9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:35.755+08:00","traceId":"ab77f474fdbaadf7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354895709,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":45,\"traceId\":\"ab77f474fdbaadf7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:35.922+08:00","traceId":"261a4adcd8b58a5b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354895709,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":207,\"traceId\":\"261a4adcd8b58a5b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:01:36.046+08:00","traceId":"9d695a6036e9f911","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354895936,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":110,\"traceId\":\"9d695a6036e9f911\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:43.876+08:00","traceId":"96607eddb8d8c59d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354963764,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":111,\"traceId\":\"96607eddb8d8c59d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:52.227+08:00","traceId":"1c399c4d1676f6be","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354972158,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":1}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"s\\\",\\\"fieldCode\\\":\\\"fff\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"xx1\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548005\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":1,\\\"validation\\\":\\\"3\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"ss\\\",\\\"fieldLength\\\":\\\"2\\\",\\\"fieldName\\\":\\\"xx2\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548006\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":1,\\\"validation\\\":\\\"ee\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"sd\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"d\\\",\\\"fieldCode\\\":\\\"pdd\\\",\\\"fieldLength\\\":\\\"12\\\",\\\"fieldName\\\":\\\"xx\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881cbb98d33bf5f79adb024\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":1,\\\"validation\\\":\\\"xd\\\"}],\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":68,\"traceId\":\"1c399c4d1676f6be\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:54.949+08:00","traceId":"18ec379575c811bc","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354974878,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":71,\"traceId\":\"18ec379575c811bc\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:57.909+08:00","traceId":"81651be0bbae7a94","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354977872,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":36,\"traceId\":\"81651be0bbae7a94\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:57.935+08:00","traceId":"317b1ec245fe102d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354977861,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":73,\"traceId\":\"317b1ec245fe102d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:02:58.181+08:00","traceId":"39290a784794e34f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753354978073,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":108,\"traceId\":\"39290a784794e34f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:30.007+08:00","traceId":"3e250461fd6cc514","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355129831,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"searchParamList\\\":[],\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":176,\"traceId\":\"3e250461fd6cc514\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:36.319+08:00","traceId":"7a37b5a4cc18958e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355136243,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":75,\"traceId\":\"7a37b5a4cc18958e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:38.393+08:00","traceId":"73c340c136866d34","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355138353,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":39,\"traceId\":\"73c340c136866d34\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:38.409+08:00","traceId":"d0ca1bac24244018","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355138338,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":71,\"traceId\":\"d0ca1bac24244018\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:38.685+08:00","traceId":"6536fc9bd5af2c59","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355138543,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":142,\"traceId\":\"6536fc9bd5af2c59\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:05:48.911+08:00","traceId":"5c68ffd88cd730be","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355148767,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"searchParamList\\\":[{\\\"fieldName\\\":\\\"name\\\",\\\"operator\\\":\\\"=\\\",\\\"value\\\":\\\"小米\\\"}],\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":144,\"traceId\":\"5c68ffd88cd730be\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:06:09.700+08:00","traceId":"0b3f65d6fd2d1f70","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355169627,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":72,\"traceId\":\"0b3f65d6fd2d1f70\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:06:09.797+08:00","traceId":"284f8235dc832f3b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355169761,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":36,\"traceId\":\"284f8235dc832f3b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:06:10.082+08:00","traceId":"22f1acfa6a61683e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355169921,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":160,\"traceId\":\"22f1acfa6a61683e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:06:18.798+08:00","traceId":"949c58be7c74d4a3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355178660,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"searchParamList\\\":[{\\\"fieldName\\\":\\\"name\\\",\\\"operator\\\":\\\"=\\\",\\\"value\\\":\\\"小米\\\"}],\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":138,\"traceId\":\"949c58be7c74d4a3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T19:06:21.139+08:00","traceId":"83b8c7445462097d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753355181005,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":133,\"traceId\":\"83b8c7445462097d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
