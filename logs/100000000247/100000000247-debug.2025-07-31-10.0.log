[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.257 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.264 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bd64833f05b04e82
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.264 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.265 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.267 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:41 GMT
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.398 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.398 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.399 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.650 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.650 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.724 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.725 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.725 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_agent_enterprise(String)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.768 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.781 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.781 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.852 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.857 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.858 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.931 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.960 DEBUG 39888 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.183 DEBUG 39888 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.301 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_tp_data_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.302 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.465 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b9a0fa8c000790d4
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.706 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.706 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:42 GMT
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.107 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.107 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.142 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.143 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.144 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.179 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.508 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5fa005ab717b08b9
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:46 GMT
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.603 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.603 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.607 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.608 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.644 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.645 DEBUG 39888 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.563 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 592841a2a4208922
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.567 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:08:24 GMT
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.675 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.717 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.718 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.753 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.753 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.755 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_agent_enterprise(String)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.790 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.790 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.791 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.825 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.825 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.826 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.923 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.924 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.925 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.992 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.992 DEBUG 39888 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.030 DEBUG 39888 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.071 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_tp_data_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.072 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.195 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ae125895b2d74c4c
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.636 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.758 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (122ms)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.759 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.759 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:08:25 GMT
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.761 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.761 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.766 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.766 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.835 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:45.720 DEBUG 39240 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.086 DEBUG 39240 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.147 DEBUG 39240 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.152 DEBUG 39240 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.252 DEBUG 39240 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.344 DEBUG 39240 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.478 DEBUG 39240 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.496 DEBUG 39240 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.522 DEBUG 39240 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.542 DEBUG 39240 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.633 DEBUG 39240 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.656 DEBUG 39240 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.676 DEBUG 39240 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.760 DEBUG 39240 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.853 DEBUG 39240 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.127 DEBUG 39240 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.150 DEBUG 39240 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.171 DEBUG 39240 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.271 DEBUG 39240 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.499 DEBUG 39240 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.791 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.793 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c8dee7942b63c156
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.263 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (468ms)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:41 GMT
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.889 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey ==>  Preparing: update t_data_template set is_deleted = 1, update_time = now(), update_id = ? where id = ? 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:42.102 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey ==> Parameters: 1(Integer), 1(Integer)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:42.181 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey <==    Updates: 1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.285 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.286 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f12ef49418ff74fe
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.289 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.290 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.409 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.409 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.410 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:43 GMT
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.918 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.919 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.065 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.075 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.075 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.114 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.716 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.719 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 177e5f90f75a4b3e
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.719 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.720 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.722 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (119ms)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:55 GMT
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.843 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.844 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.844 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.854 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.854 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.894 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:55.104 DEBUG 39240 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.327 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6e806b8a3dcd9515
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.445 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (115ms)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:58 GMT
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.447 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.564 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.565 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.608 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.609 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.610 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.662 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.680 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.682 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.729 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.739 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.739 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.015 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.017 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.018 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.091 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.108 DEBUG 39240 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.295 DEBUG 39240 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.414 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_gen_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.415 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.621 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 39e1922502f52178
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.084 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.087 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (109ms)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:59 GMT
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.199 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.199 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.204 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.204 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.240 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.241 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.243 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.282 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.285 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.287 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6b33677a11443406
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.287 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.289 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.289 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.407 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (117ms)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.408 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.408 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.409 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:11:02 GMT
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.409 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.416 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.417 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.452 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.454 DEBUG 39240 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.132 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.133 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: dc454b472a2b5a32
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.134 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.134 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (144ms)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:14 GMT
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.282 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.422 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.423 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.463 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.464 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.464 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.499 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.499 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.500 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.538 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.540 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.540 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.693 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.694 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.695 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.762 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.762 DEBUG 39240 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.800 DEBUG 39240 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.842 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_gen_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, city varchar(30) comment ?, districtCode varchar(20) comment ?, staffScale varchar(30) comment ?, annualIncome varchar(20) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.842 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会统一信用代码(String), 行业代码(String), 行业类名(String), 企业类型(String), 企业规模(String), 企业所在城市(String), 行政区划代码(String), 人员规模(String), 企业年收入(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.972 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.108 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.108 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 07befd8c282004f3
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.109 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.109 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.110 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.213 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:14 GMT
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.215 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.216 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.216 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.224 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.225 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.258 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.260 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.260 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.295 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.532 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.536 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: de4a93183a7d0b7b
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.537 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.642 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (103ms)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.642 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:21 GMT
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.652 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.652 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.688 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.689 DEBUG 39240 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.776 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.777 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 364918a9765ee6b3
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.780 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.780 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:21 GMT
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.863 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.871 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.872 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.918 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.924 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.924 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.958 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: df66e91ec0f33c50
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.368 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.372 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.373 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: df66e91ec0f33c50
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.393 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.393 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.495 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.495 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.558 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bbf63f9a8ba5b10a
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.561 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (74ms)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.639 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.639 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.643 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.644 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.678 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 0
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.068 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.071 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d6cd0ffb0764ecf1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.071 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (120ms)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.195 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:21:18 GMT
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.195 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.200 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.201 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.246 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.247 DEBUG 39240 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.340 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.341 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 08fe16916238d8bc
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.341 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.344 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.344 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.417 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.417 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:21:18 GMT
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.419 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.423 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.424 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.457 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.459 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.460 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.494 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.605 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.608 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 67b4c78698431832
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.609 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.609 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.739 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.741 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:22:58 GMT
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.744 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.789 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.790 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.831 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.833 DEBUG 39240 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.909 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8db8138b7d934c4e
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.980 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (69ms)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:22:58 GMT
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.982 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.987 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.015 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.016 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.056 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.058 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.058 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.104 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.721 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.722 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 669d22b267648df3
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.724 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.724 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.862 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:26:13 GMT
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.911 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.912 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.955 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.957 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.957 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.990 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.990 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_gen_agent_enterprise LIMIT ? 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.991 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:13.031 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 10
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:41.765 DEBUG 17560 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.016 DEBUG 17560 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.074 DEBUG 17560 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.058 DEBUG 17560 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.167 DEBUG 17560 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.243 DEBUG 17560 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.278 DEBUG 17560 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.416 DEBUG 17560 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.444 DEBUG 17560 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.485 DEBUG 17560 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.510 DEBUG 17560 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.610 DEBUG 17560 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.632 DEBUG 17560 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.655 DEBUG 17560 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.748 DEBUG 17560 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.851 DEBUG 17560 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.159 DEBUG 17560 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.180 DEBUG 17560 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.200 DEBUG 17560 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.282 DEBUG 17560 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.511 DEBUG 17560 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.082 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.082 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 80566978df8711a8
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e1ad2e7ff2391f52
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (412ms)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (412ms)
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:36:03 GMT
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:36:03 GMT
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.617 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.617 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 39daae2a50cac75a
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 555e7e9e0670637a
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.741 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.742 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.742 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.744 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.745 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (125ms)
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.747 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.747 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.746 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.750 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.750 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.855 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 86e0da374176546b
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.857 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.857 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (64ms)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.774 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.774 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5778942d8a3294e3
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7f954575279370a9
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.777 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.777 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.892 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.892 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.897 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.897 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:46:45 GMT
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.898 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:46:45 GMT
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.898 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.901 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.901 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.904 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.904 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
