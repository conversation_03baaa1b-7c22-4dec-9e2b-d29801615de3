{"@timestamp":"2025-07-23T14:18:48.585+08:00","traceId":"2b6159d165a2fa8c","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753251528433,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":152,\"traceId\":\"2b6159d165a2fa8c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:28:38.408+08:00","traceId":"8bdeef5e7557504b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753252118317,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":91,\"traceId\":\"8bdeef5e7557504b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:30:35.489+08:00","traceId":"d375a78df29f7287","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753252235371,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/add\",\"module\":\"管理\",\"params\":\"{\\\"status\\\":\\\"1\\\"}\",\"remark\":\"\\r\\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'template_name' cannot be null\\r\\n### The error may involve com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.insert-Inline\\r\\n### The error occurred while setting parameters\\r\\n### SQL: insert into t_data_template(        id,     template_name,     biz_code,     type,     status,     remark,     create_time,     create_id,     update_time,     update_id,     is_deleted,     tenant_id        )   values(                   ?,                                    ?,                                    ?,                                    ?,                                    ?,                                    ?,                                    now(),                                   ?,      now(),      ?,      '0',                                   ?                 )\\r\\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'template_name' cannot be null\\n; Column 'template_name' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'template_name' cannot be null\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":118,\"traceId\":\"d375a78df29f7287\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:39:20.582+08:00","traceId":"e4f7c6cfef8bd20b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753252760319,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":258,\"traceId\":\"e4f7c6cfef8bd20b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:43:51.007+08:00","traceId":"09ad7c88cf9a8cee","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253030926,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":81,\"traceId\":\"09ad7c88cf9a8cee\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:44:07.416+08:00","traceId":"2a9987b67b903c1c","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253046753,\"desc\":\"新增\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"x\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"x\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"x\\\",\\\"fieldName\\\":\\\"x\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"x\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xx\\\",\\\"type\\\":\\\"x\\\"}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":663,\"traceId\":\"2a9987b67b903c1c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:47:25.921+08:00","traceId":"aba8dbe3eaf56e35","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253245828,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":92,\"traceId\":\"aba8dbe3eaf56e35\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:47:49.007+08:00","traceId":"b70d7f94c45d192c","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253268861,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"x\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"x\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"x\\\",\\\"fieldName\\\":\\\"x\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xx\\\",\\\"type\\\":\\\"x\\\"}\",\"remark\":\"模板编号(bizCode)已存在\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":145,\"traceId\":\"b70d7f94c45d192c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:47:56.398+08:00","traceId":"303dcc18f5ebd3c2","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253276099,\"desc\":\"新增\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"xxx\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":{\\\"input_unit\\\":\\\"x\\\"}},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"x\\\",\\\"fieldName\\\":\\\"x\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xx\\\",\\\"type\\\":\\\"x\\\"}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":299,\"traceId\":\"303dcc18f5ebd3c2\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:48:42.084+08:00","traceId":"056f1d1e73a9350e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253322043,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":40,\"traceId\":\"056f1d1e73a9350e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:49:00.589+08:00","traceId":"d7cddfb733d3d08d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253340447,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"xxx\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"x\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"x\\\",\\\"fieldName\\\":\\\"x\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"x\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xxx\\\",\\\"type\\\":\\\"x\\\"}\",\"remark\":\"模板编号(bizCode)已存在\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":142,\"traceId\":\"d7cddfb733d3d08d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:50:36.461+08:00","traceId":"7f0db39b58e39ad8","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253436112,\"desc\":\"新增\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"2\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldName\\\":\\\"s\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"w\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xxx\\\",\\\"type\\\":\\\"w\\\"}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":348,\"traceId\":\"7f0db39b58e39ad8\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:50:36.646+08:00","traceId":"02dd98a07714bf83","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253436605,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":41,\"traceId\":\"02dd98a07714bf83\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:54:36.576+08:00","traceId":"e95ea9d033677b72","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253676169,\"desc\":\"新增\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"22\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"sd\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"x\\\",\\\"fieldName\\\":\\\"s\\\",\\\"remark\\\":\\\"sd\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\"}],\\\"remark\\\":\\\"d\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"xxx\\\",\\\"type\\\":\\\"we\\\"}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":406,\"traceId\":\"e95ea9d033677b72\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:54:36.752+08:00","traceId":"6095913a0f85e031","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253676714,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":38,\"traceId\":\"6095913a0f85e031\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:55:11.384+08:00","traceId":"660756807a2e55d6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253711344,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":39,\"traceId\":\"660756807a2e55d6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-23T14:59:09.435+08:00","traceId":"a17d3026bb88f334","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753253755585,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":193847,\"traceId\":\"a17d3026bb88f334\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
