[kbc-elms-web:10.78.8.1:7002] [da46d46f216c8741,] 2025-08-01 09:14:21.075 WARN 44740 [http-nio-7002-exec-7] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取企业基本信息失败，companyName: 浙江百达精工股份有限公司, result: {"reason":"账号信息有误","error_code":300009}
[kbc-elms-web:10.78.8.1:7002] [da46d46f216c8741,] 2025-08-01 09:14:21.082 WARN 44740 [http-nio-7002-exec-7] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 未能获取到企业的统一社会信用代码，企业名称: 浙江百达精工股份有限公司
[kbc-elms-web:10.78.8.1:7002] [4bdf07be44378e27,] 2025-08-01 09:17:25.888 WARN 44740 [http-nio-7002-exec-3] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取企业基本信息失败，companyName: 浙江百达精工股份有限公司, result: {"reason":"账号信息有误","error_code":300009}
[kbc-elms-web:10.78.8.1:7002] [4bdf07be44378e27,] 2025-08-01 09:17:25.893 WARN 44740 [http-nio-7002-exec-3] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 未能获取到企业的统一社会信用代码，企业名称: 浙江百达精工股份有限公司
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:18:50.475 WARN 44740 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:18:50.484 WARN 44740 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [3db865edd6f381b1,] 2025-08-01 09:18:50.495 WARN 44740 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取企业基本信息失败，companyName: 浙江百达精工股份有限公司, result: {"reason":"账号信息有误","error_code":300009}
[kbc-elms-web:10.78.8.1:7002] [3db865edd6f381b1,] 2025-08-01 09:18:50.496 WARN 44740 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 未能获取到企业的统一社会信用代码，企业名称: 浙江百达精工股份有限公司
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:20:20.987 WARN 14968 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:20:31.454 WARN 14968 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:20:32.139 WARN 14968 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:20:44.122 WARN 14968 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:20:49.655 WARN 14968 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:10.78.8.1:7002] [f9d07ec75d439286,] 2025-08-01 09:27:30.652 WARN 14968 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取企业股东信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [f9d07ec75d439286,] 2025-08-01 09:27:30.730 WARN 14968 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取最终受益人信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [f9d07ec75d439286,] 2025-08-01 09:27:30.802 WARN 14968 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取财务信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:35:36.592 WARN 14968 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:35:36.596 WARN 14968 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:36:20.988 WARN 9388 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:36:30.495 WARN 9388 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:36:31.169 WARN 9388 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:36:43.380 WARN 9388 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:36:49.127 WARN 9388 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:10.78.8.1:7002] [c0ec496e43cf8ff3,] 2025-08-01 09:37:01.581 WARN 9388 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取企业股东信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [c0ec496e43cf8ff3,] 2025-08-01 09:37:01.652 WARN 9388 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取最终受益人信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [c0ec496e43cf8ff3,] 2025-08-01 09:37:01.719 WARN 9388 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 获取财务信息失败，creditCode: 913310007200456372, result: {"reason":"无权限访问此api","error_code":300005}
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:40:30.073 WARN 9388 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:40:30.075 WARN 9388 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:43:40.446 WARN 34032 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:43:49.386 WARN 34032 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:43:50.053 WARN 34032 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:44:00.946 WARN 34032 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:44:05.428 WARN 34032 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:08.866 WARN 34032 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:08.871 WARN 34032 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:33.842 WARN 4736 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:43.953 WARN 4736 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:44.773 WARN 4736 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:51:58.858 WARN 4736 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:52:05.590 WARN 4736 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-01 09:56:45.708 WARN 4736 [DiscoveryClient-0] com.netflix.discovery.TimedSupervisorTask task supervisor timed out

java.util.concurrent.TimeoutException: null
	at java.util.concurrent.FutureTask.get(FutureTask.java:205)
	at com.netflix.discovery.TimedSupervisorTask.run(TimedSupervisorTask.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

