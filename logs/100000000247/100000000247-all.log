[kbc-elms-web:*********:7002] [,] 2025-08-01 11:04:34.709 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:09:34.736 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:33.653 WARN 35316 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:33.663 WARN 35316 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:33.669 INFO 35316 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:53.278 INFO 27812 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.261 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.263 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.268 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.269 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.271 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.272 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.272 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.274 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.274 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.275 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.346 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.364 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.366 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.185 ERROR 27812 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:56 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.358 ERROR 27812 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:57 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.526 ERROR 27812 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 11:12:57 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.529 INFO 27812 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.668 INFO 27812 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:03.545 WARN 27812 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.079 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.084 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.298 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 204 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.320 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.324 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.547 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 215 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.064 INFO 27812 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.111 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.113 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.113 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.114 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.116 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.117 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.118 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.118 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.120 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.121 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.235 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.245 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.245 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.756 INFO 27812 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.779 INFO 27812 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.780 INFO 27812 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.780 INFO 27812 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.949 INFO 27812 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.950 INFO 27812 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 10236 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:08.803 INFO 27812 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.231 INFO 27812 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.615 INFO 27812 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.935 INFO 27812 [cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.936 INFO 27812 [cluster-rtt-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.942 INFO 27812 [cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=118754500}
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.974 WARN 27812 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:12.613 WARN 27812 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:13.603 DEBUG 27812 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.086 DEBUG 27812 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.152 DEBUG 27812 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.559 INFO 27812 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.624 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.990 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.014 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.053 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.075 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.089 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.112 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.128 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.172 DEBUG 27812 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.272 DEBUG 27812 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.643 DEBUG 27812 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.676 DEBUG 27812 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.755 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.813 DEBUG 27812 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.831 DEBUG 27812 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.855 DEBUG 27812 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.879 DEBUG 27812 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.914 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.948 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.970 DEBUG 27812 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.991 DEBUG 27812 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.010 DEBUG 27812 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.090 DEBUG 27812 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.137 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.201 DEBUG 27812 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.252 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.325 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.419 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.445 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.455 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.465 DEBUG 27812 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.487 DEBUG 27812 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.517 DEBUG 27812 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.538 DEBUG 27812 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.684 DEBUG 27812 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.879 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.317 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.359 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.407 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.432 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.484 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.541 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.629 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.666 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.706 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.742 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.812 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.873 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.926 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.989 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.013 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.052 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.080 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.110 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.134 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.162 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.188 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.219 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.254 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.294 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.310 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.379 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.416 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.504 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.606 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.619 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.651 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.679 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.704 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.729 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.771 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.788 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.802 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.817 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.828 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.841 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.851 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.865 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.879 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.893 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.904 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.922 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.936 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:20.763 INFO 27812 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:21.020 INFO 27812 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:22.270 INFO 27812 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:26.213 INFO 27812 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:26.717 WARN 27812 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.159 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.178 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.198 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.217 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.238 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.257 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.276 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.297 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.320 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.343 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.362 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.382 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.404 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.427 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.453 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.462 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.489 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.512 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.530 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.556 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.579 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.603 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.630 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.657 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.694 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.714 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.754 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.760 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.765 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.777 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.785 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.893 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.902 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.909 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.924 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.939 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.945 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.952 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.966 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.972 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.981 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.985 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.992 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.998 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.002 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.011 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.016 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.027 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.031 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.036 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.040 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.044 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.048 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.055 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.062 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.066 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.070 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.074 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.081 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.092 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.107 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.114 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.123 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.132 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.138 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.145 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.153 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.515 INFO 27812 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.658 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.674 INFO 27812 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.849 INFO 27812 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.857 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.868 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754018010866 with initial instances count: 11
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.882 INFO 27812 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.885 INFO 27812 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.972 INFO 27812 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.974 INFO 27812 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.054 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.128 INFO 27812 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.421 INFO 27812 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.867 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.901 WARN 27812 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.964 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.977 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.980 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.989 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.103 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.114 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.115 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.198 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.204 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.206 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.210 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.214 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.246 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.266 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.277 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.401 INFO 27812 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 40.693 seconds (JVM running for 42.637)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.695 INFO 27812 [RMI TCP Connection(12)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.695 INFO 27812 [RMI TCP Connection(12)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.715 INFO 27812 [RMI TCP Connection(12)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 18 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:33.370 INFO 27812 [RMI TCP Connection(11)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.252 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.254 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e18e736d3b75b5d9
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.255 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.256 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.339 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6c8bbfbdb92d722b
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.340 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.341 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.342 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0b184a2c68028450
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.849 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.850 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (650ms)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (142ms)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (735ms)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.993 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:14:39 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.995 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.996 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.996 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.998 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.998 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:38.999 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.535 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.748 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:39.918 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:39.918 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:39.922 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.027 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 6c8bbfbdb92d722b
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.028 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.029 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.029 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e18e736d3b75b5d9
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.033 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.034 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.036 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.036 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.141 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [e18e736d3b75b5d9,] 2025-08-01 11:14:40.142 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.148 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.148 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.149 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:14:40 GMT
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [6c8bbfbdb92d722b,] 2025-08-01 11:14:40.150 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.544 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0b184a2c68028450,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.743 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.930 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:41.144 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0b184a2c68028450, 耗时=1009, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.197 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7b82f0d213f35930
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.198 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.199 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.200 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.298 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (98ms)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:06 GMT
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.299 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.300 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.306 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.307 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.365 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.366 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7b82f0d213f35930,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.367 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.405 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.445 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7b82f0d213f35930, 耗时=79, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.081 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.082 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 26b65c7efed4cdfa
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.082 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.083 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.084 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.084 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.085 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.085 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (105ms)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.191 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:15:43 GMT
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.192 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.193 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.193 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.197 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.197 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.235 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.236 INFO 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=26b65c7efed4cdfa,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.236 DEBUG 27812 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.275 DEBUG 27812 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.313 INFO 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=26b65c7efed4cdfa, 耗时=77, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:18:29.762 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [f9e432be3d2e7fc8,] 2025-08-01 11:18:44.785 WARN 27812 [http-nio-7002-exec-6] org.springframework.web.servlet.PageNotFound Request method 'POST' not supported
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.776 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.778 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 96884663160f4ebd
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.779 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.780 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.780 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.781 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.938 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (156ms)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.940 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.941 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.942 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.984 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:18:59.985 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.026 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.030 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.030 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 96884663160f4ebd
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.031 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.032 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.032 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.104 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.105 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [96884663160f4ebd,] 2025-08-01 11:19:00.106 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.579 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.579 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 12f202063b37cdf5
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.580 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.712 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.712 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: fb461fd53e54b7c6
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.713 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.719 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.720 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.721 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.727 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.727 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.763 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 12f202063b37cdf5
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.766 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.767 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.767 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.842 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.845 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.846 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.846 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.847 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.847 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:19:00 GMT
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.848 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.851 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.852 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.853 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [12f202063b37cdf5,] 2025-08-01 11:19:00.853 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.887 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.889 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=fb461fd53e54b7c6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.891 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.930 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.970 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=fb461fd53e54b7c6, 耗时=82, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.804 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.804 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8df2c362dceb347d
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 96cb706f046fe9fd
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.806 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.807 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.808 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.928 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.928 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.929 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [8df2c362dceb347d,] 2025-08-01 11:20:39.930 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.946 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [96cb706f046fe9fd,] 2025-08-01 11:20:39.947 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.753 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 342cf46b6e2aa2a7
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.754 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.755 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.755 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ef3268f8163babbe
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.757 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.840 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.841 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.842 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [ef3268f8163babbe,] 2025-08-01 11:20:40.842 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:40 GMT
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.845 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.846 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [342cf46b6e2aa2a7,] 2025-08-01 11:20:40.846 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.912 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.912 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5bef080b9b112d90
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.913 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (52ms)
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.967 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:41 GMT
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [5bef080b9b112d90,] 2025-08-01 11:20:40.968 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.577 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.578 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c0e667e9a8ac112b
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 02e55d69093b6d61
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.579 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.673 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [02e55d69093b6d61,] 2025-08-01 11:20:42.674 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.679 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:20:43 GMT
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [c0e667e9a8ac112b,] 2025-08-01 11:20:42.680 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:23:29.783 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:28:29.791 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.326 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.330 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c3ee8463295433ff
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.331 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.331 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.332 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.332 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.333 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.333 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.679 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (345ms)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.679 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.705 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.706 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.707 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.708 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.870 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:36.871 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.043 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c3ee8463295433ff
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.047 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.048 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0956d98654dbce00
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.132 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.133 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (191ms)
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.240 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.245 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.246 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c3ee8463295433ff,] 2025-08-01 11:29:37.247 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (356ms)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:37 GMT
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.489 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.490 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.502 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.505 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.507 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: af9ae3851acd2b21
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.530 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.531 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.531 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.692 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 0956d98654dbce00
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.701 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.702 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.703 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.734 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (203ms)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.734 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:29:38 GMT
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.735 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.743 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.744 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.888 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (185ms)
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.888 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.889 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:29:38 GMT
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [0956d98654dbce00,] 2025-08-01 11:29:37.890 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.901 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.902 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=af9ae3851acd2b21,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.903 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:38.056 DEBUG 27812 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:38.217 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=af9ae3851acd2b21, 耗时=315, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.150 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.152 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c80ec2f1bece9d79
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.152 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.154 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (126ms)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.282 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.283 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.284 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.323 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.324 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.388 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c80ec2f1bece9d79
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.391 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.392 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.392 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.474 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.475 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.475 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [c80ec2f1bece9d79,] 2025-08-01 11:31:12.476 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6a09c7970eb0a651
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.562 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.563 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.682 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (119ms)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.682 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.683 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.683 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.690 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.692 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.693 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.693 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.698 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.698 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.734 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d7a1844987c8ac75
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.736 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.737 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.739 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.742 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 6a09c7970eb0a651
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.747 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.747 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.748 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.742 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.749 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.749 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.835 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.836 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [6a09c7970eb0a651,] 2025-08-01 11:31:12.837 DEBUG 27812 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:13 GMT
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.861 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.862 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.865 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.866 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.906 DEBUG 27812 [http-nio-7002-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.913 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d7a1844987c8ac75,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.915 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.952 DEBUG 27812 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.993 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d7a1844987c8ac75, 耗时=80, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.633 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1a0d9e997be735fc
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.635 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.636 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (92ms)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.729 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:31:51 GMT
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.730 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.731 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.733 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.734 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.777 DEBUG 27812 [http-nio-7002-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.778 INFO 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1a0d9e997be735fc,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.779 DEBUG 27812 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.814 DEBUG 27812 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.856 INFO 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1a0d9e997be735fc, 耗时=78, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.307 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.308 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a0eaca21d6795def
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.308 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.309 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.310 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.311 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:32:26 GMT
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.405 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.406 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.416 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.420 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.457 DEBUG 27812 [http-nio-7002-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.459 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a0eaca21d6795def,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.459 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.497 DEBUG 27812 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.537 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a0eaca21d6795def, 耗时=78, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.303 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.339 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e9a965fbba31fa24
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.357 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.360 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.360 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.498 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (137ms)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.498 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.500 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.501 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.501 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.506 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.506 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.565 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e9a965fbba31fa24
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.570 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.571 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.651 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.653 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.653 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.654 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.655 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.657 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.658 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [e9a965fbba31fa24,] 2025-08-01 11:33:13.659 DEBUG 27812 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2eebb716d2155556
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.318 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.319 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.320 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:14 GMT
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.410 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.411 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.434 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.437 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.438 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.486 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.489 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.489 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 2eebb716d2155556
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.490 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.585 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (95ms)
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:33:15 GMT
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.586 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [2eebb716d2155556,] 2025-08-01 11:33:14.587 DEBUG 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b8017f712a0b2fd6
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:14.949 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:33:15 GMT
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.022 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.023 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.023 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.037 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.037 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.041 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.041 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.083 DEBUG 27812 [http-nio-7002-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.084 INFO 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b8017f712a0b2fd6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.085 DEBUG 27812 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.122 DEBUG 27812 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.171 INFO 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b8017f712a0b2fd6, 耗时=87, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:33:29.803 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.243 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.244 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 035d09a19dd3c788
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.245 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.354 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.355 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.358 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.359 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.359 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.362 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.370 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.371 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.426 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 035d09a19dd3c788
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.430 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.431 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (62ms)
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.494 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [035d09a19dd3c788,] 2025-08-01 11:34:04.497 DEBUG 27812 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.645 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.645 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f933517e60e897d3
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.646 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (86ms)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.732 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.733 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.737 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.775 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.779 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.779 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: f933517e60e897d3
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.780 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.781 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.781 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (66ms)
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.848 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [f933517e60e897d3,] 2025-08-01 11:34:04.849 DEBUG 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e3bbfaeeb8a9e5c5
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.879 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"1d25ce427d27b69a93d14a90061733c1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.880 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (82ms)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.963 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 01 Aug 2025 03:34:05 GMT
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.964 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.967 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:04.967 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.007 DEBUG 27812 [http-nio-7002-exec-1] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 11
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.008 INFO 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e3bbfaeeb8a9e5c5,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.009 DEBUG 27812 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.046 DEBUG 27812 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.083 INFO 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e3bbfaeeb8a9e5c5, 耗时=75, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:12.916 WARN 27812 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:12.918 INFO 27812 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:12.919 WARN 27812 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.128 INFO 27812 [SpringContextShutdownHook] org.mongodb.driver.connection Closed connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017 because the pool has been closed.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.145 INFO 27812 [SpringContextShutdownHook] com.alibaba.druid.pool.DruidDataSource {dataSource-1} closed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.291 INFO 27812 [SpringContextShutdownHook] com.netflix.discovery.DiscoveryClient Shutting down DiscoveryClient ...
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.295 INFO 27812 [SpringContextShutdownHook] com.netflix.discovery.DiscoveryClient Completed shut down of DiscoveryClient
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:56.763 INFO 8636 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.887 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.889 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.897 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.898 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.900 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.901 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.902 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.997 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:58.016 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:58.018 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.120 WARN 8636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar] & group[group-kbc-elms]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.126 WARN 8636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar.yml] & group[group-kbc-elms]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.127 WARN 8636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder Ignore the empty nacos configuration and get it based on dataId[sta-kbcs-app-elms-web-jar-dev.yml] & group[group-kbc-elms]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.129 INFO 8636 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.215 INFO 8636 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.206 WARN 8636 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.822 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.825 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.944 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 110 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.979 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.982 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.095 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 103 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.756 INFO 8636 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.812 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.813 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.813 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.814 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.818 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.818 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.251 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.258 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.258 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.852 INFO 8636 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.883 INFO 8636 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.884 INFO 8636 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.885 INFO 8636 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:11.058 INFO 8636 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:11.060 INFO 8636 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 11766 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:12.919 INFO 8636 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.634 ERROR 8636 [main] com.alibaba.druid.pool.DruidDataSource init datasource error, url: ************************************************************************************************************************************************************

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:918)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1481)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:671)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:659)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1300)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:82)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.lambda$createEndpointBean$1(EndpointDiscoverer.java:145)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer$EndpointBean.getBean(EndpointDiscoverer.java:447)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getFilterEndpoint(EndpointDiscoverer.java:307)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.isFilterMatch(EndpointDiscoverer.java:285)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.isExtensionExposed(EndpointDiscoverer.java:239)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.addExtensionBean(EndpointDiscoverer.java:170)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.addExtensionBeans(EndpointDiscoverer.java:159)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:124)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:117)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration$ActuatorSkipPatternProviderConfig.getEndpointsPatterns(SkipPatternConfiguration.java:152)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration$ActuatorSkipPatternProviderConfig.lambda$skipPatternForActuatorEndpointsSamePort$0(SkipPatternConfiguration.java:207)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration.consolidateSkipPatterns(SkipPatternConfiguration.java:96)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration.sleuthSkipPatternProvider(SkipPatternConfiguration.java:80)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1234)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.tracingFilter(TraceWebServletConfiguration.java:125)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.init(TraceWebServletConfiguration.java:108)
	at org.apache.catalina.core.ApplicationFilterConfig.initFilter(ApplicationFilterConfig.java:270)
	at org.apache.catalina.core.ApplicationFilterConfig.<init>(ApplicationFilterConfig.java:105)
	at org.apache.catalina.core.StandardContext.filterStart(StandardContext.java:4613)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5256)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:919)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:835)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:919)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:263)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:432)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:927)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:450)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:199)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:181)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:159)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:577)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 192 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867)
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302)
	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 195 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.650 ERROR 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} init error

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:918)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1481)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:671)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:659)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1300)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:82)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.lambda$createEndpointBean$1(EndpointDiscoverer.java:145)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer$EndpointBean.getBean(EndpointDiscoverer.java:447)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getFilterEndpoint(EndpointDiscoverer.java:307)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.isFilterMatch(EndpointDiscoverer.java:285)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.isExtensionExposed(EndpointDiscoverer.java:239)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.addExtensionBean(EndpointDiscoverer.java:170)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.addExtensionBeans(EndpointDiscoverer.java:159)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:124)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:117)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration$ActuatorSkipPatternProviderConfig.getEndpointsPatterns(SkipPatternConfiguration.java:152)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration$ActuatorSkipPatternProviderConfig.lambda$skipPatternForActuatorEndpointsSamePort$0(SkipPatternConfiguration.java:207)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration.consolidateSkipPatterns(SkipPatternConfiguration.java:96)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration.sleuthSkipPatternProvider(SkipPatternConfiguration.java:80)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1234)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.tracingFilter(TraceWebServletConfiguration.java:125)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.init(TraceWebServletConfiguration.java:108)
	at org.apache.catalina.core.ApplicationFilterConfig.initFilter(ApplicationFilterConfig.java:270)
	at org.apache.catalina.core.ApplicationFilterConfig.<init>(ApplicationFilterConfig.java:105)
	at org.apache.catalina.core.StandardContext.filterStart(StandardContext.java:4613)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5256)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:919)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:835)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:919)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:263)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:432)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:927)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:450)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:199)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:181)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:159)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:577)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 192 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867)
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302)
	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 195 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.651 INFO 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.652 ERROR 8636 [Druid-ConnectionPool-Create-1669705853] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.655 ERROR 8636 [Druid-ConnectionPool-Create-1669705853] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.751 DEBUG 8636 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.907 INFO 8636 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.937 ERROR 8636 [main] com.alibaba.druid.pool.DruidDataSource init datasource error, url: ************************************************************************************************************************************************************

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:918)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 86 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 89 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.939 ERROR 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-2} init error

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:918)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 86 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 89 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.941 INFO 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-2} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.941 ERROR 8636 [Druid-ConnectionPool-Create-481135566] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.942 WARN 8636 [main] org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authService': Unsatisfied dependency expressed through field 'sqlSession'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionTemplate' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.943 ERROR 8636 [Druid-ConnectionPool-Create-481135566] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.150 INFO 8636 [main] org.apache.catalina.core.StandardService Stopping service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.156 WARN 8636 [main] org.apache.catalina.loader.WebappClassLoaderBase The web application [ROOT] appears to have started a thread named [Druid-ConnectionPool-Create-1669705853] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2593)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.157 WARN 8636 [main] org.apache.catalina.loader.WebappClassLoaderBase The web application [ROOT] appears to have started a thread named [Druid-ConnectionPool-Destroy-1669705853] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 com.alibaba.druid.pool.DruidDataSource$DestroyConnectionThread.run(DruidDataSource.java:2641)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.165 ERROR 8636 [Druid-ConnectionPool-Create-1669705853] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.186 INFO 8636 [main] org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.286 ERROR 8636 [main] org.springframework.boot.SpringApplication Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authService': Unsatisfied dependency expressed through field 'sqlSession'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionTemplate' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authService': Unsatisfied dependency expressed through field 'sqlSession'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionTemplate' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 21 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionTemplate' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 35 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/kbao/kbcbsc/mybatis/MybatisConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 62 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:918)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 73 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 86 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 89 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.459 ERROR 8636 [Druid-ConnectionPool-Create-481135566] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.665 ERROR 8636 [Druid-ConnectionPool-Create-1669705853] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.962 ERROR 8636 [Druid-ConnectionPool-Create-481135566] com.alibaba.druid.pool.DruidDataSource create connection SQLException, url: ************************************************************************************************************************************************************, errorCode 0, state 08S01

com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:862)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:444)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:230)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:226)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1644)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2570)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:92)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:152)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:982)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:852)
	... 6 common frames omitted
Caused by: java.net.UnknownHostException: mysql-kbcs-test-lan.kbao123.com
	at java.net.InetAddress$CachedAddresses.get(InetAddress.java:764)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:150)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:66)
	... 9 common frames omitted

[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:15.041 WARN 8636 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:15.044 WARN 8636 [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
