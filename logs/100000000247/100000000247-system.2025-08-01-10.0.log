{"@timestamp":"2025-08-01T10:02:59.185+08:00","traceId":"9e62cea078f6f7e4","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"同步\",\"appName\":\"kbc-elms-web\",\"createTime\":1754013413612,\"desc\":\"同步企业所有信息\",\"flag\":false,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/syncEnterpriseAllInfo\",\"module\":\"企业信息管理\",\"params\":\"\\\"浙江百达精工股份有限公司\\\"\",\"remark\":\"同步企业信息失败：同步最终受益人信息失败：com.alibaba.fastjson.JSONArray cannot be cast to com.alibaba.fastjson.JSONObject\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":365573,\"traceId\":\"9e62cea078f6f7e4\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:04:41.622+08:00","traceId":"4a6382309551d1a1","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"同步\",\"appName\":\"kbc-elms-web\",\"createTime\":1754013873482,\"desc\":\"同步企业所有信息\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/syncEnterpriseAllInfo\",\"module\":\"企业信息管理\",\"params\":\"\\\"浙江百达精工股份有限公司\\\"\",\"response\":\"{\\\"datas\\\":\\\"企业信息同步成功\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":8140,\"traceId\":\"4a6382309551d1a1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:04:51.648+08:00","traceId":"541a5d3f4e629f3b","remoteIp":"0:0:0:0:0:0:0:1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"同步\",\"appName\":\"kbc-elms-web\",\"createTime\":1754013890282,\"desc\":\"同步企业所有信息\",\"flag\":true,\"ip\":\"0:0:0:0:0:0:0:1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/syncEnterpriseAllInfo\",\"module\":\"企业信息管理\",\"params\":\"\\\"浙江百达精工股份有限公司\\\"\",\"response\":\"{\\\"datas\\\":\\\"企业信息同步成功\\\",\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1364,\"traceId\":\"541a5d3f4e629f3b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:17:56.543+08:00","traceId":"8f96864603feaf68","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754014676402,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"agent_enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"creditCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会统一信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"categoryCode\\\",\\\"fieldLength\\\":\\\"10\\\",\\\"fieldName\\\":\\\"行业代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78a\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"最小行业代码\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"categoryName\\\",\\\"fieldLength\\\":\\\"100\\\",\\\"fieldName\\\":\\\"行业类名\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78b\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"行业名全称，显示多级\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"dtType\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"enterpriseScale\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业规模\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688acfedf0160463eae40700\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"企业所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"districtCode\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"行政区划代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688acfedf0160463eae40701\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffScale\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业年收入\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e789\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":11,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":12,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":13,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":14,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":130,\"traceId\":\"8f96864603feaf68\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:17:57.373+08:00","traceId":"8fcf0ca60ff1c30a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754014676713,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"agent_enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":10,\\\"hasNextPage\\\":true,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":false,\\\"list\\\":[{\\\"districtCode\\\":\\\"110000\\\",\\\"annualIncome\\\":\\\"1万亿以上\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张经理\\\",\\\"remark\\\":\\\"大型央企石油公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"B\\\",\\\"categoryName\\\":\\\"采矿业\\\",\\\"enterpriseScale\\\":\\\"央企\\\",\\\"creditCode\\\":\\\"91110000100020821G\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138001\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国石油天然气集团有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"districtCode\\\":\\\"110000\\\",\\\"annualIncome\\\":\\\"5000亿-1万亿\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"李总监\\\",\\\"remark\\\":\\\"建筑行业龙头企业\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"E\\\",\\\"categoryName\\\":\\\"建筑业\\\",\\\"enterpriseScale\\\":\\\"央企\\\",\\\"creditCode\\\":\\\"91110000100020822H\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138002\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国建筑集团有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"王主管\\\",\\\"remark\\\":\\\"互联网科技公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300279146858G\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138003\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"腾讯控股有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"500亿-1000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"赵部长\\\",\\\"remark\\\":\\\"新能源汽车制造商\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"C\\\",\\\"categoryName\\\":\\\"制造业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300192317458K\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138004\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1-5万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"比亚迪股份有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"陈经理\\\",\\\"remark\\\":\\\"通信设备制造商\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91440300708461136T\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138005\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"华为技术有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":5},{\\\"districtCode\\\":\\\"330100\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"杭州市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"刘总\\\",\\\"remark\\\":\\\"电商平台公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91330000MA27XL0E2Y\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138006\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"阿里巴巴集团控股有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":6},{\\\"districtCode\\\":\\\"310000\\\",\\\"annualIncome\\\":\\\"100亿-500亿\\\",\\\"dtType\\\":\\\"D\\\",\\\"city\\\":\\\"上海市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"周主任\\\",\\\"remark\\\":\\\"社交电商平台\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91310000MA1FL5LX3X\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138007\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1000-5000人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"上海小红书科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":7},{\\\"districtCode\\\":\\\"110108\\\",\\\"annualIncome\\\":\\\"500亿-1000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"孙经理\\\",\\\"remark\\\":\\\"短视频平台公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91110108MA00A1JE4R\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138008\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"北京字节跳动科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":8},{\\\"districtCode\\\":\\\"320500\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"苏州市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"吴厂长\\\",\\\"remark\\\":\\\"电子产品代工制造\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"C\\\",\\\"categoryName\\\":\\\"制造业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91320000134582639F\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138009\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"富士康科技集团\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":9},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1万亿以上\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"郑总监\\\",\\\"remark\\\":\\\"综合金融服务集团\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"J\\\",\\\"categoryName\\\":\\\"金融业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300100020611K\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138010\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1-5万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国平安保险(集团)股份有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":10}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":3,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1,2,3],\\\"nextPage\\\":2,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":3,\\\"prePage\\\":0,\\\"size\\\":10,\\\"startRow\\\":1,\\\"total\\\":24},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":659,\"traceId\":\"8fcf0ca60ff1c30a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:18:36.577+08:00","traceId":"7e04b960aff269b7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754014716475,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"agent_enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"creditCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会统一信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"categoryCode\\\",\\\"fieldLength\\\":\\\"10\\\",\\\"fieldName\\\":\\\"行业代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78a\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"最小行业代码\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"categoryName\\\",\\\"fieldLength\\\":\\\"100\\\",\\\"fieldName\\\":\\\"行业类名\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e78b\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"行业名全称，显示多级\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"dtType\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"enterpriseScale\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业规模\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688acfedf0160463eae40700\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"企业所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"districtCode\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"行政区划代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688acfedf0160463eae40701\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffScale\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业年收入\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688882b6c3add91428f4e789\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":11,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":12,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"1\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":13,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":14,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":101,\"traceId\":\"7e04b960aff269b7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:18:36.972+08:00","traceId":"33f411d4ba2c8c2a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754014716780,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"agent_enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":10,\\\"hasNextPage\\\":true,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":false,\\\"list\\\":[{\\\"districtCode\\\":\\\"110000\\\",\\\"annualIncome\\\":\\\"1万亿以上\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张经理\\\",\\\"remark\\\":\\\"大型央企石油公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"B\\\",\\\"categoryName\\\":\\\"采矿业\\\",\\\"enterpriseScale\\\":\\\"央企\\\",\\\"creditCode\\\":\\\"91110000100020821G\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138001\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国石油天然气集团有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"districtCode\\\":\\\"110000\\\",\\\"annualIncome\\\":\\\"5000亿-1万亿\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"李总监\\\",\\\"remark\\\":\\\"建筑行业龙头企业\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"E\\\",\\\"categoryName\\\":\\\"建筑业\\\",\\\"enterpriseScale\\\":\\\"央企\\\",\\\"creditCode\\\":\\\"91110000100020822H\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138002\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国建筑集团有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"王主管\\\",\\\"remark\\\":\\\"互联网科技公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300279146858G\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138003\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"腾讯控股有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"500亿-1000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"赵部长\\\",\\\"remark\\\":\\\"新能源汽车制造商\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"C\\\",\\\"categoryName\\\":\\\"制造业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300192317458K\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138004\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1-5万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"比亚迪股份有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"陈经理\\\",\\\"remark\\\":\\\"通信设备制造商\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91440300708461136T\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138005\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"华为技术有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":5},{\\\"districtCode\\\":\\\"330100\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"B\\\",\\\"city\\\":\\\"杭州市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"刘总\\\",\\\"remark\\\":\\\"电商平台公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91330000MA27XL0E2Y\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138006\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"阿里巴巴集团控股有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":6},{\\\"districtCode\\\":\\\"310000\\\",\\\"annualIncome\\\":\\\"100亿-500亿\\\",\\\"dtType\\\":\\\"D\\\",\\\"city\\\":\\\"上海市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"周主任\\\",\\\"remark\\\":\\\"社交电商平台\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91310000MA1FL5LX3X\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138007\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1000-5000人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"上海小红书科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":7},{\\\"districtCode\\\":\\\"110108\\\",\\\"annualIncome\\\":\\\"500亿-1000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"北京市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"孙经理\\\",\\\"remark\\\":\\\"短视频平台公司\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"I\\\",\\\"categoryName\\\":\\\"信息传输、软件和信息技术服务业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91110108MA00A1JE4R\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138008\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"5-10万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"北京字节跳动科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":8},{\\\"districtCode\\\":\\\"320500\\\",\\\"annualIncome\\\":\\\"1000亿-5000亿\\\",\\\"dtType\\\":\\\"C\\\",\\\"city\\\":\\\"苏州市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"吴厂长\\\",\\\"remark\\\":\\\"电子产品代工制造\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"C\\\",\\\"categoryName\\\":\\\"制造业\\\",\\\"enterpriseScale\\\":\\\"大型企业\\\",\\\"creditCode\\\":\\\"91320000134582639F\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138009\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"10万人以上\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"富士康科技集团\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":9},{\\\"districtCode\\\":\\\"440300\\\",\\\"annualIncome\\\":\\\"1万亿以上\\\",\\\"dtType\\\":\\\"A\\\",\\\"city\\\":\\\"深圳市\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"郑总监\\\",\\\"remark\\\":\\\"综合金融服务集团\\\",\\\"updateTime\\\":1753928747000,\\\"categoryCode\\\":\\\"J\\\",\\\"categoryName\\\":\\\"金融业\\\",\\\"enterpriseScale\\\":\\\"上市公司\\\",\\\"creditCode\\\":\\\"91440300100020611K\\\",\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13800138010\\\",\\\"createTime\\\":1753928747000,\\\"staffScale\\\":\\\"1-5万人\\\",\\\"createId\\\":\\\"admin\\\",\\\"name\\\":\\\"中国平安保险(集团)股份有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":10}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":3,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1,2,3],\\\"nextPage\\\":2,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":3,\\\"prePage\\\":0,\\\"size\\\":10,\\\"startRow\\\":1,\\\"total\\\":24},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":189,\"traceId\":\"33f411d4ba2c8c2a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:28:38.239+08:00","traceId":"81d3f546ea2f39d8","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754015318078,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":160,\"traceId\":\"81d3f546ea2f39d8\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:28:46.906+08:00","traceId":"f9ef7e51589c3b77","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754015326834,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":72,\"traceId\":\"f9ef7e51589c3b77\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:36:49.137+08:00","traceId":"03a558811d5ca8c9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754015809067,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"03a558811d5ca8c9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-01T10:37:18.401+08:00","traceId":"10076eadaa8de428","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754015838332,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":68,\"traceId\":\"10076eadaa8de428\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
