[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:02:28.836 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [6d99073d5b7f5812,] 2025-07-25 18:02:58.958 INFO 16728 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6d99073d5b7f5812,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [6d99073d5b7f5812,] 2025-07-25 18:02:59.076 INFO 16728 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6d99073d5b7f5812, 耗时=118, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [e74e944873cb487e,] 2025-07-25 18:03:07.054 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e74e944873cb487e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [e74e944873cb487e,] 2025-07-25 18:03:07.142 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e74e944873cb487e, 耗时=88, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [6ca8ef336a088209,] 2025-07-25 18:03:16.346 INFO 16728 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6ca8ef336a088209,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:10.78.8.1:7002] [6ca8ef336a088209,] 2025-07-25 18:03:16.422 INFO 16728 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6ca8ef336a088209, 耗时=76, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [c005be998cadd6f1,] 2025-07-25 18:03:16.612 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c005be998cadd6f1,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:10.78.8.1:7002] [c005be998cadd6f1,] 2025-07-25 18:03:16.745 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c005be998cadd6f1, 耗时=133, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:07:28.853 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [0ef45d9c9ac7aa56,] 2025-07-25 18:10:55.580 INFO 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0ef45d9c9ac7aa56,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [0ef45d9c9ac7aa56,] 2025-07-25 18:10:55.877 INFO 16728 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0ef45d9c9ac7aa56, 耗时=297, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [5a2e5ef035d2a3db,] 2025-07-25 18:11:00.733 INFO 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5a2e5ef035d2a3db,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [5a2e5ef035d2a3db,] 2025-07-25 18:11:01.060 INFO 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5a2e5ef035d2a3db, 耗时=327, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:12:28.864 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [0b91779d4000f65f,] 2025-07-25 18:13:56.164 INFO 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0b91779d4000f65f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [0b91779d4000f65f,] 2025-07-25 18:14:03.564 INFO 16728 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0b91779d4000f65f, 耗时=7400, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [6eebd79697deb615,] 2025-07-25 18:14:05.322 INFO 16728 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6eebd79697deb615,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [6eebd79697deb615,] 2025-07-25 18:14:05.399 INFO 16728 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6eebd79697deb615, 耗时=76, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [9fe9f8f169d71847,] 2025-07-25 18:14:14.609 INFO 16728 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9fe9f8f169d71847,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [9fe9f8f169d71847,] 2025-07-25 18:14:14.688 INFO 16728 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9fe9f8f169d71847, 耗时=78, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [d9404734a569d76f,] 2025-07-25 18:14:23.479 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d9404734a569d76f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [d9404734a569d76f,] 2025-07-25 18:14:23.566 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d9404734a569d76f, 耗时=88, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:17:28.867 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [ce6d5035a0c13ee2,] 2025-07-25 18:17:55.628 INFO 16728 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ce6d5035a0c13ee2,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:10.78.8.1:7002] [ce6d5035a0c13ee2,] 2025-07-25 18:17:55.751 INFO 16728 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ce6d5035a0c13ee2, 耗时=123, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:22:28.878 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [471a9493630a5e74,] 2025-07-25 18:23:00.404 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=471a9493630a5e74,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:10.78.8.1:7002] [471a9493630a5e74,] 2025-07-25 18:23:00.522 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=471a9493630a5e74, 耗时=118, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [b79cb2ba6ba361ea,] 2025-07-25 18:23:00.939 INFO 16728 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b79cb2ba6ba361ea,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:10.78.8.1:7002] [b79cb2ba6ba361ea,] 2025-07-25 18:23:01.069 INFO 16728 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b79cb2ba6ba361ea, 耗时=130, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [bd27cf438cfc3a84,] 2025-07-25 18:23:04.204 INFO 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bd27cf438cfc3a84,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [bd27cf438cfc3a84,] 2025-07-25 18:23:04.280 INFO 16728 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bd27cf438cfc3a84, 耗时=77, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:27:28.883 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [d8dc00fa85012921,] 2025-07-25 18:31:40.294 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d8dc00fa85012921,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:10.78.8.1:7002] [d8dc00fa85012921,] 2025-07-25 18:31:40.414 INFO 16728 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d8dc00fa85012921, 耗时=120, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [4a488961fb80b238,] 2025-07-25 18:31:40.734 INFO 16728 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4a488961fb80b238,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:10.78.8.1:7002] [4a488961fb80b238,] 2025-07-25 18:31:40.876 INFO 16728 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4a488961fb80b238, 耗时=142, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [eff45fa51752e313,] 2025-07-25 18:32:08.545 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=eff45fa51752e313,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:10.78.8.1:7002] [eff45fa51752e313,] 2025-07-25 18:32:08.620 INFO 16728 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=eff45fa51752e313, 耗时=75, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:32:28.890 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:37:28.905 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:42:28.923 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:47:28.932 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:52:28.942 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 18:57:28.955 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
