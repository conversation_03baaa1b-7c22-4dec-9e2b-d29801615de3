[kbc-elms-web:*********:7002] [,] 2025-07-25 14:00:04.006 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [416f2b1f5f979364,] 2025-07-25 14:00:58.706 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=416f2b1f5f979364,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:*********:7002] [416f2b1f5f979364,] 2025-07-25 14:00:58.881 INFO 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=416f2b1f5f979364, 耗时=175, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [63538b45de01e0f2,] 2025-07-25 14:00:58.994 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=63538b45de01e0f2,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:*********:7002] [63538b45de01e0f2,] 2025-07-25 14:00:59.139 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=63538b45de01e0f2, 耗时=145, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e5632b0e554b9e60,] 2025-07-25 14:02:13.509 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e5632b0e554b9e60,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [e5632b0e554b9e60,] 2025-07-25 14:02:13.625 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e5632b0e554b9e60, 耗时=116, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"company","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1b7b48c5cec1af54,] 2025-07-25 14:02:17.258 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1b7b48c5cec1af54,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [1b7b48c5cec1af54,] 2025-07-25 14:02:17.332 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1b7b48c5cec1af54, 耗时=74, resp={"datas":{"bizCode":"company","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:05:04.025 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [c8320e33fe008a3f,] 2025-07-25 14:05:13.944 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c8320e33fe008a3f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:*********:7002] [c8320e33fe008a3f,] 2025-07-25 14:05:14.062 INFO 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c8320e33fe008a3f, 耗时=118, resp={"datas":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [49b5b278becb60c9,] 2025-07-25 14:05:14.176 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=49b5b278becb60c9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"company"}}} 
[kbc-elms-web:*********:7002] [49b5b278becb60c9,] 2025-07-25 14:05:14.290 INFO 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=49b5b278becb60c9, 耗时=114, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"income":111,"tenant_id":"T0001","industry_id":1,"city":"背景","createTime":1753355126000,"name":"小米","company_code":"xx","id":1,"staff_size":100},{"income":22,"tenant_id":"T0001","industry_id":2,"city":"北京","createTime":1753355127000,"name":"美团","company_code":"11","id":2,"staff_size":22},{"income":33,"tenant_id":"T0001","industry_id":3,"city":"背景","createTime":1753355127000,"name":"京东","company_code":"xx","id":3,"staff_size":33},{"income":44,"tenant_id":"T0001","industry_id":4,"city":"杭州","createTime":1753355127000,"name":"淘宝","company_code":"df","id":4,"staff_size":44}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b133b243c6ec0b01,] 2025-07-25 14:05:42.671 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b133b243c6ec0b01,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [b133b243c6ec0b01,] 2025-07-25 14:05:42.757 INFO 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b133b243c6ec0b01, 耗时=86, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"company","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [c8b3cee4381781a2,] 2025-07-25 14:05:44.888 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c8b3cee4381781a2,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [c8b3cee4381781a2,] 2025-07-25 14:05:44.964 INFO 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c8b3cee4381781a2, 耗时=76, resp={"datas":{"bizCode":"company","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"company_code","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","remark":"","required":"0","showType":"1","sort":2,"templateId":3,"validation":""},{"additional":{"input_unit":"人"},"change":"1","defaultValue":"","fieldCode":"staff_size","fieldLength":"","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","remark":"","required":"0","showType":"1","sort":3,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"industry_id","fieldLength":"","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","remark":"","required":"0","showType":"1","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","defaultValue":"","fieldCode":"income","fieldLength":"","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:10:04.031 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:15:04.043 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:20:04.047 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:25:04.053 INFO 23660 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [ad1d9c72462f7fce,] 2025-07-25 14:25:37.123 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ad1d9c72462f7fce,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","defaultValue":"","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"0","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"contacterPhone","fieldLength":"","fieldName":"联系人电话","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{},"change":"1","defaultValue":"","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"6","validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}} 
[kbc-elms-web:*********:7002] [ad1d9c72462f7fce,] 2025-07-25 14:25:37.607 INFO 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ad1d9c72462f7fce, 耗时=484, resp=null:
[kbc-elms-web:*********:7002] [d397333ede247198,] 2025-07-25 14:25:43.962 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d397333ede247198,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","defaultValue":"","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"0","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"contacterPhone","fieldLength":"","fieldName":"联系人电话","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{},"change":"1","defaultValue":"","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"6","validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}} 
[kbc-elms-web:*********:7002] [d397333ede247198,] 2025-07-25 14:25:44.129 INFO 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d397333ede247198, 耗时=167, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:28:43.200 INFO 23660 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:24.788 INFO 19848 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.669 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.671 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.675 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.677 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.679 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.681 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.682 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.683 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.683 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.683 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.787 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.793 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:31:25.794 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:26.049 INFO 19848 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.575 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.579 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.675 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 89 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.696 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.699 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:31.791 INFO 19848 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 83 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.219 INFO 19848 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.256 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.257 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.257 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.257 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.258 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.258 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.258 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.258 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.259 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.259 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.259 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.260 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:32.260 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.227 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.236 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.237 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.729 INFO 19848 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.751 INFO 19848 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.752 INFO 19848 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.753 INFO 19848 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.905 INFO 19848 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:33.905 INFO 19848 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 7820 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:35.398 INFO 19848 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:37.808 INFO 19848 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:38.158 INFO 19848 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:38.623 INFO 19848 [cluster-ClusterId{value='688324ca585c3e034bbc602f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:38.623 INFO 19848 [cluster-rtt-ClusterId{value='688324ca585c3e034bbc602f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:38.626 INFO 19848 [cluster-ClusterId{value='688324ca585c3e034bbc602f', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=270024700}
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.110 INFO 19848 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.175 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.701 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.723 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.762 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.785 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.800 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.844 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.861 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:41.897 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.059 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.090 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.184 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.518 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.654 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.717 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:42.802 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.200 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.242 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.287 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.315 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.361 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.410 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.465 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.500 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.533 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.564 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.611 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.666 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.715 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.744 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.774 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.798 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.825 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.850 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.878 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.904 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.931 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.956 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:43.987 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.014 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.041 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.056 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.115 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.148 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.218 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.345 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.354 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.384 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.414 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.437 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.455 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.478 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.492 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.501 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.513 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.524 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.537 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.546 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.557 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.566 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.578 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.591 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.609 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.619 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.633 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.645 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:44.657 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:45.331 INFO 19848 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:45.583 INFO 19848 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:46.691 INFO 19848 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:48.431 INFO 19848 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.012 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.030 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.047 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.062 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.079 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.094 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.110 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.127 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.145 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.164 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.181 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.196 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.212 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.231 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.247 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.253 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.272 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.293 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.310 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.337 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.355 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.372 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.390 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.409 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.426 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.442 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.468 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.476 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.480 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.494 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.501 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.507 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.513 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.518 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.525 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.532 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.537 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.541 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.547 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.554 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.558 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.564 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.570 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.577 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.581 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.586 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.593 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.598 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.607 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.610 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.615 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.619 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.622 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.626 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.633 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.639 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.642 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.646 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.651 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.657 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.669 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.680 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.685 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.688 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.693 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.698 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.702 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.707 INFO 19848 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:50.989 INFO 19848 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.102 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.117 INFO 19848 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.182 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:51.183 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.271 INFO 19848 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.276 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.285 INFO 19848 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753425112282 with initial instances count: 10
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.294 INFO 19848 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.296 INFO 19848 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.364 INFO 19848 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.367 INFO 19848 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.367 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.367 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.367 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.368 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.369 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.369 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.369 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.371 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.371 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.434 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.435 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.442 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.442 INFO 19848 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.442 INFO 19848 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.491 INFO 19848 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:52.713 INFO 19848 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.136 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.215 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getTenantUsersUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.216 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getWebUserInfoUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.260 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.266 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.271 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.276 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.298 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.316 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.326 INFO 19848 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.447 INFO 19848 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 30.096 seconds (JVM running for 31.782)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.762 INFO 19848 [RMI TCP Connection(5)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.762 INFO 19848 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:53.783 INFO 19848 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 20 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:31:54.279 INFO 19848 [RMI TCP Connection(3)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [202d03adf0176ce3,] 2025-07-25 14:32:47.748 INFO 19848 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=202d03adf0176ce3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","defaultValue":"","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"0","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"contacterPhone","fieldLength":"","fieldName":"联系人电话","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{},"change":"1","defaultValue":"","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"6","validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753354888000}} 
[kbc-elms-web:*********:7002] [202d03adf0176ce3,] 2025-07-25 14:32:49.461 INFO 19848 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=202d03adf0176ce3, 耗时=1987, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [feaf492f7e87ac31,] 2025-07-25 14:32:49.735 INFO 19848 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=feaf492f7e87ac31,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [feaf492f7e87ac31,] 2025-07-25 14:32:50.150 INFO 19848 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=feaf492f7e87ac31, 耗时=417, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425168000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [bd1f6ef535002314,] 2025-07-25 14:32:56.119 INFO 19848 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bd1f6ef535002314,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [bd1f6ef535002314,] 2025-07-25 14:32:56.338 INFO 19848 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bd1f6ef535002314, 耗时=218, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","defaultValue":"","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","remark":"","required":"0","showType":"0","sort":7,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","remark":"","required":"0","showType":"1","sort":8,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","remark":"","required":"0","showType":"1","sort":9,"templateId":3,"validation":""},{"additional":{"input_unit":""},"change":"1","defaultValue":"","fieldCode":"contacterPhone","fieldLength":"","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","remark":"","required":"0","showType":"1","sort":10,"templateId":3,"validation":""},{"additional":{},"change":"1","defaultValue":"","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","remark":"","required":"0","showType":"6","sort":11,"templateId":3,"validation":""}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425168000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [ab1a4a7d57d86d32,] 2025-07-25 14:33:46.375 INFO 19848 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ab1a4a7d57d86d32,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":5,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":7,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425168000}} 
[kbc-elms-web:*********:7002] [ab1a4a7d57d86d32,] 2025-07-25 14:33:46.934 INFO 19848 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ab1a4a7d57d86d32, 耗时=560, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [390d888b355b0201,] 2025-07-25 14:33:47.404 INFO 19848 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=390d888b355b0201,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [390d888b355b0201,] 2025-07-25 14:33:47.473 INFO 19848 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=390d888b355b0201, 耗时=69, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425226000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [08c843897f4ec649,] 2025-07-25 14:33:49.809 INFO 19848 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=08c843897f4ec649,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [08c843897f4ec649,] 2025-07-25 14:33:49.888 INFO 19848 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=08c843897f4ec649, 耗时=79, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425226000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:36:51.191 INFO 19848 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [83aec18b9068caf1,] 2025-07-25 14:38:09.178 INFO 19848 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=83aec18b9068caf1,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [83aec18b9068caf1,] 2025-07-25 14:38:09.321 INFO 19848 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=83aec18b9068caf1, 耗时=143, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425226000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [9b06fe82ccc83a7f,] 2025-07-25 14:39:09.004 INFO 19848 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9b06fe82ccc83a7f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [9b06fe82ccc83a7f,] 2025-07-25 14:39:09.080 INFO 19848 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9b06fe82ccc83a7f, 耗时=76, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425226000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e3562fed023b142a,] 2025-07-25 14:39:41.247 INFO 19848 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e3562fed023b142a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425226000}} 
[kbc-elms-web:*********:7002] [e3562fed023b142a,] 2025-07-25 14:39:41.877 INFO 19848 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e3562fed023b142a, 耗时=630, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [07a68284d0f66a81,] 2025-07-25 14:39:42.359 INFO 19848 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=07a68284d0f66a81,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [07a68284d0f66a81,] 2025-07-25 14:39:42.432 INFO 19848 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=07a68284d0f66a81, 耗时=73, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:29.661 INFO 19848 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:37.031 INFO 22208 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:37.990 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:37.991 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:37.998 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:37.999 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.000 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.002 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.002 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.003 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.003 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.003 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.188 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.202 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 14:40:38.207 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:38.585 INFO 22208 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.140 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.144 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.246 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 95 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.267 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.269 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.381 INFO 22208 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 103 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.816 INFO 22208 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.856 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.857 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.857 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.857 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.858 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.858 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.858 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.858 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.859 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.859 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.860 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.860 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:45.860 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:46.844 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:46.854 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:46.854 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.346 INFO 22208 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.369 INFO 22208 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.370 INFO 22208 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.370 INFO 22208 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.524 INFO 22208 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:47.524 INFO 22208 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 8899 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:49.059 INFO 22208 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:54.693 INFO 22208 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:55.038 INFO 22208 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:55.452 INFO 22208 [cluster-rtt-ClusterId{value='688326f707f8f0772ba8ca9c', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:55.451 INFO 22208 [cluster-ClusterId{value='688326f707f8f0772ba8ca9c', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:55.454 INFO 22208 [cluster-ClusterId{value='688326f707f8f0772ba8ca9c', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=255257100}
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:57.929 INFO 22208 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:57.989 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.510 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.533 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.570 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.591 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.607 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.647 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.662 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.694 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.847 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.876 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:58.970 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:59.309 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:59.467 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:59.542 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:40:59.623 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.018 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.059 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.102 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.128 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.176 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.226 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.289 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.324 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.357 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.388 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.439 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.505 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.554 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.582 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.608 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.632 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.666 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.697 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.725 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.749 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.775 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.799 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.827 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.851 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.877 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.894 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.964 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:00.996 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.062 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.188 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.197 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.225 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.249 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.272 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.289 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.313 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.325 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.335 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.346 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.356 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.370 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.378 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.390 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.401 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.419 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.438 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.461 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.477 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.496 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.507 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:01.523 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:02.206 INFO 22208 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:02.422 INFO 22208 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:03.441 INFO 22208 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:04.981 INFO 22208 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.475 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.492 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.511 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.529 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.548 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.567 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.586 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.607 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.627 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.650 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.674 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.696 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.725 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.749 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.779 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.785 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.803 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.823 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.840 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.863 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.880 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.898 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.974 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:06.998 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.017 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.036 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.065 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.070 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.074 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.085 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.091 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.097 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.105 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.109 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.118 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.131 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.136 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.142 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.151 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.158 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.164 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.174 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.180 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.189 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.195 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.200 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.209 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.213 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.224 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.228 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.232 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.236 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.241 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.245 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.253 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.259 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.263 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.267 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.273 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.280 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.290 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.300 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.305 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.308 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.315 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.320 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.326 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.332 INFO 22208 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.628 INFO 22208 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.745 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.757 INFO 22208 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.827 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:07.828 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.185 INFO 22208 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.192 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.200 INFO 22208 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753425669198 with initial instances count: 10
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.210 INFO 22208 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.212 INFO 22208 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.278 INFO 22208 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.282 INFO 22208 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.282 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.283 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.284 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.284 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.284 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.286 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.287 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.354 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.355 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.363 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.363 INFO 22208 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.364 INFO 22208 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.418 INFO 22208 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:09.646 INFO 22208 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.031 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.107 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getTenantUsersUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.116 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getWebUserInfoUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.159 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.164 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.170 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.175 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.199 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.217 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.226 INFO 22208 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.350 INFO 22208 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 35.071 seconds (JVM running for 36.957)
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.715 INFO 22208 [RMI TCP Connection(1)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.715 INFO 22208 [RMI TCP Connection(1)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:10.740 INFO 22208 [RMI TCP Connection(1)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 24 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:41:11.596 INFO 22208 [RMI TCP Connection(2)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [4c5193d4c99724ef,] 2025-07-25 14:41:21.391 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4c5193d4c99724ef,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:*********:7002] [4c5193d4c99724ef,] 2025-07-25 14:41:22.010 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4c5193d4c99724ef, 耗时=1108, resp=null:
[kbc-elms-web:*********:7002] [58caa74b8ffb6da7,] 2025-07-25 14:41:28.264 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=58caa74b8ffb6da7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:*********:7002] [58caa74b8ffb6da7,] 2025-07-25 14:41:28.310 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=58caa74b8ffb6da7, 耗时=46, resp=null:
[kbc-elms-web:*********:7002] [168ab98698075a2b,] 2025-07-25 14:41:58.507 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=168ab98698075a2b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [168ab98698075a2b,] 2025-07-25 14:41:59.449 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=168ab98698075a2b, 耗时=942, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [f693b62efe4f4401,] 2025-07-25 14:41:59.903 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f693b62efe4f4401,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [f693b62efe4f4401,] 2025-07-25 14:42:00.644 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f693b62efe4f4401, 耗时=743, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e14484d89f6247eb,] 2025-07-25 14:42:45.647 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e14484d89f6247eb,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [e14484d89f6247eb,] 2025-07-25 14:42:45.766 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e14484d89f6247eb, 耗时=119, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [0ab01ad050eebc4e,] 2025-07-25 14:42:46.204 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0ab01ad050eebc4e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [0ab01ad050eebc4e,] 2025-07-25 14:42:46.326 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0ab01ad050eebc4e, 耗时=122, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [8f4dab687db5f597,] 2025-07-25 14:43:04.784 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8f4dab687db5f597,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [8f4dab687db5f597,] 2025-07-25 14:43:04.864 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8f4dab687db5f597, 耗时=80, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [989bc3175e3fb038,] 2025-07-25 14:43:05.006 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=989bc3175e3fb038,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [989bc3175e3fb038,] 2025-07-25 14:43:05.118 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=989bc3175e3fb038, 耗时=112, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a7be1d5c4194d5c3,] 2025-07-25 14:43:10.679 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a7be1d5c4194d5c3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [a7be1d5c4194d5c3,] 2025-07-25 14:43:10.813 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a7be1d5c4194d5c3, 耗时=139, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d515c6ecedfdb03b,] 2025-07-25 14:43:17.999 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d515c6ecedfdb03b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [d515c6ecedfdb03b,] 2025-07-25 14:43:18.142 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d515c6ecedfdb03b, 耗时=142, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [deb12f0a193055e6,] 2025-07-25 14:43:18.311 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=deb12f0a193055e6,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [deb12f0a193055e6,] 2025-07-25 14:43:18.521 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=deb12f0a193055e6, 耗时=210, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1c8d5d9fc33ef392,] 2025-07-25 14:43:48.350 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1c8d5d9fc33ef392,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [1c8d5d9fc33ef392,] 2025-07-25 14:43:48.430 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1c8d5d9fc33ef392, 耗时=80, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [4f4bddc7229541d6,] 2025-07-25 14:43:48.550 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4f4bddc7229541d6,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [4f4bddc7229541d6,] 2025-07-25 14:43:48.662 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4f4bddc7229541d6, 耗时=111, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7ddf2b5d46190181,] 2025-07-25 14:43:51.006 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7ddf2b5d46190181,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [7ddf2b5d46190181,] 2025-07-25 14:43:51.085 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7ddf2b5d46190181, 耗时=78, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [39876636ab2705a5,] 2025-07-25 14:43:51.222 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=39876636ab2705a5,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [39876636ab2705a5,] 2025-07-25 14:43:51.326 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=39876636ab2705a5, 耗时=105, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [688e782eb78498a0,] 2025-07-25 14:44:55.988 INFO 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=688e782eb78498a0,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [688e782eb78498a0,] 2025-07-25 14:44:56.141 INFO 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=688e782eb78498a0, 耗时=153, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6dfcbeb90eda5bd3,] 2025-07-25 14:44:56.252 INFO 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6dfcbeb90eda5bd3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [6dfcbeb90eda5bd3,] 2025-07-25 14:44:56.364 INFO 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6dfcbeb90eda5bd3, 耗时=112, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [bef3c6daedd72f1f,] 2025-07-25 14:46:07.487 INFO 22208 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bef3c6daedd72f1f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [bef3c6daedd72f1f,] 2025-07-25 14:46:07.623 INFO 22208 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bef3c6daedd72f1f, 耗时=136, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [bfd13892cc4e14ed,] 2025-07-25 14:46:07.778 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bfd13892cc4e14ed,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:46:07.833 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [bfd13892cc4e14ed,] 2025-07-25 14:46:07.906 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bfd13892cc4e14ed, 耗时=128, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [abadbac159927ca5,] 2025-07-25 14:46:21.562 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=abadbac159927ca5,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [abadbac159927ca5,] 2025-07-25 14:46:21.636 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=abadbac159927ca5, 耗时=74, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7dea540d2c45b4e9,] 2025-07-25 14:46:21.799 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7dea540d2c45b4e9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [7dea540d2c45b4e9,] 2025-07-25 14:46:21.913 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7dea540d2c45b4e9, 耗时=114, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d591b05589a942eb,] 2025-07-25 14:47:24.119 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d591b05589a942eb,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [d591b05589a942eb,] 2025-07-25 14:47:24.232 INFO 22208 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d591b05589a942eb, 耗时=113, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [34d5ba2b488a7bbc,] 2025-07-25 14:47:24.408 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=34d5ba2b488a7bbc,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [34d5ba2b488a7bbc,] 2025-07-25 14:47:24.516 INFO 22208 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=34d5ba2b488a7bbc, 耗时=108, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b2215681ad323032,] 2025-07-25 14:48:08.313 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b2215681ad323032,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [b2215681ad323032,] 2025-07-25 14:48:08.515 INFO 22208 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b2215681ad323032, 耗时=202, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [2027e98fd42af333,] 2025-07-25 14:48:08.706 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=2027e98fd42af333,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [2027e98fd42af333,] 2025-07-25 14:48:09.039 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=2027e98fd42af333, 耗时=333, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [dd60acf0f7e92f29,] 2025-07-25 14:49:02.816 INFO 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=dd60acf0f7e92f29,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [dd60acf0f7e92f29,] 2025-07-25 14:49:02.893 INFO 22208 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=dd60acf0f7e92f29, 耗时=77, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [0981ee90cd748b34,] 2025-07-25 14:49:03.010 INFO 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0981ee90cd748b34,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [0981ee90cd748b34,] 2025-07-25 14:49:03.127 INFO 22208 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0981ee90cd748b34, 耗时=117, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:51:07.843 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [f425444d8ca55ded,] 2025-07-25 14:51:22.249 INFO 22208 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f425444d8ca55ded,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [f425444d8ca55ded,] 2025-07-25 14:51:22.392 INFO 22208 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f425444d8ca55ded, 耗时=143, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e26565ee50c77bea,] 2025-07-25 14:51:22.495 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e26565ee50c77bea,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [e26565ee50c77bea,] 2025-07-25 14:51:22.610 INFO 22208 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e26565ee50c77bea, 耗时=115, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7ae84024df388d05,] 2025-07-25 14:51:47.339 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7ae84024df388d05,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [7ae84024df388d05,] 2025-07-25 14:51:47.413 INFO 22208 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7ae84024df388d05, 耗时=73, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d796c00bca026ae2,] 2025-07-25 14:51:49.501 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d796c00bca026ae2,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [d796c00bca026ae2,] 2025-07-25 14:51:49.575 INFO 22208 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d796c00bca026ae2, 耗时=74, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a31471a3f1280783,] 2025-07-25 14:51:49.689 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a31471a3f1280783,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [a31471a3f1280783,] 2025-07-25 14:51:49.804 INFO 22208 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a31471a3f1280783, 耗时=115, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 14:56:07.848 INFO 22208 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
