{"@timestamp":"2025-07-28T09:02:36.300+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:07:36.369+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:12:36.378+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:17:36.380+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:22:36.395+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:27:36.402+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:32:36.419+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.072+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.075+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: e8d6e7a30fa8553a","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.076+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.078+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.079+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.080+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.082+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"33eb0597875aaf04f770c31382d1af75\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.083+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.221+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.222+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.222+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.223+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:34:59 GMT","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.223+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.223+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.224+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.224+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.224+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.225+08:00","traceId":"e8d6e7a30fa8553a","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.706+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: b9341c21abe7d5cb","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.708+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"33eb0597875aaf04f770c31382d1af75\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.709+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.787+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.787+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.787+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.787+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:34:59 GMT","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-07-28T09:34:59.788+08:00","traceId":"b9341c21abe7d5cb","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.081+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.084+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 34e7e0985df7f4d5","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.084+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.084+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.085+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.085+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.085+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"33eb0597875aaf04f770c31382d1af75\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.085+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.174+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (88ms)","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.174+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.175+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.175+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Mon, 28 Jul 2025 01:35:01 GMT","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.175+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.175+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.175+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.176+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.176+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-07-28T09:35:01.176+08:00","traceId":"34e7e0985df7f4d5","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-07-28T09:37:36.424+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:42:36.428+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:47:36.431+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:52:36.435+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-28T09:57:36.438+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
