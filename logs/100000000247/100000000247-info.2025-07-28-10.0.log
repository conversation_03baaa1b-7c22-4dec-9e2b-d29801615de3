[kbc-elms-web:*********:7002] [,] 2025-07-28 10:02:36.450 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:07:36.463 INFO 16728 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:10:15.186 INFO 16728 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:30.967 INFO 21536 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.968 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.970 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.975 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.977 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.979 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.981 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.981 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.982 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.982 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:31.983 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:32.090 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:32.096 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-28 10:12:32.098 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:32.353 INFO 21536 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.498 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.505 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.638 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 122 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.663 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.666 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:41.799 INFO 21536 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 123 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.336 INFO 21536 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=c0b4a6dd-5ccf-3f6b-8b35-d7153d573574
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.394 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.394 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.394 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.394 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.395 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.396 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.396 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.396 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.397 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.398 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.398 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.398 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:42.398 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:43.615 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:43.626 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:43.627 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.248 INFO 21536 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.270 INFO 21536 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.271 INFO 21536 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.272 INFO 21536 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.450 INFO 21536 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:44.451 INFO 21536 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 12055 ms
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:46.074 INFO 21536 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:48.573 INFO 21536 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:48.930 INFO 21536 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:49.394 INFO 21536 [cluster-rtt-ClusterId{value='6886dca0abe72c15d2062838', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:49.394 INFO 21536 [cluster-ClusterId{value='6886dca0abe72c15d2062838', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:49.395 INFO 21536 [cluster-ClusterId{value='6886dca0abe72c15d2062838', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=264735000}
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:52.435 INFO 21536 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:52.510 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.001 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.051 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.146 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.185 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.219 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.454 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.626 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.661 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.854 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.872 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:54.916 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:55.107 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:55.286 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:55.370 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:55.462 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.017 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.065 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.112 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.142 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.194 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.261 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.324 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.364 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.402 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.442 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.503 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.563 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.611 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.641 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.668 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.692 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.721 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.758 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.785 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.810 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.836 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.862 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.888 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.913 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.949 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:56.973 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.049 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.084 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.149 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.278 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.288 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.317 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.341 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.363 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.382 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.408 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.421 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.430 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.444 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.457 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.469 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.477 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.490 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.501 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.520 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.536 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.555 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.567 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.584 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.597 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:57.611 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:58.363 INFO 21536 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:58.598 INFO 21536 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:12:59.705 INFO 21536 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:01.318 INFO 21536 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.081 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.102 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.121 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.139 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.158 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.178 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.195 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.216 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.233 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.253 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.271 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.288 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.308 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.330 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.351 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.356 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.380 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.399 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.415 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.438 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.455 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.471 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.490 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.515 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.548 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.580 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.615 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.621 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.625 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.636 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.642 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.648 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.655 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.659 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.666 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.674 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.678 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.682 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.688 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.695 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.701 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.710 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.715 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.723 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.731 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.736 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.750 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.755 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.770 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.774 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.781 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.786 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.791 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.798 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.805 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.812 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.816 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.820 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.824 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.832 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.844 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.860 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.865 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.869 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.889 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.898 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.908 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:03.925 INFO 21536 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.461 INFO 21536 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.694 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.724 INFO 21536 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.853 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.854 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.854 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.854 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.854 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.855 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:04.855 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.028 INFO 21536 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.075 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.128 INFO 21536 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753668787122 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.159 INFO 21536 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.163 INFO 21536 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.343 INFO 21536 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.352 INFO 21536 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.352 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.352 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.353 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.353 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.354 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.355 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.355 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.355 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.358 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.358 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.432 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.433 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.434 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.434 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.434 INFO 21536 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.435 INFO 21536 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.501 INFO 21536 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:07.743 INFO 21536 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.171 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.225 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.238 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.246 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.348 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.355 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.362 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.369 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.409 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.437 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.454 INFO 21536 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.566 INFO 21536 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 39.338 seconds (JVM running for 41.198)
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.820 INFO 21536 [RMI TCP Connection(4)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.820 INFO 21536 [RMI TCP Connection(4)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:08.859 INFO 21536 [RMI TCP Connection(4)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 38 ms
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:13:09.526 INFO 21536 [RMI TCP Connection(1)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [8876e9909b8b329a,] 2025-07-28 10:13:13.112 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8876e9909b8b329a,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [8876e9909b8b329a,] 2025-07-28 10:13:13.597 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8876e9909b8b329a, 耗时=1340, resp={"datas":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":10,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [4db38d2610adc380,] 2025-07-28 10:13:54.146 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4db38d2610adc380,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/add , httpMethod=null, reqData={"param1":{"code":"A","description":"","name":"A类","priority":1,"rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}]}} 
[kbc-elms-web:*********:7002] [4db38d2610adc380,] 2025-07-28 10:13:54.471 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4db38d2610adc380, 耗时=327, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1a086f229831c9a3,] 2025-07-28 10:13:54.637 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1a086f229831c9a3,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [1a086f229831c9a3,] 2025-07-28 10:13:54.767 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1a086f229831c9a3, 耗时=130, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","employeeRangeText":"<=100000人;>=50000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [58014987d54efff7,] 2025-07-28 10:15:40.736 INFO 21536 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=58014987d54efff7,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [58014987d54efff7,] 2025-07-28 10:15:41.035 INFO 21536 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=58014987d54efff7, 耗时=299, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","employeeRangeText":"<=100000人;>=50000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [3f7ec11963e1ee34,] 2025-07-28 10:17:56.820 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=3f7ec11963e1ee34,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [3f7ec11963e1ee34,] 2025-07-28 10:17:56.897 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=3f7ec11963e1ee34, 耗时=77, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","employeeRangeText":"<=100000人;>=50000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:18:04.872 INFO 21536 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [b14f18d91ae5bd10,] 2025-07-28 10:18:24.604 INFO 21536 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b14f18d91ae5bd10,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/detail , httpMethod=null, reqData={"param1":{"id":"6886dce2abe72c15d2062839"}} 
[kbc-elms-web:*********:7002] [b14f18d91ae5bd10,] 2025-07-28 10:18:24.644 INFO 21536 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b14f18d91ae5bd10, 耗时=40, resp={"datas":{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [59a38227480c13e7,] 2025-07-28 10:18:28.761 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=59a38227480c13e7,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [59a38227480c13e7,] 2025-07-28 10:18:28.856 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=59a38227480c13e7, 耗时=95, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","employeeRangeText":"<=100000人;>=50000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [15238871eeeaea0a,] 2025-07-28 10:18:33.937 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=15238871eeeaea0a,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/detail , httpMethod=null, reqData={"param1":{"id":"6886dce2abe72c15d2062839"}} 
[kbc-elms-web:*********:7002] [15238871eeeaea0a,] 2025-07-28 10:18:33.976 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=15238871eeeaea0a, 耗时=39, resp={"datas":{"code":"A","createId":"U000162","createTime":1753668834202,"description":"","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834202},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [c30deb6dd1f6fbba,] 2025-07-28 10:18:53.912 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c30deb6dd1f6fbba,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/update , httpMethod=null, reqData={"param1":{"code":"A","createId":"U000162","createTime":1753668834000,"id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}} 
[kbc-elms-web:*********:7002] [c30deb6dd1f6fbba,] 2025-07-28 10:18:54.015 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c30deb6dd1f6fbba, 耗时=103, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [ad8ec658753d6ce3,] 2025-07-28 10:18:54.138 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ad8ec658753d6ce3,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [ad8ec658753d6ce3,] 2025-07-28 10:18:54.210 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ad8ec658753d6ce3, 耗时=72, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [c2fd7b99d662da2c,] 2025-07-28 10:19:14.175 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c2fd7b99d662da2c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [c2fd7b99d662da2c,] 2025-07-28 10:19:15.138 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c2fd7b99d662da2c, 耗时=965, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1be11b806c89bbef,] 2025-07-28 10:19:15.424 INFO 21536 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1be11b806c89bbef,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [1be11b806c89bbef,] 2025-07-28 10:19:16.345 INFO 21536 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1be11b806c89bbef, 耗时=921, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d35799e1da9ef7c1,] 2025-07-28 10:19:23.958 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d35799e1da9ef7c1,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [d35799e1da9ef7c1,] 2025-07-28 10:19:24.139 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d35799e1da9ef7c1, 耗时=187, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [578023c7fad1ee34,] 2025-07-28 10:19:27.135 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=578023c7fad1ee34,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [578023c7fad1ee34,] 2025-07-28 10:19:27.213 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=578023c7fad1ee34, 耗时=78, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [5ab2a47dcd817cad,] 2025-07-28 10:19:27.334 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5ab2a47dcd817cad,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [5ab2a47dcd817cad,] 2025-07-28 10:19:27.441 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5ab2a47dcd817cad, 耗时=106, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1b5337232faf9dad,] 2025-07-28 10:19:34.436 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1b5337232faf9dad,  url=com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [1b5337232faf9dad,] 2025-07-28 10:19:34.507 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1b5337232faf9dad, 耗时=71, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [9b9f8846bfad71eb,] 2025-07-28 10:19:35.759 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9b9f8846bfad71eb,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [9b9f8846bfad71eb,] 2025-07-28 10:19:35.828 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9b9f8846bfad71eb, 耗时=69, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b92216a7ed044a12,] 2025-07-28 10:19:39.868 INFO 21536 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b92216a7ed044a12,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":1}} 
[kbc-elms-web:*********:7002] [b92216a7ed044a12,] 2025-07-28 10:19:39.943 INFO 21536 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b92216a7ed044a12, 耗时=76, resp={"datas":{"bizCode":"test","createId":"U000162","createTime":1753336762000,"fields":[{"additional":{"input_unit":"d"},"change":"1","defaultValue":"s","fieldCode":"fff","fieldLength":"1","fieldName":"xx1","fieldType":"varchar","id":"6881e29c71fde748d6548005","isIndex":"0","remark":"","required":"0","showType":"1","sort":1,"templateId":1,"validation":"3"},{"additional":{"input_unit":"d"},"change":"1","defaultValue":"","fieldCode":"ss","fieldLength":"2","fieldName":"xx2","fieldType":"varchar","id":"6881e29c71fde748d6548006","isIndex":"0","remark":"","required":"0","showType":"1","sort":2,"templateId":1,"validation":"ee"},{"additional":{"input_unit":"sd"},"change":"1","defaultValue":"d","fieldCode":"pdd","fieldLength":"12","fieldName":"xx","fieldType":"varchar","id":"6881cbb98d33bf5f79adb024","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":1,"validation":"xd"}],"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [fbde11e55e7ff2d7,] 2025-07-28 10:19:42.392 INFO 21536 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=fbde11e55e7ff2d7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [fbde11e55e7ff2d7,] 2025-07-28 10:19:42.468 INFO 21536 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=fbde11e55e7ff2d7, 耗时=76, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [8a0e6ec4f6289d77,] 2025-07-28 10:19:43.617 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8a0e6ec4f6289d77,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [8a0e6ec4f6289d77,] 2025-07-28 10:19:43.694 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8a0e6ec4f6289d77, 耗时=77, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a2f232a4743f0fda,] 2025-07-28 10:19:52.780 INFO 21536 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a2f232a4743f0fda,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [a2f232a4743f0fda,] 2025-07-28 10:19:52.867 INFO 21536 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a2f232a4743f0fda, 耗时=86, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [3892883770123b90,] 2025-07-28 10:22:43.693 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=3892883770123b90,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [3892883770123b90,] 2025-07-28 10:22:43.816 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=3892883770123b90, 耗时=123, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e69170ea0d590d80,] 2025-07-28 10:22:46.151 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e69170ea0d590d80,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [e69170ea0d590d80,] 2025-07-28 10:22:46.224 INFO 21536 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e69170ea0d590d80, 耗时=73, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [790942f9280a7d7c,] 2025-07-28 10:22:48.334 INFO 21536 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=790942f9280a7d7c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [790942f9280a7d7c,] 2025-07-28 10:22:48.416 INFO 21536 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=790942f9280a7d7c, 耗时=82, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [db280ef310120840,] 2025-07-28 10:22:48.539 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=db280ef310120840,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [db280ef310120840,] 2025-07-28 10:22:48.654 INFO 21536 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=db280ef310120840, 耗时=115, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [36bf656fc9f76b32,] 2025-07-28 10:23:00.621 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=36bf656fc9f76b32,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [36bf656fc9f76b32,] 2025-07-28 10:23:00.700 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=36bf656fc9f76b32, 耗时=80, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:23:04.886 INFO 21536 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [b8a7085fcb57d129,] 2025-07-28 10:23:05.719 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b8a7085fcb57d129,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [b8a7085fcb57d129,] 2025-07-28 10:23:05.793 INFO 21536 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b8a7085fcb57d129, 耗时=74, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7d21fd20082ffa76,] 2025-07-28 10:24:05.100 INFO 21536 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7d21fd20082ffa76,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [7d21fd20082ffa76,] 2025-07-28 10:24:05.177 INFO 21536 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7d21fd20082ffa76, 耗时=77, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:28:04.888 INFO 21536 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [5f584d8564538d0e,] 2025-07-28 10:29:08.012 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5f584d8564538d0e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [5f584d8564538d0e,] 2025-07-28 10:29:08.463 INFO 21536 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5f584d8564538d0e, 耗时=451, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753436363000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a39e56be0744191b,] 2025-07-28 10:29:15.921 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a39e56be0744191b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [a39e56be0744191b,] 2025-07-28 10:29:16.027 INFO 21536 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a39e56be0744191b, 耗时=106, resp={"datas":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"inputUnit":"人1"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"selectOptions":["1","2","3","4","5"]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"688350cb7854ad28146e9f32","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"inputUnit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688350cb7854ad28146e9f33","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [4df97b398969499e,] 2025-07-28 10:29:16.159 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4df97b398969499e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [4df97b398969499e,] 2025-07-28 10:29:16.288 INFO 21536 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4df97b398969499e, 耗时=129, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-28 10:31:11.489 INFO 21536 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
