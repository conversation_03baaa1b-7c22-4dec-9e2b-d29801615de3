{"@timestamp":"2025-07-24T11:31:51.560+08:00","traceId":"e9f60c4043dff3da","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753327910562,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":997,\"traceId\":\"e9f60c4043dff3da\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T11:33:00.672+08:00","traceId":"6ab09051a0123d6e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753327980408,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"test\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"x\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"1\\\",\\\"fieldLength\\\":\\\"10\\\",\\\"fieldName\\\":\\\"a\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"xx\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"xx\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldLength\\\":\\\"10,2\\\",\\\"fieldName\\\":\\\"b\\\",\\\"fieldType\\\":\\\"float\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"e\\\"}],\\\"remark\\\":\\\"测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"测试\\\",\\\"type\\\":\\\"测试\\\"}\",\"remark\":\"字段类型不正确\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":264,\"traceId\":\"6ab09051a0123d6e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T11:34:32.034+08:00","traceId":"6f599fb06cf60eff","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753328040065,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"test\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"x\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"1\\\",\\\"fieldLength\\\":\\\"10\\\",\\\"fieldName\\\":\\\"a\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"xx\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"xx\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldLength\\\":\\\"10,2\\\",\\\"fieldName\\\":\\\"b\\\",\\\"fieldType\\\":\\\"float\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"x\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"e\\\"}],\\\"remark\\\":\\\"测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"测试\\\",\\\"type\\\":\\\"测试\\\"}\",\"remark\":\"字段类型不正确\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":31969,\"traceId\":\"6f599fb06cf60eff\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-24T11:39:00.444+08:00","traceId":"2cac2f5eecc833ae","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"新增\",\"appName\":\"kbc-elms-web\",\"createTime\":1753328336820,\"desc\":\"新增\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/saveWithFields\",\"module\":\"管理\",\"params\":\"{\\\"bizCode\\\":\\\"test\\\",\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"a\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"validation\\\":\\\"1\\\"}],\\\"remark\\\":\\\"1\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"type\\\":\\\"1\\\"}\",\"remark\":\"nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'fileName' in 'class com.kbao.kbcelms.dataTemplate.model.DataTemplateField'\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":3622,\"traceId\":\"2cac2f5eecc833ae\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
