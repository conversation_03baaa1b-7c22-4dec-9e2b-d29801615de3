[kbc-elms-web:*********:7002] [,] 2025-07-31 10:03:22.324 INFO 39888 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.257 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.264 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bd64833f05b04e82
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.264 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.265 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.266 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.267 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.396 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:41 GMT
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.397 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.398 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.398 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.399 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.411 INFO 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bd64833f05b04e82,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","defaultValue":"","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{},"change":"1","defaultValue":"","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","isIndex":"0","remark":"","required":"0","showType":"1","validation":""},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":12,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753783497000}} 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.650 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.650 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.724 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.725 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.725 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_agent_enterprise(String)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.768 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.781 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.781 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.852 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.857 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.858 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.931 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:40.960 DEBUG 39888 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.183 DEBUG 39888 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.301 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_tp_data_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.302 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.465 DEBUG 39888 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [bd64833f05b04e82,] 2025-07-31 10:07:41.534 INFO 39888 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bd64833f05b04e82, 耗时=1124, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b9a0fa8c000790d4
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.614 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.706 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.706 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:42 GMT
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.707 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.709 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:41.719 INFO 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b9a0fa8c000790d4,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.107 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.107 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.142 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.143 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.144 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.179 DEBUG 39888 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [b9a0fa8c000790d4,] 2025-07-31 10:07:42.186 INFO 39888 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b9a0fa8c000790d4, 耗时=469, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927661000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.508 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5fa005ab717b08b9
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.509 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.510 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:07:46 GMT
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.601 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.602 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.603 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.603 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.607 INFO 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5fa005ab717b08b9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.607 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.608 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.644 DEBUG 39888 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.645 DEBUG 39888 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [5fa005ab717b08b9,] 2025-07-31 10:07:45.688 INFO 39888 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5fa005ab717b08b9, 耗时=81, resp={"datas":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","defaultValue":"","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","remark":"","required":"0","showType":"1","sort":4,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","defaultValue":"","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","remark":"","required":"0","showType":"1","sort":6,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927661000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:08:22.345 INFO 39888 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.563 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 592841a2a4208922
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.565 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.566 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.567 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:08:24 GMT
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.674 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.675 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.676 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.682 INFO 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=592841a2a4208922,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927661000}} 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.717 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.718 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.753 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.753 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.755 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_agent_enterprise(String)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.790 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.790 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.791 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.825 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.825 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_tp_data_agent_enterprise 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.826 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.923 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.924 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.925 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.992 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:23.992 DEBUG 39888 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.030 DEBUG 39888 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.071 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_tp_data_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.072 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.195 DEBUG 39888 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [592841a2a4208922,] 2025-07-31 10:08:24.262 INFO 39888 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=592841a2a4208922, 耗时=580, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ae125895b2d74c4c
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.635 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.636 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.758 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (122ms)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.759 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.759 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:08:25 GMT
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.760 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.761 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.761 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.764 INFO 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ae125895b2d74c4c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.766 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.766 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.799 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.835 DEBUG 39888 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [ae125895b2d74c4c,] 2025-07-31 10:08:24.836 INFO 39888 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ae125895b2d74c4c, 耗时=72, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927704000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:09.271 WARN 39888 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:09.274 INFO 39888 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:09.285 WARN 39888 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:23.982 INFO 39240 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.961 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.964 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.969 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.971 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.972 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.974 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.975 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.976 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.977 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:24.977 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:25.048 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:25.067 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:09:25.070 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:26.645 ERROR 39240 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:09:27 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:26.708 ERROR 39240 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:09:27 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:26.765 ERROR 39240 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:09:27 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:26.767 INFO 39240 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:26.908 INFO 39240 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:35.626 WARN 39240 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.185 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.189 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.474 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 275 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.497 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.500 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:36.742 INFO 39240 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 233 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.259 INFO 39240 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=afe28187-3541-37e7-9277-6114bbf8b135
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.309 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.310 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.310 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.311 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.312 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.312 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.312 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.313 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.314 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.314 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.314 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.314 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.314 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.315 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.315 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:37.315 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:38.544 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:38.553 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:38.554 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.079 INFO 39240 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.102 INFO 39240 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.103 INFO 39240 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.103 INFO 39240 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.314 INFO 39240 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:39.315 INFO 39240 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 12345 ms
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:41.128 INFO 39240 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:43.531 INFO 39240 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:43.895 INFO 39240 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:44.177 INFO 39240 [cluster-rtt-ClusterId{value='688ad067f8c8a31e16185e4b', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:44.178 INFO 39240 [cluster-ClusterId{value='688ad067f8c8a31e16185e4b', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:44.184 INFO 39240 [cluster-ClusterId{value='688ad067f8c8a31e16185e4b', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=113961300}
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:44.240 WARN 39240 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:44.798 WARN 39240 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:45.720 DEBUG 39240 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.086 DEBUG 39240 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.147 DEBUG 39240 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.526 INFO 39240 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.588 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.959 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:47.982 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.024 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.048 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.066 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.092 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.108 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.152 DEBUG 39240 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.252 DEBUG 39240 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.344 DEBUG 39240 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.417 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.478 DEBUG 39240 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.496 DEBUG 39240 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.522 DEBUG 39240 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.542 DEBUG 39240 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.582 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.611 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.633 DEBUG 39240 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.656 DEBUG 39240 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.676 DEBUG 39240 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.760 DEBUG 39240 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.800 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.853 DEBUG 39240 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.904 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:48.973 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.058 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.085 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.095 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.127 DEBUG 39240 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.150 DEBUG 39240 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.171 DEBUG 39240 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.271 DEBUG 39240 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.455 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.499 DEBUG 39240 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.918 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:49.963 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.010 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.035 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.086 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.145 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.206 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.243 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.277 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.309 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.363 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.425 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.474 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.506 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.536 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.565 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.598 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.627 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.656 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.681 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.709 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.733 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.762 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.790 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.827 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.844 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.911 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:50.951 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.023 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.155 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.166 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.196 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.223 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.248 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.272 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.298 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.311 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.322 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.335 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.347 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.360 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.368 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.381 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.394 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.409 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.420 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.432 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.444 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:51.459 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:52.145 INFO 39240 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:52.382 INFO 39240 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:53.499 INFO 39240 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:55.224 INFO 39240 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:55.640 WARN 39240 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.087 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.105 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.123 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.141 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.159 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.177 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.193 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.212 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.228 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.250 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.268 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.284 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.309 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.333 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.354 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.359 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.379 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.401 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.434 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.473 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.501 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.530 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.560 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.592 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.619 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.639 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.669 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.673 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.677 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.688 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.693 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.699 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.703 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.707 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.714 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.721 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.725 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.729 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.735 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.744 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.749 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.758 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.764 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.772 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.777 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.784 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.794 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.798 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.808 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.813 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.819 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.823 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.826 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.832 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.838 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.844 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.849 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.854 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.858 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.865 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.875 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.887 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.890 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.894 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.898 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.903 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.906 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:57.911 INFO 39240 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.260 INFO 39240 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.390 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.406 INFO 39240 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.497 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.497 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.498 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.498 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.498 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.498 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:58.499 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.445 INFO 39240 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.455 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.466 INFO 39240 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753927799464 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.478 INFO 39240 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.481 INFO 39240 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.562 INFO 39240 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.565 INFO 39240 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.565 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.566 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.566 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.567 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.568 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.569 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.570 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.639 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.640 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.641 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.641 INFO 39240 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.641 INFO 39240 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:09:59.727 INFO 39240 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.060 INFO 39240 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.533 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.570 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.574 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.581 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.664 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.762 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.771 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.772 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.776 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.782 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.815 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.835 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.845 INFO 39240 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:00.940 INFO 39240 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 38.608 seconds (JVM running for 41.263)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:01.596 INFO 39240 [RMI TCP Connection(9)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:01.597 INFO 39240 [RMI TCP Connection(9)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:01.625 INFO 39240 [RMI TCP Connection(9)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 28 ms
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:10:02.156 INFO 39240 [RMI TCP Connection(6)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.791 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.793 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c8dee7942b63c156
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.794 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:40.795 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.263 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (468ms)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:41 GMT
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.265 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.782 INFO 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c8dee7942b63c156,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/delete , httpMethod=null, reqData={"param1":{"id":1}} 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:41.889 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey ==>  Preparing: update t_data_template set is_deleted = 1, update_time = now(), update_id = ? where id = ? 
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:42.102 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey ==> Parameters: 1(Integer), 1(Integer)
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:42.181 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.deleteByPrimaryKey <==    Updates: 1
[kbc-elms-web:*********:7002] [c8dee7942b63c156,] 2025-07-31 10:10:42.185 INFO 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c8dee7942b63c156, 耗时=654, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.285 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.286 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f12ef49418ff74fe
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.287 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.289 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.290 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.409 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (118ms)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.409 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.410 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:43 GMT
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.411 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.412 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.421 INFO 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f12ef49418ff74fe,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.918 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:42.919 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.065 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.075 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.075 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.114 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [f12ef49418ff74fe,] 2025-07-31 10:10:43.121 INFO 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f12ef49418ff74fe, 耗时=703, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927704000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.716 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.719 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 177e5f90f75a4b3e
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.719 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.720 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.721 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.722 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (119ms)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:55 GMT
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.842 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.843 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.844 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.844 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.848 INFO 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=177e5f90f75a4b3e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.854 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.854 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:54.894 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:55.104 DEBUG 39240 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [177e5f90f75a4b3e,] 2025-07-31 10:10:55.382 INFO 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=177e5f90f75a4b3e, 耗时=534, resp={"datas":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927704000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.327 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6e806b8a3dcd9515
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.328 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.329 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.445 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (115ms)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:58 GMT
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.446 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.447 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.458 INFO 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6e806b8a3dcd9515,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927704000}} 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.564 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.565 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.608 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.609 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.610 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.662 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.680 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.682 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.729 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.739 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:57.739 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.015 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.017 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.018 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.091 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.108 DEBUG 39240 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.295 DEBUG 39240 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.414 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_gen_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, staffScale varchar(30) comment ?, districtCode varchar(20) comment ?, city varchar(30) comment ?, annualIncome int comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.415 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会信用代码(String), 企业类型(String), 企业规模(String), 人员规模(String), 行政区划代码(String), 所在城市(String), 年收入(String), 行业代码(String), 行业类名(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.621 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [6e806b8a3dcd9515,] 2025-07-31 10:10:58.691 INFO 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6e806b8a3dcd9515, 耗时=1233, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 39e1922502f52178
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.083 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.084 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.086 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.087 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (109ms)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.197 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:10:59 GMT
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.198 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.199 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.199 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.202 INFO 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=39e1922502f52178,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.204 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.204 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.240 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.241 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.243 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.282 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [39e1922502f52178,] 2025-07-31 10:10:59.282 INFO 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=39e1922502f52178, 耗时=80, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927858000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.285 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.287 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6b33677a11443406
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.287 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.288 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.289 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.289 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.407 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (117ms)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.408 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.408 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.409 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:11:02 GMT
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.409 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.410 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.415 INFO 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6b33677a11443406,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.416 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where id = ? 
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.417 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey ==> Parameters: 3(Integer)
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.452 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByPrimaryKey <==      Total: 1
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.454 DEBUG 39240 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [6b33677a11443406,] 2025-07-31 10:11:01.494 INFO 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6b33677a11443406, 耗时=79, resp={"datas":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"1","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"1","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"0","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"1","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"0","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"688882b6c3add91428f4e789","isIndex":"0","required":"0","showType":"1","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"0","showType":"0","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"0","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"0","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927858000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:14:58.512 INFO 39240 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:19:58.514 INFO 39240 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.132 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.133 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: dc454b472a2b5a32
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.134 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.134 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.135 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (144ms)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.281 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:14 GMT
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.282 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.283 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.290 INFO 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=dc454b472a2b5a32,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/updateWithFields , httpMethod=null, reqData={"param1":{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"fields":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":9,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":3,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":6,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":5,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":8,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753927858000}} 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.422 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==>  Preparing: select count(1) from t_data_template where is_deleted = 0 and biz_code = ? and id != ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.423 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode ==> Parameters: agent_enterprise(String), 3(Integer)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.463 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTemplateCode <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.464 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.464 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.499 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.499 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==>  Preparing: select count(1) from t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.500 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData ==> Parameters: 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.538 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistData <==      Total: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.540 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==>  Preparing: drop table if exists t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.540 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable ==> Parameters: 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.693 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlDropTable <==    Updates: 0
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.694 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==>  Preparing: update t_data_template SET template_name = ?, biz_code = ?, type = ?, status = ?, remark = ?, update_id = ?, update_time = now() where id = ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.695 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective ==> Parameters: 顾问企业信息表(String), agent_enterprise(String), 企业信息(String), 1(String), 顾问企业信息表(String), U000162(String), 3(Integer)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.762 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.updateByPrimaryKeySelective <==    Updates: 1
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.762 DEBUG 39240 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Remove using query: { "templateId" : 3} in collection: DataTemplateField.
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.800 DEBUG 39240 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate Inserting list of Documents containing 14 items
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.842 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==>  Preparing: create table t_gen_agent_enterprise ( id INT primary key auto_increment not null comment '主键', name varchar(50) comment ?, creditCode varchar(50) comment ?, categoryCode varchar(10) comment ?, categoryName varchar(100) comment ?, dtType varchar(20) comment ?, enterpriseScale varchar(20) comment ?, city varchar(30) comment ?, districtCode varchar(20) comment ?, staffScale varchar(30) comment ?, annualIncome varchar(20) comment ?, isVerified char(1) comment ?, enterpriseContacter varchar(20) comment ?, contacterPhone varchar(20) comment ?, remark varchar(300) comment ?, createId VARCHAR(50) comment '创建人 当前用户ID', createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', updateId VARCHAR(50) comment '更新人 默认为当前时间', updateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', isDeleted TINYINT not null default '0' comment '是否删除 0-未删除 1-已删除', tenantId VARCHAR(10) not null default 'T0001' comment '租户ID' , index idx_field_creditCode(creditCode) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment ? 
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.842 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable ==> Parameters: 企业名称(String), 社会统一信用代码(String), 行业代码(String), 行业类名(String), 企业类型(String), 企业规模(String), 企业所在城市(String), 行政区划代码(String), 人员规模(String), 企业年收入(String), 是否验真(String), 企业联系人(String), 联系人电话(String), 备注信息(String), 顾问企业信息表(String)
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:14.972 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.ddlCreateTable <==    Updates: 0
[kbc-elms-web:*********:7002] [dc454b472a2b5a32,] 2025-07-31 10:20:15.037 INFO 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=dc454b472a2b5a32, 耗时=747, resp={"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.108 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.108 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 07befd8c282004f3
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.109 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.109 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.110 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.111 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.212 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.213 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:14 GMT
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.214 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.215 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.216 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.216 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.222 INFO 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=07befd8c282004f3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.224 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.225 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.258 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.260 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.260 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.295 DEBUG 39240 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 1
[kbc-elms-web:*********:7002] [07befd8c282004f3,] 2025-07-31 10:20:15.296 INFO 39240 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=07befd8c282004f3, 耗时=75, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"agent_enterprise","createId":"U000162","createTime":*************,"id":3,"isDeleted":0,"remark":"顾问企业信息表","status":"1","templateName":"顾问企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753928415000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.532 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.536 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: de4a93183a7d0b7b
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.537 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.538 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.642 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (103ms)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.642 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:21 GMT
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.643 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.644 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.651 INFO 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=de4a93183a7d0b7b,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"agent_enterprise"}} 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.652 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.652 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.688 DEBUG 39240 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.689 DEBUG 39240 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [de4a93183a7d0b7b,] 2025-07-31 10:20:20.740 INFO 39240 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=de4a93183a7d0b7b, 耗时=92, resp={"datas":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":3,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.776 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.777 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 364918a9765ee6b3
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.779 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.780 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.780 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.861 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:21 GMT
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.862 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.863 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.870 INFO 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=364918a9765ee6b3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.871 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.872 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.918 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.924 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.924 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.958 DEBUG 39240 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [364918a9765ee6b3,] 2025-07-31 10:20:20.958 INFO 39240 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=364918a9765ee6b3, 耗时=90, resp={"datas":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":10,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.264 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: df66e91ec0f33c50
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.266 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.267 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.368 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.369 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.372 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.373 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: df66e91ec0f33c50
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.391 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.393 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.393 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.494 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.495 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"resp_code":1,"resp_msg":"登录信息已失效, 请重新登录!"}
[kbc-elms-web:*********:7002] [df66e91ec0f33c50,] 2025-07-31 10:20:23.495 DEBUG 39240 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (68-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.558 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bbf63f9a8ba5b10a
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.559 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.561 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.562 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (74ms)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.637 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:20:23 GMT
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.638 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.639 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.639 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.642 INFO 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bbf63f9a8ba5b10a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.643 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.644 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.678 DEBUG 39240 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 0
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.679 ERROR 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求报错，transId=bbf63f9a8ba5b10a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"company"}} ,error =null 
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.679 INFO 39240 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bbf63f9a8ba5b10a, 耗时=37, resp=null:
[kbc-elms-web:*********:7002] [bbf63f9a8ba5b10a,] 2025-07-31 10:20:23.682 ERROR 39240 [http-nio-7002-exec-2] com.kbao.commons.config.GlobalExceptionHandler  空指针异常 ：null

java.lang.NullPointerException: null
	at com.kbao.kbcelms.dataTemplate.service.DataTemplateService.getFieldList(DataTemplateService.java:144)
	at com.kbao.kbcelms.dataTemplate.service.DataTemplateService$$FastClassBySpringCGLIB$$e479f27f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.kbao.kbcelms.dataTemplate.service.DataTemplateService$$EnhancerBySpringCGLIB$$e20af06e.getFieldList(<generated>)
	at com.kbao.kbcelms.controller.dataTemplate.DataTemplateController.getTemplateFields(DataTemplateController.java:91)
	at com.kbao.kbcelms.controller.dataTemplate.DataTemplateController$$FastClassBySpringCGLIB$$a5d29e29.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.kbao.kbcbsc.log.aop.LogAnnotationAOP.logSave(LogAnnotationAOP.java:135)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.kbao.kbcelms.controller.dataTemplate.DataTemplateController$$EnhancerBySpringCGLIB$$a9909f02.getTemplateFields(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.kbao.commons.filter.TraceContextFilter.doFilterInternal(TraceContextFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.068 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.071 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d6cd0ffb0764ecf1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.071 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.073 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (120ms)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.194 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.195 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:21:18 GMT
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.195 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.196 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.199 INFO 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d6cd0ffb0764ecf1,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"agent_enterprise"}} 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.200 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.201 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.246 DEBUG 39240 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.247 DEBUG 39240 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [d6cd0ffb0764ecf1,] 2025-07-31 10:21:18.287 INFO 39240 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d6cd0ffb0764ecf1, 耗时=88, resp={"datas":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":3,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.340 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.341 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 08fe16916238d8bc
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.341 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.343 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.344 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.344 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.417 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.417 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:21:18 GMT
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.418 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.419 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.422 INFO 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=08fe16916238d8bc,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.423 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.424 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.457 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.459 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.460 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.494 DEBUG 39240 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [08fe16916238d8bc,] 2025-07-31 10:21:18.494 INFO 39240 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=08fe16916238d8bc, 耗时=72, resp={"datas":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":10,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.605 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.608 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 67b4c78698431832
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.609 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.609 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.610 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.739 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.741 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:22:58 GMT
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.742 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.744 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.749 INFO 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=67b4c78698431832,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"agent_enterprise"}} 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.789 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.790 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: agent_enterprise(String)
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.831 DEBUG 39240 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.833 DEBUG 39240 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [67b4c78698431832,] 2025-07-31 10:22:57.876 INFO 39240 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=67b4c78698431832, 耗时=127, resp={"datas":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":3,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.909 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8db8138b7d934c4e
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.910 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.980 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (69ms)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.981 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:22:58 GMT
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.982 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.983 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:57.987 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.010 INFO 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8db8138b7d934c4e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.015 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.016 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.056 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.058 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.058 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.104 DEBUG 39240 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [8db8138b7d934c4e,] 2025-07-31 10:22:58.104 INFO 39240 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8db8138b7d934c4e, 耗时=95, resp={"datas":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":10,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:24:58.521 INFO 39240 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.721 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.722 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 669d22b267648df3
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.723 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.724 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"12c27e91b823184a9980639580d73711","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.724 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.862 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (138ms)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.863 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:26:13 GMT
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.864 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.865 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.868 INFO 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=669d22b267648df3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"searchParamList":[],"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.911 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.912 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_gen_agent_enterprise(String)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.955 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.957 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_gen_agent_enterprise 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.957 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.990 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.990 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_gen_agent_enterprise LIMIT ? 
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:12.991 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:13.031 DEBUG 39240 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 10
[kbc-elms-web:*********:7002] [669d22b267648df3,] 2025-07-31 10:26:13.034 INFO 39240 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=669d22b267648df3, 耗时=165, resp={"datas":{"endRow":10,"hasNextPage":true,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":false,"list":[{"districtCode":"110000","annualIncome":"1万亿以上","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"张经理","remark":"大型央企石油公司","updateTime":1753928747000,"categoryCode":"B","categoryName":"采矿业","enterpriseScale":"央企","creditCode":"91110000100020821G","isDeleted":0,"contacterPhone":"13800138001","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"中国石油天然气集团有限公司","tenantId":"T0001","id":1},{"districtCode":"110000","annualIncome":"5000亿-1万亿","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"李总监","remark":"建筑行业龙头企业","updateTime":1753928747000,"categoryCode":"E","categoryName":"建筑业","enterpriseScale":"央企","creditCode":"91110000100020822H","isDeleted":0,"contacterPhone":"13800138002","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"中国建筑集团有限公司","tenantId":"T0001","id":2},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"王主管","remark":"互联网科技公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91440300279146858G","isDeleted":0,"contacterPhone":"13800138003","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"腾讯控股有限公司","tenantId":"T0001","id":3},{"districtCode":"440300","annualIncome":"500亿-1000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"赵部长","remark":"新能源汽车制造商","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"上市公司","creditCode":"91440300192317458K","isDeleted":0,"contacterPhone":"13800138004","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"比亚迪股份有限公司","tenantId":"T0001","id":4},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"C","city":"深圳市","isVerified":"1","enterpriseContacter":"陈经理","remark":"通信设备制造商","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91440300708461136T","isDeleted":0,"contacterPhone":"13800138005","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"华为技术有限公司","tenantId":"T0001","id":5},{"districtCode":"330100","annualIncome":"1000亿-5000亿","dtType":"B","city":"杭州市","isVerified":"1","enterpriseContacter":"刘总","remark":"电商平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91330000MA27XL0E2Y","isDeleted":0,"contacterPhone":"13800138006","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"阿里巴巴集团控股有限公司","tenantId":"T0001","id":6},{"districtCode":"310000","annualIncome":"100亿-500亿","dtType":"D","city":"上海市","isVerified":"1","enterpriseContacter":"周主任","remark":"社交电商平台","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91310000MA1FL5LX3X","isDeleted":0,"contacterPhone":"13800138007","createTime":1753928747000,"staffScale":"1000-5000人","createId":"admin","name":"上海小红书科技有限公司","tenantId":"T0001","id":7},{"districtCode":"110108","annualIncome":"500亿-1000亿","dtType":"C","city":"北京市","isVerified":"1","enterpriseContacter":"孙经理","remark":"短视频平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91110108MA00A1JE4R","isDeleted":0,"contacterPhone":"13800138008","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"北京字节跳动科技有限公司","tenantId":"T0001","id":8},{"districtCode":"320500","annualIncome":"1000亿-5000亿","dtType":"C","city":"苏州市","isVerified":"1","enterpriseContacter":"吴厂长","remark":"电子产品代工制造","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"大型企业","creditCode":"91320000134582639F","isDeleted":0,"contacterPhone":"13800138009","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"富士康科技集团","tenantId":"T0001","id":9},{"districtCode":"440300","annualIncome":"1万亿以上","dtType":"A","city":"深圳市","isVerified":"1","enterpriseContacter":"郑总监","remark":"综合金融服务集团","updateTime":1753928747000,"categoryCode":"J","categoryName":"金融业","enterpriseScale":"上市公司","creditCode":"91440300100020611K","isDeleted":0,"contacterPhone":"13800138010","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"中国平安保险(集团)股份有限公司","tenantId":"T0001","id":10}],"navigateFirstPage":1,"navigateLastPage":3,"navigatePages":8,"navigatepageNums":[1,2,3],"nextPage":2,"pageNum":1,"pageSize":10,"pages":3,"prePage":0,"size":10,"startRow":1,"total":24},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:29:58.528 INFO 39240 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:33:58.564 WARN 39240 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:33:58.566 INFO 39240 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:33:58.567 WARN 39240 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:22.600 INFO 17560 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.543 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.545 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.551 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.552 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.553 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.555 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.555 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.556 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.556 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.557 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.622 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.640 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-31 10:34:23.642 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:25.095 ERROR 17560 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:34:25 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:25.149 ERROR 17560 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:34:25 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:25.199 ERROR 17560 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 10:34:25 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:25.202 INFO 17560 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:25.310 INFO 17560 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:31.514 WARN 17560 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.050 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.054 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.207 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 144 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.226 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.229 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:32.392 INFO 17560 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 155 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.011 INFO 17560 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=48002bdc-d6b3-3a0c-a7d0-f808545e257e
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.057 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.058 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.059 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.059 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.060 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.060 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.060 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.061 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.062 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.062 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.062 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.062 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.062 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.063 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.063 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:33.064 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:34.310 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:34.322 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:34.322 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:34.975 INFO 17560 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:34.999 INFO 17560 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:35.000 INFO 17560 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:35.001 INFO 17560 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:35.163 INFO 17560 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:35.163 INFO 17560 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 9816 ms
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:37.221 INFO 17560 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:39.579 INFO 17560 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:39.940 INFO 17560 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:40.221 INFO 17560 [cluster-rtt-ClusterId{value='688ad63f184b28131f73ebaf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:40.222 INFO 17560 [cluster-ClusterId{value='688ad63f184b28131f73ebaf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:40.226 INFO 17560 [cluster-ClusterId{value='688ad63f184b28131f73ebaf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=111820900}
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:40.279 WARN 17560 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:40.826 WARN 17560 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:41.765 DEBUG 17560 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.016 DEBUG 17560 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.074 DEBUG 17560 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.438 INFO 17560 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.516 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.861 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.885 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.924 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.946 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.958 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:43.987 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.011 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.058 DEBUG 17560 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.167 DEBUG 17560 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.243 DEBUG 17560 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.278 DEBUG 17560 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.347 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.416 DEBUG 17560 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.444 DEBUG 17560 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.485 DEBUG 17560 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.510 DEBUG 17560 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.553 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.585 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.610 DEBUG 17560 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.632 DEBUG 17560 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.655 DEBUG 17560 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.748 DEBUG 17560 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.788 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.851 DEBUG 17560 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:44.920 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.000 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.088 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.115 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.125 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.159 DEBUG 17560 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.180 DEBUG 17560 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.200 DEBUG 17560 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.282 DEBUG 17560 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.467 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.511 DEBUG 17560 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.955 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:45.996 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.043 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.069 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.118 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.177 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.240 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.274 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.310 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.342 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.396 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.459 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.510 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.539 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.589 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.620 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.652 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.676 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.704 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.729 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.756 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.780 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.807 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.834 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.871 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.892 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:46.966 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.006 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.083 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.208 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.218 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.247 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.273 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.295 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.321 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.347 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.361 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.372 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.386 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.396 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.408 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.417 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.430 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.444 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.458 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.468 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.483 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.494 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:47.510 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:48.200 INFO 17560 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:48.486 INFO 17560 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:49.593 INFO 17560 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:51.438 INFO 17560 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:51.826 WARN 17560 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.258 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.277 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.294 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.313 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.333 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.350 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.367 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.385 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.405 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.429 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.449 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.469 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.494 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.521 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.545 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.552 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.572 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.594 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.615 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.643 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.664 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.684 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.709 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.736 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.761 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.782 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.809 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.814 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.817 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.828 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.834 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.839 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.846 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.849 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.855 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.863 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.866 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.870 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.876 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.880 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.885 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.892 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.897 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.904 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.909 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.913 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.923 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.927 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.936 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.942 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.947 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.950 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.954 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.960 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.966 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.973 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.978 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.982 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.986 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:53.994 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.006 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.017 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.021 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.025 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.029 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.033 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.039 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.043 INFO 17560 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.401 INFO 17560 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.532 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.545 INFO 17560 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.631 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:54.632 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.530 INFO 17560 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.537 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.546 INFO 17560 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753929295544 with initial instances count: 8
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.557 INFO 17560 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.559 INFO 17560 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.631 INFO 17560 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.634 INFO 17560 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.634 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.635 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.635 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.635 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.636 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.644 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.712 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.713 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.713 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.713 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.713 INFO 17560 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.713 INFO 17560 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:55.779 INFO 17560 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.082 INFO 17560 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.529 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.562 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.567 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.572 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.652 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.759 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.767 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.770 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.774 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.780 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.812 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.833 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.843 INFO 17560 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:56.958 INFO 17560 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 35.905 seconds (JVM running for 37.654)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:57.276 INFO 17560 [RMI TCP Connection(5)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:57.277 INFO 17560 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:57.281 INFO 17560 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 4 ms
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:34:57.921 INFO 17560 [RMI TCP Connection(8)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.082 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.082 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 80566978df8711a8
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e1ad2e7ff2391f52
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.083 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.084 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.085 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (412ms)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (412ms)
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.497 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:36:03 GMT
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:36:03 GMT
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.498 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [80566978df8711a8,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [e1ad2e7ff2391f52,] 2025-07-31 10:36:02.499 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.617 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.617 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 39daae2a50cac75a
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 555e7e9e0670637a
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.618 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.619 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.620 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.741 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (121ms)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.742 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.742 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.743 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.744 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.745 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (125ms)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.746 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.747 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.747 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.748 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.749 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [39daae2a50cac75a,] 2025-07-31 10:38:11.750 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [555e7e9e0670637a,] 2025-07-31 10:38:11.750 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.855 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 86e0da374176546b
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.856 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.857 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.857 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (64ms)
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.922 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:38:12 GMT
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [86e0da374176546b,] 2025-07-31 10:38:11.923 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:39:54.638 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:44:54.640 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.774 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.774 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5778942d8a3294e3
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7f954575279370a9
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.775 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.776 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.777 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.777 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.892 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.892 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.894 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.897 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.897 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:46:45 GMT
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.898 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 02:46:45 GMT
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.898 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.900 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.901 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.901 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.902 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [5778942d8a3294e3,] 2025-07-31 10:46:44.904 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [7f954575279370a9,] 2025-07-31 10:46:44.904 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:49:54.653 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:54:54.658 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 10:59:54.664 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
