{"@timestamp":"2025-07-25T10:00:11.191+08:00","traceId":"f2ac903f61933ae7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408811044,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":144,\"traceId\":\"f2ac903f61933ae7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:01:16.844+08:00","traceId":"12ac9533e575a97f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408876716,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":128,\"traceId\":\"12ac9533e575a97f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:11.777+08:00","traceId":"41231140dd04782b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408931700,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"41231140dd04782b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:16.278+08:00","traceId":"29cc8c8cdd24c563","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408936243,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":34,\"traceId\":\"29cc8c8cdd24c563\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:16.312+08:00","traceId":"47ec647ca203eed5","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408936243,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"47ec647ca203eed5\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:16.541+08:00","traceId":"41abd8e486f7949a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408936377,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":164,\"traceId\":\"41abd8e486f7949a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:28.840+08:00","traceId":"3e8983620029acf6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408948765,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":75,\"traceId\":\"3e8983620029acf6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:02:31.614+08:00","traceId":"2046e63de2cffad6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753408951453,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":160,\"traceId\":\"2046e63de2cffad6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:04:14.283+08:00","traceId":"d9123c9beccac22e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409054158,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":123,\"traceId\":\"d9123c9beccac22e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:04:38.163+08:00","traceId":"93348779f4746236","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409078069,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":94,\"traceId\":\"93348779f4746236\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:06:22.772+08:00","traceId":"564f7acc6d8eaad9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409181944,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":827,\"traceId\":\"564f7acc6d8eaad9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:06:48.927+08:00","traceId":"3b53119d89e592a6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409208697,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":230,\"traceId\":\"3b53119d89e592a6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:07:01.928+08:00","traceId":"2dd96de7a0cef83a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409221591,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":336,\"traceId\":\"2dd96de7a0cef83a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:02.270+08:00","traceId":"52436384cceed5f6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409402123,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":146,\"traceId\":\"52436384cceed5f6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:04.487+08:00","traceId":"ccfbd2c43519f3cc","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409404413,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":73,\"traceId\":\"ccfbd2c43519f3cc\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:10.693+08:00","traceId":"b5a2c77c9aff6075","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409410618,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"b5a2c77c9aff6075\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:14.539+08:00","traceId":"7f34c995e7f6377b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409414468,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":71,\"traceId\":\"7f34c995e7f6377b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:14.774+08:00","traceId":"87a4ae9fd0fcc7d9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409414740,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":34,\"traceId\":\"87a4ae9fd0fcc7d9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:10:15.348+08:00","traceId":"62fbd87d59b7589f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409415192,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":155,\"traceId\":\"62fbd87d59b7589f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:11:59.686+08:00","traceId":"c03bbad58af4903e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409519488,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"searchParamList\\\":[],\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":197,\"traceId\":\"c03bbad58af4903e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:12:04.073+08:00","traceId":"94fc05b90bf24e83","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409523926,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":146,\"traceId\":\"94fc05b90bf24e83\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:14:39.415+08:00","traceId":"d22786c9bd692913","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409678846,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":568,\"traceId\":\"d22786c9bd692913\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:14:44.468+08:00","traceId":"c0132fd38460588b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409684407,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":1}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"s\\\",\\\"fieldCode\\\":\\\"fff\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"xx1\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548005\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":1,\\\"validation\\\":\\\"3\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"ss\\\",\\\"fieldLength\\\":\\\"2\\\",\\\"fieldName\\\":\\\"xx2\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548006\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":1,\\\"validation\\\":\\\"ee\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"sd\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"d\\\",\\\"fieldCode\\\":\\\"pdd\\\",\\\"fieldLength\\\":\\\"12\\\",\\\"fieldName\\\":\\\"xx\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881cbb98d33bf5f79adb024\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":1,\\\"validation\\\":\\\"xd\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":60,\"traceId\":\"c0132fd38460588b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:14:44.522+08:00","traceId":"66036e71af0553b4","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409684402,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":1}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"s\\\",\\\"fieldCode\\\":\\\"fff\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"xx1\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548005\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":1,\\\"validation\\\":\\\"3\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"ss\\\",\\\"fieldLength\\\":\\\"2\\\",\\\"fieldName\\\":\\\"xx2\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548006\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":1,\\\"validation\\\":\\\"ee\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"sd\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"d\\\",\\\"fieldCode\\\":\\\"pdd\\\",\\\"fieldLength\\\":\\\"12\\\",\\\"fieldName\\\":\\\"xx\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881cbb98d33bf5f79adb024\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":1,\\\"validation\\\":\\\"xd\\\"}],\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":119,\"traceId\":\"66036e71af0553b4\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:14:45.140+08:00","traceId":"f0de5f7d55382491","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409684943,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":1}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":3,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"ss\\\":\\\"2\\\",\\\"tenant_id\\\":\\\"T0001\\\",\\\"pdd\\\":\\\"x\\\",\\\"createTime\\\":1753346081000,\\\"fff\\\":\\\"1\\\",\\\"id\\\":1},{\\\"ss\\\":\\\"1\\\",\\\"tenant_id\\\":\\\"T0001\\\",\\\"pdd\\\":\\\"d\\\",\\\"createTime\\\":1753346081000,\\\"fff\\\":\\\"2\\\",\\\"id\\\":2},{\\\"ss\\\":\\\"1\\\",\\\"tenant_id\\\":\\\"T0001\\\",\\\"pdd\\\":\\\"edw\\\",\\\"createTime\\\":1753346081000,\\\"fff\\\":\\\"3\\\",\\\"id\\\":3}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":3,\\\"startRow\\\":1,\\\"total\\\":3},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":197,\"traceId\":\"f0de5f7d55382491\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:15:09.210+08:00","traceId":"b7b55283e717235f","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409709138,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":71,\"traceId\":\"b7b55283e717235f\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:15:32.343+08:00","traceId":"df01023576255b06","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409732269,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":72,\"traceId\":\"df01023576255b06\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:15:40.348+08:00","traceId":"461f6973f8550864","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409740271,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":76,\"traceId\":\"461f6973f8550864\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:15:40.575+08:00","traceId":"b826182bceeabee1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409740540,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":34,\"traceId\":\"b826182bceeabee1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:15:41.133+08:00","traceId":"e4d880c80bff34dc","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409740981,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":152,\"traceId\":\"e4d880c80bff34dc\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:19:28.814+08:00","traceId":"48ddf2df03adb16d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409968702,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":111,\"traceId\":\"48ddf2df03adb16d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:19:30.473+08:00","traceId":"f9c59031b1400cb6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409970437,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":35,\"traceId\":\"f9c59031b1400cb6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:19:30.523+08:00","traceId":"7f0a185092e8f3c1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409970454,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"7f0a185092e8f3c1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:19:30.764+08:00","traceId":"9821bb91fcaae907","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753409970586,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":176,\"traceId\":\"9821bb91fcaae907\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:23:33.206+08:00","traceId":"9b5a2098e461a2e4","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410212774,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":430,\"traceId\":\"9b5a2098e461a2e4\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:23:39.821+08:00","traceId":"fc6c9fa910a51359","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410219641,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":179,\"traceId\":\"fc6c9fa910a51359\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:23:39.996+08:00","traceId":"0d95cde855631b2d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410219649,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":347,\"traceId\":\"0d95cde855631b2d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:23:41.020+08:00","traceId":"a4614b29397375d7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410220374,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":645,\"traceId\":\"a4614b29397375d7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:24:11.402+08:00","traceId":"08ba0103ff2f788e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410251225,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":177,\"traceId\":\"08ba0103ff2f788e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:36:17.539+08:00","traceId":"41cd484bb776291b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410977425,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":114,\"traceId\":\"41cd484bb776291b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:36:20.070+08:00","traceId":"33fe4892e78cf741","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410980034,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":35,\"traceId\":\"33fe4892e78cf741\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:36:20.085+08:00","traceId":"f3dd7f038fd9c341","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410980012,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa0\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"company_code\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa1\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"人\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"staff_size\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa2\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa3\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"industry_id\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa4\\\",\\\"isIndex\\\":\\\"1\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":5,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"income\\\",\\\"fieldLength\\\":\\\"\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"68821286e0cf4711e805daa5\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":6,\\\"templateId\\\":3,\\\"validation\\\":\\\"\\\"}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":72,\"traceId\":\"f3dd7f038fd9c341\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-25T10:36:20.570+08:00","traceId":"2623704f372d4c6e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753410980177,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateId\\\":3}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"income\\\":111,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":1,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355126000,\\\"name\\\":\\\"小米\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":1,\\\"staff_size\\\":100},{\\\"income\\\":22,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":2,\\\"city\\\":\\\"北京\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"美团\\\",\\\"company_code\\\":\\\"11\\\",\\\"id\\\":2,\\\"staff_size\\\":22},{\\\"income\\\":33,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":3,\\\"city\\\":\\\"背景\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"京东\\\",\\\"company_code\\\":\\\"xx\\\",\\\"id\\\":3,\\\"staff_size\\\":33},{\\\"income\\\":44,\\\"tenant_id\\\":\\\"T0001\\\",\\\"industry_id\\\":4,\\\"city\\\":\\\"杭州\\\",\\\"createTime\\\":1753355127000,\\\"name\\\":\\\"淘宝\\\",\\\"company_code\\\":\\\"df\\\",\\\"id\\\":4,\\\"staff_size\\\":44}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":393,\"traceId\":\"2623704f372d4c6e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
