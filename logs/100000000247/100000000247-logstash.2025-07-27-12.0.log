{"@timestamp":"2025-07-27T12:02:33.829+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:07:33.843+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:33.861+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.554+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.561+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 53c9abdc17c26ce2","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.562+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.562+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.564+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 127.0.0.1","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.564+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.565+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"33eb0597875aaf04f770c31382d1af75\",\"tenantId\":\"T0001\",\"funcId\":\"F04010\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.565+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.728+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (162ms)","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.728+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.729+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.729+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Sun, 27 Jul 2025 04:12:37 GMT","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.729+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.729+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.735+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.738+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.741+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-07-27T12:12:37.742+08:00","traceId":"53c9abdc17c26ce2","remoteIp":"127.0.0.1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-07-27T12:17:33.874+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:22:33.878+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:27:33.892+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:32:33.903+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:37:33.911+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:42:33.921+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:47:33.930+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:52:33.938+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-07-27T12:57:33.943+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
