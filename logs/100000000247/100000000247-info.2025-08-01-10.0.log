[kbc-elms-web:*********:7002] [,] 2025-08-01 10:02:59.106 INFO 4736 [DiscoveryClient-CacheRefreshExecutor-0] org.apache.http.impl.execchain.RetryExec I/O exception (org.apache.http.NoHttpResponseException) caught when processing request to {s}->https://kbc-eureka-dev.kbao123.com:443: The target server failed to respond
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:02:59.079 INFO 4736 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:02:59.183 INFO 4736 [DiscoveryClient-CacheRefreshExecutor-0] org.apache.http.impl.execchain.RetryExec Retrying request to {s}->https://kbc-eureka-dev.kbao123.com:443
[kbc-elms-web:*********:7002] [9e62cea078f6f7e4,] 2025-08-01 10:02:59.185 INFO 4736 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9e62cea078f6f7e4, 耗时=365573, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:02:59.221 INFO 4736 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:32.646 INFO 34580 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.653 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.655 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.660 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.662 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.663 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.665 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.666 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.667 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.669 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.670 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.749 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.766 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:03:33.769 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:35.377 INFO 34580 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:35.518 INFO 34580 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:47.618 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:47.622 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:47.883 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 253 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:47.904 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:47.907 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.153 INFO 34580 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 237 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.628 INFO 34580 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.668 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.669 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.670 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.671 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.671 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.672 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.672 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.673 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.673 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.673 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.673 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.674 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.674 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.674 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.674 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:48.675 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:49.756 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:49.765 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:49.765 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.292 INFO 34580 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.319 INFO 34580 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.321 INFO 34580 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.321 INFO 34580 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.533 INFO 34580 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:50.535 INFO 34580 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 14953 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:52.415 INFO 34580 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:54.837 INFO 34580 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:55.269 INFO 34580 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:55.564 INFO 34580 [cluster-ClusterId{value='688c208b5bc0eb6d1eadbfe7', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:55.565 INFO 34580 [cluster-rtt-ClusterId{value='688c208b5bc0eb6d1eadbfe7', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:55.570 INFO 34580 [cluster-ClusterId{value='688c208b5bc0eb6d1eadbfe7', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=115198800}
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.185 INFO 34580 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.249 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.612 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.634 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.676 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.697 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.711 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.737 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:03:59.754 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:00.405 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:00.576 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:00.608 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:00.817 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:00.929 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:01.004 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:01.106 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:01.136 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:01.146 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:01.608 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.064 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.112 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.161 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.193 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.252 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.323 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.384 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.420 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.459 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.490 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.555 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.625 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.676 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.706 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.736 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.765 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.809 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.839 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.875 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.901 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.926 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:02.958 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.000 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.037 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.065 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.082 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.151 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.187 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.267 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.361 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.374 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.405 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.432 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.456 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.479 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.515 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.533 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.544 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.558 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.570 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.590 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.602 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.624 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.641 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.657 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.669 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.685 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.702 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:03.724 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:04.481 INFO 34580 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:04.793 INFO 34580 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:05.919 INFO 34580 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:07.686 INFO 34580 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.738 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.757 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.780 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.798 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.817 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.835 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.852 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.871 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.893 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.914 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.933 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.956 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:09.979 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.005 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.029 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.035 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.056 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.075 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.094 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.118 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.134 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.153 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.178 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.208 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.234 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.261 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.293 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.298 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.305 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.315 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.320 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.326 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.333 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.337 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.343 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.351 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.355 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.359 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.365 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.370 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.376 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.383 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.389 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.397 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.402 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.408 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.420 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.426 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.437 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.443 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.449 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.454 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.460 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.465 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.473 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.480 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.485 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.491 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.495 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.501 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.513 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.524 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.528 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.531 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.536 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.541 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.545 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.550 INFO 34580 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:10.892 INFO 34580 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.032 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.046 INFO 34580 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.148 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.149 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.149 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.149 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.150 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.150 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:11.150 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.448 INFO 34580 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.455 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.469 INFO 34580 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754013852467 with initial instances count: 10
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.481 INFO 34580 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.483 INFO 34580 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.566 INFO 34580 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.570 INFO 34580 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.570 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.571 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.571 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.572 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.573 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.573 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.574 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.574 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.574 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.575 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.575 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.575 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.575 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.575 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.641 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.641 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.641 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.641 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.641 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.642 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.642 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.642 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.642 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.643 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.643 INFO 34580 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.643 INFO 34580 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.716 INFO 34580 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:12.976 INFO 34580 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.414 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.502 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.507 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.512 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.617 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.628 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.629 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.723 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.731 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.733 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.736 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.741 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.775 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.799 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.811 INFO 34580 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:13.981 INFO 34580 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 43.234 seconds (JVM running for 45.509)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:14.410 INFO 34580 [RMI TCP Connection(8)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:14.411 INFO 34580 [RMI TCP Connection(8)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:14.415 INFO 34580 [RMI TCP Connection(8)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 4 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:04:15.463 INFO 34580 [RMI TCP Connection(7)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:33.672 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4a6382309551d1a1,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/syncEnterpriseAllInfo , httpMethod=null, reqData={"param1":"浙江百达精工股份有限公司"} 
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:34.288 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业规模信息同步成功，creditCode: 913310007200456372, scale: 大型
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:35.142 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业基本信息同步成功，creditCode: 913310007200456372
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:35.450 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业股东信息同步成功，creditCode: 913310007200456372, count: 0
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:40.934 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 最终受益人信息同步成功，creditCode: 913310007200456372, count: 3
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:41.558 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 财务信息同步成功，creditCode: 913310007200456372
[kbc-elms-web:*********:7002] [4a6382309551d1a1,] 2025-08-01 10:04:41.613 INFO 34580 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4a6382309551d1a1, 耗时=8140, resp={"datas":"企业信息同步成功","resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:50.282 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=541a5d3f4e629f3b,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/syncEnterpriseAllInfo , httpMethod=null, reqData={"param1":"浙江百达精工股份有限公司"} 
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:50.599 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业规模信息同步成功，creditCode: 913310007200456372, scale: 大型
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:50.685 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业基本信息同步成功，creditCode: 913310007200456372
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:50.886 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 企业股东信息同步成功，creditCode: 913310007200456372, count: 0
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:51.288 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 最终受益人信息同步成功，creditCode: 913310007200456372, count: 3
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:51.645 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcelms.enterprise.base.service.TianyanchaService 财务信息同步成功，creditCode: 913310007200456372
[kbc-elms-web:*********:7002] [541a5d3f4e629f3b,] 2025-08-01 10:04:51.646 INFO 34580 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=541a5d3f4e629f3b, 耗时=1364, resp={"datas":"企业信息同步成功","resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:09:11.160 INFO 34580 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:13:23.547 INFO 34580 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:57.985 INFO 35316 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.033 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.035 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.040 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.042 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.044 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.046 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.046 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.047 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.048 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.048 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.130 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.156 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 10:13:59.158 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:00.812 INFO 35316 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:00.947 INFO 35316 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.304 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.307 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.595 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 278 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.622 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.628 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:10.908 INFO 35316 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 272 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.395 INFO 35316 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.441 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.443 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.443 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.444 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.444 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.445 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.445 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.446 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.446 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.446 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.447 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.447 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.447 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.447 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.447 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:11.448 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:12.730 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:12.740 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:12.740 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.280 INFO 35316 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.339 INFO 35316 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.340 INFO 35316 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.341 INFO 35316 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.565 INFO 35316 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:13.566 INFO 35316 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 12567 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:15.490 INFO 35316 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:18.068 INFO 35316 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:18.435 INFO 35316 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:18.738 INFO 35316 [cluster-rtt-ClusterId{value='688c22fa9555eb3a7f8ed6ed', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:18.739 INFO 35316 [cluster-ClusterId{value='688c22fa9555eb3a7f8ed6ed', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:18.745 INFO 35316 [cluster-ClusterId{value='688c22fa9555eb3a7f8ed6ed', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=102842000}
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:22.454 INFO 35316 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:22.555 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.026 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.055 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.099 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.122 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.137 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.164 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.184 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:23.869 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.069 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.105 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.317 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.442 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.519 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.655 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.686 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:24.698 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.132 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.638 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.743 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.824 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.860 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.916 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:25.970 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.038 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.081 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.122 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.160 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.216 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.277 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.345 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.381 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.416 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.447 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.480 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.509 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.545 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.580 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.624 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.651 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.684 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.710 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.738 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.753 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.832 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.873 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:26.944 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.037 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.051 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.085 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.112 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.135 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.155 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.181 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.194 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.205 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.218 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.231 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.246 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.257 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.275 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.292 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.310 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.323 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.343 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.355 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:27.368 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:28.159 INFO 35316 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:28.432 INFO 35316 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:29.681 INFO 35316 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:31.439 INFO 35316 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.192 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.211 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.228 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.246 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.266 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.283 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.299 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.317 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.338 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.359 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.376 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.391 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.414 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.435 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.454 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.461 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.483 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.503 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.519 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.550 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.571 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.593 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.612 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.637 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.657 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.680 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.714 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.719 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.723 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.734 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.740 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.745 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.752 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.756 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.763 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.771 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.774 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.778 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.783 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.789 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.793 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.800 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.804 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.810 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.814 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.818 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.825 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.828 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.837 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.840 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.846 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.850 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.854 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.858 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.866 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.872 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.879 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.883 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.887 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.897 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.908 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.921 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.925 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.931 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.935 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.941 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.947 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:33.952 INFO 35316 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.287 INFO 35316 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.430 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.453 INFO 35316 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.544 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.544 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.544 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.544 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.545 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.545 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:34.545 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.462 INFO 35316 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.468 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.478 INFO 35316 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754014475476 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.490 INFO 35316 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.492 INFO 35316 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.571 INFO 35316 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.573 INFO 35316 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.574 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.574 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.574 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.575 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.576 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.577 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.577 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.577 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.577 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.577 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.578 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.585 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.653 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.654 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.655 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.655 INFO 35316 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.655 INFO 35316 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.723 INFO 35316 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:35.989 INFO 35316 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.421 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.510 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.514 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.524 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.631 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.645 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.650 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.750 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.757 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.759 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.762 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.767 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.805 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.826 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.838 INFO 35316 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:36.965 INFO 35316 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 41.002 seconds (JVM running for 43.914)
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:37.471 INFO 35316 [RMI TCP Connection(7)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:37.472 INFO 35316 [RMI TCP Connection(7)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:37.476 INFO 35316 [RMI TCP Connection(7)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 4 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:14:38.406 INFO 35316 [RMI TCP Connection(11)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [b0275ef200d16d77,] 2025-08-01 10:15:43.482 INFO 35316 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b0275ef200d16d77,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/getEnterpriseTypeEnum , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7002] [b0275ef200d16d77,] 2025-08-01 10:15:43.951 INFO 35316 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b0275ef200d16d77, 耗时=487, resp={"datas":[{"code":"A","name":"A类"}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [8f96864603feaf68,] 2025-08-01 10:17:56.407 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8f96864603feaf68,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"agent_enterprise"}} 
[kbc-elms-web:*********:7002] [8f96864603feaf68,] 2025-08-01 10:17:56.532 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8f96864603feaf68, 耗时=130, resp={"datas":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":3,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [8fcf0ca60ff1c30a,] 2025-08-01 10:17:56.716 INFO 35316 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8fcf0ca60ff1c30a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [8fcf0ca60ff1c30a,] 2025-08-01 10:17:57.372 INFO 35316 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8fcf0ca60ff1c30a, 耗时=659, resp={"datas":{"endRow":10,"hasNextPage":true,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":false,"list":[{"districtCode":"110000","annualIncome":"1万亿以上","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"张经理","remark":"大型央企石油公司","updateTime":1753928747000,"categoryCode":"B","categoryName":"采矿业","enterpriseScale":"央企","creditCode":"91110000100020821G","isDeleted":0,"contacterPhone":"13800138001","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"中国石油天然气集团有限公司","tenantId":"T0001","id":1},{"districtCode":"110000","annualIncome":"5000亿-1万亿","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"李总监","remark":"建筑行业龙头企业","updateTime":1753928747000,"categoryCode":"E","categoryName":"建筑业","enterpriseScale":"央企","creditCode":"91110000100020822H","isDeleted":0,"contacterPhone":"13800138002","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"中国建筑集团有限公司","tenantId":"T0001","id":2},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"王主管","remark":"互联网科技公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91440300279146858G","isDeleted":0,"contacterPhone":"13800138003","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"腾讯控股有限公司","tenantId":"T0001","id":3},{"districtCode":"440300","annualIncome":"500亿-1000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"赵部长","remark":"新能源汽车制造商","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"上市公司","creditCode":"91440300192317458K","isDeleted":0,"contacterPhone":"13800138004","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"比亚迪股份有限公司","tenantId":"T0001","id":4},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"C","city":"深圳市","isVerified":"1","enterpriseContacter":"陈经理","remark":"通信设备制造商","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91440300708461136T","isDeleted":0,"contacterPhone":"13800138005","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"华为技术有限公司","tenantId":"T0001","id":5},{"districtCode":"330100","annualIncome":"1000亿-5000亿","dtType":"B","city":"杭州市","isVerified":"1","enterpriseContacter":"刘总","remark":"电商平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91330000MA27XL0E2Y","isDeleted":0,"contacterPhone":"13800138006","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"阿里巴巴集团控股有限公司","tenantId":"T0001","id":6},{"districtCode":"310000","annualIncome":"100亿-500亿","dtType":"D","city":"上海市","isVerified":"1","enterpriseContacter":"周主任","remark":"社交电商平台","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91310000MA1FL5LX3X","isDeleted":0,"contacterPhone":"13800138007","createTime":1753928747000,"staffScale":"1000-5000人","createId":"admin","name":"上海小红书科技有限公司","tenantId":"T0001","id":7},{"districtCode":"110108","annualIncome":"500亿-1000亿","dtType":"C","city":"北京市","isVerified":"1","enterpriseContacter":"孙经理","remark":"短视频平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91110108MA00A1JE4R","isDeleted":0,"contacterPhone":"13800138008","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"北京字节跳动科技有限公司","tenantId":"T0001","id":8},{"districtCode":"320500","annualIncome":"1000亿-5000亿","dtType":"C","city":"苏州市","isVerified":"1","enterpriseContacter":"吴厂长","remark":"电子产品代工制造","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"大型企业","creditCode":"91320000134582639F","isDeleted":0,"contacterPhone":"13800138009","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"富士康科技集团","tenantId":"T0001","id":9},{"districtCode":"440300","annualIncome":"1万亿以上","dtType":"A","city":"深圳市","isVerified":"1","enterpriseContacter":"郑总监","remark":"综合金融服务集团","updateTime":1753928747000,"categoryCode":"J","categoryName":"金融业","enterpriseScale":"上市公司","creditCode":"91440300100020611K","isDeleted":0,"contacterPhone":"13800138010","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"中国平安保险(集团)股份有限公司","tenantId":"T0001","id":10}],"navigateFirstPage":1,"navigateLastPage":3,"navigatePages":8,"navigatepageNums":[1,2,3],"nextPage":2,"pageNum":1,"pageSize":10,"pages":3,"prePage":0,"size":10,"startRow":1,"total":24},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7e04b960aff269b7,] 2025-08-01 10:18:36.475 INFO 35316 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7e04b960aff269b7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"agent_enterprise"}} 
[kbc-elms-web:*********:7002] [7e04b960aff269b7,] 2025-08-01 10:18:36.576 INFO 35316 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7e04b960aff269b7, 耗时=101, resp={"datas":[{"additional":{},"change":"0","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"688350cb7854ad28146e9f2d","isIndex":"0","required":"1","showType":"1","sort":1,"templateId":3},{"additional":{},"change":"0","fieldCode":"creditCode","fieldLength":"50","fieldName":"社会统一信用代码","fieldType":"varchar","id":"688350cb7854ad28146e9f2e","isIndex":"1","required":"1","showType":"1","sort":2,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryCode","fieldLength":"10","fieldName":"行业代码","fieldType":"varchar","id":"688882b6c3add91428f4e78a","isIndex":"0","remark":"最小行业代码","required":"1","showType":"3","sort":3,"templateId":3},{"additional":{},"change":"0","fieldCode":"categoryName","fieldLength":"100","fieldName":"行业类名","fieldType":"varchar","id":"688882b6c3add91428f4e78b","isIndex":"0","remark":"行业名全称，显示多级","required":"0","showType":"0","sort":4,"templateId":3},{"additional":{},"change":"0","fieldCode":"dtType","fieldLength":"20","fieldName":"企业类型","fieldType":"varchar","id":"688350cb7854ad28146e9f2f","isIndex":"0","required":"0","showType":"0","sort":5,"templateId":3,"validation":""},{"additional":{},"change":"0","fieldCode":"enterpriseScale","fieldLength":"20","fieldName":"企业规模","fieldType":"varchar","id":"688acfedf0160463eae40700","isIndex":"0","required":"1","showType":"3","sort":6,"templateId":3},{"additional":{},"change":"0","fieldCode":"city","fieldLength":"30","fieldName":"企业所在城市","fieldType":"varchar","id":"688350cb7854ad28146e9f31","isIndex":"0","required":"1","showType":"3","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"districtCode","fieldLength":"20","fieldName":"行政区划代码","fieldType":"varchar","id":"688acfedf0160463eae40701","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3,"validation":""},{"additional":{"inputUnit":"人"},"change":"1","fieldCode":"staffScale","fieldLength":"30","fieldName":"人员规模","fieldType":"varchar","id":"688350cb7854ad28146e9f30","isIndex":"0","required":"1","showType":"1","sort":9,"templateId":3},{"additional":{},"change":"1","fieldCode":"annualIncome","fieldLength":"20","fieldName":"企业年收入","fieldType":"varchar","id":"688882b6c3add91428f4e789","isIndex":"0","required":"1","showType":"3","sort":10,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"688350cb7854ad28146e9f34","isIndex":"0","required":"0","showType":"0","sort":11,"templateId":3},{"additional":{},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"688350cb7854ad28146e9f35","isIndex":"0","required":"1","showType":"1","sort":12,"templateId":3},{"additional":{},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"688350cb7854ad28146e9f36","isIndex":"0","required":"1","showType":"1","sort":13,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"688350cb7854ad28146e9f37","isIndex":"0","required":"0","showType":"6","sort":14,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [33f411d4ba2c8c2a,] 2025-08-01 10:18:36.780 INFO 35316 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=33f411d4ba2c8c2a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"agent_enterprise"}}} 
[kbc-elms-web:*********:7002] [33f411d4ba2c8c2a,] 2025-08-01 10:18:36.969 INFO 35316 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=33f411d4ba2c8c2a, 耗时=189, resp={"datas":{"endRow":10,"hasNextPage":true,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":false,"list":[{"districtCode":"110000","annualIncome":"1万亿以上","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"张经理","remark":"大型央企石油公司","updateTime":1753928747000,"categoryCode":"B","categoryName":"采矿业","enterpriseScale":"央企","creditCode":"91110000100020821G","isDeleted":0,"contacterPhone":"13800138001","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"中国石油天然气集团有限公司","tenantId":"T0001","id":1},{"districtCode":"110000","annualIncome":"5000亿-1万亿","dtType":"A","city":"北京市","isVerified":"1","enterpriseContacter":"李总监","remark":"建筑行业龙头企业","updateTime":1753928747000,"categoryCode":"E","categoryName":"建筑业","enterpriseScale":"央企","creditCode":"91110000100020822H","isDeleted":0,"contacterPhone":"13800138002","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"中国建筑集团有限公司","tenantId":"T0001","id":2},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"王主管","remark":"互联网科技公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91440300279146858G","isDeleted":0,"contacterPhone":"13800138003","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"腾讯控股有限公司","tenantId":"T0001","id":3},{"districtCode":"440300","annualIncome":"500亿-1000亿","dtType":"B","city":"深圳市","isVerified":"1","enterpriseContacter":"赵部长","remark":"新能源汽车制造商","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"上市公司","creditCode":"91440300192317458K","isDeleted":0,"contacterPhone":"13800138004","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"比亚迪股份有限公司","tenantId":"T0001","id":4},{"districtCode":"440300","annualIncome":"1000亿-5000亿","dtType":"C","city":"深圳市","isVerified":"1","enterpriseContacter":"陈经理","remark":"通信设备制造商","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91440300708461136T","isDeleted":0,"contacterPhone":"13800138005","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"华为技术有限公司","tenantId":"T0001","id":5},{"districtCode":"330100","annualIncome":"1000亿-5000亿","dtType":"B","city":"杭州市","isVerified":"1","enterpriseContacter":"刘总","remark":"电商平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"上市公司","creditCode":"91330000MA27XL0E2Y","isDeleted":0,"contacterPhone":"13800138006","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"阿里巴巴集团控股有限公司","tenantId":"T0001","id":6},{"districtCode":"310000","annualIncome":"100亿-500亿","dtType":"D","city":"上海市","isVerified":"1","enterpriseContacter":"周主任","remark":"社交电商平台","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91310000MA1FL5LX3X","isDeleted":0,"contacterPhone":"13800138007","createTime":1753928747000,"staffScale":"1000-5000人","createId":"admin","name":"上海小红书科技有限公司","tenantId":"T0001","id":7},{"districtCode":"110108","annualIncome":"500亿-1000亿","dtType":"C","city":"北京市","isVerified":"1","enterpriseContacter":"孙经理","remark":"短视频平台公司","updateTime":1753928747000,"categoryCode":"I","categoryName":"信息传输、软件和信息技术服务业","enterpriseScale":"大型企业","creditCode":"91110108MA00A1JE4R","isDeleted":0,"contacterPhone":"13800138008","createTime":1753928747000,"staffScale":"5-10万人","createId":"admin","name":"北京字节跳动科技有限公司","tenantId":"T0001","id":8},{"districtCode":"320500","annualIncome":"1000亿-5000亿","dtType":"C","city":"苏州市","isVerified":"1","enterpriseContacter":"吴厂长","remark":"电子产品代工制造","updateTime":1753928747000,"categoryCode":"C","categoryName":"制造业","enterpriseScale":"大型企业","creditCode":"91320000134582639F","isDeleted":0,"contacterPhone":"13800138009","createTime":1753928747000,"staffScale":"10万人以上","createId":"admin","name":"富士康科技集团","tenantId":"T0001","id":9},{"districtCode":"440300","annualIncome":"1万亿以上","dtType":"A","city":"深圳市","isVerified":"1","enterpriseContacter":"郑总监","remark":"综合金融服务集团","updateTime":1753928747000,"categoryCode":"J","categoryName":"金融业","enterpriseScale":"上市公司","creditCode":"91440300100020611K","isDeleted":0,"contacterPhone":"13800138010","createTime":1753928747000,"staffScale":"1-5万人","createId":"admin","name":"中国平安保险(集团)股份有限公司","tenantId":"T0001","id":10}],"navigateFirstPage":1,"navigateLastPage":3,"navigatePages":8,"navigatepageNums":[1,2,3],"nextPage":2,"pageNum":1,"pageSize":10,"pages":3,"prePage":0,"size":10,"startRow":1,"total":24},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:19:34.562 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:24:34.581 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [81d3f546ea2f39d8,] 2025-08-01 10:28:38.081 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=81d3f546ea2f39d8,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [81d3f546ea2f39d8,] 2025-08-01 10:28:38.238 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=81d3f546ea2f39d8, 耗时=160, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [f9ef7e51589c3b77,] 2025-08-01 10:28:46.834 INFO 35316 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f9ef7e51589c3b77,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [f9ef7e51589c3b77,] 2025-08-01 10:28:46.906 INFO 35316 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f9ef7e51589c3b77, 耗时=72, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:29:34.598 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:34:34.637 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [03a558811d5ca8c9,] 2025-08-01 10:36:49.067 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=03a558811d5ca8c9,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [03a558811d5ca8c9,] 2025-08-01 10:36:49.136 INFO 35316 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=03a558811d5ca8c9, 耗时=69, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [10076eadaa8de428,] 2025-08-01 10:37:18.332 INFO 35316 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=10076eadaa8de428,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [10076eadaa8de428,] 2025-08-01 10:37:18.400 INFO 35316 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=10076eadaa8de428, 耗时=68, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":"<=100000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":"<=","value":100000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:39:34.665 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:44:34.680 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:49:34.684 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:54:34.690 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 10:59:34.702 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
