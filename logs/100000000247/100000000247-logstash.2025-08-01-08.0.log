{"@timestamp":"2025-08-01T08:01:58.285+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:06:58.299+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:11:58.312+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:16:58.319+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:21:58.333+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:26:58.344+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:31:58.357+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:29.091+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-10","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Start destroying common HttpClient","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:29.100+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"Thread-10","class":"c.a.n.common.http.HttpClientBeanHolder","msg":"[HttpClientBeanHolder] Destruction of the end","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:29.108+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"SpringContextShutdownHook","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Unregistering application KBC-ELMS-WEB with eureka with status DOWN","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:44.833+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"background-preinit","class":"o.h.validator.internal.util.Version","msg":"HV000001: Hibernate Validator 6.1.7.Final","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.913+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.920+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.921+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.923+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.924+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.926+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.928+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.929+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:45.998+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:46.018+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:46.022+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:47.674+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 08:33:48 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T08:33:47.741+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 08:33:48 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T08:33:47.798+08:00","traceId":"","remoteIp":"","ip":"*********","level":"ERROR","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.c.n.c.NacosPropertySourceBuilder","msg":"get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, ","stack_trace":"com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Aug 01 08:33:48 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>\r\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)\r\n\tat com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)\r\n\tat com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)\r\n\tat org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)\r\n"}
{"@timestamp":"2025-08-01T08:33:47.800+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.b.c.PropertySourceBootstrapConfiguration","msg":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:48.014+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"The following profiles are active: dev","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:58.920+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","msg":"Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:59.550+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:59.555+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:59.899+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 334 ms. Found 0 MongoDB repository interfaces.","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:59.923+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Multiple Spring Data modules found, entering strict repository configuration mode!","stack_trace":""}
{"@timestamp":"2025-08-01T08:33:59.927+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.285+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","msg":"Finished Spring Data repository scanning in 349 ms. Found 0 Redis repository interfaces.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.756+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.cloud.context.scope.GenericScope","msg":"BeanFactory id=32e1403c-75f2-36d9-aa00-f4e0ee4dd465","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.797+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor","msg":"Post-processing PropertySource instances","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.798+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.799+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.800+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.801+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.801+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.801+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.802+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.803+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.804+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.804+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:00.805+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:01.941+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.filter.DefaultLazyPropertyFilter","msg":"Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:01.950+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.r.DefaultLazyPropertyResolver","msg":"Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:01.950+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.d.DefaultLazyPropertyDetector","msg":"Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.630+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat initialized with port(s): 7002 (http)","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.657+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Initializing ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.659+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardService","msg":"Starting service [Tomcat]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.659+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.apache.catalina.core.StandardEngine","msg":"Starting Servlet engine: [Apache Tomcat/9.0.53]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.891+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring embedded WebApplicationContext","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:02.892+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.s.c.ServletWebServerApplicationContext","msg":"Root WebApplicationContext: initialization completed in 14794 ms","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:05.213+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.a.d.s.b.a.DruidDataSourceAutoConfigure","msg":"Init DruidDataSource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:07.820+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.alibaba.druid.pool.DruidDataSource","msg":"{dataSource-1} inited","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:08.245+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"org.mongodb.driver.cluster","msg":"Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:08.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-rtt-ClusterId{value='688c0b806b4b582478991fa0', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:08.611+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688c0b806b4b582478991fa0', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:08.615+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"cluster-ClusterId{value='688c0b806b4b582478991fa0', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017","class":"org.mongodb.driver.cluster","msg":"Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=119767200}","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:08.696+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:09.588+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.data.convert.CustomConversions","msg":"Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:10.542+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.commons.filter.TraceContextFilter","msg":"Filter 'traceContextFilter' configured for use","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:12.414+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.auth.service.AuthService","msg":"interface com.kbao.kbcelms.auth.dao.AuthMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:12.521+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.bascode.service.BasCodeService","msg":"interface com.kbao.kbcelms.bascode.dao.BasCodeMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:13.331+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.f.a.AutowiredAnnotationBeanPostProcessor","msg":"Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:13.473+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.115+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.144+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.233+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.297+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.315+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.358+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.423+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.505+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.c.service.ConstantConfigService","msg":"interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:14.625+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.d.service.DataTemplateService","msg":"interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.069+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.g.s.GenAgentEnterpriseService","msg":"interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.139+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.industry.service.IndustryService","msg":"interface com.kbao.kbcelms.industry.dao.IndustryMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.244+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.333+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.roleauth.service.RoleAuthService","msg":"interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.354+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.role.service.RoleService","msg":"interface com.kbao.kbcelms.role.dao.RoleMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.384+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.u.service.UserTenantService","msg":"interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.458+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessLogService","msg":"interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.526+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.602+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.631+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userrole.service.UserRoleService","msg":"interface com.kbao.kbcelms.userrole.dao.UserRoleMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.657+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.userorg.service.UserOrgService","msg":"interface com.kbao.kbcelms.userorg.dao.UserOrgMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.679+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.kbao.kbcelms.user.service.UserService","msg":"interface com.kbao.kbcelms.user.dao.UserMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.765+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.p.service.ProcessDefineService","msg":"interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.812+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.888+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityProcessService","msg":"interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:15.947+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.075+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.207+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.265+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.292+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.339+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.s.OpportunityTeamDivisionService","msg":"interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.398+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityTeamService","msg":"interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.465+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityDetailService","msg":"interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.528+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityService","msg":"interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:16.826+08:00","traceId":"","remoteIp":"","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.k.o.service.OpportunityOrderService","msg":"interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:17.073+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:17.795+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:17.843+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:17.902+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:17.934+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.117+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.210+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.254+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.316+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.369+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.471+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.546+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.609+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.643+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.691+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.730+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.789+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.828+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.877+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.915+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:18.965+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.017+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.053+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.087+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.118+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.136+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.225+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.264+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.361+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.511+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.562+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.659+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.694+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.721+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.763+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.796+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.827+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.848+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.864+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.890+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.931+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:19.999+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.029+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.053+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.065+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.093+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.107+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:20.130+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:21.027+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","msg":"Exposing 2 endpoint(s) beneath base path '/actuator'","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:21.386+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.PropertySourcedRequestMappingHandlerMapping","msg":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:22.624+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.k.j.a.XxlJobClientAutoConfigure","msg":">>>>>>>>>>> xxl-job config init.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:24.934+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.c.DiscoveryClientOptionalArgsConfiguration","msg":"Eureka HTTP Client uses RestTemplate.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:25.516+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger","msg":"Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.447+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.467+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.486+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.507+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.530+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.549+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.566+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.585+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.608+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.629+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.651+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.696+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.723+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.788+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.819+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.841+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.870+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.889+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.918+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.937+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.955+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:27.985+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.014+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.059+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.083+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.130+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.136+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.140+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.152+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.157+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.168+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.185+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.202+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.214+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.224+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.229+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.235+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.241+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.249+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.256+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.283+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.288+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.297+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.301+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.306+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.316+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.319+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.332+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.337+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.342+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.347+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.352+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.356+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.364+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.374+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.386+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.420+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.425+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.436+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.454+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.472+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.478+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.485+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.496+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.506+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.511+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.518+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.openfeign.FeignClientFactoryBean","msg":"For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:28.923+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","msg":"Setting initial instance status as: STARTING","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.138+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Initializing Eureka in region us-east-1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.154+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.235+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Disable delta property : false","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Single vip registry refresh property : null","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Force full registry fetch : false","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application is null : false","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Registered Applications size is zero : true","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Application version is -1: true","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:29.236+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Getting all instance registry info from the eureka server","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.545+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"The response status is 200","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.562+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Not registering with Eureka server per configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.584+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.netflix.discovery.DiscoveryClient","msg":"Discovery Client initialized at timestamp 1754008470580 with initial instances count: 10","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.596+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","msg":"Registering application KBC-ELMS-WEB with eureka with status UP","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.600+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.apache.coyote.http11.Http11NioProtocol","msg":"Starting ProtocolHandler [\"http-nio-7002\"]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.740+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","msg":"Tomcat started on port(s): 7002 (http) with context path ''","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.743+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","msg":"Updating port to 7002","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.744+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.745+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.746+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.763+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.766+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.777+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.780+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.781+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.810+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.810+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.810+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.812+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudDefaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.813+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.815+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.815+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.823+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:30.823+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.006+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.RefreshScopeRefreshedEventListener","msg":"Refreshing cached encryptable property sources on ServletWebServerInitializedEvent","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.007+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.007+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source systemEnvironment refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.010+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source random refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source cachedrandom refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source springCloudClientHostInfo refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.023+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.c.CachingDelegateEncryptablePropertySource","msg":"Property Source defaultProperties refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.024+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.024+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.024+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"c.u.j.EncryptablePropertySourceConverter","msg":"Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.025+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Context refreshed","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.124+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.p.DocumentationPluginsBootstrapper","msg":"Found 1 custom documentation plugin(s)","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:31.523+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.s.ApiListingReferenceScanner","msg":"Scanning for api listing references","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.105+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.147+08:00","traceId":"","remoteIp":"","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.p.ParameterDataTypeReader","msg":"Trying to infer dataType java.lang.String[]","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.204+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.208+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.214+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.404+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: addUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.413+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: listUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.414+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: removeUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.538+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: deleteUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.544+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.546+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: listUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.550+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.555+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_1","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.592+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: detailUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.628+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: pageUsingPOST_3","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.642+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"s.d.s.w.r.o.CachingOperationNameGenerator","msg":"Generating unique operation named: saveOrUpdateUsingPOST_2","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:32.821+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"main","class":"com.kbao.kbcelms.KbcElmsWebApplication","msg":"Started KbcElmsWebApplication in 49.829 seconds (JVM running for 52.364)","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:33.651+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(5)-*********","class":"o.a.c.c.C.[Tomcat].[localhost].[/]","msg":"Initializing Spring DispatcherServlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:33.653+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(5)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Initializing Servlet 'dispatcherServlet'","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:33.657+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(5)-*********","class":"o.s.web.servlet.DispatcherServlet","msg":"Completed initialization in 4 ms","stack_trace":""}
{"@timestamp":"2025-08-01T08:34:35.280+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"RMI TCP Connection(8)-*********","class":"org.mongodb.driver.connection","msg":"Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017","stack_trace":""}
{"@timestamp":"2025-08-01T08:39:29.253+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:44:29.266+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:46:03.436+08:00","traceId":"8a09eb5cf293c394","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"i.s.m.p.AbstractSerializableParameter","msg":"Illegal DefaultValue null for parameter type integer","stack_trace":"java.lang.NumberFormatException: For input string: \"\"\r\n\tat java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)\r\n\tat java.lang.Long.parseLong(Long.java:601)\r\n\tat java.lang.Long.valueOf(Long.java:803)\r\n\tat io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:412)\r\n\tat sun.reflect.GeneratedMethodAccessor237.invoke(Unknown Source)\r\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.lang.reflect.Method.invoke(Method.java:498)\r\n\tat com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)\r\n\tat com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)\r\n\tat com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)\r\n"}
{"@timestamp":"2025-08-01T08:46:03.439+08:00","traceId":"8a09eb5cf293c394","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"i.s.m.p.AbstractSerializableParameter","msg":"Illegal DefaultValue null for parameter type integer","stack_trace":"java.lang.NumberFormatException: For input string: \"\"\r\n\tat java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)\r\n\tat java.lang.Long.parseLong(Long.java:601)\r\n\tat java.lang.Long.valueOf(Long.java:803)\r\n\tat io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:412)\r\n\tat sun.reflect.GeneratedMethodAccessor237.invoke(Unknown Source)\r\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.lang.reflect.Method.invoke(Method.java:498)\r\n\tat com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)\r\n\tat com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)\r\n\tat com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)\r\n"}
{"@timestamp":"2025-08-01T08:46:03.441+08:00","traceId":"8a09eb5cf293c394","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"i.s.m.p.AbstractSerializableParameter","msg":"Illegal DefaultValue null for parameter type integer","stack_trace":"java.lang.NumberFormatException: For input string: \"\"\r\n\tat java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)\r\n\tat java.lang.Long.parseLong(Long.java:601)\r\n\tat java.lang.Long.valueOf(Long.java:803)\r\n\tat io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:412)\r\n\tat sun.reflect.GeneratedMethodAccessor237.invoke(Unknown Source)\r\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.lang.reflect.Method.invoke(Method.java:498)\r\n\tat com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)\r\n\tat com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)\r\n\tat com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)\r\n"}
{"@timestamp":"2025-08-01T08:46:03.443+08:00","traceId":"8a09eb5cf293c394","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"i.s.m.p.AbstractSerializableParameter","msg":"Illegal DefaultValue null for parameter type integer","stack_trace":"java.lang.NumberFormatException: For input string: \"\"\r\n\tat java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)\r\n\tat java.lang.Long.parseLong(Long.java:601)\r\n\tat java.lang.Long.valueOf(Long.java:803)\r\n\tat io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:412)\r\n\tat sun.reflect.GeneratedMethodAccessor237.invoke(Unknown Source)\r\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.lang.reflect.Method.invoke(Method.java:498)\r\n\tat com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)\r\n\tat com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)\r\n\tat com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)\r\n"}
{"@timestamp":"2025-08-01T08:46:03.444+08:00","traceId":"8a09eb5cf293c394","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"WARN","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"i.s.m.p.AbstractSerializableParameter","msg":"Illegal DefaultValue null for parameter type integer","stack_trace":"java.lang.NumberFormatException: For input string: \"\"\r\n\tat java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)\r\n\tat java.lang.Long.parseLong(Long.java:601)\r\n\tat java.lang.Long.valueOf(Long.java:803)\r\n\tat io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:412)\r\n\tat sun.reflect.GeneratedMethodAccessor237.invoke(Unknown Source)\r\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.lang.reflect.Method.invoke(Method.java:498)\r\n\tat com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)\r\n\tat com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)\r\n\tat com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)\r\n"}
{"@timestamp":"2025-08-01T08:49:25.555+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.557+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: f7c46c6b1ea92254","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.559+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.559+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.560+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.560+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.561+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"da90bf43e61c8c1ac048fe4c55edd399\",\"tenantId\":\"T0001\",\"funcId\":\"F00111\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:25.561+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.201+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (638ms)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.201+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.201+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.201+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:49:26 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.202+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.202+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.203+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.203+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.203+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"resp_code\":401,\"resp_msg\":\"用户登录的Token无效，请重新登录快保云服\"}","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:26.203+08:00","traceId":"f7c46c6b1ea92254","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (87-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:29.277+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.896+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.898+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 564f4f00636369a1","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.899+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.900+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.900+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.900+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.901+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"26ffd5bec599e9dad60714f020b2292d\",\"tenantId\":\"T0001\",\"funcId\":\"F00111\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:50.902+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.187+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (284ms)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.187+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.188+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:49:51 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.189+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F00111\",\"funcName\":\"用户列表\",\"funcType\":\"2\",\"funcLevel\":2,\"parentFuncId\":\"F00110\",\"funcIcon\":\"icondt4\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/ucs-web/userList\",\"urlType\":\"2\",\"sort\":1,\"isPublic\":0,\"applyId\":\"APP000067\",\"funcAuths\":[{\"authCode\":\"ucs:user:bindAgent\",\"funcId\":\"F00111\",\"authName\":\"绑定工号\",\"sort\":4},{\"authCode\":\"ucs:user:bindAgentUrl\",\"funcId\":\"F00111\",\"authName\":\"生成绑定工号链接\",\"sort\":13},{\"authCode\":\"ucs:user:blocked\",\"funcId\":\"F00111\",\"authName\":\"锁定用户\",\"sort\":2},{\"authCode\":\"ucs:user:changeBind\",\"funcId\":\"F00111\",\"authName\":\"换绑代理人工号\",\"sort\":5},{\"authCode\":\"ucs:user:changeReferee\",\"funcId\":\"F00111\",\"authName\":\"变更推荐人\",\"sort\":7},{\"authCode\":\"ucs:user:channelagencySalesAdd\",\"funcId\":\"F00111\",\"authName\":\"添加渠道租户下的业务员\",\"sort\":17},{\"authCode\":\"ucs:user:clearAuthStatus\",\"funcId\":\"F00111\",\"authName\":\"清除用户认证状态\",\"sort\":9},{\"authCode\":\"ucs:user:clearReferee\",\"funcId\":\"F00111\",\"authName\":\"清除推荐人\",\"sort\":8},{\"authCode\":\"ucs:user:destorySwitch\",\"funcId\":\"F00111\",\"authName\":\"账户注销开关\",\"sort\":3},{\"authCode\":\"ucs:user:export:direct\",\"funcId\":\"F00111\",\"authName\":\"立即导出\",\"authRoute\":\"/ucs/user/export\",\"sort\":10},{\"authCode\":\"ucs:user:export:plan\",\"funcId\":\"F00111\",\"authName\":\"计划导出\",\"authRoute\":\"/ucs/user/export\",\"sort\":14},{\"authCode\":\"ucs:user:forcedKick\",\"funcId\":\"F00111\",\"authName\":\"用户详情强制踢出登录\",\"sort\":19},{\"authCode\":\"ucs:user:gaiRiskManager\",\"funcId\":\"F00111\",\"authName\":\"团财风险等级流水\",\"sort\":25},{\"authCode\":\"ucs:user:info\",\"funcId\":\"F00111\",\"authName\":\"查看\",\"sort\":15},{\"authCode\":\"ucs:user:kickOut\",\"funcId\":\"F00111\",\"authName\":\"踢出登录\",\"sort\":18},{\"authCode\":\"ucs:user:lockOrUnlockUser\",\"funcId\":\"F00111\",\"authName\":\"锁定解锁用户\",\"sort\":20},{\"authCode\":\"ucs:user:matching\",\"funcId\":\"F00111\",\"authName\":\"用户匹配\",\"sort\":12},{\"authCode\":\"ucs:user:modifyPhone\",\"funcId\":\"F00111\",\"authName\":\"修改登录手机号\",\"sort\":22},{\"authCode\":\"ucs:user:openOrCloseSwitch\",\"funcId\":\"F00111\",\"authName\":\"打开关闭注销开关\",\"sort\":21},{\"authCode\":\"ucs:user:riskManager\",\"funcId\":\"F00111\",\"authName\":\"风险等级管理\",\"sort\":24},{\"authCode\":\"ucs:user:search\",\"funcId\":\"F00111\",\"authName\":\"查询\",\"sort\":16},{\"authCode\":\"ucs:user:syncAgent\",\"funcId\":\"F00111\",\"authName\":\"同步代理人信息\",\"sort\":11},{\"authCode\":\"ucs:user:unBind\",\"funcId\":\"F00111\",\"authName\":\"解绑代理人工号\",\"sort\":6},{\"authCode\":\"ucs:user:updateResetDeviceIdSwitch\",\"funcId\":\"F00111\",\"authName\":\"重置设备标识开关\",\"sort\":23}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.190+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (4871-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.442+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.672+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T08:49:51.861+08:00","traceId":"564f4f00636369a1","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 0","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.546+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.548+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: 24bb86e7cf136dc0","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.549+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.549+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.549+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.550+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.551+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"26ffd5bec599e9dad60714f020b2292d\",\"tenantId\":\"T0001\",\"funcId\":\"F03982\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.551+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.684+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (132ms)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.684+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.684+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.684+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:51:08 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.684+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.685+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.686+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.686+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.686+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F03982\",\"funcName\":\"产品资料维护\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F03889\",\"funcIcon\":\"icondt-103\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/chatbot/productInfo\",\"urlType\":\"2\",\"sort\":9,\"isPublic\":0,\"applyId\":\"APP000220\",\"funcAuths\":[{\"authCode\":\"chatbot:productData:show\",\"funcId\":\"F03982\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.686+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2593-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.730+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.730+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:07.771+08:00","traceId":"24bb86e7cf136dc0","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 0","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.280+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.284+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] app_trace_id: a7fd556fbb957ef4","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.285+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Length: 157","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.288+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] Content-Type: application/json","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.291+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] remoteIp: 0:0:0:0:0:0:0:1","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.291+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.291+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"accessToken\":\"26ffd5bec599e9dad60714f020b2292d\",\"tenantId\":\"T0001\",\"funcId\":\"F03982\",\"userId\":null,\"phone\":null,\"email\":null,\"roleId\":null,\"userName\":null}","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.291+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ---> END HTTP (157-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.452+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- HTTP/1.1 200  (159ms)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.453+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] connection: keep-alive","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.453+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] content-type: application/json;charset=UTF-8","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.453+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] date: Fri, 01 Aug 2025 00:51:10 GMT","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.453+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] keep-alive: timeout=60","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.453+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] transfer-encoding: chunked","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.454+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] vary: accept-encoding","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.454+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.454+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] {\"datas\":{\"user\":{\"userId\":\"U000162\",\"userName\":\"ganjie\",\"nickName\":\"甘杰\",\"isAdmin\":1,\"tenantId\":\"T0001\",\"tenantName\":\"大童保险销售服务有限公司\",\"userType\":\"1\",\"phone\":\"17656543456\"},\"function\":{\"funcId\":\"F03982\",\"funcName\":\"产品资料维护\",\"funcType\":\"2\",\"funcLevel\":3,\"parentFuncId\":\"F03889\",\"funcIcon\":\"icondt-103\",\"url\":\"iframe?iframeUrl=https://kbc-sta.kbao123.com/chatbot/productInfo\",\"urlType\":\"2\",\"sort\":9,\"isPublic\":0,\"applyId\":\"APP000220\",\"funcAuths\":[{\"authCode\":\"chatbot:productData:show\",\"funcId\":\"F03982\",\"authName\":\"查看\",\"sort\":1}]},\"encrypt\":{\"proposer\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"insured\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"beneficiary\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"order\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"},\"kbUser\":{\"birthday\":\"0\",\"name\":\"0\",\"identityCode\":\"0\",\"phone\":\"0\",\"email\":\"0\",\"workUnit\":\"0\",\"contactAddress\":\"0\",\"workUnitAddress\":\"0\",\"bankCardNo\":\"0\",\"orderCode\":\"0\",\"policyNo\":\"0\",\"printNo\":\"0\",\"firstYearPremium\":\"0\",\"enquiryPremium\":\"0\",\"standardPremium\":\"0\",\"visitDate\":\"0\",\"underwriteDate\":\"0\",\"effectiveDate\":\"0\",\"receiptDate\":\"0\",\"plateNumber\":\"0\",\"enterpriseCert\":\"0\",\"VIN\":\"0\"}},\"orgInfo\":{}},\"resp_code\":0,\"resp_msg\":\"查询成功\"}","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.455+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"com.kbao.kbcbsc.client.UserClientService","msg":"[UserClientService#getUserInfo] <--- END HTTP (2593-byte body)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.458+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? ","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.459+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"==> Parameters: T0001(String), U000162(String)","stack_trace":""}
{"@timestamp":"2025-08-01T08:51:09.493+08:00","traceId":"a7fd556fbb957ef4","remoteIp":"0:0:0:0:0:0:0:1","ip":"*********","level":"DEBUG","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.k.u.d.U.getUserRoleAuthByUserId","msg":"<==      Total: 0","stack_trace":""}
{"@timestamp":"2025-08-01T08:54:29.286+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
{"@timestamp":"2025-08-01T08:59:29.300+08:00","traceId":"","remoteIp":"","ip":"*********","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"AsyncResolver-bootstrap-executor-0","class":"c.n.d.s.r.aws.ConfigClusterResolver","msg":"Resolving eureka endpoints via configuration","stack_trace":""}
