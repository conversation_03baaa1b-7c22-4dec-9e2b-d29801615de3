[kbc-elms-web:*********:7002] [,] 2025-07-31 11:04:54.669 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.072 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.072 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.075 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2b102ca17f6612e8
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.075 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b40fe2ae3cdcf06c
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.076 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.076 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.076 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.076 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.076 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.077 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.229 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (151ms)
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.229 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:08:23 GMT
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [2b102ca17f6612e8,] 2025-07-31 11:08:23.230 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.238 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (160ms)
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.239 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.239 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.240 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:08:23 GMT
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.240 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.240 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.240 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.240 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.241 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [b40fe2ae3cdcf06c,] 2025-07-31 11:08:23.241 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:09:54.685 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:14:54.694 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.739 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.739 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.740 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6c986b043272a6f0
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.741 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: da260c57e2b87486
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.741 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.741 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.741 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.741 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.742 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.867 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (124ms)
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.867 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.867 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.868 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:32 GMT
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.868 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.868 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.868 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.868 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (125ms)
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:32 GMT
[kbc-elms-web:*********:7002] [6c986b043272a6f0,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.869 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.870 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [da260c57e2b87486,] 2025-07-31 11:19:31.870 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.872 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.872 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.873 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b1973b1d4f50fb7c
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.873 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: abb54e2150371b85
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.874 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.875 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.875 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.875 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.875 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.971 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (95ms)
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.971 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.971 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:36 GMT
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.972 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [abb54e2150371b85,] 2025-07-31 11:19:35.973 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.976 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (101ms)
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.976 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.976 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:36 GMT
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.977 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [b1973b1d4f50fb7c,] 2025-07-31 11:19:35.978 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.046 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.046 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b8bff835ef345c72
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 974c2071a70c1af1
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.047 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.048 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.152 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (104ms)
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.152 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.152 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (105ms)
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:46 GMT
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:46 GMT
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.153 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.154 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [974c2071a70c1af1,] 2025-07-31 11:19:46.154 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.154 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [b8bff835ef345c72,] 2025-07-31 11:19:46.154 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.790 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.790 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: da90ce85e341ebef
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 74c34ef69da08c3c
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.791 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.854 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (63ms)
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.855 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.855 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.855 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:46 GMT
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.856 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.856 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.856 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.857 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.857 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [74c34ef69da08c3c,] 2025-07-31 11:19:46.857 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.868 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (76ms)
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.870 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.870 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.870 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:46 GMT
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.871 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.871 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.872 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.872 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.872 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [da90ce85e341ebef,] 2025-07-31 11:19:46.872 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.523 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.524 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 98c1f444f61cbca8
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.524 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.525 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f997db45ff59ecc2
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.526 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.618 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.618 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.618 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.618 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.619 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.619 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.619 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:49 GMT
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.619 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:49 GMT
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.620 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.620 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.620 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.621 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [f997db45ff59ecc2,] 2025-07-31 11:19:48.622 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [98c1f444f61cbca8,] 2025-07-31 11:19:48.622 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.629 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7861dc62a5be98db
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 334b596de6c099b1
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.630 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.632 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.687 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (56ms)
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.687 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (56ms)
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.687 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.687 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:49 GMT
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:49 GMT
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.688 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.689 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [7861dc62a5be98db,] 2025-07-31 11:19:48.689 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [334b596de6c099b1,] 2025-07-31 11:19:48.689 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.740 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9ca956c7aff2ac77
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.741 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.827 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.827 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.827 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.827 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:19:52 GMT
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.827 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.828 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.828 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.828 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.828 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [9ca956c7aff2ac77,] 2025-07-31 11:19:51.828 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:19:54.697 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.270 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.271 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 54f3d3709b0a22f5
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.272 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.272 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.272 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.272 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.273 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.273 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.273 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.273 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9e158441559ab4c9
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.273 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.274 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.274 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.275 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.275 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.275 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (76ms)
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:20:19 GMT
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.352 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:20:19 GMT
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.353 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [54f3d3709b0a22f5,] 2025-07-31 11:20:19.354 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.354 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [9e158441559ab4c9,] 2025-07-31 11:20:19.354 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.187 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.187 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 3d653fa7ae59733c
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 623991f6f36d5d19
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.188 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.189 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:20:52 GMT
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:20:52 GMT
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.267 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.268 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.268 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.268 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.268 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.268 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [3d653fa7ae59733c,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [623991f6f36d5d19,] 2025-07-31 11:20:52.269 DEBUG 17560 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.126 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.127 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 392cfa240f352969
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.127 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.127 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.127 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.128 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.128 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.128 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.292 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (163ms)
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.292 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.292 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.292 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:21:54 GMT
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [392cfa240f352969,] 2025-07-31 11:21:54.293 DEBUG 17560 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.210 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.210 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d6be2f0d82386a3a
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1afdb80abde08179
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.211 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.212 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.212 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.212 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.212 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.212 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.348 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (136ms)
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.349 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (136ms)
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.349 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.351 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.351 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.351 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.351 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:23:52 GMT
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:23:52 GMT
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.353 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.354 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.354 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.355 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.355 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1afdb80abde08179,] 2025-07-31 11:23:52.355 DEBUG 17560 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.355 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [d6be2f0d82386a3a,] 2025-07-31 11:23:52.358 DEBUG 17560 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:24:54.703 INFO 17560 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.061 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.061 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.062 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 372b0fbbd378505f
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.062 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 182f3ed23d583be6
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.062 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.063 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.063 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.063 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.063 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.064 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.064 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.064 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.064 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.064 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.065 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.065 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.194 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (127ms)
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.194 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.194 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.194 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:37 GMT
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [372b0fbbd378505f,] 2025-07-31 11:27:37.195 DEBUG 17560 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.196 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (131ms)
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.197 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.197 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.197 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:37 GMT
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.198 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.198 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.198 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.198 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.199 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [182f3ed23d583be6,] 2025-07-31 11:27:37.199 DEBUG 17560 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.520 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.520 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.521 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7409b9dacb94525e
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.522 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f227199c434f2f36
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.522 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.522 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.523 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.523 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.523 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.523 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.524 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.524 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.524 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.524 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.524 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.525 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (104ms)
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:41 GMT
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.629 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:41 GMT
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.630 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [f227199c434f2f36,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [7409b9dacb94525e,] 2025-07-31 11:27:40.631 DEBUG 17560 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.953 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.953 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.954 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 751cc81cbf430994
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.954 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c00f525e11e25500
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.954 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.955 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.955 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.955 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.956 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:42.957 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:42.957 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.048 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:43 GMT
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.049 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.049 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Thu, 31 Jul 2025 03:27:43 GMT
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [751cc81cbf430994,] 2025-07-31 11:27:43.050 DEBUG 17560 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.051 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.051 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [c00f525e11e25500,] 2025-07-31 11:27:43.051 DEBUG 17560 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:28:23.847 WARN 17560 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:28:23.851 INFO 17560 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:*********:7002] [,] 2025-07-31 11:28:23.853 WARN 17560 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
