[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 15:19:32.868 WARN 3344 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 15:19:33.285 WARN 3344 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 15:19:42.663 WARN 3344 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 17:25:49.006 WARN 23304 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 17:25:49.509 WARN 23304 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 17:26:05.544 WARN 23304 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 17:26:09.188 WARN 23304 [main] org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 7002 is already in use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 17:26:09.425 WARN 23304 [main] org.apache.catalina.loader.WebappClassLoaderBase The web application [ROOT] appears to have started a thread named [logback-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:04:52.988 WARN 9468 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:04:53.333 WARN 9468 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:05:03.208 WARN 9468 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:05:07.188 WARN 9468 [main] org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 7002 is already in use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:05:07.438 WARN 9468 [main] org.apache.catalina.loader.WebappClassLoaderBase The web application [ROOT] appears to have started a thread named [logback-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:05:07.439 WARN 9468 [main] org.apache.catalina.loader.WebappClassLoaderBase The web application [ROOT] appears to have started a thread named [logback-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:08:08.995 WARN 19812 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:08:09.307 WARN 19812 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:08:18.922 WARN 19812 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:08:22.567 WARN 19812 [main] org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 7002 is already in use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:09:54.696 WARN 21880 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:09:55.054 WARN 21880 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:10:05.048 WARN 21880 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:59:49.480 WARN 16772 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:59:49.783 WARN 16772 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-24 18:59:59.840 WARN 16772 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
