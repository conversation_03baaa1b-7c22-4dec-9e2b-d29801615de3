[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.942 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.942 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.946 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 90868747b57d5f89
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.946 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 32cb2cfc871100ef
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.947 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.947 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.947 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.948 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.948 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.948 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.949 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.949 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"eac9ca900e3a6a4c1ad8619f30b19577","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.949 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:18.949 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.950 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"eac9ca900e3a6a4c1ad8619f30b19577","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:18.950 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.598 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (649ms)
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.598 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (649ms)
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.599 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.599 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.599 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.599 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.599 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:29:19 GMT
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:29:19 GMT
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.600 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.601 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.601 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [90868747b57d5f89,] 2025-07-25 13:29:19.602 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [32cb2cfc871100ef,] 2025-07-25 13:29:19.602 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.836 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.837 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1879265b54d65a5e
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.837 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.837 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.838 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.838 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.838 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"eac9ca900e3a6a4c1ad8619f30b19577","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.839 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.945 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.945 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.946 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.946 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:29:20 GMT
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.946 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.946 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.947 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.947 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.947 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [1879265b54d65a5e,] 2025-07-25 13:29:20.947 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e00a8a57a9352ff7
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.106 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.107 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"eac9ca900e3a6a4c1ad8619f30b19577","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.107 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.169 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (62ms)
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.170 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.170 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.172 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:29:20 GMT
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [e00a8a57a9352ff7,] 2025-07-25 13:29:21.177 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.894 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.895 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d414be5a4a132755
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.895 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.895 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.895 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.896 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.897 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.897 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.987 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.988 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.988 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.988 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:11 GMT
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.988 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.988 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.989 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.989 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.995 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:11.995 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.265 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.265 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: d414be5a4a132755
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.265 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.266 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.266 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.266 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.266 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.267 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (139ms)
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:30:11 GMT
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.406 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d414be5a4a132755,] 2025-07-25 13:30:12.407 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.549 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.549 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 01425d2b30d0e189
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.549 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.549 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.549 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.550 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.551 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.551 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.632 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:11 GMT
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.634 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.641 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.642 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 01425d2b30d0e189
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.642 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.643 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.643 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.643 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.643 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.643 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.716 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.716 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.716 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.716 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:30:11 GMT
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.716 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.717 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.717 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.717 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.717 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [01425d2b30d0e189,] 2025-07-25 13:30:12.717 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.217 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: aed672fe80dc58d2
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.218 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (110ms)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:14 GMT
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.328 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.329 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.329 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.329 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.335 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: aed672fe80dc58d2
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (97ms)
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:30:14 GMT
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.434 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.435 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.435 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.436 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [aed672fe80dc58d2,] 2025-07-25 13:30:14.436 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.565 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.565 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 59a37b797959fb4c
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.566 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.656 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.657 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.658 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.658 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:14 GMT
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.658 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.658 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.658 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.659 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.659 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:14.659 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.528 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.718 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.870 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.881 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.889 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [59a37b797959fb4c,] 2025-07-25 13:30:15.926 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.988 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.989 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6209021181f9db5a
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.989 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.990 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.990 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.990 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.990 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:17.991 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.094 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.095 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.095 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:18 GMT
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.096 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.108 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.109 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.146 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [6209021181f9db5a,] 2025-07-25 13:30:18.372 DEBUG 23660 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.677 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.677 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a65e3d0229793fb2
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.678 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.748 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (69ms)
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.748 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.748 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.750 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:18 GMT
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.750 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.750 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.750 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.757 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.758 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.793 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.801 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.801 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.845 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.846 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.846 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [a65e3d0229793fb2,] 2025-07-25 13:30:18.887 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.104 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.106 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: abdb4566c671257d
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.106 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.106 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.106 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.106 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.107 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.107 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.216 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.216 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.216 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.216 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:27 GMT
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.217 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.217 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.217 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.217 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.218 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.218 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.224 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: abdb4566c671257d
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.225 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.226 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.296 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (70ms)
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.297 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.298 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.298 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:30:27 GMT
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.301 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.302 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.303 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.303 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.304 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [abdb4566c671257d,] 2025-07-25 13:30:27.304 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.334 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.335 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 4eef5ed900dac642
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.336 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.336 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.336 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.336 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.337 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.337 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.421 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.421 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.423 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.423 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:27 GMT
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.424 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.424 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.424 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.425 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.427 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.427 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.432 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.432 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.467 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [4eef5ed900dac642,] 2025-07-25 13:30:27.468 DEBUG 23660 [http-nio-7002-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.537 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d2bff4e58f39467c
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.538 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.607 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (69ms)
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:30:27 GMT
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.609 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.609 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.614 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.614 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.650 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.652 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.652 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.685 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.686 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.686 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [d2bff4e58f39467c,] 2025-07-25 13:30:27.721 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.918 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.918 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e4d4f909f705cb9a
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.919 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.919 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.919 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.919 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.919 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:50.920 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.165 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.165 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1547337a8f230b83
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.165 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.165 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.166 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.166 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.166 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.166 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.302 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (382ms)
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.302 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.302 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.302 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:35:51 GMT
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.303 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.307 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.307 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e4d4f909f705cb9a
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.308 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.308 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.308 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.309 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.309 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.309 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.523 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (213ms)
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.523 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:35:51 GMT
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.524 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e4d4f909f705cb9a,] 2025-07-25 13:35:51.525 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.532 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (365ms)
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.532 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.532 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.532 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:35:51 GMT
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.532 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.533 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.533 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.533 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.533 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.533 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.725 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==>  Preparing: SELECT count(0) FROM t_data_template t WHERE is_deleted = 0 
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.725 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.907 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.907 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==>  Preparing: select t.id, t.template_name, t.biz_code, t.type, t.status, t.remark, t.create_time, t.create_id, t.update_time, t.update_id, t.is_deleted, t.tenant_id from t_data_template t WHERE is_deleted = 0 LIMIT ? 
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:51.908 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [1547337a8f230b83,] 2025-07-25 13:35:52.058 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectAll <==      Total: 2
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.405 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9ac26e77e48125b1
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.406 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.407 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.611 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (203ms)
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.611 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.612 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.614 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:35:54 GMT
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.615 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.615 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.615 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.616 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.616 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.617 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.631 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.633 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.758 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [9ac26e77e48125b1,] 2025-07-25 13:35:54.759 DEBUG 23660 [http-nio-7002-exec-6] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.941 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.941 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 258430d29fd30eec
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:54.942 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.147 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (205ms)
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.147 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.147 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.147 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:35:54 GMT
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.147 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.148 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.148 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.148 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.149 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.149 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.154 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.155 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.334 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.336 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.337 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.795 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.797 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.800 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [258430d29fd30eec,] 2025-07-25 13:35:55.945 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.705 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.707 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b548914ee496be32
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.708 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.840 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (131ms)
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.840 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.840 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.841 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:37:23 GMT
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.841 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.841 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.841 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.841 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.842 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.843 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: b548914ee496be32
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.849 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (84ms)
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:37:23 GMT
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.933 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.934 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.941 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.945 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b548914ee496be32,] 2025-07-25 13:37:23.945 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 58ab4f0f50be9f4f
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.006 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.081 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (74ms)
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.081 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.081 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.081 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:37:23 GMT
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.082 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.082 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.082 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.082 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.083 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.083 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.128 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.128 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.168 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [58ab4f0f50be9f4f,] 2025-07-25 13:37:24.168 DEBUG 23660 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.261 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.261 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 72a07329eae14b76
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.261 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.261 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.261 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.262 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.262 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.262 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.332 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (71ms)
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.333 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.333 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.333 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:37:23 GMT
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.333 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.334 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.334 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.334 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.337 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.338 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.357 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.358 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.400 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.401 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.402 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.440 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.441 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.441 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [72a07329eae14b76,] 2025-07-25 13:37:24.476 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.500 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.503 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1cba3fce921f2932
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.503 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.504 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.504 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.504 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.504 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.506 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.850 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (342ms)
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.850 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.850 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.850 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:40:31 GMT
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.850 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.851 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.851 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.851 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.851 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.852 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.858 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.858 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 1cba3fce921f2932
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.859 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.859 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.859 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.860 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.860 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:31.860 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.067 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (207ms)
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.067 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.067 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.068 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:40:31 GMT
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.068 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.068 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.068 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.069 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.069 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1cba3fce921f2932,] 2025-07-25 13:40:32.069 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.745 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.750 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 4e422a8610d52b06
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.750 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.751 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.751 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.751 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.751 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.752 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.880 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.881 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 873b38baab7f6e79
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.881 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.882 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.883 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.883 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.883 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:32.883 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.964 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (212ms)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:40:32 GMT
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.965 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.966 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.966 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.966 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 4e422a8610d52b06
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.971 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:32.972 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.182 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (209ms)
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.184 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.185 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.185 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:40:32 GMT
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.185 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.186 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.186 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.186 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.187 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4e422a8610d52b06,] 2025-07-25 13:40:33.187 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (342ms)
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:40:32 GMT
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.226 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.227 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.228 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.230 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.230 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.379 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.379 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.532 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [873b38baab7f6e79,] 2025-07-25 13:40:33.533 DEBUG 23660 [http-nio-7002-exec-3] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.703 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.703 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: bf554e6a5eb0ca79
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.703 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.703 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.704 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.704 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.704 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.704 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (195ms)
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:40:32 GMT
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.900 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.901 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.901 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.904 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:33.905 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.067 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.069 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.069 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.221 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.221 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.221 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [bf554e6a5eb0ca79,] 2025-07-25 13:40:34.390 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.911 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.912 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 80232ecddc9ccc08
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.913 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.913 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.914 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.914 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.914 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:38.914 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.306 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (392ms)
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.307 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.309 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.309 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:41:39 GMT
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.309 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.309 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.310 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.310 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.310 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.310 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.485 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.485 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.663 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [80232ecddc9ccc08,] 2025-07-25 13:41:39.665 DEBUG 23660 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.197 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.199 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9093ed393475143c
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.200 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.200 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.200 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.200 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.201 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.201 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.437 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (235ms)
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.437 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:41:40 GMT
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.438 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.443 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.444 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.568 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.570 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.570 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.682 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.683 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.683 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [9093ed393475143c,] 2025-07-25 13:41:40.776 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.695 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.696 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f2ceda9340f56133
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.696 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.697 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.697 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.697 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.698 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.698 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.886 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (188ms)
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.886 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.886 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.886 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:41:51 GMT
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.886 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.887 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.887 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.888 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.888 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.888 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.892 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:51.893 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:52.008 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [f2ceda9340f56133,] 2025-07-25 13:41:52.009 DEBUG 23660 [http-nio-7002-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e6a94b1eaf655351
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.512 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.513 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.668 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (155ms)
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.668 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.668 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.669 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:41:51 GMT
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.669 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.674 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.674 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.674 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.674 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.674 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.680 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.681 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.795 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.795 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.795 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.920 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.921 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:52.922 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [e6a94b1eaf655351,] 2025-07-25 13:41:53.037 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.050 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.053 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9fa9d2b1e00ab8bc
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.054 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.054 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.056 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.056 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.056 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.056 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.206 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (149ms)
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.206 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:44:18 GMT
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.207 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.208 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.209 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.214 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.214 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 9fa9d2b1e00ab8bc
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.215 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.317 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (101ms)
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.317 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.317 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.317 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:44:18 GMT
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.318 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.318 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.318 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.319 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.319 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9fa9d2b1e00ab8bc,] 2025-07-25 13:44:18.319 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.628 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.633 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 811cf805983c0311
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.634 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.634 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.634 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.634 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.635 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.635 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.734 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (98ms)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.734 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.734 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.734 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:44:22 GMT
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.735 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.735 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.736 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.736 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.736 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.741 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.748 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.749 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 811cf805983c0311
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.751 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.780 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.781 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6b1967f9012c7464
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.786 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.788 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.789 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.789 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.789 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.789 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:44:22 GMT
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.835 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.836 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [811cf805983c0311,] 2025-07-25 13:44:22.836 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.904 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (115ms)
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.904 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.905 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.905 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:44:22 GMT
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.905 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.905 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.905 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.906 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.906 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.906 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.961 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:22.962 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:23.004 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [6b1967f9012c7464,] 2025-07-25 13:44:23.005 DEBUG 23660 [http-nio-7002-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.081 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.081 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 39a9354e4ae3d983
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.081 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.082 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.082 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.082 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.082 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.082 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.166 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.166 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.166 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.167 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:44:22 GMT
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.167 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.167 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.168 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.168 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.169 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.169 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.186 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.188 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.227 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.227 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.228 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.265 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.266 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.266 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [39a9354e4ae3d983,] 2025-07-25 13:44:23.301 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.710 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.711 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: aa21ae3f18797435
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.711 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.713 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.713 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.862 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (149ms)
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.863 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.864 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.864 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:48:02 GMT
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.864 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.864 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.865 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.865 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.865 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.866 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: aa21ae3f18797435
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.870 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.871 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.873 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.957 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (84ms)
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:48:02 GMT
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.958 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.959 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [aa21ae3f18797435,] 2025-07-25 13:48:02.959 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.516 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.518 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: fce2602ac4e6504a
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.518 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.518 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.518 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.519 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.520 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.520 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.627 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.627 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.627 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.627 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:48:07 GMT
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.628 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.628 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.628 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.629 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.629 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.633 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.639 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.639 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: fce2602ac4e6504a
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.639 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.641 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.641 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.642 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.642 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.642 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.725 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (84ms)
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.726 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.726 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.726 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:48:07 GMT
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.726 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.727 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.727 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.727 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.727 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [fce2602ac4e6504a,] 2025-07-25 13:48:07.727 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.855 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.855 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7c56a6cfb6ae7778
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.856 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.856 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.856 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.856 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.857 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.857 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:48:07 GMT
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.937 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.938 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.938 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.938 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.981 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:07.982 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:08.021 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [7c56a6cfb6ae7778,] 2025-07-25 13:48:08.022 DEBUG 23660 [http-nio-7002-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.095 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.096 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b958da8c5a480d28
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.097 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.178 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (81ms)
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.179 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.179 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:48:07 GMT
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.180 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.181 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.189 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.190 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.224 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.225 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.225 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.269 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.269 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.270 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [b958da8c5a480d28,] 2025-07-25 13:48:08.305 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.623 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.626 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 38773863eb84d545
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.627 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.630 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.631 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.631 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.631 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.632 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.780 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (145ms)
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.780 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.780 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.780 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:52:31 GMT
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.781 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.781 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.781 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.781 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.781 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.796 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.802 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.802 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 38773863eb84d545
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.803 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.884 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (80ms)
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.884 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.884 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.884 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:52:31 GMT
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [38773863eb84d545,] 2025-07-25 13:52:31.885 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.816 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.817 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e0a61d3b4d6dbf53
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.817 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.817 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.817 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.817 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.818 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.818 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.921 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.921 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.921 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.921 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:52:33 GMT
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.922 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.922 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.922 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.922 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.923 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.923 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.928 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.928 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e0a61d3b4d6dbf53
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.928 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.929 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.929 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.929 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.929 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:33.929 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.002 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (72ms)
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.002 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:52:33 GMT
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.003 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e0a61d3b4d6dbf53,] 2025-07-25 13:52:34.004 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.072 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.072 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9171f6ef2770422e
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.073 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:52:33 GMT
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.163 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.205 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.205 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.256 DEBUG 23660 [http-nio-7002-exec-9] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [9171f6ef2770422e,] 2025-07-25 13:52:34.258 DEBUG 23660 [http-nio-7002-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.340 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0c2f217a316d8518
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.341 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.342 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.410 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (68ms)
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.411 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.412 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.412 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:52:33 GMT
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.412 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.413 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.413 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.414 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.414 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.415 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.428 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.429 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.467 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.468 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.468 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.507 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.509 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.509 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [0c2f217a316d8518,] 2025-07-25 13:52:34.557 DEBUG 23660 [http-nio-7002-exec-10] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.287 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.291 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 4d4abbe09fc42249
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.292 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.292 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.292 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.292 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.293 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.294 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.440 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (146ms)
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.440 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.440 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.440 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:54:18 GMT
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.441 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.441 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.441 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.441 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.441 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.442 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.447 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.447 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 4d4abbe09fc42249
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.447 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.447 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.447 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.449 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.449 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.449 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.558 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (108ms)
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.558 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.558 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.558 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:54:18 GMT
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [4d4abbe09fc42249,] 2025-07-25 13:54:18.559 DEBUG 23660 [http-nio-7002-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.456 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.458 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1d283fa154269902
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.458 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.458 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.459 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.459 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.459 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.459 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.544 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (85ms)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.544 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.544 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.544 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:54:19 GMT
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.545 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.550 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.551 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 1d283fa154269902
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.551 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.552 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.552 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.552 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.552 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.552 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:54:19 GMT
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.626 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [1d283fa154269902,] 2025-07-25 13:54:19.627 DEBUG 23660 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.608 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.609 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 11a422c75785340b
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.610 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.610 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.610 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.610 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.610 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.611 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (100ms)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:54:21 GMT
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.712 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.713 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.713 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.713 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.718 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.718 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 11a422c75785340b
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.719 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.795 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (75ms)
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.795 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.795 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.795 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:54:21 GMT
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.796 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.797 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.797 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.797 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.797 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [11a422c75785340b,] 2025-07-25 13:54:21.797 DEBUG 23660 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6d9dafd92b045204
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.867 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.868 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.868 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.936 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (68ms)
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.936 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.936 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:54:21 GMT
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.937 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.985 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==>  Preparing: select id, template_name, biz_code, type, status, remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id from t_data_template where is_deleted = 0 and biz_code = ? 
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:21.986 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode ==> Parameters: company(String)
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:22.031 DEBUG 23660 [http-nio-7002-exec-4] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.selectByBizCode <==      Total: 1
[kbc-elms-web:*********:7002] [6d9dafd92b045204,] 2025-07-25 13:54:22.032 DEBUG 23660 [http-nio-7002-exec-4] org.springframework.data.mongodb.core.MongoTemplate find using query: { "templateId" : 3} fields: Document{{}} for class: class com.kbao.kbcelms.dataTemplate.model.DataTemplateField in collection: DataTemplateField
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.120 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.120 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a369618f126c2e41
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.120 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.121 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.121 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.121 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.121 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.122 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (94ms)
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:54:21 GMT
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.217 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.218 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.222 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==>  Preparing: select count(1) from information_schema.tables where table_name = ? 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.222 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable ==> Parameters: t_tp_data_company(String)
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.255 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.isExistTable <==      Total: 1
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.256 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==>  Preparing: SELECT count(0) FROM t_tp_data_company 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.256 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT ==> Parameters: 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.298 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList_COUNT <==      Total: 1
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.298 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==>  Preparing: select * from t_tp_data_company LIMIT ? 
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.298 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList ==> Parameters: 10(Integer)
[kbc-elms-web:*********:7002] [a369618f126c2e41,] 2025-07-25 13:54:22.333 DEBUG 23660 [http-nio-7002-exec-5] com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper.getDataList <==      Total: 4
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.829 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.830 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b6283f732fe1eae8
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.830 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.831 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.831 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.832 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.832 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.832 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.923 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (90ms)
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.923 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.924 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.924 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:55:16 GMT
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.924 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.925 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.925 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.925 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.925 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.925 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.930 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.930 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: b6283f732fe1eae8
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.930 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.931 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.931 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.931 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.931 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:16.931 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (70ms)
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:55:16 GMT
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [b6283f732fe1eae8,] 2025-07-25 13:55:17.002 DEBUG 23660 [http-nio-7002-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.474 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.475 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e060043844237b97
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.476 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.817 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (341ms)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:59:11 GMT
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.818 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.823 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.823 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e060043844237b97
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.824 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.824 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.825 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.825 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.825 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.825 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (143ms)
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:59:11 GMT
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.969 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.970 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.970 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [e060043844237b97,] 2025-07-25 13:59:11.970 DEBUG 23660 [http-nio-7002-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.915 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.917 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: af9be34d21efbd7c
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.918 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.918 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.918 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.918 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.919 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"cd59d64aa130176f68e366dbaafa912b","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:19.919 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.077 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (159ms)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.078 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.078 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.078 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Fri, 25 Jul 2025 05:59:20 GMT
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.078 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.079 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.079 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.079 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.081 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"17656543456"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.081 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.087 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.087 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: af9be34d21efbd7c
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.087 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.088 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.088 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.088 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.088 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.088 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.265 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (176ms)
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.266 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.267 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.270 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Fri, 25 Jul 2025 05:59:20 GMT
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.271 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.271 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.272 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.272 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.274 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7002] [af9be34d21efbd7c,] 2025-07-25 13:59:20.274 DEBUG 23660 [http-nio-7002-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
