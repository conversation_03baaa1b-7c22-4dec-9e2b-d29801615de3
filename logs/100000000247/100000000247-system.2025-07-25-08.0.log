{"@timestamp": "2025-07-25T08:58:31.450+08:00", "traceId": "b720348aad02619c", "remoteIp": "127.0.0.1", "level": "INFO", "xB3TraceId": "", "xB3SpanId": "", "xB3ParentId": "", "thread": "http-nio-7002-exec-10", "class": "c.k.kbcbsc.log.util.OperateLogELKHelper", "msg": "{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753405110927,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"company\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753354888000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":522,\"traceId\":\"b720348aad02619c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}