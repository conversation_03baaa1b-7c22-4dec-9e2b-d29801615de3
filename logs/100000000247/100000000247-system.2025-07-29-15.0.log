{"@timestamp":"2025-07-29T15:27:56.863+08:00","traceId":"1c8ab86b4fee06b8","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753774075478,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1397,\"traceId\":\"1c8ab86b4fee06b8\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-29T15:27:57.594+08:00","traceId":"d57dd05f6ecb4d0b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753774077010,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":583,\"traceId\":\"d57dd05f6ecb4d0b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-29T15:28:46.780+08:00","traceId":"5366a372dff2b21e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753774126625,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":152,\"traceId\":\"5366a372dff2b21e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-29T15:28:55.623+08:00","traceId":"f5d0267d1bcfebc7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753774135541,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":81,\"traceId\":\"f5d0267d1bcfebc7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
