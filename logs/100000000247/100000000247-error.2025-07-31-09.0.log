[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:21.233 ERROR 39656 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:26:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:21.286 ERROR 39656 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:26:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:26:21.342 ERROR 39656 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:26:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:20.491 ERROR 35140 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:45:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:20.542 ERROR 35140 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:45:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:45:20.596 ERROR 35140 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:45:21 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:42.310 ERROR 31476 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:51:42 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:42.386 ERROR 31476 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:51:42 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:51:42.449 ERROR 31476 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:51:43 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:50.452 ERROR 39888 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:52:51 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:50.508 ERROR 39888 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:52:51 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 09:52:50.566 ERROR 39888 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Thu Jul 31 09:52:51 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

