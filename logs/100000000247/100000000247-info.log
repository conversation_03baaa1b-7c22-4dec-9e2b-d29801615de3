[kbc-elms-web:*********:7002] [,] 2025-08-01 11:04:34.709 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:09:34.736 INFO 35316 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:33.669 INFO 35316 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:53.278 INFO 27812 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.261 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.263 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.268 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.269 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.271 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.272 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.272 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.274 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.274 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.275 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.346 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.364 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:12:54.366 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.529 INFO 27812 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:12:56.668 INFO 27812 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.079 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.084 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.298 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 204 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.320 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.324 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:04.547 INFO 27812 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 215 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.064 INFO 27812 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.111 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.113 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.113 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.114 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.115 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.116 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.117 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.118 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.118 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.119 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.120 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:05.121 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.235 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.245 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.245 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.756 INFO 27812 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.779 INFO 27812 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.780 INFO 27812 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.780 INFO 27812 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.949 INFO 27812 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:06.950 INFO 27812 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 10236 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:08.803 INFO 27812 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.231 INFO 27812 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.615 INFO 27812 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.935 INFO 27812 [cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.936 INFO 27812 [cluster-rtt-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:11.942 INFO 27812 [cluster-ClusterId{value='688c30c7645f9f754478cb08', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=118754500}
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.559 INFO 27812 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.624 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:15.990 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.014 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.053 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.075 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.089 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.112 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.128 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.755 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.914 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:16.948 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.137 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.252 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.325 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.419 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.445 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.455 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:17.879 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.317 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.359 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.407 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.432 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.484 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.541 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.629 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.666 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.706 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.742 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.812 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.873 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.926 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:18.989 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.013 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.052 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.080 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.110 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.134 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.162 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.188 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.219 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.254 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.294 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.310 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.379 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.416 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.504 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.606 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.619 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.651 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.679 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.704 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.729 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.771 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.788 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.802 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.817 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.828 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.841 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.851 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.865 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.879 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.893 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.904 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.922 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.936 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:19.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:20.763 INFO 27812 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:21.020 INFO 27812 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:22.270 INFO 27812 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:26.213 INFO 27812 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.159 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.178 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.198 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.217 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.238 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.257 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.276 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.297 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.320 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.343 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.362 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.382 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.404 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.427 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.453 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.462 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.489 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.512 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.530 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.556 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.579 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.603 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.630 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.657 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.694 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.714 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.754 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.760 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.765 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.777 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.785 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.893 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.902 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.909 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.924 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.939 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.945 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.952 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.959 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.966 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.972 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.981 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.985 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.992 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:28.998 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.002 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.011 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.016 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.027 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.031 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.036 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.040 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.044 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.048 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.055 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.062 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.066 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.070 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.074 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.081 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.092 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.107 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.114 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.123 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.132 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.138 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.145 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.153 INFO 27812 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.515 INFO 27812 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.658 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.674 INFO 27812 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:29.756 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.849 INFO 27812 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.857 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.868 INFO 27812 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754018010866 with initial instances count: 11
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.882 INFO 27812 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.885 INFO 27812 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.972 INFO 27812 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.974 INFO 27812 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.975 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.976 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.977 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.978 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:30.986 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.054 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.055 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.056 INFO 27812 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.128 INFO 27812 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.421 INFO 27812 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.867 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.964 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.977 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.980 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:31.989 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.103 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.114 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.115 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.198 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.204 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.206 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.210 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.214 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.246 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.266 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.277 INFO 27812 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.401 INFO 27812 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 40.693 seconds (JVM running for 42.637)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.695 INFO 27812 [RMI TCP Connection(12)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.695 INFO 27812 [RMI TCP Connection(12)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:32.715 INFO 27812 [RMI TCP Connection(12)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 18 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:13:33.370 INFO 27812 [RMI TCP Connection(11)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:40.544 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0b184a2c68028450,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [0b184a2c68028450,] 2025-08-01 11:14:41.144 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0b184a2c68028450, 耗时=1009, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.366 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7b82f0d213f35930,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [7b82f0d213f35930,] 2025-08-01 11:15:05.445 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7b82f0d213f35930, 耗时=79, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.236 INFO 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=26b65c7efed4cdfa,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [26b65c7efed4cdfa,] 2025-08-01 11:15:43.313 INFO 27812 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=26b65c7efed4cdfa, 耗时=77, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:18:29.762 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.889 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=fb461fd53e54b7c6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [fb461fd53e54b7c6,] 2025-08-01 11:19:00.970 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=fb461fd53e54b7c6, 耗时=82, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:23:29.783 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:28:29.791 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:37.902 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=af9ae3851acd2b21,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [af9ae3851acd2b21,] 2025-08-01 11:29:38.217 INFO 27812 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=af9ae3851acd2b21, 耗时=315, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.913 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d7a1844987c8ac75,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [d7a1844987c8ac75,] 2025-08-01 11:31:12.993 INFO 27812 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d7a1844987c8ac75, 耗时=80, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.778 INFO 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=1a0d9e997be735fc,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [1a0d9e997be735fc,] 2025-08-01 11:31:50.856 INFO 27812 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=1a0d9e997be735fc, 耗时=78, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.459 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a0eaca21d6795def,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [a0eaca21d6795def,] 2025-08-01 11:32:25.537 INFO 27812 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a0eaca21d6795def, 耗时=78, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.084 INFO 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b8017f712a0b2fd6,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [b8017f712a0b2fd6,] 2025-08-01 11:33:15.171 INFO 27812 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b8017f712a0b2fd6, 耗时=87, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:33:29.803 INFO 27812 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.008 INFO 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e3bbfaeeb8a9e5c5,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7002] [e3bbfaeeb8a9e5c5,] 2025-08-01 11:34:05.083 INFO 27812 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e3bbfaeeb8a9e5c5, 耗时=75, resp={"datas":{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754013890600,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:12.918 INFO 27812 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.128 INFO 27812 [SpringContextShutdownHook] org.mongodb.driver.connection Closed connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017 because the pool has been closed.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.145 INFO 27812 [SpringContextShutdownHook] com.alibaba.druid.pool.DruidDataSource {dataSource-1} closed
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.291 INFO 27812 [SpringContextShutdownHook] com.netflix.discovery.DiscoveryClient Shutting down DiscoveryClient ...
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:38:13.295 INFO 27812 [SpringContextShutdownHook] com.netflix.discovery.DiscoveryClient Completed shut down of DiscoveryClient
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:56.763 INFO 8636 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.887 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.889 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.897 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.898 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.900 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.901 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.902 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.903 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:57.997 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:58.016 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-01 11:47:58.018 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.129 INFO 8636 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:47:59.215 INFO 8636 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.822 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.825 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.944 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 110 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.979 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:07.982 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.095 INFO 8636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 103 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.756 INFO 8636 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=5accf91f-44f7-39ec-988e-430ec3e732e4
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.812 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.813 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.813 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.814 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.815 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.816 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.817 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.818 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:08.818 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.251 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.258 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.258 INFO 8636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.852 INFO 8636 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.883 INFO 8636 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.884 INFO 8636 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:10.885 INFO 8636 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:11.058 INFO 8636 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:11.060 INFO 8636 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 11766 ms
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:12.919 INFO 8636 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.651 INFO 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.907 INFO 8636 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:13.941 INFO 8636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-2} inited
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.150 INFO 8636 [main] org.apache.catalina.core.StandardService Stopping service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-08-01 11:48:14.186 INFO 8636 [main] org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
