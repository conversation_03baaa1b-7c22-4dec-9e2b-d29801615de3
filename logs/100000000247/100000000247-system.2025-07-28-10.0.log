{"@timestamp":"2025-07-28T10:13:13.603+08:00","traceId":"8876e9909b8b329a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753668792276,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":0,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[],\\\"navigateFirstPage\\\":0,\\\"navigateLastPage\\\":0,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":0,\\\"prePage\\\":0,\\\"size\\\":0,\\\"startRow\\\":0,\\\"total\\\":0},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1340,\"traceId\":\"8876e9909b8b329a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:13:54.471+08:00","traceId":"4db38d2610adc380","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"创建\",\"appName\":\"kbc-elms-web\",\"createTime\":1753668834144,\"desc\":\"创建企业类型\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/add\",\"module\":\"企业类型管理\",\"params\":\"{\\\"code\\\":\\\"A\\\",\\\"description\\\":\\\"\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}]}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":327,\"traceId\":\"4db38d2610adc380\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:13:54.767+08:00","traceId":"1a086f229831c9a3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753668834637,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人;>=50000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":130,\"traceId\":\"1a086f229831c9a3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:15:41.037+08:00","traceId":"58014987d54efff7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753668940736,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人;>=50000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":299,\"traceId\":\"58014987d54efff7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:17:56.897+08:00","traceId":"3f7ec11963e1ee34","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669076820,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人;>=50000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":77,\"traceId\":\"3f7ec11963e1ee34\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:18:24.647+08:00","traceId":"b14f18d91ae5bd10","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669104604,\"desc\":\"查询企业类型详情\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/detail\",\"module\":\"企业类型管理\",\"params\":\"{\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\"}\",\"response\":\"{\\\"datas\\\":{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":40,\"traceId\":\"b14f18d91ae5bd10\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:18:28.856+08:00","traceId":"59a38227480c13e7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669108761,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人;>=50000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":95,\"traceId\":\"59a38227480c13e7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:18:33.979+08:00","traceId":"15238871eeeaea0a","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669113937,\"desc\":\"查询企业类型详情\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/detail\",\"module\":\"企业类型管理\",\"params\":\"{\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\"}\",\"response\":\"{\\\"datas\\\":{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834202,\\\"description\\\":\\\"\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834202},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":39,\"traceId\":\"15238871eeeaea0a\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:18:54.016+08:00","traceId":"c30deb6dd1f6fbba","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"更新\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669133912,\"desc\":\"更新企业类型\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/update\",\"module\":\"企业类型管理\",\"params\":\"{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}\",\"response\":\"{\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":103,\"traceId\":\"c30deb6dd1f6fbba\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:18:54.211+08:00","traceId":"ad8ec658753d6ce3","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669134138,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":72,\"traceId\":\"ad8ec658753d6ce3\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:15.139+08:00","traceId":"c2fd7b99d662da2c","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669154173,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":965,\"traceId\":\"c2fd7b99d662da2c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:16.346+08:00","traceId":"1be11b806c89bbef","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669155424,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":921,\"traceId\":\"1be11b806c89bbef\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:24.142+08:00","traceId":"d35799e1da9ef7c1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669163952,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":187,\"traceId\":\"d35799e1da9ef7c1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:27.213+08:00","traceId":"578023c7fad1ee34","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669167135,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":78,\"traceId\":\"578023c7fad1ee34\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:27.442+08:00","traceId":"5ab2a47dcd817cad","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669167334,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":106,\"traceId\":\"5ab2a47dcd817cad\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:34.508+08:00","traceId":"1b5337232faf9dad","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669174436,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":1,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=100000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":100000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":1,\\\"startRow\\\":1,\\\"total\\\":1},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":71,\"traceId\":\"1b5337232faf9dad\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:35.829+08:00","traceId":"9b9f8846bfad71eb","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669175759,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":69,\"traceId\":\"9b9f8846bfad71eb\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:39.944+08:00","traceId":"b92216a7ed044a12","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669179867,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":1}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"fields\\\":[{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"s\\\",\\\"fieldCode\\\":\\\"fff\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"xx1\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548005\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":1,\\\"validation\\\":\\\"3\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"d\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"\\\",\\\"fieldCode\\\":\\\"ss\\\",\\\"fieldLength\\\":\\\"2\\\",\\\"fieldName\\\":\\\"xx2\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881e29c71fde748d6548006\\\",\\\"isIndex\\\":\\\"0\\\",\\\"remark\\\":\\\"\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":1,\\\"validation\\\":\\\"ee\\\"},{\\\"additional\\\":{\\\"input_unit\\\":\\\"sd\\\"},\\\"change\\\":\\\"1\\\",\\\"defaultValue\\\":\\\"d\\\",\\\"fieldCode\\\":\\\"pdd\\\",\\\"fieldLength\\\":\\\"12\\\",\\\"fieldName\\\":\\\"xx\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"6881cbb98d33bf5f79adb024\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":1,\\\"validation\\\":\\\"xd\\\"}],\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":76,\"traceId\":\"b92216a7ed044a12\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:42.468+08:00","traceId":"fbde11e55e7ff2d7","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669182392,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":76,\"traceId\":\"fbde11e55e7ff2d7\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:43.695+08:00","traceId":"8a0e6ec4f6289d77","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669183617,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":77,\"traceId\":\"8a0e6ec4f6289d77\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:19:52.870+08:00","traceId":"a2f232a4743f0fda","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669192780,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":86,\"traceId\":\"a2f232a4743f0fda\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:22:43.818+08:00","traceId":"3892883770123b90","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669363693,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":123,\"traceId\":\"3892883770123b90\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:22:46.226+08:00","traceId":"e69170ea0d590d80","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669366151,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":73,\"traceId\":\"e69170ea0d590d80\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:22:48.417+08:00","traceId":"790942f9280a7d7c","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669368334,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":82,\"traceId\":\"790942f9280a7d7c\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:22:48.655+08:00","traceId":"db280ef310120840","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669368539,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":115,\"traceId\":\"db280ef310120840\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:23:00.700+08:00","traceId":"36bf656fc9f76b32","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669380620,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":80,\"traceId\":\"36bf656fc9f76b32\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:23:05.796+08:00","traceId":"b8a7085fcb57d129","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查看明细\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669385719,\"desc\":\"查看明细\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail\",\"module\":\"管理\",\"params\":\"{\\\"id\\\":3}\",\"response\":\"{\\\"datas\\\":{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"fields\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":74,\"traceId\":\"b8a7085fcb57d129\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:24:05.178+08:00","traceId":"7d21fd20082ffa76","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669445100,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":77,\"traceId\":\"7d21fd20082ffa76\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:29:08.464+08:00","traceId":"5f584d8564538d0e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669748012,\"desc\":\"分页查询列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"status\\\":\\\"\\\",\\\"templateName\\\":\\\"\\\",\\\"type\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"bizCode\\\":\\\"test\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753336762000,\\\"id\\\":1,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"xx\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"test\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"xx\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753342622000},{\\\"bizCode\\\":\\\"enterprise\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753354888000,\\\"id\\\":3,\\\"isDeleted\\\":0,\\\"remark\\\":\\\"企业表测试\\\",\\\"status\\\":\\\"1\\\",\\\"templateName\\\":\\\"企业信息表\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":\\\"企业信息\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753436363000}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":451,\"traceId\":\"5f584d8564538d0e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:29:16.027+08:00","traceId":"a39e56be0744191b","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669755921,\"desc\":\"查询模板字段列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields\",\"module\":\"管理\",\"params\":\"{\\\"templateCode\\\":\\\"enterprise\\\"}\",\"response\":\"{\\\"datas\\\":[{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"企业名称\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2d\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":1,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseCode\\\",\\\"fieldLength\\\":\\\"50\\\",\\\"fieldName\\\":\\\"社会信用代码\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2e\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":2,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"type\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"企业类型\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f2f\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":3,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"人1\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"staffSize\\\",\\\"fieldName\\\":\\\"人员规模\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f30\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":4,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"selectOptions\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\",\\\"5\\\"]},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"city\\\",\\\"fieldLength\\\":\\\"30\\\",\\\"fieldName\\\":\\\"所在城市\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f31\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"3\\\",\\\"sort\\\":5,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"industryId\\\",\\\"fieldName\\\":\\\"所属行业ID\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f32\\\",\\\"isIndex\\\":\\\"1\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":6,\\\"templateId\\\":3},{\\\"additional\\\":{\\\"inputUnit\\\":\\\"万\\\"},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"annualIncome\\\",\\\"fieldName\\\":\\\"年收入\\\",\\\"fieldType\\\":\\\"int\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f33\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":7,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"0\\\",\\\"fieldCode\\\":\\\"isVerified\\\",\\\"fieldLength\\\":\\\"1\\\",\\\"fieldName\\\":\\\"是否验真\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f34\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"0\\\",\\\"sort\\\":8,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"enterpriseContacter\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"企业联系人\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f35\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":9,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"contacterPhone\\\",\\\"fieldLength\\\":\\\"20\\\",\\\"fieldName\\\":\\\"联系人电话\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f36\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"1\\\",\\\"sort\\\":10,\\\"templateId\\\":3},{\\\"additional\\\":{},\\\"change\\\":\\\"1\\\",\\\"fieldCode\\\":\\\"remark\\\",\\\"fieldLength\\\":\\\"300\\\",\\\"fieldName\\\":\\\"备注信息\\\",\\\"fieldType\\\":\\\"varchar\\\",\\\"id\\\":\\\"688350cb7854ad28146e9f37\\\",\\\"isIndex\\\":\\\"0\\\",\\\"required\\\":\\\"0\\\",\\\"showType\\\":\\\"6\\\",\\\"sort\\\":11,\\\"templateId\\\":3}],\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":106,\"traceId\":\"a39e56be0744191b\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-07-28T10:29:16.288+08:00","traceId":"4df97b398969499e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7002-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1753669756159,\"desc\":\"分页查询模板数据\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData\",\"module\":\"管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"templateCode\\\":\\\"enterprise\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":4,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"enterpriseCode\\\":\\\"X1111\\\",\\\"annualIncome\\\":10000000,\\\"city\\\":\\\"北京\\\",\\\"isVerified\\\":\\\"1\\\",\\\"enterpriseContacter\\\":\\\"张三\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"A\\\",\\\"industryId\\\":1,\\\"staffSize\\\":10000,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13255554444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"北京科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":1},{\\\"enterpriseCode\\\":\\\"X222\\\",\\\"annualIncome\\\":2222222,\\\"city\\\":\\\"上海\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"李四\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"B\\\",\\\"industryId\\\":2,\\\"staffSize\\\":2333,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"13944445555\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"上海科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":2},{\\\"enterpriseCode\\\":\\\"X2223\\\",\\\"annualIncome\\\":33333,\\\"city\\\":\\\"湖北\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"王五\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"C\\\",\\\"industryId\\\":3,\\\"staffSize\\\":45,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"14257884444\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"湖北科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":3},{\\\"enterpriseCode\\\":\\\"X4433\\\",\\\"annualIncome\\\":444444,\\\"city\\\":\\\"天津\\\",\\\"isVerified\\\":\\\"0\\\",\\\"enterpriseContacter\\\":\\\"赵六\\\",\\\"updateTime\\\":1753425602000,\\\"type\\\":\\\"D\\\",\\\"industryId\\\":4,\\\"staffSize\\\":6777,\\\"isDeleted\\\":0,\\\"contacterPhone\\\":\\\"17845454545\\\",\\\"createTime\\\":1753425602000,\\\"name\\\":\\\"天津科技有限公司\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"id\\\":4}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":4,\\\"startRow\\\":1,\\\"total\\\":4},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":129,\"traceId\":\"4df97b398969499e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
