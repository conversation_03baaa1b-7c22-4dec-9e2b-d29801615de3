[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:50.246 DEBUG 23660 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:51.251 DEBUG 23660 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:51.310 DEBUG 23660 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.582 DEBUG 23660 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.680 DEBUG 23660 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.783 DEBUG 23660 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.841 DEBUG 23660 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.864 DEBUG 23660 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.908 DEBUG 23660 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.970 DEBUG 23660 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:52.986 DEBUG 23660 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.071 DEBUG 23660 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.172 DEBUG 23660 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.190 DEBUG 23660 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.448 DEBUG 23660 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.467 DEBUG 23660 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-25 12:19:53.484 DEBUG 23660 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
