[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:54.390 INFO 17700 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.369 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.371 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.379 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.379 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.381 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.382 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.383 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.383 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.384 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.385 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.516 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.524 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:12:55.526 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:12:55.801 INFO 17700 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.508 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.512 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.605 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 84 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.627 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.631 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:03.730 INFO 17700 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 89 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.188 INFO 17700 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.228 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.228 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.228 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.228 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.229 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.229 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.229 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.229 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.230 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.230 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.230 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.231 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:04.231 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.227 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.236 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.237 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.727 INFO 17700 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.751 INFO 17700 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.752 INFO 17700 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.752 INFO 17700 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.905 INFO 17700 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:05.906 INFO 17700 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 10037 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:07.622 INFO 17700 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:09.997 INFO 17700 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:10.417 INFO 17700 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:10.883 INFO 17700 [cluster-ClusterId{value='68833c963671bb4f990f7b21', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:10.883 INFO 17700 [cluster-rtt-ClusterId{value='68833c963671bb4f990f7b21', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:10.886 INFO 17700 [cluster-ClusterId{value='68833c963671bb4f990f7b21', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=273632400}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:13.655 INFO 17700 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:13.733 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.317 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.341 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.381 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.404 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.421 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.472 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.489 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.524 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.705 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.743 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:14.862 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.240 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.395 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.479 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:15.592 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.041 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.089 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.142 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.171 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.224 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.285 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.343 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.380 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.416 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.453 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.509 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.573 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.624 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.655 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.686 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.715 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.753 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.782 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.808 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.836 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.862 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.888 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.921 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.956 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:16.992 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.008 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.072 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.109 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.199 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.343 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.355 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.387 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.418 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.443 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.466 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.492 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.506 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.518 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.532 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.545 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.559 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.570 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.583 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.594 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.609 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.626 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.649 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.660 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.678 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.693 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:17.715 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:18.555 INFO 17700 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:18.804 INFO 17700 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:19.996 INFO 17700 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:21.808 INFO 17700 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.703 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.728 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.752 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.774 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.794 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.814 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.835 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.857 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.881 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.908 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.935 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.964 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:23.995 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.016 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.036 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.043 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.060 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.082 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.100 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.128 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.150 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.171 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.198 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.227 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.248 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.268 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.304 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.310 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.314 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.327 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.333 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.343 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.350 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.357 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.366 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.379 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.385 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.393 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.455 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.464 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.470 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.480 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.486 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.496 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.500 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.506 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.518 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.523 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.534 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.540 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.550 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.556 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.564 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.572 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.582 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.593 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.599 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.606 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.615 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.629 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.645 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.660 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.666 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.670 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.677 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.682 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.687 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:24.694 INFO 17700 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.047 INFO 17700 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.206 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.223 INFO 17700 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.334 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.334 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.335 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.335 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.335 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.335 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:25.335 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.839 INFO 17700 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.846 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.859 INFO 17700 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753431206856 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.870 INFO 17700 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.873 INFO 17700 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.957 INFO 17700 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.962 INFO 17700 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.963 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.963 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.963 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.964 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.964 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.964 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.964 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.964 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.965 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.967 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:26.968 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.038 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.039 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.047 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.047 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.047 INFO 17700 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.048 INFO 17700 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.149 INFO 17700 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.464 INFO 17700 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:27.957 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.043 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getTenantUsersUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.044 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getWebUserInfoUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.090 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.096 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.102 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.107 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.133 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.149 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.158 INFO 17700 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.278 INFO 17700 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 35.535 seconds (JVM running for 37.845)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.582 INFO 17700 [RMI TCP Connection(7)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.582 INFO 17700 [RMI TCP Connection(7)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:28.609 INFO 17700 [RMI TCP Connection(7)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 26 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:13:29.193 INFO 17700 [RMI TCP Connection(6)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:56.512 INFO 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=731e0af419972184,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [731e0af419972184,] 2025-07-25 16:15:57.446 INFO 17700 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=731e0af419972184, 耗时=1213, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.064 INFO 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=20caf7f97035d099,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [20caf7f97035d099,] 2025-07-25 16:16:01.608 INFO 17700 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=20caf7f97035d099, 耗时=544, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:24.854 INFO 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6ec9121d8f963015,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [6ec9121d8f963015,] 2025-07-25 16:16:28.096 INFO 17700 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6ec9121d8f963015, 耗时=3242, resp=null:
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:08.042 INFO 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5c8103e7ac09830a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:18:37.249 INFO 17700 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [5c8103e7ac09830a,] 2025-07-25 16:18:37.282 INFO 17700 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5c8103e7ac09830a, 耗时=29240, resp=null:
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:49.884 INFO 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5727d75774eca14d,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [5727d75774eca14d,] 2025-07-25 16:18:55.717 INFO 17700 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5727d75774eca14d, 耗时=5833, resp=null:
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.663 INFO 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ece13e33d9ccd940,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [ece13e33d9ccd940,] 2025-07-25 16:19:02.731 INFO 17700 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ece13e33d9ccd940, 耗时=68, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:20:54.215 INFO 17700 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:05.758 INFO 23100 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.643 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.645 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.651 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.652 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.654 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.656 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.656 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.657 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.658 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.658 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.761 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.768 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:21:06.770 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:06.999 INFO 23100 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:12.778 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:12.782 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:12.885 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 95 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:12.903 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:12.906 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.006 INFO 23100 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 91 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.430 INFO 23100 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.468 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.468 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.469 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.469 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.470 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.470 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.470 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.471 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.471 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.472 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.472 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.472 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:13.472 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.456 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.466 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.466 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.953 INFO 23100 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.972 INFO 23100 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.973 INFO 23100 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:14.974 INFO 23100 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:15.125 INFO 23100 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:15.125 INFO 23100 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 8089 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:16.597 INFO 23100 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:18.942 INFO 23100 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:19.282 INFO 23100 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:19.696 INFO 23100 [cluster-rtt-ClusterId{value='68833e7f4b8ec71fda471fdf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:19.697 INFO 23100 [cluster-ClusterId{value='68833e7f4b8ec71fda471fdf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:19.698 INFO 23100 [cluster-ClusterId{value='68833e7f4b8ec71fda471fdf', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=256260800}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.191 INFO 23100 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.248 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.782 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.808 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.849 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.870 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.885 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.928 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.945 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:22.977 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.152 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.185 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.280 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.626 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.755 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.821 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:23.907 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.308 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.352 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.393 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.416 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.463 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.518 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.576 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.609 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.643 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.675 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.724 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.791 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.839 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.868 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.894 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.917 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.945 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:24.974 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.003 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.026 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.051 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.076 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.102 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.127 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.154 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.173 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.243 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.276 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.338 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.469 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.480 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.509 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.535 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.555 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.574 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.598 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.610 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.619 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.631 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.641 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.656 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.664 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.678 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.689 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.705 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.722 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.740 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.753 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.771 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.783 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:25.796 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:26.461 INFO 23100 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:26.694 INFO 23100 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:27.693 INFO 23100 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:29.259 INFO 23100 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.815 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.833 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.849 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.865 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.882 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.901 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.918 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.935 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.954 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.973 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:30.991 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.007 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.025 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.046 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.064 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.068 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.090 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.111 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.132 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.155 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.171 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.189 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.212 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.234 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.257 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.278 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.311 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.320 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.326 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.337 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.344 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.351 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.358 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.361 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.367 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.374 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.379 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.382 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.387 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.392 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.397 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.404 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.409 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.415 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.418 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.422 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.431 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.434 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.444 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.447 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.452 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.457 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.461 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.466 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.472 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.478 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.481 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.486 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.491 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.498 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.509 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.522 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.526 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.530 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.535 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.540 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.545 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.550 INFO 23100 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.843 INFO 23100 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.952 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:31.967 INFO 23100 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.033 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:32.034 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.194 INFO 23100 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.200 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.208 INFO 23100 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753431693206 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.218 INFO 23100 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.220 INFO 23100 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.283 INFO 23100 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.288 INFO 23100 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.288 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.289 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.290 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.290 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.290 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.292 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.293 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.355 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.356 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.357 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.357 INFO 23100 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.357 INFO 23100 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.411 INFO 23100 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:33.632 INFO 23100 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.065 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.147 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getTenantUsersUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.148 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getWebUserInfoUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.192 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.205 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.210 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.215 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.236 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.251 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.259 INFO 23100 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.372 INFO 23100 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 30.146 seconds (JVM running for 31.881)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.579 INFO 23100 [RMI TCP Connection(3)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.579 INFO 23100 [RMI TCP Connection(3)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:34.585 INFO 23100 [RMI TCP Connection(3)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 5 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:21:35.165 INFO 23100 [RMI TCP Connection(5)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:47.583 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d8df3f2b99838ba9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [d8df3f2b99838ba9,] 2025-07-25 16:21:50.717 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d8df3f2b99838ba9, 耗时=3384, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:26:32.044 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.917 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8a75fdd1d49b8512,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [8a75fdd1d49b8512,] 2025-07-25 16:29:57.991 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8a75fdd1d49b8512, 耗时=74, resp=null:
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:57.985 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b37535f345e63f67,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [b37535f345e63f67,] 2025-07-25 16:30:58.780 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b37535f345e63f67, 耗时=794, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.243 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e851df77ca71b205,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [e851df77ca71b205,] 2025-07-25 16:31:21.909 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e851df77ca71b205, 耗时=668, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.700 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=de63b0873063ca57,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateFields , httpMethod=null, reqData={"param1":{"templateCode":"enterprise"}} 
[kbc-elms-web:*********:7002] [de63b0873063ca57,] 2025-07-25 16:31:24.785 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=de63b0873063ca57, 耗时=87, resp={"datas":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:24.912 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=969eed6105bd9713,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/getTemplateData , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"templateCode":"enterprise"}}} 
[kbc-elms-web:*********:7002] [969eed6105bd9713,] 2025-07-25 16:31:25.058 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=969eed6105bd9713, 耗时=148, resp={"datas":{"endRow":4,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"enterpriseCode":"X1111","annualIncome":10000000,"city":"北京","isVerified":"1","enterpriseContacter":"张三","updateTime":1753425602000,"type":"A","industryId":1,"staffSize":10000,"isDeleted":0,"contacterPhone":"13255554444","createTime":1753425602000,"name":"北京科技有限公司","tenantId":"T0001","id":1},{"enterpriseCode":"X222","annualIncome":2222222,"city":"上海","isVerified":"0","enterpriseContacter":"李四","updateTime":1753425602000,"type":"B","industryId":2,"staffSize":2333,"isDeleted":0,"contacterPhone":"13944445555","createTime":1753425602000,"name":"上海科技有限公司","tenantId":"T0001","id":2},{"enterpriseCode":"X2223","annualIncome":33333,"city":"湖北","isVerified":"0","enterpriseContacter":"王五","updateTime":1753425602000,"type":"C","industryId":3,"staffSize":45,"isDeleted":0,"contacterPhone":"14257884444","createTime":1753425602000,"name":"湖北科技有限公司","tenantId":"T0001","id":3},{"enterpriseCode":"X4433","annualIncome":444444,"city":"天津","isVerified":"0","enterpriseContacter":"赵六","updateTime":1753425602000,"type":"D","industryId":4,"staffSize":6777,"isDeleted":0,"contacterPhone":"17845454545","createTime":1753425602000,"name":"天津科技有限公司","tenantId":"T0001","id":4}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":4,"startRow":1,"total":4},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.883 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=24d6bc68faef1562,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [24d6bc68faef1562,] 2025-07-25 16:31:27.957 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=24d6bc68faef1562, 耗时=75, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.252 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c4bd3bfa37ec9de8,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [c4bd3bfa37ec9de8,] 2025-07-25 16:31:31.328 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c4bd3bfa37ec9de8, 耗时=77, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:31:32.050 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.717 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=90362e0c82003035,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [90362e0c82003035,] 2025-07-25 16:31:33.785 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=90362e0c82003035, 耗时=68, resp=null:
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.380 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b4dfebf869ad3f30,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [b4dfebf869ad3f30,] 2025-07-25 16:32:27.457 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b4dfebf869ad3f30, 耗时=77, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.548 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=3a4596c35d5611e4,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [3a4596c35d5611e4,] 2025-07-25 16:33:18.644 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=3a4596c35d5611e4, 耗时=96, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.026 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=dcebcbab22ba68a5,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [dcebcbab22ba68a5,] 2025-07-25 16:33:58.099 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=dcebcbab22ba68a5, 耗时=73, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:17.919 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=56bbb43b161bc8d8,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [56bbb43b161bc8d8,] 2025-07-25 16:34:18.012 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=56bbb43b161bc8d8, 耗时=93, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.515 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=50f48720aa775b7e,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [50f48720aa775b7e,] 2025-07-25 16:34:34.593 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=50f48720aa775b7e, 耗时=78, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.781 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=beaa876f1e6b22e0,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [beaa876f1e6b22e0,] 2025-07-25 16:34:47.862 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=beaa876f1e6b22e0, 耗时=82, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.752 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=a50a39ba727a784a,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [a50a39ba727a784a,] 2025-07-25 16:35:01.827 INFO 23100 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=a50a39ba727a784a, 耗时=76, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.142 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e6684b0a1f7bf9da,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [e6684b0a1f7bf9da,] 2025-07-25 16:35:16.223 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e6684b0a1f7bf9da, 耗时=80, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.484 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=bbffc88b400e37c9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [bbffc88b400e37c9,] 2025-07-25 16:35:22.554 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=bbffc88b400e37c9, 耗时=70, resp=null:
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.285 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=daac08cda3ac782f,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [daac08cda3ac782f,] 2025-07-25 16:35:36.389 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=daac08cda3ac782f, 耗时=104, resp=null:
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.386 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e49406bfdb990782,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [e49406bfdb990782,] 2025-07-25 16:36:20.505 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e49406bfdb990782, 耗时=119, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.683 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6d30f6e9ca932bdc,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [6d30f6e9ca932bdc,] 2025-07-25 16:36:22.791 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6d30f6e9ca932bdc, 耗时=108, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:36:32.059 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.773 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9fdac824a07e00a7,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [9fdac824a07e00a7,] 2025-07-25 16:40:46.907 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9fdac824a07e00a7, 耗时=134, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.257 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=076423e27a87d4e5,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [076423e27a87d4e5,] 2025-07-25 16:41:15.334 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=076423e27a87d4e5, 耗时=77, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:41:32.275 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:35.980 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0123b85ce17209cf,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [0123b85ce17209cf,] 2025-07-25 16:41:36.056 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0123b85ce17209cf, 耗时=75, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:51.883 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6a9ff11e86a1ccef,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [6a9ff11e86a1ccef,] 2025-07-25 16:42:52.019 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6a9ff11e86a1ccef, 耗时=136, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.113 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4ae5c100378ad9e9,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"status":"","templateName":"","type":""}}} 
[kbc-elms-web:*********:7002] [4ae5c100378ad9e9,] 2025-07-25 16:43:51.190 INFO 23100 [http-nio-7002-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4ae5c100378ad9e9, 耗时=76, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"bizCode":"test","createId":"U000162","createTime":1753336762000,"id":1,"isDeleted":0,"remark":"xx","status":"1","templateName":"test","tenantId":"T0001","type":"xx","updateId":"U000162","updateTime":1753342622000},{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.172 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e168f73331e97629,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [e168f73331e97629,] 2025-07-25 16:43:53.252 INFO 23100 [http-nio-7002-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e168f73331e97629, 耗时=80, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:54.555 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b49ffc2dbf0f8f2c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [b49ffc2dbf0f8f2c,] 2025-07-25 16:44:55.025 INFO 23100 [http-nio-7002-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b49ffc2dbf0f8f2c, 耗时=470, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.733 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=2a461c97b35a5449,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [2a461c97b35a5449,] 2025-07-25 16:44:57.797 INFO 23100 [http-nio-7002-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=2a461c97b35a5449, 耗时=64, resp=null:
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.168 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=671167412939f3da,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [671167412939f3da,] 2025-07-25 16:45:01.235 INFO 23100 [http-nio-7002-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=671167412939f3da, 耗时=67, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:46:32.289 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.220 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=74efacc11920ce17,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [74efacc11920ce17,] 2025-07-25 16:47:29.514 INFO 23100 [http-nio-7002-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=74efacc11920ce17, 耗时=295, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:32.925 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5f87b40d52d5ec81,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [5f87b40d52d5ec81,] 2025-07-25 16:47:33.061 INFO 23100 [http-nio-7002-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5f87b40d52d5ec81, 耗时=136, resp=null:
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.496 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=db131671f300a7af,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [db131671f300a7af,] 2025-07-25 16:51:10.729 INFO 23100 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=db131671f300a7af, 耗时=233, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.561 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=b4375f391399f3eb,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [b4375f391399f3eb,] 2025-07-25 16:51:15.644 INFO 23100 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=b4375f391399f3eb, 耗时=83, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:51:32.292 INFO 23100 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:51:47.238 INFO 23100 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:00.279 INFO 15372 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.347 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.348 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.354 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.355 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.356 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.358 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.358 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.359 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.359 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.360 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.482 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.489 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-07-25 16:52:01.490 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:01.727 INFO 15372 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.650 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.654 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.742 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 79 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.760 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.763 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:07.844 INFO 15372 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 73 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.279 INFO 15372 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=29b0e0de-309c-3324-92cd-840960532cb9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.317 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.317 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.317 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.317 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.318 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.318 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.318 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.318 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.319 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.319 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.319 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.320 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:08.320 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.303 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.312 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.313 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.798 INFO 15372 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7002 (http)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.819 INFO 15372 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.820 INFO 15372 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.820 INFO 15372 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.996 INFO 15372 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:09.996 INFO 15372 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 8231 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:11.714 INFO 15372 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:14.107 INFO 15372 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:14.456 INFO 15372 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:14.909 INFO 15372 [cluster-ClusterId{value='688345be2f45477d7a5a7035', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:14.912 INFO 15372 [cluster-rtt-ClusterId{value='688345be2f45477d7a5a7035', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:14.914 INFO 15372 [cluster-ClusterId{value='688345be2f45477d7a5a7035', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=290076100}
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:17.717 INFO 15372 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:17.785 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.340 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.367 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.411 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.434 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.449 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.497 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.515 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.551 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.707 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.755 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:18.851 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.177 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.316 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.391 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.479 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.890 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.931 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:19.976 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.003 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.048 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.098 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.152 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.186 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.222 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.252 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.300 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.363 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.414 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.442 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.468 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.492 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.521 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.543 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.567 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.595 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.627 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.651 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.678 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.702 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.729 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.745 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.805 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.844 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:20.929 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.072 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.082 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.114 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.145 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.167 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.188 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.216 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.229 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.239 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.251 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.262 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.274 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.283 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.295 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.305 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.319 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.333 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.348 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.358 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.373 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.385 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:21.399 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:22.077 INFO 15372 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:22.331 INFO 15372 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:23.326 INFO 15372 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:24.981 INFO 15372 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.734 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.754 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.773 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.790 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.808 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.826 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.842 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.860 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.879 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.901 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.921 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.940 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.959 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.976 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.993 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:26.998 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.017 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.034 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.054 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.075 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.092 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.109 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.127 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.147 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.164 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.184 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.211 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.216 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.219 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.230 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.236 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.242 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.248 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.252 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.258 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.266 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.270 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.274 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.279 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.285 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.289 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.296 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.301 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.306 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.309 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.314 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.322 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.325 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.335 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.338 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.341 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.345 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.348 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.352 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.357 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.363 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.366 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.369 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.372 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.380 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.389 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.400 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.403 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.407 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.412 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.415 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.419 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.424 INFO 15372 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.721 INFO 15372 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.832 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.845 INFO 15372 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.918 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.919 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.919 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.919 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.920 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.920 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:27.921 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.143 INFO 15372 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.151 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.160 INFO 15372 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1753433549158 with initial instances count: 9
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.170 INFO 15372 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.172 INFO 15372 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7002"]
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.241 INFO 15372 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7002 (http) with context path ''
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.244 INFO 15372 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7002
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.244 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.245 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.246 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.246 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.246 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.246 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.246 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.248 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.249 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.314 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.314 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.314 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.314 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.315 INFO 15372 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.368 INFO 15372 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:29.578 INFO 15372 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.023 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.111 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getTenantUsersUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.112 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getWebUserInfoUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.155 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.162 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.167 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.180 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.209 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.225 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.238 INFO 15372 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.335 INFO 15372 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 31.784 seconds (JVM running for 33.528)
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:30.999 INFO 15372 [RMI TCP Connection(6)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:31.001 INFO 15372 [RMI TCP Connection(6)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:31.008 INFO 15372 [RMI TCP Connection(6)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 6 ms
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:52:32.717 INFO 15372 [RMI TCP Connection(7)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:52.913 INFO 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e3d52e2805373d4c,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [e3d52e2805373d4c,] 2025-07-25 16:54:54.347 INFO 15372 [http-nio-7002-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e3d52e2805373d4c, 耗时=1766, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:08.076 INFO 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=88787bf2b051c4c3,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/export , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [88787bf2b051c4c3,] 2025-07-25 16:55:10.887 INFO 15372 [http-nio-7002-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=88787bf2b051c4c3, 耗时=2811, resp=null:
[kbc-elms-web:*********:7002] [,] 2025-07-25 16:57:27.929 INFO 15372 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.258 INFO 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d4abc03272161338,  url=com.kbao.kbcelms.controller.dataTemplate.DataTemplateController/detail , httpMethod=null, reqData={"param1":{"id":3}} 
[kbc-elms-web:*********:7002] [d4abc03272161338,] 2025-07-25 16:59:18.376 INFO 15372 [http-nio-7002-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d4abc03272161338, 耗时=118, resp={"datas":{"bizCode":"enterprise","createId":"U000162","createTime":1753354888000,"fields":[{"additional":{"input_unit":""},"change":"1","fieldCode":"name","fieldLength":"50","fieldName":"企业名称","fieldType":"varchar","id":"68821286e0cf4711e805daa0","isIndex":"0","required":"0","showType":"1","sort":1,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseCode","fieldLength":"50","fieldName":"社会信用代码","fieldType":"varchar","id":"68821286e0cf4711e805daa1","isIndex":"1","required":"0","showType":"1","sort":2,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"type","fieldLength":"1","fieldName":"企业类型","fieldType":"varchar","id":"68832511585c3e034bbc6031","isIndex":"0","required":"0","showType":"1","sort":3,"templateId":3},{"additional":{"input_unit":"人"},"change":"1","fieldCode":"staffSize","fieldName":"人员规模","fieldType":"int","id":"68821286e0cf4711e805daa2","isIndex":"0","required":"0","showType":"1","sort":4,"templateId":3},{"additional":{"select_options":[]},"change":"1","fieldCode":"city","fieldLength":"30","fieldName":"所在城市","fieldType":"varchar","id":"68821286e0cf4711e805daa3","isIndex":"0","required":"0","showType":"3","sort":5,"templateId":3},{"additional":{},"change":"0","fieldCode":"industryId","fieldName":"所属行业ID","fieldType":"int","id":"68821286e0cf4711e805daa4","isIndex":"1","required":"0","showType":"0","sort":6,"templateId":3},{"additional":{"input_unit":"万"},"change":"1","fieldCode":"annualIncome","fieldName":"年收入","fieldType":"int","id":"68821286e0cf4711e805daa5","isIndex":"0","required":"0","showType":"1","sort":7,"templateId":3},{"additional":{},"change":"0","fieldCode":"isVerified","fieldLength":"1","fieldName":"是否验真","fieldType":"varchar","id":"68832511585c3e034bbc6030","isIndex":"0","required":"0","showType":"0","sort":8,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"enterpriseContacter","fieldLength":"20","fieldName":"企业联系人","fieldType":"varchar","id":"68832511585c3e034bbc6032","isIndex":"0","required":"0","showType":"1","sort":9,"templateId":3},{"additional":{"input_unit":""},"change":"1","fieldCode":"contacterPhone","fieldLength":"20","fieldName":"联系人电话","fieldType":"varchar","id":"68832511585c3e034bbc6033","isIndex":"0","required":"0","showType":"1","sort":10,"templateId":3},{"additional":{},"change":"1","fieldCode":"remark","fieldLength":"300","fieldName":"备注信息","fieldType":"varchar","id":"68832511585c3e034bbc6034","isIndex":"0","required":"0","showType":"6","sort":11,"templateId":3}],"id":3,"isDeleted":0,"remark":"企业表测试","status":"1","templateName":"企业信息表","tenantId":"T0001","type":"企业信息","updateId":"U000162","updateTime":1753425581000},"resp_code":0,"resp_msg":"请求成功"}:
