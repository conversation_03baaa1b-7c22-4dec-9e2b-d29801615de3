[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:40.386 DEBUG 44400 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:41.923 DEBUG 44400 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:42.024 DEBUG 44400 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:43.342 DEBUG 44400 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:43.458 DEBUG 44400 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:43.858 DEBUG 44400 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:43.902 DEBUG 44400 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.083 DEBUG 44400 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.108 DEBUG 44400 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.135 DEBUG 44400 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.159 DEBUG 44400 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.252 DEBUG 44400 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.278 DEBUG 44400 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.305 DEBUG 44400 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.400 DEBUG 44400 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.519 DEBUG 44400 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.848 DEBUG 44400 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.872 DEBUG 44400 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.899 DEBUG 44400 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:44.922 DEBUG 44400 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:10.78.8.1:7002] [,] 2025-07-31 17:41:45.075 DEBUG 44400 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
