<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-elms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-elms-web-client</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>kbc-elms-web-client</name>
    <description>内网端 fegin客户端</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- 业务实体-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-elms-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>feign-spring-boot-starter</artifactId>
        </dependency>
        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>tool-spring-boot-starter</artifactId>
        </dependency>

        <!-- 公共实体配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
