

window.dtChalk = ""
window.dtHandleTheme = function(val, oldVal,version) {

  var ORIGINAL_THEME = "#409EFF" // 默认主题
  function getThemeCluster(theme) {
    var tintColor = (color, tint) => {
      var red = parseInt(color.slice(0, 2), 16);
      var green = parseInt(color.slice(2, 4), 16);
      var blue = parseInt(color.slice(4, 6), 16);
  
      if (tint === 0) {
        // when primary color is in its rgb space
        return [red, green, blue].join(",");
      } else {
        red += Math.round(tint * (255 - red));
        green += Math.round(tint * (255 - green));
        blue += Math.round(tint * (255 - blue));
  
        red = red.toString(16);
        green = green.toString(16);
        blue = blue.toString(16);
  
        return `#${red}${green}${blue}`;
      }
    };
  
    var shadeColor = (color, shade) => {
      var red = parseInt(color.slice(0, 2), 16);
      var green = parseInt(color.slice(2, 4), 16);
      var blue = parseInt(color.slice(4, 6), 16);
  
      red = Math.round((1 - shade) * red);
      green = Math.round((1 - shade) * green);
      blue = Math.round((1 - shade) * blue);
  
      red = red.toString(16);
      green = green.toString(16);
      blue = blue.toString(16);
  
      return `#${red}${green}${blue}`;
    };
  
    var clusters = [theme];
    for (var i = 0; i <= 9; i++) {
      clusters.push(tintColor(theme, Number((i / 10).toFixed(2))));
    }
    clusters.push(shadeColor(theme, 0.1));
    return clusters;
  }

  function updateStyle(style, oldCluster, newCluster) {
    var newStyle = style;
    oldCluster.forEach((color, index) => {
      newStyle = newStyle.replace(new RegExp(color, "ig"), newCluster[index]);
    });
    return newStyle;
  }

  function getCSSString(url, callback, variable) {
    var xhr = new XMLHttpRequest();
    xhr.onreadystatechange = () => {
      if (xhr.readyState === 4 && xhr.status === 200) {
        this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, "");
        callback();
      }
    };
    xhr.open("GET", url);
    xhr.send();
  }



  var themeCluster = getThemeCluster(val.replace("#", ""));
  var originalCluster = getThemeCluster(oldVal.replace("#", ""));
  var getHandler = (variable, id) => {
    return () => {
      var originalCluster = getThemeCluster(
        ORIGINAL_THEME.replace("#", "")
      );
      var newStyle = updateStyle(
        this[variable],
        originalCluster,
        themeCluster
      );

      var styleTag = document.getElementById(id);
      if (!styleTag) {
        styleTag = document.createElement("style");
        styleTag.setAttribute("id", id);
        document.head.appendChild(styleTag);
      }
      styleTag.innerText = newStyle;
    };
  };

  var chalkHandler = getHandler("dtChalk", "chalk-style");

  if (!window.dtChalk) {
    var url = "https://unpkg.com/element-ui@"+version+"/lib/theme-chalk/index.css";
    getCSSString(url, chalkHandler, "chalk");
  } else {
    chalkHandler();
  }

  var styles = [].slice
    .call(document.querySelectorAll("style"))
    .filter(style => {
      var text = style.innerText;
      return (
        new RegExp(oldVal, "i").test(text) && !/Chalk Variables/.test(text)
      );
    });
  styles.forEach(style => {
    var { innerText } = style;
    if (typeof innerText !== "string") return;
    style.innerText = updateStyle(
      innerText,
      originalCluster,
      themeCluster
    );
  });
}